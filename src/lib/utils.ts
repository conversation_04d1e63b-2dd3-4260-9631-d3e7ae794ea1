import axios from "axios";
import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function searchObject(obj, word) {
  return Object.values(obj).some((value) => {
    if (typeof value === "object" && value !== null) {
      return searchObject(value, word); // Recursive call for nested objects
    } else {
      return String(value).toLowerCase().includes(word);
    }
  });
}

export const formatNumberWithDots = (num: number | string): string => {
  const value = typeof num === "string" ? parseFloat(num) : num;

  if (isNaN(value)) return "0";
  if (value < 1000) return value.toString();

  return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");
};

export const extractLastElapsedTimeFromMedusa = (logs: string): number => {
  const lines = logs.split("\n");
  const fuzzLines = lines.filter((line) => line.includes("fuzz: elapsed:"));

  if (!fuzzLines.length) return 0;

  const lastLine = fuzzLines[fuzzLines.length - 1];
  const elapsedMatch = lastLine.match(/elapsed: (?:(\d+)h)?(?:(\d+)m)?(\d+)s/);

  if (!elapsedMatch) return 0;

  const hours = parseInt(elapsedMatch[1] || "0");
  const minutes = parseInt(elapsedMatch[2] || "0");
  const seconds = parseInt(elapsedMatch[3] || "0");

  return hours * 3600 + minutes * 60 + seconds;
};

export const extractLastTestCountFromEchidna = (logs: string): number => {
  const lines = logs.split("\n");
  const statusLines = lines.filter((line) => line.includes("[status] tests:"));

  if (!statusLines.length) return 0;

  const lastLine = statusLines[statusLines.length - 1];
  const fuzzingMatch = lastLine.match(/fuzzing: (\d+)\/(\d+)/);

  if (!fuzzingMatch) return 0;

  const current = parseInt(fuzzingMatch[1]);
  const total = parseInt(fuzzingMatch[2]);

  const progress = (current / total) * 100;
  return Math.min(Math.max(Math.round(progress), 0), 100);
};

export const extractEchidnaPace = (logs: string): number => {
  const lines = logs.split("\n");
  const statusLines = lines.filter((line) => line.includes("[status] tests:"));

  if (statusLines.length < 2) return 0;

  const lastLine = statusLines[statusLines.length - 1];
  const fuzzingMatch = lastLine.match(/fuzzing: (\d+)\/(\d+)/);

  if (!fuzzingMatch) return 0;

  const current = parseInt(fuzzingMatch[1]);
  const total = parseInt(fuzzingMatch[2]);

  if (current >= total) return 0; // Current test in echidna logs may be >= total

  let count = 5; // Start with 5 lines back -- approx 1 log every 3s
  const maxCount = Math.min(20, statusLines.length - 1); // Don't go back more than 20 lines or array length

  let prev = current;
  while (current === prev && count < maxCount) {
    const prevLine = statusLines[statusLines.length - count];
    const prevMatch = prevLine.match(/fuzzing: (\d+)\/(\d+)/);
    if (!prevMatch) break;

    prev = parseInt(prevMatch[1]);
    count++;
  }

  // If we couldn't find different values, use minimum pace
  if (current === prev) {
    return Math.round((total - current) / 1500); // Assume 1500 tests per second minimum
  }

  // Each status line is roughly 3 seconds apart
  const timeSpan = (count - 1) * 3;
  const pace = Math.max((current - prev) / timeSpan, 1);
  const remainingTests = Math.max(total - current, 0);

  return Math.round(remainingTests / pace);
};

export const formatSecondsToDaysHoursMinutes = (seconds: number): string => {
  const days = Math.floor(seconds / (24 * 3600));
  const hours = Math.floor((seconds % (24 * 3600)) / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);

  const etaString =
    days > 0
      ? `${days}d ${hours}h ${minutes}m`
      : hours > 0
        ? `${hours}h ${minutes}m`
        : `${minutes}m`;
  return etaString;
};

export interface BytecodeComparisonResult {
  isIdentical: boolean;
  replacedSections: {
    file1: Array<{type: string, position: number, length: number, content: string}>;
    file2: Array<{type: string, position: number, length: number, content: string}>;
  };
  differences: Array<{
    start: number;
    end: number;
    content1: string;
    content2: string;
    context1: string;
    context2: string;
  }>;
  summary: {
    totalDiffLength: number;
    percentDiff: string;
  };
}

export function compareBytecode(
  bytecode1: string,
  bytecode2: string,
  ignoreBytecodeHash?: boolean,
  ignoreCborMetadata?: boolean,
  // bytecodeHashPattern?: RegExp,
  // cborMetadataPattern?: RegExp,
): BytecodeComparisonResult {
  try {
    // Default patterns for bytecode hash and CBOR metadata
    // const bytecodeHashRegex = bytecodeHashPattern ?? /a264697066735822[0-9a-fA-F]{68}/g;
    // const cborMetadataRegex = cborMetadataPattern ?? /a2644970667358[0-9a-fA-F]+?6673/g;
    const bytecodeHashRegex = /a264697066735822[0-9a-fA-F]{68}/g;
    const cborMetadataRegex = /a2644970667358[0-9a-fA-F]+?6673/g;
    
    /**
     *
     * For a more generic pattern, you might want to look for:
      CBOR-encoded hashes:
      Copy/a[1-9][0-9a-f]*58[0-9a-f]{2}[0-9a-fA-F]{40,128}/g
      This would match CBOR maps with byte strings that are between 20-64 bytes long (typical hash lengths).
      Raw hashes without encoding:
      Copy/(?<![0-9a-fA-F])[0-9a-fA-F]{64}(?![0-9a-fA-F])/g
      This would match standalone 32-byte (64 hex character) sequences typical of SHA-256/Keccak-256.
      Metadata identifier with hash:
      Bytecode might have specific prefixes before hash values that identify what they are. For example, in Solidity contracts, you might see patterns like:
      Copy/metadata([0-9a-fA-F]{64})/g


      The specific patterns would depend on:
     *
     */

    // Create copies for analysis that can be modified
    let analyzedBytecode1 = bytecode1;
    let analyzedBytecode2 = bytecode2;

    // Maps to track replaced sections for reporting
    const replacedSections1: {type: string, position: number, length: number, content: string}[] = [];
    const replacedSections2: {type: string, position: number, length: number, content: string}[] = [];

    // Replace bytecode hash if requested
    if (ignoreBytecodeHash) {
      // Find and replace bytecode hash in first file
      let match;
      while ((match = bytecodeHashRegex.exec(analyzedBytecode1)) !== null) {
        const hashContent = match[0];
        replacedSections1.push({
          type: 'Bytecode Hash',
          position: match.index,
          length: hashContent.length,
          content: hashContent
        });
      }

      // Find and replace bytecode hash in second file
      bytecodeHashRegex.lastIndex = 0; // Reset regex index
      while ((match = bytecodeHashRegex.exec(analyzedBytecode2)) !== null) {
        const hashContent = match[0];
        replacedSections2.push({
          type: 'Bytecode Hash',
          position: match.index,
          length: hashContent.length,
          content: hashContent
        });
      }

      // Replace the hashes with placeholders for comparison
      analyzedBytecode1 = analyzedBytecode1.replace(bytecodeHashRegex, '[BYTECODE_HASH]');
      analyzedBytecode2 = analyzedBytecode2.replace(bytecodeHashRegex, '[BYTECODE_HASH]');
    }

    // Replace CBOR metadata if requested
    if (ignoreCborMetadata) {
      // Find and replace CBOR metadata in first file
      let match;
      while ((match = cborMetadataRegex.exec(analyzedBytecode1)) !== null) {
        const metadataContent = match[0];
        replacedSections1.push({
          type: 'CBOR Metadata',
          position: match.index,
          length: metadataContent.length,
          content: metadataContent
        });
      }

      // Find and replace CBOR metadata in second file
      cborMetadataRegex.lastIndex = 0; // Reset regex index
      while ((match = cborMetadataRegex.exec(analyzedBytecode2)) !== null) {
        const metadataContent = match[0];
        replacedSections2.push({
          type: 'CBOR Metadata',
          position: match.index,
          length: metadataContent.length,
          content: metadataContent
        });
      }

      // Replace the metadata with placeholders for comparison
      analyzedBytecode1 = analyzedBytecode1.replace(cborMetadataRegex, '[CBOR_METADATA]');
      analyzedBytecode2 = analyzedBytecode2.replace(cborMetadataRegex, '[CBOR_METADATA]');
    }

    // Check if bytecodes are identical after replacements
    if (analyzedBytecode1 === analyzedBytecode2) {
      return {
        isIdentical: true,
        replacedSections: {
          file1: replacedSections1,
          file2: replacedSections2
        },
        differences: [],
        summary: {
          totalDiffLength: 0,
          percentDiff: "0.00"
        }
      };
    }

    // Find differences in the analyzed bytecode
    const differences: Array<{
      start: number,
      end: number,
      content1: string,
      content2: string,
      context1: string,
      context2: string
    }> = [];
    let currentDiffStart: number | null = null;

    for (let i = 0; i < Math.max(analyzedBytecode1.length, analyzedBytecode2.length); i++) {
      if (analyzedBytecode1[i] !== analyzedBytecode2[i]) {
        // Start of a new difference
        if (currentDiffStart === null) {
          currentDiffStart = i;
        }
      } else if (currentDiffStart !== null) {
        // End of a difference
        const diffEnd = i - 1;

        // Get some context around the difference
        const contextSize = 16; // Number of characters to show before and after
        const contextStart = Math.max(0, currentDiffStart - contextSize);
        const contextEnd = Math.min(Math.max(analyzedBytecode1.length, analyzedBytecode2.length), diffEnd + contextSize + 1);

        const context1 = analyzedBytecode1.substring(contextStart, contextEnd);
        const context2 = analyzedBytecode2.substring(contextStart, contextEnd);

        differences.push({
          start: currentDiffStart,
          end: diffEnd,
          content1: analyzedBytecode1.substring(currentDiffStart, i),
          content2: analyzedBytecode2.substring(currentDiffStart, i),
          context1,
          context2
        });
        currentDiffStart = null;
      }
    }

    // Check if there's still an open difference at the end
    if (currentDiffStart !== null) {
      const diffEnd = Math.max(analyzedBytecode1.length, analyzedBytecode2.length) - 1;

      // Get some context around the difference
      const contextSize = 16; // Number of characters to show before and after
      const contextStart = Math.max(0, currentDiffStart - contextSize);
      const contextEnd = Math.min(Math.max(analyzedBytecode1.length, analyzedBytecode2.length), diffEnd + contextSize + 1);

      const context1 = analyzedBytecode1.substring(contextStart, contextEnd);
      const context2 = analyzedBytecode2.substring(contextStart, contextEnd);

      differences.push({
        start: currentDiffStart,
        end: diffEnd,
        content1: analyzedBytecode1.substring(currentDiffStart),
        content2: analyzedBytecode2.substring(currentDiffStart),
        context1,
        context2
      });
    }

    // Summary statistics
    const totalDiffLength = differences.reduce((sum, diff) => sum + (diff.end - diff.start + 1), 0);
    const percentDiff = (totalDiffLength / Math.max(analyzedBytecode1.length, analyzedBytecode2.length) * 100).toFixed(2);

    return {
      isIdentical: false,
      replacedSections: {
        file1: replacedSections1,
        file2: replacedSections2
      },
      differences,
      summary: {
        totalDiffLength,
        percentDiff
      }
    };
  } catch (error) {
    console.error('Error comparing bytecode files:', error);
    return {
      isIdentical: false,
      replacedSections: {
        file1: [],
        file2: []
      },
      differences: [],
      summary: {
        totalDiffLength: 0,
        percentDiff: "0.00"
      }
    };
  }
}

export const chains = [
  { id: 1, name: "Ethereum Mainnet" },
  { id: 5, name: "Goerli Testnet" },
  { id: 137, name: "Polygon Mainnet" },
  { id: 42161, name: "Arbitrum One" },
  { id: 10, name: "Optimism" },
  { id: 11155111, name: "Sepolia Testnet" },
  { id: 534352, name: "Scroll Mainnet" },
  { id: 21000001, name: "Corn Testnet" },
  { id: 21000000, name: "Corn Mainnet" },
];

// Ensure the input is a valid hex string
function normalizeHexString(hexString: string): string {
  // Remove '0x' prefix if present
  if (hexString.startsWith('0x')) {
    hexString = hexString.slice(2);
  }
  
  // Ensure the string only contains valid hex characters
  if (!/^[0-9a-fA-F]*$/.test(hexString)) {
    throw new Error('Input must be a valid hexadecimal string');
  }
  
  // Pad to even length if needed
  if (hexString.length % 2 !== 0) {
    hexString = '0' + hexString;
  }
  
  return hexString;
}

// Format hex data by 32-byte words
function formatByWords(hexString: string): string {
  const normalizedHex = normalizeHexString(hexString);
  let result = '';
  
  // Each word is 32 bytes (64 hex characters)
  const wordSize = 64;
  
  for (let i = 0; i < normalizedHex.length; i += wordSize) {
    const wordEnd = Math.min(i + wordSize, normalizedHex.length);
    const word = normalizedHex.slice(i, wordEnd);
    
    // Add word index and the word itself
    result += `${word}\n`;
  }
  
  return result;
}

// Format as calldata (4-byte selector + 32-byte words)
function formatAsCalldata(hexString: string): string {
  const normalizedHex = normalizeHexString(hexString);
  let result = '';
  
  // Check if we have at least 4 bytes for the function selector
  if (normalizedHex.length < 8) {
    return 'Input is too short to be valid calldata (needs at least 4 bytes)';
  }
  
  // Extract function selector (first 4 bytes / 8 hex characters)
  const selector = normalizedHex.slice(0, 8);
  result += `Function selector: 0x${selector}\n\n`;
  
  // Format the remaining data as 32-byte words
  const remainingData = normalizedHex.slice(8);
  const wordSize = 64; // 32 bytes = 64 hex characters
  
  for (let i = 0; i < remainingData.length; i += wordSize) {
    const wordEnd = Math.min(i + wordSize, remainingData.length);
    const word = remainingData.slice(i, wordEnd);
    
    // Add word index and the word itself
    result += `${word}\n`;
    
    // If this word is incomplete (less than 32 bytes), note it
    if (word.length < wordSize) {
      result += `(incomplete word: ${word.length / 2} bytes)\n`;
    }
  }
  
  return result;
}

// Main function to process input
export function formatHexData(hexString: string, format: 'words' | 'calldata'): string {
  try {
    // Choose formatting function based on format parameter
    if (format === 'words') {
      return formatByWords(hexString);
    } else if (format === 'calldata') {
      return formatAsCalldata(hexString);
    } else {
      return 'Invalid format specified. Use "words" or "calldata".';
    }
  } catch (error) {
    return `Error: ${error.message}`;
  }
}
