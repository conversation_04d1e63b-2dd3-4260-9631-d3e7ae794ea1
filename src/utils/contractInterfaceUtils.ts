import { ethers } from "ethers";

/**
 * Represents a function signature with name and parameter types
 */
interface FunctionSignature {
    name: string;
    signature: string;
    paramTypes: string[];
    defs?: string; // Add struct definitions
}

/**
 * Lookup function signature from public signature databases
 */
export const lookupFunctionSignature = async (selector: string, expectedArgTypes: string[] = [], index: number): Promise<FunctionSignature | null> => {
    try {
        const signatures: FunctionSignature[] = [];

        // Try to lookup from 4byte.directory
        try {
            const url = `https://www.4byte.directory/api/v1/signatures/?hex_signature=0x${selector}`;
            const response = await fetch(url);
            const data = await response.json();

            if (data.results && data.results.length > 0) {
                for (const result of data.results) {
                    if (result.text_signature) {
                        const match = result.text_signature.match(/^([^(]+)\((.*)\)$/);
                        if (match) {
                            const name = match[1];
                            const paramStr = match[2];
                            const { paramTypes, defs } = parseStructType(paramStr, index);
                            signatures.push({
                                name,
                                signature: result.text_signature,
                                paramTypes,
                                defs
                            });
                        }
                    }
                }
            }
        } catch (error) {
            console.log("Error fetching from 4byte.directory:", error);
        }

        // Try OpenChain API as fallback
        try {
            const openChainUrl = `https://api.openchain.xyz/signature-database/v1/lookup?function=0x${selector}`;
            const openChainResponse = await fetch(openChainUrl);
            const openChainData = await openChainResponse.json();

            if (openChainData.ok &&
                openChainData.result &&
                openChainData.result.function &&
                openChainData.result.function[`0x${selector}`]) {

                const results = openChainData.result.function[`0x${selector}`];
                for (const result of results) {
                    if (result.name) {
                        const match = result.name.match(/^([^(]+)\((.*)\)$/);
                        if (match) {
                            const name = match[1];
                            const paramStr = match[2];
                            const { paramTypes, defs } = parseStructType(paramStr, index);
                            signatures.push({
                                name,
                                signature: result.name,
                                paramTypes,
                                defs
                            });
                        }
                    }
                }
            }
        } catch (error) {
            console.log("Error fetching from OpenChain API:", error);
        }

        // If we have any signatures, find the best match
        if (signatures.length > 0) {
            return findBestSignatureMatch(signatures, expectedArgTypes);
        }

        return null;
    } catch (err) {
        console.log("Error looking up function signature:", err);
        return null;
    }
};

/**
 * Find the best signature match based on number of parameters and type matches
 */
const findBestSignatureMatch = (
    signatures: FunctionSignature[],
    expectedArgTypes: string[]
): FunctionSignature => {
    // First filter by matching parameter count
    const countMatches = signatures.filter(sig => sig.paramTypes.length === expectedArgTypes.length);

    if (countMatches.length > 0) {
        // If we have multiple matches with the right param count, prefer those with matching types
        if (countMatches.length > 1) {
            // Calculate "score" for each signature based on matching parameter types
            const scoredMatches = countMatches.map(sig => {
                let score = 0;
                for (let i = 0; i < sig.paramTypes.length; i++) {
                    // Normalize types for comparison (uint256 vs uint, etc)
                    const normalizedExpected = normalizeType(expectedArgTypes[i]);
                    const normalizedActual = normalizeType(sig.paramTypes[i]);

                    if (normalizedExpected === normalizedActual) {
                        score++;
                    }
                }

                // Add name quality score: penalize names with special characters or numbers
                // This will be a secondary sort criterion
                const nameQualityScore = getNameQualityScore(sig.name);

                return {
                    signature: sig,
                    typeScore: score,
                    nameQualityScore: nameQualityScore,
                    totalScore: score * 1000 + nameQualityScore // Weight type matches much higher
                };
            });

            // Sort by type score descending, then by name quality score
            scoredMatches.sort((a, b) => {
                // First compare by type score
                if (b.typeScore !== a.typeScore) {
                    return b.typeScore - a.typeScore;
                }

                // If type scores are equal, compare by name quality
                return b.nameQualityScore - a.nameQualityScore;
            });

            // Return the signature with highest score
            return scoredMatches[0].signature;
        }

        return countMatches[0];
    }

    // If no parameter count matches, sort by name quality and return the first
    const scoredByName = signatures.map(sig => ({
        signature: sig,
        nameQualityScore: getNameQualityScore(sig.name)
    }));

    scoredByName.sort((a, b) => b.nameQualityScore - a.nameQualityScore);
    return scoredByName[0].signature;
};

/**
 * Score a function name based on quality
 * Higher score = better name quality
 * Penalizes names with special characters, numbers and unusual patterns
 */
const getNameQualityScore = (name: string): number => {
    let score = 100; // Start with a baseline score

    // Penalize names with numbers
    const numberCount = (name.match(/\d/g) || []).length;
    score -= numberCount * 10;

    // Penalize names with underscores
    const underscoreCount = (name.match(/_/g) || []).length;
    score -= underscoreCount * 8;

    // Penalize names with other special characters
    const specialCharCount = (name.match(/[^\w]/g) || []).length - underscoreCount;
    score -= specialCharCount * 15;

    // Penalize very short names (likely not descriptive)
    if (name.length < 3) {
        score -= 20;
    }

    return Math.max(0, score); // Ensure score is not negative
};

/**
 * Normalize type names for better comparison
 * e.g. uint256 -> uint, address -> address
 */
const normalizeType = (type: string): string => {
    // Handle arrays
    if (type.endsWith('[]')) {
        return normalizeType(type.slice(0, -2)) + '[]';
    }

    // Basic normalization for common types
    if (type.startsWith('uint')) return 'uint';
    if (type.startsWith('int')) return 'int';
    if (type.startsWith('bytes')) return type; // Keep bytes specific (bytes vs bytes32)

    return type;
};

/**
 * Find function info from ABI by matching selector
 */
export const findFunctionInfoFromAbi = (abi: any[] | null, selector: string): { name: string, signature: string, inputs: any[] } | null => {
    if (!abi) return null;

    for (const item of abi) {
        if (item.type === "function") {
            const funcSignature = `${item.name}(${item.inputs.map(i => i.type).join(',')})`;
            const funcId = ethers.keccak256(ethers.toUtf8Bytes(funcSignature)).slice(0, 10);

            if (funcId.slice(2) === selector) {
                return {
                    name: item.name,
                    signature: funcSignature,
                    inputs: item.inputs
                };
            }
        }
    }
    return null;
};

// Add helper functions for struct handling
const parseStructType = (paramStr: string, index: number): { paramTypes: string[], defs: string } => {
    const structs = new Map<string, string[]>();
    let structCounter = 0;

    const parseParams = (str: string, parentStruct?: string): string => {
        if (!str.startsWith('(')) return str;

        const inner = str.slice(1, -1);
        const params = splitTopLevel(inner);

        const structName = parentStruct || `S${index}_${structCounter++}`;
        const fields: string[] = [];

        const paramTypes = params.map((param, idx) => {
            if (param.startsWith('(')) {
                const nested = parseParams(param, `S${index}_${structCounter++}`);
                fields.push(`${nested} arg${idx}`);
                return nested;
            } else {
                fields.push(`${param} arg${idx}`);
                return param;
            }
        });

        structs.set(structName, fields);
        return structName;
    };

    const paramTypes = splitTopLevel(paramStr).map((param, idx) => {
        const isArray = param.endsWith('[]');
        const baseType = isArray ? param.slice(0, -2) : param;
        const parsedType = parseParams(baseType);
        const finalType = isArray ? `${parsedType}[]` : parsedType;
        return `${finalType} arg${idx}`;
    });

    // Generate struct definitions
    let defs = '';
    for (const [name, fields] of structs.entries()) {
        defs += `    struct ${name} {\n`;
        defs += fields.map(field => `        ${field};`).join('\n');
        defs += '\n    }\n\n';
    }

    return { paramTypes, defs: defs.trim() };
};

const splitTopLevel = (str: string): string[] => {
    const results: string[] = [];
    let current = '';
    let depth = 0;

    for (const char of str) {
        if (char === '(') depth++;
        else if (char === ')') depth--;
        else if (char === ',' && depth === 0) {
            results.push(current);
            current = '';
            continue;
        }
        current += char;
    }
    if (current) results.push(current);

    return results;
};
