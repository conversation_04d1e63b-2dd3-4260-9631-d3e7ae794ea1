const ONE_MINUTE = 60;
const ONE_HOUR = 60 * 60;
const ONE_DAY = 60 * 60 * 24;

export function formatDateString(utcDate: string): string {
  const currentTime = Math.floor(new Date().getTime() / 1000);
  const dateTime = Math.floor(new Date(utcDate).getTime() / 1000);
  const secondsAgo = currentTime - dateTime;
  // Days
  if (secondsAgo > ONE_DAY) {
    const math = Math.round(secondsAgo / ONE_DAY);
    return `${math} day${math !== 1 ? "s " : " "}ago`;
  }

  // Hours
  if (secondsAgo > ONE_HOUR) {
    const math = Math.round(secondsAgo / ONE_HOUR);
    return `${math} hour${math !== 1 ? "s " : " "} ago`;
  }

  // Minutes
  if (secondsAgo > ONE_MINUTE) {
    const math = Math.round(secondsAgo / ONE_MINUTE);
    return `${Math.round(secondsAgo / ONE_MINUTE)} minute${
      math !== 1 ? "s " : " "
    } ago`;
  }

  // Seconds
  return `${Math.round(secondsAgo)} second${secondsAgo !== 1 ? "s " : " "} ago`;
}
