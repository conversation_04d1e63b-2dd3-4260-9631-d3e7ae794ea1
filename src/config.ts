const config = {
  auth: {
    // This should be a session token
    // Handled by logging in with us
    // Our could just be storing the OAUTH
    // But we need to make sure this is not done via JWT
    secret: process.env.NEXTAUTH_SECRET!,
  },
  github: {
    // Authentication, we need this to be shared
    oauth: {
      clientId: process.env.GITHUB_APP_CLIENT_ID!,
      secret: process.env.GITHUB_APP_SECRET!,
    },
    app: {
      // This should only be used in the backend
      // Then the backend can send back the list of projects
      // And from the list we can pick the ones to handle, etc..
      // We don't need this to be here
      installationUrl: process.env.NEXT_PUBLIC_GITHUB_APP_INSTALLATION_URL!,
    },
  },
};

export default config;
