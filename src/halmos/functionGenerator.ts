import { type PropertyAndSequence } from "../types/types";
import { parseCallStatement, formatSolidityValue } from "./parameterUtils";
import { findMatchingVariable, generateArrayDeclarations } from "./arrayUtils";

/**
 * Generate function call with mapped parameters
 */
export const generateFunctionCall = (
  brokenProperty: string,
  variableMapping: Map<string, string>
): string => {
  // Extract function name and parameter types from the property
  const functionMatch = brokenProperty.match(/^([^(]+)\(([^)]*)\)/);
  if (!functionMatch) {
    return `${brokenProperty}(/* parameters not parsed */)`;
  }

  const functionName = functionMatch[1];
  const paramTypes = functionMatch[2];

  if (!paramTypes.trim()) {
    return `${functionName}()`;
  }

  // Parse parameter types
  const types = paramTypes.split(",").map((t) => t.trim());
  const parameters: string[] = [];

  // Map parameter types to variable names
  // First, group parameters by type to handle position correctly
  const paramsByType = new Map<string, number[]>();
  types.forEach((type, index) => {
    if (!paramsByType.has(type)) {
      paramsByType.set(type, []);
    }
    paramsByType.get(type)!.push(index);
  });

  types.forEach((type, index) => {
    // For array types, find the position within arrays of the same type
    let positionInType = 0;
    if (type.includes("[]")) {
      const indicesOfSameType = paramsByType.get(type)!;
      positionInType = indicesOfSameType.indexOf(index);
    }
    
    // Look for variables that match this parameter position
    const matchingVar = findMatchingVariable(type, positionInType, variableMapping);
    parameters.push(matchingVar || `/* ${type} parameter */`);
  });

  return `${functionName}(${parameters.join(", ")})`;
};

/**
 * Generate Foundry test function from property and sequence
 * Similar to Medusa's function generation approach
 */
export const generateTestFunction = (
  propSeq: PropertyAndSequence,
  identifier: string,
  index: number
): string => {
  const functionName = `test_${propSeq.brokenProperty.replace(
    /\W/g,
    "_"
  )}_${identifier}_${index}`;
  const sequences = Array.isArray(propSeq.sequence)
    ? propSeq.sequence
    : [propSeq.sequence];

  const parameterDeclarations: string[] = [];
  const usedVariableNames = new Set<string>();
  const variableMapping = new Map<string, string>();
  const arrayDeclarations: string[] = [];
  const sequenceCalls: string[] = [];

  // Process parameter declarations and build variable mapping
  sequences
    .filter(
      (param): param is string =>
        typeof param === "string" &&
        param.includes("=") &&
        param.startsWith("p_")
    )
    .forEach((param) => {
      const [paramName, paramValue] = param.split("=").map((s) => s.trim());
      const solidityDeclaration = formatSolidityValue(paramName, paramValue);

      // Extract variable name to check for duplicates
      const varPattern = /\w+\s+(\w+)\s*=/;
      const varMatch = varPattern.exec(solidityDeclaration);
      if (varMatch) {
        const varName = varMatch[1];
        if (!usedVariableNames.has(varName)) {
          // Only add to parameter declarations if it's not a length parameter
          if (!paramName.includes("_length")) {
            parameterDeclarations.push(`    ${solidityDeclaration}`);
          }
          usedVariableNames.add(varName);
          variableMapping.set(paramName, varName);
        }
      }
    });

  // Process sequence calls
  sequences
    .filter(
      (line): line is string =>
        typeof line === "string" && line.startsWith("CALL ")
    )
    .forEach((callLine) => {
      const parsedCall = parseCallStatement(callLine);
      if (parsedCall) {
        const { functionName: callFunctionName, parameters } = parsedCall;

        // Map parameters to variable names with improved matching
        const mappedParams = parameters.map((param) => {
          // Extract parameter name from complex expressions like Concat(p_param, ...)
          const paramMatch = /p_\w+_[a-f0-9]+_\d+/.exec(param);
          if (paramMatch) {
            const paramName = paramMatch[0];
            const mappedVar = variableMapping.get(paramName);
            if (mappedVar) {
              return mappedVar;
            }

            // If direct mapping fails, try to find a similar parameter by base name
            const baseName = paramName.split("_")[1]; // Extract base name like "flag", "account", etc.
            for (const [key, value] of variableMapping) {
              if (key.includes(baseName)) {
                return value;
              }
            }

            return paramName;
          }

          // Handle simple parameter names that start with p_
          if (param.startsWith("p_")) {
            const mappedVar = variableMapping.get(param);
            if (mappedVar) {
              return mappedVar;
            }

            // Try to find by base name matching
            const baseName = param.split("_")[1];
            for (const [key, value] of variableMapping) {
              if (key.includes(baseName)) {
                return value;
              }
            }
          }

          return param;
        });

        const functionCallStr = `${callFunctionName}(${mappedParams.join(
          ", "
        )})`;
        sequenceCalls.push(`    ${functionCallStr};`);
      }
    });

  // Generate array declarations if needed
  const arrayInfo = generateArrayDeclarations(variableMapping);
  if (arrayInfo.declarations.length > 0) {
    arrayDeclarations.push(...arrayInfo.declarations);
    // Add array variables to mapping for function call generation
    arrayInfo.arrayVariables.forEach((varName: string, arrayName: string) => {
      variableMapping.set(arrayName, varName);
    });
  }

  const parts = [
    `function ${functionName}() public {`,
    `    // Counterexample for: ${propSeq.brokenProperty}`,
  ];

  if (parameterDeclarations.length > 0) {
    parts.push("", "    // Parameter declarations:");
    parts.push(...parameterDeclarations);
  }

  if (arrayDeclarations.length > 0) {
    parts.push("", "    // Array declarations:");
    parts.push(...arrayDeclarations);
  }

  parts.push("", "    // Reproduction sequence:");

  // Add sequence calls if available, otherwise fall back to the original property call
  if (sequenceCalls.length > 0) {
    parts.push(...sequenceCalls);

    // For invariant tests, add the invariant call at the end
    if (propSeq.brokenProperty.includes("invariant")) {
      const invariantCall = generateFunctionCall(
        propSeq.brokenProperty,
        variableMapping
      );
      parts.push(`    ${invariantCall};`);
    }
  } else {
    // Fallback to original behavior for unit tests
    // Make sure array variables are properly mapped before generating the function call
    const functionCall = generateFunctionCall(
      propSeq.brokenProperty,
      variableMapping
    );
    parts.push(`    ${functionCall};`);
  }

  parts.push("}");

  return parts.join("\n");
};
