import { extractTypeFromParamName } from "./parameterUtils";

/**
 * Find matching variable for a parameter type and position
 */
export const findMatchingVariable = (
  type: string,
  position: number,
  variableMapping: Map<string, string>
): string | null => {
  if (type.includes("[") && type.includes("]")) {
    // Extract the base type from array type (e.g., "address" from "address[]")
    const baseType = type.replace("[]", "");
    const elementPattern = /^(.+)\[(\d+)\]/;
    const lengthPattern = /^p_(.+)_length/;
    const arraysByType = new Map<string, Set<string>>();

    // Group arrays by their element type (from array elements)
    for (const [paramName] of variableMapping) {
      const match = elementPattern.exec(paramName);
      if (match) {
        const fullArrayName = match[1];
        const arrayName = fullArrayName.startsWith("p_")
          ? fullArrayName.substring(2)
          : fullArrayName;

        // Check if this array contains elements of the correct type
        const paramType = extractTypeFromParamName(paramName);
        if (!arraysByType.has(paramType)) {
          arraysByType.set(paramType, new Set());
        }
        arraysByType.get(paramType)!.add(arrayName);
      }
    }

    // Also check for arrays that only have length parameters (no elements)
    for (const [paramName] of variableMapping) {
      const lengthMatch = lengthPattern.exec(paramName);
      if (lengthMatch) {
        const arrayName = lengthMatch[1];

        // Only add this array if it doesn't already exist in arraysByType
        // (to avoid duplicates from arrays that have both elements and length)
        let alreadyExists = false;
        for (const [, arraySet] of arraysByType) {
          if (arraySet.has(arrayName)) {
            alreadyExists = true;
            break;
          }
        }

        if (!alreadyExists) {
          // Try to infer the type from the array name
          let inferredType = "uint256";
          if (arrayName.includes("address")) {
            inferredType = "address";
          } else if (arrayName.includes("bool")) {
            inferredType = "bool";
          }

          // If this matches our target type, add it to the arrays
          if (inferredType === baseType) {
            if (!arraysByType.has(baseType)) {
              arraysByType.set(baseType, new Set());
            }
            arraysByType.get(baseType)!.add(arrayName);
          }
        }
      }
    }

    // Get arrays of the matching type
    const matchingArrays = arraysByType.get(baseType);

    if (matchingArrays && matchingArrays.size > 0) {
      const sortedArrayNames = Array.from(matchingArrays).sort();

      if (position < sortedArrayNames.length) {
        const arrayName = sortedArrayNames[position];
        const arrayVarName = `${arrayName}_array`;

        // Check if the array variable is already in the mapping
        if (variableMapping.has(arrayName)) {
          return variableMapping.get(arrayName)!;
        }

        return arrayVarName;
      }
    }

    return createArrayParameter(type, variableMapping);
  }

  // Generic type patterns for parameter matching
  const typePatterns = {
    address: ["_address"],
    bool: ["_bool"],
    uint256: ["_uint256"],
    uint8: ["_uint8"],
    uint16: ["_uint16"],
    uint32: ["_uint32"],
    uint64: ["_uint64"],
    uint128: ["_uint128"],
    bytes: ["_bytes"],
    string: ["_string"],
  };

  // First, try to find exact matches based on position
  const positionNames = ["a", "b", "c", "d", "e"];
  if (position < positionNames.length) {
    const expectedName = `${positionNames[position]}_${type.replace("[]", "")}`;
    for (const [paramName, varName] of variableMapping) {
      if (
        paramName.includes(expectedName.replace("uint256", "uint256")) ||
        paramName.includes(expectedName.replace("address", "address")) ||
        paramName.includes(expectedName.replace("bool", "bool"))
      ) {
        return varName;
      }
    }
  }

  // Enhanced fallback: find any variable of the matching type with pattern matching
  const patterns = typePatterns[type as keyof typeof typePatterns] || [
    `_${type}`,
  ];
  for (const pattern of patterns) {
    for (const [paramName, varName] of variableMapping) {
      // Check if the parameter name contains the type pattern
      if (paramName.includes(pattern)) {
        return varName;
      }
    }
  }

  // Last resort: try to find any variable that matches the base type
  for (const [paramName, varName] of variableMapping) {
    if (paramName.includes(`_${type}_`) || paramName.includes(`_${type}`)) {
      return varName;
    }
  }

  return null;
};

export const createArrayParameter = (
  type: string,
  variableMapping: Map<string, string>
): string => {
  const arrayElements: string[] = [];
  let arrayLength = 0;

  // Find all array elements
  const elementMap = new Map<number, string>();
  const elementPattern = /arr\[(\d+)\]/;

  for (const [paramName, varName] of variableMapping) {
    const match = elementPattern.exec(paramName);
    if (match) {
      const index = parseInt(match[1], 10);
      elementMap.set(index, varName);
      arrayLength = Math.max(arrayLength, index + 1);
    }
  }

  // Build array elements in order
  for (let i = 0; i < arrayLength; i++) {
    const element = elementMap.get(i);
    if (element) {
      arrayElements.push(element);
    }
  }

  if (arrayElements.length > 0) {
    return `[${arrayElements.join(", ")}]`;
  }

  return `/* ${type} parameter */`;
};

export const generateArrayDeclarations = (
  variableMapping: Map<string, string>
): { declarations: string[]; arrayVariables: Map<string, string> } => {
  const declarations: string[] = [];
  const arrayVariables = new Map<string, string>();

  // Find all array elements and group them
  const arrayGroups = new Map<
    string,
    Map<number, { varName: string; type: string }>
  >();

  // Pattern to match array elements like p_keys[0], p_values[1], arr[2], etc.
  const elementPattern = /^(.+)\[(\d+)\]/;
  // Pattern to match array length parameters like p_keys_length, p_values_length
  const lengthPattern = /^p_(.+)_length/;

  // First pass: collect array elements
  for (const [paramName, varName] of variableMapping) {
    const match = elementPattern.exec(paramName);
    if (match) {
      const fullArrayName = match[1]; // e.g., "p_keys", "p_values", "arr"
      const index = parseInt(match[2], 10);

      // Extract the base array name (remove p_ prefix if present)
      const arrayName = fullArrayName.startsWith("p_")
        ? fullArrayName.substring(2)
        : fullArrayName;

      // Determine the type from the parameter name
      const type = extractTypeFromParamName(paramName);

      if (!arrayGroups.has(arrayName)) {
        arrayGroups.set(arrayName, new Map());
      }
      arrayGroups.get(arrayName)!.set(index, { varName, type });
    }
  }

  // Second pass: check for arrays that only have length parameters (no elements)
  for (const [paramName] of variableMapping) {
    const lengthMatch = lengthPattern.exec(paramName);
    if (lengthMatch) {
      const arrayName = lengthMatch[1];

      // If we don't already have this array from elements, create an empty one
      if (!arrayGroups.has(arrayName)) {
        // Try to infer the type from the array name or use uint256 as default
        let inferredType = "uint256";
        if (arrayName.includes("address")) {
          inferredType = "address";
        } else if (arrayName.includes("bool")) {
          inferredType = "bool";
        }

        arrayGroups.set(arrayName, new Map());
        // We'll handle the empty array case in the generation loop below
      }
    }
  }

  // Generate array declarations
  for (const [arrayName, elements] of arrayGroups) {
    let elementType = "uint256"; // default type
    const arrayElements: string[] = [];

    if (elements.size > 0) {
      // Array has elements
      const maxIndex = Math.max(...elements.keys());

      for (let i = 0; i <= maxIndex; i++) {
        const element = elements.get(i);
        if (element) {
          arrayElements.push(element.varName);
          elementType = element.type; // Use the type from the first element
        } else {
          // Fill missing elements with default values
          arrayElements.push(elementType === "address" ? "address(0)" : "0");
        }
      }
    } else {
      // Array has no elements, but we have a length parameter
      // Try to infer the type from the array name
      if (arrayName.includes("address")) {
        elementType = "address";
      } else if (arrayName.includes("bool")) {
        elementType = "bool";
      }
      // Create an empty array - we'll use the length parameter if available
    }

    const arrayVarName = `${arrayName}_array`;

    if (arrayElements.length > 0) {
      // Array with elements
      declarations.push(
        `    ${elementType}[] memory ${arrayVarName} = new ${elementType}[](${arrayElements.length});`
      );

      // Add individual assignments
      arrayElements.forEach((element, index) => {
        declarations.push(`    ${arrayVarName}[${index}] = ${element};`);
      });
    } else {
      // Empty array - create with length 0 or use length parameter if available
      declarations.push(
        `    ${elementType}[] memory ${arrayVarName} = new ${elementType}[](0);`
      );
    }

    arrayVariables.set(arrayName, arrayVarName);
  }

  return { declarations, arrayVariables };
};
