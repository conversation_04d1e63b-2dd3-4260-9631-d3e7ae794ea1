/**
 * Helper function to parse CALL statements from Halmos sequence logs
 * Extracts function name and parameters from lines like:
 * "CALL CryticToFoundry::setTheManager(p_manager_address_83d2b33_33)"
 */
export const parseCallStatement = (
  callLine: string
): { functionName: string; parameters: string[] } | null => {
  // Match pattern: CALL ContractName::functionName(param1, param2, ...)
  const callMatch = callLine.match(/CALL\s+\w+::(\w+)\(([^)]*)\)/);
  if (!callMatch) {
    return null;
  }

  const functionName = callMatch[1];
  const paramString = callMatch[2].trim();

  if (!paramString) {
    return { functionName, parameters: [] };
  }

  // Handle Concat() expressions that combine multiple parameters
  if (paramString.startsWith("Concat(")) {
    // Extract parameters from Concat(param1, param2, ...)
    const concatContent = paramString.slice(7, -1); // Remove "Concat(" and ")"
    const concatParams = parseConcatParameters(concatContent);
    return { functionName, parameters: concatParams };
  }

  // Parse parameters - handle complex expressions like Concat(param1, param2)
  const parameters: string[] = [];
  let currentParam = "";
  let parenDepth = 0;
  let i = 0;

  while (i < paramString.length) {
    const char = paramString[i];

    if (char === "(") {
      parenDepth++;
      currentParam += char;
    } else if (char === ")") {
      parenDepth--;
      currentParam += char;
    } else if (char === "," && parenDepth === 0) {
      // Found a parameter separator at top level
      if (currentParam.trim()) {
        parameters.push(currentParam.trim());
      }
      currentParam = "";
    } else {
      currentParam += char;
    }
    i++;
  }

  // Add the last parameter
  if (currentParam.trim()) {
    parameters.push(currentParam.trim());
  }

  return { functionName, parameters };
};

/**
 * Parse parameters from Concat() expressions
 * Handles nested expressions and extracts individual parameter names
 */
export const parseConcatParameters = (concatContent: string): string[] => {
  const parameters: string[] = [];
  let currentParam = "";
  let parenDepth = 0;
  let i = 0;

  while (i < concatContent.length) {
    const char = concatContent[i];

    if (char === "(") {
      parenDepth++;
      currentParam += char;
    } else if (char === ")") {
      parenDepth--;
      currentParam += char;
    } else if (char === "," && parenDepth === 0) {
      // Found a parameter separator at top level
      const param = currentParam.trim();
      if (param && param.startsWith("p_")) {
        parameters.push(param);
      }
      currentParam = "";
    } else {
      currentParam += char;
    }
    i++;
  }

  // Add the last parameter
  const lastParam = currentParam.trim();
  if (lastParam && lastParam.startsWith("p_")) {
    parameters.push(lastParam);
  }

  return parameters;
};

/**
 * Helper function to clean parameter names for Solidity variable names
 */
export const cleanParameterName = (paramName: string): string =>
  paramName
    .replace(/^p_/, "")
    .replace(/_[a-f0-9]+_\d+$/, "") // Remove hash_number suffix
    .replace(/_[a-zA-Z0-9]+_\d+$/, "") // Remove alphanumeric_number suffix
    .replace(/\[(\d+)\]/, "$1");

/**
 * Helper function to extract type from parameter name
 * Uses only generic type patterns, not specific variable names
 */
export const extractTypeFromParamName = (paramName: string): string => {
  // Check for explicit type patterns in parameter names
  if (paramName.includes("_bool")) return "bool";
  if (paramName.includes("_address")) return "address";
  if (paramName.includes("_uint256")) return "uint256";
  if (paramName.includes("_uint8")) return "uint8";
  if (paramName.includes("_uint16")) return "uint16";
  if (paramName.includes("_uint32")) return "uint32";
  if (paramName.includes("_uint64")) return "uint64";
  if (paramName.includes("_uint128")) return "uint128";
  if (paramName.includes("_bytes")) return "bytes";
  if (paramName.includes("_string")) return "string";

  // Handle dynamic uint types like _uint24_, _uint96_, etc.
  const uintMatch = paramName.match(/_uint(\d+)_/);
  if (uintMatch) return `uint${uintMatch[1]}`;

  // Handle signed integers
  const intMatch = paramName.match(/_int(\d+)_/);
  if (intMatch) return `int${intMatch[1]}`;

  // Handle fixed-size bytes
  const bytesMatch = paramName.match(/_bytes(\d+)_/);
  if (bytesMatch) return `bytes${bytesMatch[1]}`;

  return "uint256"; // default fallback
};

/**
 * Helper function to format Solidity value declarations
 */
export const formatSolidityValue = (paramName: string, value: string): string => {
  const cleanName = cleanParameterName(paramName);
  const cleanValue = value.replace(/^0x/, "");
  const type = extractTypeFromParamName(paramName);

  if (type === "bool") {
    // Handle different bool value formats
    const boolValue =
      cleanValue === "01" || cleanValue === "true" || cleanValue === "1"
        ? "true"
        : "false";
    return `bool ${cleanName} = ${boolValue};`;
  }

  if (type === "address") {
    return `address ${cleanName} = 0x${cleanValue.padStart(40, "0")};`;
  }

  if (type.startsWith("uint") || type.startsWith("int")) {
    return `${type} ${cleanName} = 0x${cleanValue};`;
  }

  if (type === "bytes") {
    return `bytes ${cleanName} = 0x${cleanValue};`;
  }

  return `uint256 ${cleanName} = 0x${cleanValue};`;
};
