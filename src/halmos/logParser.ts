import { type FuzzingResults, type PropertyAndSequence } from "../types/types";

// Store all lines for batch processing
let allLines: string[] = [];

/**
 * Extract CALL statement from a line, handling nested parentheses
 */
export function extractCallStatement(line: string): string | null {
  const callStart = line.indexOf("CALL ");
  if (callStart === -1) return null;

  // Find the function call pattern: CALL ContractName::functionName(
  const functionMatch = line.match(/CALL\s+(\w+::[\w]+)\(/);
  if (!functionMatch) return null;

  const functionName = functionMatch[1];
  const openParenIndex = line.indexOf("(", callStart);
  if (openParenIndex === -1) return null;

  // Find the matching closing parenthesis, handling nested parentheses
  let parenCount = 1;
  let i = openParenIndex + 1;
  while (i < line.length && parenCount > 0) {
    if (line[i] === "(") parenCount++;
    else if (line[i] === ")") parenCount--;
    i++;
  }

  if (parenCount === 0) {
    const params = line.substring(openParenIndex + 1, i - 1);
    return `CALL ${functionName}(${params})`;
  }

  return null;
}

export function processHalmos(line: string, jobStats: FuzzingResults): void {
  // Store all lines for later processing
  allLines.push(line);

  // Handle pass/fail counts and test counts immediately
  if (line.includes("[FAIL]") || line.includes("[TIMEOUT]")) {
    jobStats.failed++;
  } else if (line.includes("[PASS]")) {
    jobStats.passed++;
  }

  // Extract test count from summary line
  if (line.includes("Running") && line.includes("tests for")) {
    const testCountMatch = line.match(/Running (\d+) tests/);
    if (testCountMatch) {
      jobStats.numberOfTests = parseInt(testCountMatch[1]);
    }
  }

  // Process broken properties when we hit a FAIL/TIMEOUT line
  if (line.includes("[FAIL]") || line.includes("[TIMEOUT]")) {
    // Process all accumulated lines to extract broken properties
    const logsText = allLines.join("\n");
    const propertySequences = getHalmosPropertyAndSequence(logsText);

    // Add any new broken properties that aren't already in jobStats
    propertySequences.forEach((propSeq) => {
      const exists = jobStats.brokenProperties.some(
        (existing) => existing.brokenProperty === propSeq.brokenProperty
      );
      if (!exists) {
        const sequenceString = Array.isArray(propSeq.sequence)
          ? propSeq.sequence.join("\n")
          : propSeq.sequence;
        jobStats.brokenProperties.push({
          brokenProperty: propSeq.brokenProperty,
          sequence: sequenceString,
        });
      }
    });
  }

  // Reset for next batch when we see a new counterexample
  if (line.includes("Counterexample:")) {
    allLines = [line];
  }

  // Always add to traces
  if (line.trim()) {
    jobStats.traces.push(line.trim());
  }
}

/**
 * Simple function to extract property and sequence from Halmos logs
 * Similar to Medusa's getPropertyAndSequenceString function
 */
export function getHalmosPropertyAndSequence(
  logs: string
): PropertyAndSequence[] {
  const results: PropertyAndSequence[] = [];
  const lines = logs.split("\n");

  let currentCounterexample: string[] = [];
  let currentSequenceCalls: string[] = [];
  let capturing = false;
  let capturingSequence = false;
  let currentCall = "";
  let currentProperty = "";

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();

    // Handle different failure formats
    // Format 1: [FAIL] property_name (paths: ...)
    const failMatch = /\[(?:FAIL|TIMEOUT)\]\s+(.+?)\s+\(paths:/.exec(line);
    if (failMatch) {
      currentProperty = failMatch[1].trim();
    }

    // Format 2: Assertion failure detected in ContractName.property_name()
    const assertionFailMatch =
      /Assertion failure detected in \w+\.(.+?)\(\)/.exec(line);
    if (assertionFailMatch) {
      currentProperty = assertionFailMatch[1].trim();
    }

    // Start capturing when we see "Counterexample:"
    if (line === "Counterexample:" || line.includes("Counterexample:")) {
      capturing = true;
      capturingSequence = false;
      currentCounterexample = [];
      currentSequenceCalls = [];
      currentCall = "";
      continue;
    }

    // Start capturing sequence when we see "Sequence:"
    if (line === "Sequence:" || line.includes("Sequence:")) {
      capturingSequence = true;
      currentCall = "";
      continue;
    }

    if (capturing) {
      // Check for end conditions
      const isEndCondition =
        line.includes("[FAIL]") ||
        line.includes("[TIMEOUT]") ||
        line.includes("Symbolic test result:") ||
        (currentProperty && i === lines.length - 1);

      if (isEndCondition) {
        // Finalize any pending call
        if (currentCall && capturingSequence) {
          const callMatch = extractCallStatement(currentCall);
          if (callMatch) {
            currentSequenceCalls.push(callMatch);
          }
        }

        // Use the property we found earlier or try to extract from current line
        let propertyName = currentProperty;
        if (!propertyName) {
          const propertyMatch = /\[(?:FAIL|TIMEOUT)\]\s+(.+?)\s+\(paths:/.exec(
            line
          );
          if (propertyMatch) {
            propertyName = propertyMatch[1].trim();
          }
        }

        if (propertyName && currentCounterexample.length > 0) {
          // Combine parameter declarations and sequence calls
          const combinedSequence = [
            ...currentCounterexample,
            ...currentSequenceCalls,
          ];
          results.push({
            brokenProperty: propertyName,
            sequence: combinedSequence,
          });
        }
        capturing = false;
        capturingSequence = false;
        currentCounterexample = [];
        currentSequenceCalls = [];
        currentCall = "";
        currentProperty = "";
      } else if (capturingSequence && line.startsWith("CALL ")) {
        // Finalize previous call if any
        if (currentCall) {
          const callMatch = extractCallStatement(currentCall);
          if (callMatch) {
            currentSequenceCalls.push(callMatch);
          }
        }
        // Start new call
        currentCall = line;
      } else if (
        capturingSequence &&
        currentCall &&
        !line.startsWith("    CALL ") &&
        !line.startsWith("        ") &&
        line.trim() &&
        !line.includes("[FAIL]") &&
        !line.includes("[TIMEOUT]")
      ) {
        // This might be a continuation of the current CALL statement
        // Check if it looks like part of the function parameters
        if (line.includes(")") || line.includes(",") || line.includes("p_")) {
          currentCall += " " + line.trim();
        }
      } else if (
        capturingSequence &&
        currentCall &&
        (line.includes("(value:") ||
          line.includes("(caller:") ||
          line.startsWith("halmos_msg_"))
      ) {
        // Skip value and caller information for CALL statements
        continue;
      } else if (
        capturingSequence &&
        line.startsWith("    ") &&
        (line.includes("SLOAD") ||
          line.includes("SSTORE") ||
          line.includes("STATICCALL") ||
          line.includes("CREATE") ||
          line.includes("↩ RETURN"))
      ) {
        // Skip internal EVM operations
        continue;
      } else if (
        !capturingSequence &&
        line.includes("=") &&
        (line.startsWith("p_") ||
          line.includes("_uint256") ||
          line.includes("_address") ||
          line.includes("_bool") ||
          line.includes("halmos_"))
      ) {
        // Extract parameter declarations - handle various parameter naming patterns
        // Skip halmos internal variables unless they're actual parameters
        if (!line.includes("halmos_") || line.startsWith("p_")) {
          currentCounterexample.push(line);
        }
      } else if (
        !capturingSequence &&
        line.length > 0 &&
        !line.includes("=") &&
        currentCounterexample.length > 0
      ) {
        // Handle multi-line values (like bytes data)
        const lastParam =
          currentCounterexample[currentCounterexample.length - 1];
        if (lastParam.includes("=") && !lastParam.includes("0x")) {
          // This is likely a continuation of the previous parameter
          currentCounterexample[currentCounterexample.length - 1] =
            lastParam + line;
        }
      }
    }
  }

  return results;
}
