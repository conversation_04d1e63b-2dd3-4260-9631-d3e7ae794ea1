"use client";

import { useEffect, useState } from "react";
import { getProviders, signIn } from "next-auth/react";
import { FaGithub } from "react-icons/fa";
import Link from "next/link";
import GenericButton from "@/app/components/GenericButton/GenericButton";
import styles from "./styles.module.scss";
import ToolsList from "@/app/components/ToolsList/ToolsList";

interface Provider {
  id: string;
  name: string;
}

export default function SignIn() {
  const [providers, setProviders] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchProviders = async () => {
      const res = await getProviders();
      setProviders(res);
      setIsLoading(false);
    };

    fetchProviders();
  }, []);

  return (
    <div className=" main-container w-full overflow-x-hidden">
      {isLoading ? (
        <div className="flex justify-center items-center h-screen">
          <div className="loader text-white">Loading ...</div>
        </div>
      ) : (
        <div className="relative z-10 flex min-h-screen w-full flex-col items-center justify-center bg-black text-white">
          <h1 className="main-title-custom mb-5 text-center text-[56px] font-bold leading-[48px] tracking-normal lg:text-[120px] lg:leading-[100px]">
            Welcome to Recon
          </h1>
          <div className="mb-10 align-start w-[500px]">
            <h2 className="mb-8 text-3xl font-bold">Login</h2>
            {providers &&
              Object.values(providers).map((provider: Provider) => (
                <div key={provider.name} className="flex flex-col items-center">
                  <button
                    onClick={() =>
                      signIn(provider.id, { callbackUrl: "/dashboard" })
                    }
                    className={styles.gradientButton}
                  >
                    Sign in with {provider.name}
                    <FaGithub size={24} />
                  </button>
                </div>
              ))}
          </div>
          <div className={"flex flex-col justify-center align-start w-[500px]"}>
            <ToolsList />
          </div>
        </div>
      )}
    </div>
  );
}
