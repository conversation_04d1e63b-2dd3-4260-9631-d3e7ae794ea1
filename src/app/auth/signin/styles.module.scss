.gradientButton {
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
  position: relative;
  z-index: 1;
  color: transparent;
  background-clip: text;
  -webkit-background-clip: text;
  font-size: 16px;
  font-weight: bold;
  transition: all 0.3s ease;
  display:flex;
  width: 100%;
  justify-content: center;
  align-items: center;
  color: black;
  background-color: #5100ff;
  background: linear-gradient(277.21deg, #5100ff -13.33%, #fff 51.57%);
  width: 100%;
  margin: 10px;
  padding: 10px;
}

.gradientButton::before {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(277.21deg, #fff -13.33%, #5100ff 51.57%, #fff 116.47%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  z-index: -1;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.gradientButton:hover {
  transform: scale(1.05);
  background-color: #5100ff;
  background: linear-gradient(277.21deg, #5100ff -13.33%, #fff 51.57%);
  width: 100%;
  margin: 10px;
  padding: 10px;
}
