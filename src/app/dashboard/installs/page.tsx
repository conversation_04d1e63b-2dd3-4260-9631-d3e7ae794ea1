"use client";

import Link from "next/link";

import { App<PERSON><PERSON><PERSON> } from "@/app/components/app-button";
import { AppListCheckItem } from "@/app/components/app-list-check-item";
import { AppPageTitle } from "@/app/components/app-page-title";
import { AppSpinner } from "@/app/components/app-spinner";
import { Button } from "@/app/components/Button";
import { useGetInstalls } from "@/app/services/installs.hook";
import config from "@/config";

// TODO Custom Hook is prob better

export default function Repos() {
  const { data: installs, isLoading } = useGetInstalls();

  return (
    <div className="pl-[45px] pt-[45px]">
      <AppPageTitle>Installed Repos</AppPageTitle>
      <p className="my-[20px] text-[20px] leading-[25px] text-textSecondary">
        To use Recon Pro Features, you need to install the Recon Github App into
        your Repository
      </p>
      <p className="my-[20px] text-[20px] leading-[25px] text-textSecondary">
        This will enable:
        <AppListCheckItem text="Webhooks" />
        <AppListCheckItem text="Build Abi and Run Jobs on Private Repos" />
      </p>
      <a href={config.github.app.installationUrl}>
        <Button>Add Recon to your Repos</Button>
      </a>

      <ul className="py-[20px]">
        <AppListCheckItem
          text="Pro Tip: Limit to Read Permissions to only generate handlers and run the
        code"
        />
        <AppListCheckItem text="You need write permissions only to receive PRs for your handlers - FEATURE IS NOT YET LIVE" />
        <AppListCheckItem
          text="NOTE / TODO: Currently we only show you, your Repos, if you add a Repo
        from a org it will not show"
        />
      </ul>

      <p className="my-[20px] text-[20px] leading-[25px] text-textSecondary">
        Build From Private Repos!
      </p>
      <div className="flex gap-[10px]">
        {isLoading && <AppSpinner />}
        {!isLoading && installs && installs.length > 0 &&
          installs.map((install) => (
            <Link
              key={install.id}
              href={`/dashboard/installs/${install.full_name}/`}
            >
              <AppButton>
                {install.full_name} | {install.default_branch}
              </AppButton>
            </Link>
          ))}
      </div>

      <ul className="py-[20px]">
        {[
          "TODO: Separate Page to build a different branch, either pull all, orallow typing and we verify if it exists",
          "NOTE: Once your project is compiled, you'll be able to build handlers for it",
        ].map((x) => {
          return <AppListCheckItem text={x} key={x} />;
        })}
      </ul>

      <p className="my-[20px] text-[20px] leading-[25px] text-textSecondary">
        What is this? Repos are repositories you have setup for:
      </p>

      <ul className="py-[20px]">
        {[
          "Running a Job (NOW)",
          "Bening able to build ABIs (NOW)",
          'Automatic Re-building of ABI (TODO: Week 2) -{">"} Webhook or cron for installed apps for each pro project',
          "Automatic Re-building of Handlers -&gt; NOTE: User edits may be deleted!!! (TODO: Week 4)",
          "Auto Injection of Recon Handlers (TODO: Month2)",
        ].map((x) => {
          return <AppListCheckItem text={x} key={x} />;
        })}
      </ul>
    </div>
  );
}
