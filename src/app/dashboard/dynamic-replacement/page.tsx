"use client";

import axios from "axios";
import { useState } from "react";

import { ENV_TYPE } from "@/app/app.constants";
import type { GitHubLinkFormValues } from "@/app/components/github-link-form/create-job-form";
import { CreateJobForm } from "@/app/components/github-link-form/create-job-form";
import { Webhooks } from "@/app/components/Webhooks";
import { useGetJobs } from "@/app/services/jobs.hooks";
import { checkFields } from "@/utils/fieldChecker";
import { useGetMyOrg } from "@/app/services/organization.hooks";


export interface PreparedDynamicReplacementContract {
  target: string;
  replacement: string;
  endOfTargetMarker: string;
  targetContract: string;
}
export default function JobsPage() {
  const [env, setEnv] = useState(ENV_TYPE.MEDUSA);
  const [jobId, setJobId] = useState<null | number>(null);

  const { data: allJobs, refetch: refetchJobs } = useGetJobs();
  const { data: organization } = useGetMyOrg();

  const runningJobs = () => {
  return allJobs.filter(
      (job) =>
        job.status === "RUNNING" ||
        job.status === "STARTED" ||
        job.status === "QUEUED"
    )
  };
  const startEchidnaAbiJob = async ({
    pathToTester,
    echidnaConfig,
    contract,
    corpusDir,
    forkBlock,
    forkMode,
    forkReplacement,
    ref,
    repoName,
    rpcUrl,
    testLimit,
    testMode,
    preprocess,
    directory,
    orgName,
    targetCorpus,
    label,
    prepareContracts,
  }: GitHubLinkFormValues) => {
    if (organization.billingStatus === "TRIAL" && runningJobs().length > 0) {
      alert(`You can only run one job at a time, currently running: ${runningJobs().map((job) => `${job.label}-${job.id}`).join(", ")}`);
      return;
    }
    const fuzzerArgs = {
      pathToTester,
      config: echidnaConfig,
      contract,
      corpusDir,
      forkBlock,
      forkMode,
      forkReplacement,
      rpcUrl,
      testLimit,
      testMode,
      preprocess,
      targetCorpus,
      label,
      prepareContracts,

      // TODO: Make gov fuzz a default (use prepareContracts.length) and remove the need to explicitly add it as a var
      govFuzz: true
    };

    const areBasicFieldsOK = checkFields(orgName, repoName, ref, prepareContracts);
    if (!areBasicFieldsOK) return;

    try {
      const foundData = await axios({
        method: "POST",
        url: `/api/jobs/echidna`,
        data: {
          directory,
          orgName,
          repoName,
          ref,
          fuzzerArgs,
          preprocess,
        },
      });

      setJobId(foundData?.data?.data?.id as number);
      refetchJobs();
    } catch (e) {
      console.log("e", e);
      alert(`Something went wrong: ${e.response.data.message}`);
    }
  };

  const startFoundryJob = async ({
    //pathToTester,
    //echidnaConfig,
    contract,
    //corpusDir,
    forkBlock,
    forkMode,
    ref,
    repoName,
    //testLimit,
    //testMode,
    preprocess,
    directory,
    orgName,
    rpcUrl,
    runs,
    seed,
    verbosity,
    testCommand,
    testTarget,
    //targetCorpus,
    prepareContracts,
  }: GitHubLinkFormValues) => {
    if (organization.billingStatus === "TRIAL" && runningJobs().length > 0) {
      alert(`You can only run one job at a time, currently running: ${runningJobs().map((job) => `${job.label}-${job.id}`).join(", ")}`);
      return;
    }
    const fuzzerArgs = {
      //pathToTester,
      //config: echidnaConfig,
      contract,
      //corpusDir,
      forkBlock,
      forkMode,
      //testLimit,
      //testMode,
      preprocess,
      //targetCorpus,
      rpcUrl,
      runs,
      seed,
      verbosity,
      testCommand,
      testTarget,
      prepareContracts,

      // TODO: Make gov fuzz a default (use prepareContracts.length) and remove the need to explicitly add it as a var
      govFuzz: true
    };

    const areBasicFieldsOK = checkFields(orgName, repoName, ref, prepareContracts);
    if (!areBasicFieldsOK) return;

    try {
      const foundData = await axios({
        method: "POST",
        url: `/api/jobs/foundry`,
        data: {
          directory,
          orgName,
          repoName,
          ref,
          fuzzerArgs,
          preprocess,
        },
      });

      setJobId(foundData?.data?.data?.id as number);
      refetchJobs();
    } catch (e) {
      console.log("e", e);
      alert(`Something went wrong: ${e.response.data.message}`);
    }
  };

  const startHalmosJob = async ({
    //pathToTester,
    //echidnaConfig,
    contract,
    ref,
    repoName,
    preprocess,
    directory,
    orgName,
    halmosPrefix,
    halmosArray,
    halmosLoops,
    verbosity,
    prepareContracts,
  }: GitHubLinkFormValues) => {
    if (organization.billingStatus === "TRIAL" && runningJobs().length > 0) {
      alert(`You can only run one job at a time, currently running: ${runningJobs().map((job) => `${job.label}-${job.id}`).join(", ")}`);
      return;
    }
    const fuzzerArgs = {
      contract,
      preprocess,
      halmosArray,
      halmosLoops,
      halmosPrefix,
      verbosity,
      prepareContracts,
    };

    try {
      const foundData = await axios({
        method: "POST",
        url: `/api/jobs/halmos`,
        data: {
          directory,
          orgName,
          repoName,
          ref,
          fuzzerArgs,
          preprocess,
        },
      });

      setJobId(foundData?.data?.data?.id as number);
      refetchJobs();
    } catch (e) {
      console.log("e", e);
      alert("Something went wrong");
    }
  };

  const startKontrolJob = async ({
    //pathToTester,
    //echidnaConfig,
    contract,
    ref,
    repoName,
    preprocess,
    directory,
    orgName,
    kontrolTest,
    prepareContracts,
  }: GitHubLinkFormValues) => {
    if (organization.billingStatus === "TRIAL" && runningJobs().length > 0) {
      alert(`You can only run one job at a time, currently running: ${runningJobs().map((job) => `${job.label}-${job.id}`).join(", ")}`);
      return;
    }
    const fuzzerArgs = {
      //pathToTester,
      //config: echidnaConfig,
      contract,
      //testLimit,
      //testMode,
      preprocess,
      //targetCorpus,
      kontrolTest,
      prepareContracts,
    };

    try {
      const foundData = await axios({
        method: "POST",
        url: `/api/jobs/kontrol`,
        data: {
          directory,
          orgName,
          repoName,
          ref,
          fuzzerArgs,
          preprocess,
        },
      });

      setJobId(foundData?.data?.data?.id as number);
      refetchJobs();
    } catch (e) {
      console.log("e", e);
      alert("Something went wrong");
    }
  };

  const startMedusaAbiJob = async ({
    orgName,
    repoName,
    ref,
    directory,
    medusaConfig,
    timeout,
    preprocess,
    targetCorpus,
    label,
    prepareContracts,
  }: GitHubLinkFormValues) => {
    if (organization.billingStatus === "TRIAL" && runningJobs().length > 0) {
      alert(`You can only run one job at a time, currently running: ${runningJobs().map((job) => `${job.label}-${job.id}`).join(", ")}`);
      return;
    }
    const fuzzerArgs = {
      timeout,
      config: medusaConfig,
      targetCorpus,
      prepareContracts,

      // TODO: Make gov fuzz a default (use prepareContracts.length) and remove the need to explicitly add it as a var
      govFuzz: true
    };

    const areBasicFieldsOK = checkFields(orgName, repoName, ref, prepareContracts);
    if (!areBasicFieldsOK) return;
    console.log("prepareContracts", prepareContracts)
    try {
      const foundData = await axios({
        method: "POST",
        url: `/api/jobs/medusa`,
        data: {
          fuzzerArgs,
          preprocess,
          orgName,
          repoName,
          ref,
          directory,
          label,
        },
      });

      // setJobId(foundData?.data?.data?.id as number);
      refetchJobs();
    } catch (e) {
      alert(`Something went wrong: ${e.response.data.message}`);
    }
  };

  const onSubmit =
    env === ENV_TYPE.MEDUSA
      ? startMedusaAbiJob
      : env === ENV_TYPE.ECHIDNA
        ? startEchidnaAbiJob
        : env === ENV_TYPE.FOUNDRY
          ? startFoundryJob
          : env === ENV_TYPE.HALMOS
            ? startHalmosJob
            : startKontrolJob;

  return (
    <div className="pl-[45px] pt-[45px]">
      <Webhooks />
      <div className="mt-[40px] text-[15px] leading-[18px] text-textSecondary">
        <h1 className="mb-[16px] text-[28px] leading-[33px] text-textPrimary">
          Dynamic replacement
        </h1>
        <p>Dynamic Replacement is in EXPERIMENTAL mode</p>
        <p>All variables Dynamically Replaced MUST be in the `Setup.sol` file</p>
        <p>Make sure you have no clashing file!</p>
      </div>
      <CreateJobForm
        title=""
        submitLabel="Run Job"
        {...{
          env,
          jobId,
          setEnv,
        }}
        onSubmit={onSubmit}
        dynamicReplacement={true}
      />
    </div>
  );
}
