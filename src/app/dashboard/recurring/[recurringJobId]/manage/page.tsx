"use client";
import { useEffect, useState } from "react";
import { useGetRecurring } from "@/app/services/recurring.hook";
import AlertManager from "@/app/components/AlertManager/AlertManager";

export default function SingleRecurringJobPage({
  params: { recurringJobId },
}: {
  params: { recurringJobId: string };
}) {
  const { data, refetch } = useGetRecurring();
  const [currentRecurring, setCurrentRecurring] = useState(null);

  useEffect(() => {
    if (recurringJobId && data) {
      const currentRecurring = data.find(
        (recurring) => recurring.id === recurringJobId
      );
      setCurrentRecurring(currentRecurring);
    }
  }, [recurringJobId, data]);

  return (
    <AlertManager
      isRecipe={false}
      isRecurring={true}
      refetch={refetch}
      data={currentRecurring}
    />
  );
}
