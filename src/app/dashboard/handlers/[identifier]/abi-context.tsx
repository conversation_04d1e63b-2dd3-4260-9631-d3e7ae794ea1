import type {
  BoilerplateFile,
  ReconProject,
} from "@recon-fuzz/abi-to-invariants-ts/src/types";
import { saveAs } from "file-saver";
import JSZip from "jszip";
import cloneDeep from "lodash/cloneDeep";
import {
  createContext,
  useCallback,
  useEffect,
  useMemo,
  useState,
} from "react";

import { useGetAbiByIdentifier } from "@/app/services/abi.hook";
import {
  generateFullFuzzerContracts,
  generateReconProjectData,
  ReconMode,
} from "@/app/services/generateFuzzerContracts";

// Define the shape of the context
interface ABIContextProps {
  trackedContracts: string[];
  skippedFunctions: Record<string, string[]>;
  trackedViewFunctions: Record<string, string[]>;
  allFuzzerContracts: BoilerplateFile[];
  reconSettingsFiles: ReconProject | null;
  filteredAbiData: any;
  isContractActive: (contractName: string) => boolean;
  addContract: (contractName: string) => void;
  removeContract: (contractName: string) => void;
  addDisabledFunction: (
    contractName: string,
    functionSelector: string | string[]
  ) => void;
  removeDisabledFunction: (
    contractName: string,
    functionSelector: string | string[]
  ) => void;
  addTrackedViewFunction: (
    contractName: string,
    functionSelector: string
  ) => void;
  removeTrackedViewFunction: (
    contractName: string,
    functionSelector: string
  ) => void;
  setShowMode: (mode: ShowMode) => void;
  showMode: ShowMode;
  downloadAllContracts: () => void;
  setReconMode: (mode: ReconMode) => void;
  reconMode: ReconMode;
}

export enum ShowMode {
  src = "src",
  all = "all",
}

// Create the context with a default value
export const ABIContext = createContext<ABIContextProps | null>(null);

interface ABIProviderProps {
  children: React.ReactNode;
  identifier?: any;
  forced?: boolean;
}

// Create a provider component
export const ABIProvider = ({
  children,
  identifier,
  forced,
}: ABIProviderProps) => {
  // Pagination
  // 0 - Activate Contracts and Functions | TODO: Clamping
  // 1 - TODO - Activate B4 and After View Functions
  // 2 - Get Handlers
  const [showMode, setShowMode] = useState(ShowMode.src);
  const [reconMode, setReconMode] = useState(ReconMode.normal);
  // The compilation data, TODO: move to custom hook, potentially context as well
  // NOTE: We could move everything into a separate context and make the UI represent it
  // Prob best refactoring

  const data = useGetAbiByIdentifier(identifier, forced);
  const abiApiData = data.data;

  const [trackedContracts, setTrackedContracts] = useState<string[]>([]);
  const [skippedFunctions, setSkippedFunctions] = useState<
    Record<string, string[]>
  >({});
  const [trackedViewFunctions, setTrackedViewFunctions] = useState<
    Record<string, string[]>
  >({});

  const isContractActive = useCallback(
    (contractName: string) => trackedContracts.includes(contractName),
    [trackedContracts]
  );

  const addContract = useCallback(
    (contractName: string) =>
      setTrackedContracts((prev) => [...prev, contractName]),
    []
  );

  useEffect(() => {
    if (!forced) {
      return;
    }
    if (abiApiData?.abiData && abiApiData.abiData.length > 0) {
      const abiData = JSON.parse(abiApiData.abiData);
      const ctrNames: string[] = [];
      abiData.map((abiEntry) => {
        ctrNames.push(abiEntry.name);
      });

      setTrackedContracts(ctrNames);
    }
  }, [abiApiData, forced])

  const removeContract = useCallback(
    (contractName: string) =>
      setTrackedContracts((prev) =>
        prev.filter((name) => name !== contractName)
      ),
    []
  );

  const addDisabledFunction = useCallback(
    (contractName: string, functionSelector: string | string[]) => {
      const selectors = Array.isArray(functionSelector)
        ? functionSelector
        : [functionSelector];
      if (
        !skippedFunctions?.[contractName] ||
        !Array.isArray(skippedFunctions?.[contractName])
      ) {
        const copyOfSkippedFunctions = cloneDeep(skippedFunctions);

        copyOfSkippedFunctions[contractName] = [...selectors];
        setSkippedFunctions(copyOfSkippedFunctions);
      } else {
        const copyOfSkippedFunctions = { ...skippedFunctions };
        copyOfSkippedFunctions[contractName].push(...selectors);
        setSkippedFunctions(copyOfSkippedFunctions);
      }
    },
    [skippedFunctions]
  );

  const removeDisabledFunction = useCallback(
    (contractName: string, functionSelector: string | string[]) => {
      const selectors = Array.isArray(functionSelector)
        ? functionSelector
        : [functionSelector];
      if (
        !skippedFunctions?.[contractName] ||
        !Array.isArray(skippedFunctions?.[contractName])
      ) {
        // Do nothing, it's already removed
        return;
      } else {
        setSkippedFunctions({
          ...skippedFunctions,
          [contractName]: skippedFunctions[contractName].filter(
            (selector: string) => !selectors.includes(selector)
          ),
        });
      }
    },
    [skippedFunctions]
  );

  // Page 1 - Given active contracts
  // Let's allow a way to toggle tracked b4AndAfterFunctions

  // TODO: Basically exactly the same code as above, but for tracked fns
  // TODO: Refactor to a simple find method
  // So we can re-use the same for both lists

  const addTrackedViewFunction = useCallback(
    (contractName: string, functionSelector: string) => {
      if (
        !trackedViewFunctions?.[contractName] ||
        !Array.isArray(trackedViewFunctions?.[contractName])
      ) {
        const copyOfViewFunctions = cloneDeep(trackedViewFunctions);
        copyOfViewFunctions[contractName] = [functionSelector];
        setTrackedViewFunctions(copyOfViewFunctions);
      } else {
        const copyOfViewFunctions = { ...trackedViewFunctions };
        copyOfViewFunctions[contractName].push(functionSelector);
        setTrackedViewFunctions(copyOfViewFunctions);
      }
    },
    [trackedViewFunctions]
  );

  const removeTrackedViewFunction = useCallback(
    (contractName: string, functionSelector: string) => {
      if (
        !trackedViewFunctions?.[contractName] ||
        !Array.isArray(trackedViewFunctions?.[contractName])
      ) {
        // Do nothing, it's already removed
        return;
      } else {
        setTrackedViewFunctions({
          ...trackedViewFunctions,
          [contractName]: trackedViewFunctions[contractName].filter(
            (selector: string) => selector != functionSelector
          ),
        });
      }
    },
    [trackedViewFunctions]
  );

  const compilationData = useMemo(() => {
    const data = abiApiData
      ? {
          ...abiApiData,
          abiData: JSON.parse(abiApiData.abiData),
        }
      : null;
    return data;
  }, [abiApiData]);

  const filteredAbiData = useMemo(() => {
    if (compilationData && showMode === ShowMode.src) {
      // Filter by checking `startsWith`
      // For each of them we need to filter
      // const deepCopy = JSON.parse(JSON.stringify(compilationData));
      // NOTE: Can extend to filter by folder or smth
      const cleanedUpAbi = compilationData.abiData.filter((abiEntry) =>
        abiEntry.abiPath.startsWith("src")
      );
      // TODO: Could add a simple regex to allow choosing specific paths

      return {
        ...compilationData,
        abiData: cleanedUpAbi,
      };
    } else {
      return compilationData;
    }
  }, [showMode, compilationData]);

  const [allFuzzerContracts, setAllFuzzerContracts] = useState<
    BoilerplateFile[]
  >([]);
  const [reconSettingsFiles, setReconSettingsFiles] =
    useState<ReconProject | null>(null);

  useEffect(() => {
    try {
      if (filteredAbiData) {
        const reconSettings = generateReconProjectData(
          filteredAbiData.abiData,
          trackedContracts,
          skippedFunctions,
          trackedViewFunctions,
          reconMode
        );
        const reconData = generateFullFuzzerContracts(reconSettings);
        setReconSettingsFiles(reconSettings);
        setAllFuzzerContracts(reconData);
      }
    } catch (e) {
      console.log("Exception in creating recon contracts", e);
    }
  }, [
    filteredAbiData,
    trackedContracts,
    skippedFunctions,
    trackedViewFunctions,
    reconMode,
    identifier
  ]);

  const downloadAllContracts = useCallback(async () => {
    if (allFuzzerContracts.length > 0) {
      const zip = new JSZip();

      for (const fuzzerContract of allFuzzerContracts) {
        zip.file(fuzzerContract.name, fuzzerContract.data);
      }
      saveAs(await zip.generateAsync({ type: "blob" }), "recon.zip");
    }
  }, [allFuzzerContracts]);

  // Context value
  const contextValue = useMemo(
    () => ({
      showMode,
      allFuzzerContracts,
      reconSettingsFiles,
      downloadAllContracts,
      setShowMode,
      filteredAbiData,
      trackedContracts,
      skippedFunctions,
      trackedViewFunctions,
      isContractActive,
      addContract,
      removeContract,
      addDisabledFunction,
      removeDisabledFunction,
      addTrackedViewFunction,
      removeTrackedViewFunction,
      setReconMode,
      reconMode,
    }),
    [
      showMode,
      allFuzzerContracts,
      reconSettingsFiles,
      downloadAllContracts,
      filteredAbiData,
      trackedContracts,
      skippedFunctions,
      trackedViewFunctions,
      isContractActive,
      addContract,
      removeContract,
      addDisabledFunction,
      removeDisabledFunction,
      addTrackedViewFunction,
      removeTrackedViewFunction,
      reconMode,
    ]
  );

  return (
    <ABIContext.Provider value={contextValue}>{children}</ABIContext.Provider>
  );
};
