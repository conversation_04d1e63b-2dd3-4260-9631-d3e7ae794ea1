"use client";

import type {
  AB<PERSON>,
  ABIWithSelector,
  BoilerplateFile,
  ReconProject,
} from "@recon-fuzz/abi-to-invariants-ts/src/types";
import axios from "axios";
import { saveAs } from "file-saver";
import J<PERSON><PERSON><PERSON> from "jszip";
import { useCallback, useEffect, useMemo, useState } from "react";

import { AppCode } from "@/app/components/app-code";
import { Button } from "@/app/components/Button";
import {
  filterOutAllNonViewFunctions,
  generateFullFuzzerContracts,
  generateReconProjectData,
  getAllViewFunctions,
  processFunctions,
  ReconMode,
} from "@/app/services/generateFuzzerContracts";

import styles from "./page.module.scss";
//TODO 0XSI
// Seems that this page in outdated now ?
export interface AbiApiData {
  identifier: string;
  abiData: abiEntry[];
  updatedAt: string;
}

export interface abiEntry {
  name: string;
  abi: ABI[];
  abiPath: string;
}

export default function RepoPage({
  params: { orgName, repoName, branch },
}: {
  params: { orgName: string; repoName: string; branch: string };
}) {
  // Pagination
  // 0 - Activate Contracts and Functions
  // 1 - TODO - Activate B4 and After View Functions
  // 2 - Get Handlers
  const [pageNumber, setPageNumber] = useState(0);

  // Should we filter Lib Files? (Dev etc..)
  const [filterLib, setFilterLib] = useState(true);

  // The compilation data, TODO: move to custom hook, potentially context as well
  // Prob best refactoring

  const [compilationData, setCompilationData] = useState<AbiApiData>();

  // Onload, get the ABI
  useEffect(() => {
    async function getTheData() {
      let data;

      if (branch && orgName && repoName) {
        data = {
          branch: branch,
          orgName: orgName,
          repoName: repoName,
        };

        try {
          const foundData = await axios({
            method: "GET",
            url: `${process.env.NEXT_PUBLIC_API_URL}/abi/${data?.orgName}/${data?.repoName}/${data?.branch}`,
          });

          const unsorted: AbiApiData[] = foundData.data;

          // Find the most recent one
          const sorted = unsorted.sort(
            (first, second) =>
              new Date(second.updatedAt).getTime() -
              new Date(first.updatedAt).getTime(),
          );

          // TODO: Adapt abiData to be the data that recon uses (which has selectors, etc...)
          // Basically we need to filter view stuff from the hanl
          // Data of the contract will have all functions
          // UI will show view functions as B4 and After
          // UI will show non-view for handler
          // We then reconcile this via a local function

          setCompilationData(sorted[0]);
        } catch (e) {
          console.log(e);
        }
      }
    }
    getTheData();
  }, [orgName, repoName, branch]);

  // NOTE: Given ABI, filter for Path if toggled
  const filteredAbiData = useMemo(() => {
    if (!!compilationData && filterLib) {
      // Filter by checking `startsWith`
      // For each of them we need to filter
      const deepCopy = JSON.parse(JSON.stringify(compilationData));
      // NOTE: Can extend to filter by folder or smth
      const cleanedUpAbi = compilationData.abiData.filter((abiEntry) =>
        abiEntry.abiPath.startsWith("src"),
      );
      // TODO: Could add a simple regex to allow choosing specific paths

      return {
        ...deepCopy,
        abiData: cleanedUpAbi,
      };
    } else {
      return compilationData;
    }
  }, [compilationData, filterLib]);

  // PAGE - 0
  // If they toggle
  // We don't necessarily want to forget anything
  // So we are prob better off using the same idea of Contract - Selector
  // Rather than anything else

  // Abi data -> name
  // Abi data -> abiPath

  // Contract Name
  // Abi selectors

  // We automatically toggle all on on Change
  const [trackedContracts, setTrackedContracts] = useState<string[]>([]); // Just the names
  const [skippedFunctions, setSkippedFunctions] = useState<{
    [key: string]: string[];
  }>({});
  const [trackedViewFunctions, setTrackedViewFunctions] = useState<{
    [key: string]: string[];
  }>({});

  /**
   * {
   *  name
   *  path
   *  filtered
   * }
   */

  const isContractActive = useCallback(
    (contractName: string) => {
      return trackedContracts.includes(contractName);
    },
    [trackedContracts],
  );

  const addContract = useCallback(
    (contractName: string) => {
      setTrackedContracts([...trackedContracts, contractName]);
    },
    [trackedContracts],
  );
  function removeContract(contractName: string) {
    const copy = Object.assign([], trackedContracts);
    const withRemoved = copy.filter((name) => name != contractName);

    setTrackedContracts(withRemoved);
  }

  // TODO: Refactor to a simple find method
  // So we can re-use the same for both lists
  function isFunctionDisabled(contractName: string, functionSelector: string) {
    if (
      !skippedFunctions?.[contractName] ||
      !Array.isArray(skippedFunctions?.[contractName])
    ) {
      return false;
    }

    // It must be an array, so check for inclusion
    return skippedFunctions?.[contractName].includes(functionSelector);
  }

  const addDisabledFunction = useCallback(
    (contractName: string, functionSelector: string) => {
      if (
        !skippedFunctions?.[contractName] ||
        !Array.isArray(skippedFunctions?.[contractName])
      ) {
        const copyOfSkippedFunctions = JSON.parse(
          JSON.stringify(skippedFunctions),
        );
        copyOfSkippedFunctions[contractName] = [functionSelector];
        setSkippedFunctions(copyOfSkippedFunctions);
      } else {
        const copyOfSkippedFunctions = { ...skippedFunctions };
        copyOfSkippedFunctions[contractName].push(functionSelector);
        setSkippedFunctions(copyOfSkippedFunctions);
      }
    },
    [skippedFunctions],
  );

  const removeDisabledFunction = useCallback(
    (contractName: string, functionSelector: string) => {
      if (
        !skippedFunctions?.[contractName] ||
        !Array.isArray(skippedFunctions?.[contractName])
      ) {
        // Do nothing, it's already removed
        return;
      } else {
        setSkippedFunctions({
          ...skippedFunctions,
          [contractName]: skippedFunctions[contractName].filter(
            (selector: string) => selector != functionSelector,
          ),
        });
      }
    },
    [skippedFunctions],
  );

  // Page 1 - Given active contracts
  // Let's allow a way to toggle tracked b4AndAfterFunctions

  // TODO: Basically exactly the same code as above, but for tracked fns

  // TODO: Refactor to a simple find method
  // So we can re-use the same for both lists
  function isViewFunctionActive(
    contractName: string,
    functionSelector: string,
  ) {
    if (
      !trackedViewFunctions?.[contractName] ||
      !Array.isArray(trackedViewFunctions?.[contractName])
    ) {
      return false;
    }

    // It must be an array, so check for inclusion
    return trackedViewFunctions?.[contractName].includes(functionSelector);
  }

  const addTrackedViewFunction = useCallback(
    (contractName: string, functionSelector: string) => {
      if (
        !trackedViewFunctions?.[contractName] ||
        !Array.isArray(trackedViewFunctions?.[contractName])
      ) {
        const copyOfViewFunctions = JSON.parse(
          JSON.stringify(trackedViewFunctions),
        );
        copyOfViewFunctions[contractName] = [functionSelector];
        setTrackedViewFunctions(copyOfViewFunctions);
      } else {
        const copyOfViewFunctions = { ...trackedViewFunctions };
        copyOfViewFunctions[contractName].push(functionSelector);
        setTrackedViewFunctions(copyOfViewFunctions);
      }
    },
    [trackedViewFunctions],
  );

  const removeTrackedViewFunction = useCallback(
    (contractName: string, functionSelector: string) => {
      if (
        !trackedViewFunctions?.[contractName] ||
        !Array.isArray(trackedViewFunctions?.[contractName])
      ) {
        // Do nothing, it's already removed
        return;
      } else {
        setTrackedViewFunctions({
          ...trackedViewFunctions,
          [contractName]: trackedViewFunctions[contractName].filter(
            (selector: string) => selector != functionSelector,
          ),
        });
      }
    },
    [trackedViewFunctions],
  );

  // Page 2 -  All of the Fuzzer Contracts
  const entry = filteredAbiData;

  const [allFuzzerContracts, setAllFuzzerContracts] = useState<
    BoilerplateFile[]
  >([]);
  const [reconSettingsFiles, setReconSettingsFiles] =
    useState<ReconProject | null>(null);

  const reconMode = ReconMode.normal;
  // Onload, set fuzzer contracts
  useEffect(() => {
    try {
      // NOTE: check `pageNumber` if you change it!
      if (pageNumber == 2 && filteredAbiData) {
        const reconSettings = generateReconProjectData(
          filteredAbiData.abiData,
          trackedContracts,
          skippedFunctions,
          trackedViewFunctions,
          reconMode,
        );
        const recondData = generateFullFuzzerContracts(reconSettings);
        setReconSettingsFiles(reconSettings);
        setAllFuzzerContracts(recondData);
      }
    } catch (e) {
      console.log("Exception in creating recon contracts", e);
    }
  }, [
    filteredAbiData,
    trackedContracts,
    skippedFunctions,
    trackedViewFunctions,
    pageNumber,
    reconMode,
  ]);

  const activateAllContracts = useCallback(() => {
    filteredAbiData.abiData.forEach((entry: abiEntry) => {
      console.log("???");
      if (!isContractActive(entry.name)) {
        addContract(entry.name);
      }
    });
  }, [filteredAbiData, addContract, isContractActive]);

  const downloadAllContracts = useCallback(() => {
    if (allFuzzerContracts.length > 0) {
      const zip = new JSZip();
      // Does this chain them?
      allFuzzerContracts.forEach((fuzzerContract) => {
        zip.file(fuzzerContract.name, fuzzerContract.data);
        fuzzerContract.name;
        fuzzerContract.data;
      });

      zip.generateAsync({ type: "blob" }).then(function (blob) {
        saveAs(blob, "recon.zip");
      });
    }
  }, [allFuzzerContracts]);

  return (
    <div className={styles.section}>
      <h1>
        Build your Handlers{" "}
        {pageNumber > 0 && (
          <Button onClick={() => setPageNumber(0)}>Select Contracts</Button>
        )}
        {pageNumber != 1 && (
          <Button onClick={() => setPageNumber(1)}>
            B4 and After Trackers
          </Button>
        )}
        {/* Only show results page if at least 1 contract is tracked */}
        {pageNumber != 2 && trackedContracts?.length > 0 && (
          <Button onClick={() => setPageNumber(2)}>Results Page</Button>
        )}
        {pageNumber == 2 && allFuzzerContracts?.length > 0 && (
          <Button onClick={() => setPageNumber(3)}>Installation Help</Button>
        )}
      </h1>

      {/* Page 0 - Select Contracts */}
      {pageNumber == 0 && (
        <>
          {/* Filters for path */}
          <Button onClick={() => setFilterLib(!filterLib)}>
            {filterLib && "Only showing files from src"}
            {!filterLib && "Showing all files"}
          </Button>

          {/* Toggle Contracts */}
          <div>
            <h2>{entry?.identifier}</h2>
            <div>
              {/* TODO: Make the button fully work! */}
              {/* <h3><Button onClick={() => activateAllContracts()}>Enable All Contracts!</Button> Enable All!</h3> */}

              {Array.isArray(entry?.abiData) &&
                entry?.abiData?.map((singleContract: abiEntry) => (
                  <div
                    className={styles.singleContract}
                    key={singleContract.name}
                  >
                    <h2>
                      {isContractActive(singleContract.name) ? (
                        <Button
                          onClick={() => removeContract(singleContract.name)}
                        >
                          Disable
                        </Button>
                      ) : (
                        <Button
                          onClick={() => addContract(singleContract.name)}
                        >
                          Enable
                        </Button>
                      )}{" "}
                      {singleContract.name}
                    </h2>
                    {/* Contract */}
                    {isContractActive(singleContract.name) && (
                      <div>
                        {/* Function */}
                        <div className={styles.buttonGrid}>
                          {processFunctions(
                            filterOutAllNonViewFunctions(singleContract.abi),
                          ).map((singleFunction: ABIWithSelector) => (
                            <div key={singleFunction.selector}>
                              {isFunctionDisabled(
                                singleContract.name,
                                singleFunction.selector,
                              ) ? (
                                <Button
                                  className={styles.handlerButtonDisabled}
                                  onClick={() =>
                                    removeDisabledFunction(
                                      singleContract.name,
                                      singleFunction.selector,
                                    )
                                  }
                                >
                                  Enable {singleFunction.name}
                                </Button>
                              ) : (
                                <Button
                                  className={styles.handlerButtonEnabled}
                                  onClick={() =>
                                    addDisabledFunction(
                                      singleContract.name,
                                      singleFunction.selector,
                                    )
                                  }
                                >
                                  Disable {singleFunction.name}
                                </Button>
                              )}
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                ))}
            </div>
          </div>
        </>
      )}

      {/* Page 1 - B4 and after Contracts */}
      {pageNumber == 1 && (
        <>
          {/* Given active contracts */}
          {/* Get the view functions */}
          {/* List them in some list (prob by [contractName]: selector[]) */}
          {/* Check if that's compatible */}

          {/* TODO FIX */}
          {Array.isArray(entry?.abiData) &&
            entry?.abiData?.map((singleContract: abiEntry) => (
              <div className={styles.singleContract} key={singleContract.name}>
                {/* Show contract if there's more than 1 view function */}
                {isContractActive(singleContract.name) &&
                  getAllViewFunctions(singleContract.abi).length > 0 && (
                    <div>
                      <h2>{singleContract.name}</h2>
                      {/* Function */}
                      <div className={styles.buttonGrid}>
                        {processFunctions(
                          getAllViewFunctions(singleContract.abi),
                        ).map((singleFunction: ABIWithSelector) => (
                          <div key={singleFunction.selector}>
                            {isViewFunctionActive(
                              singleContract.name,
                              singleFunction.selector,
                            ) ? (
                              <Button
                                className={styles.handlerButtonEnabled}
                                onClick={() =>
                                  removeTrackedViewFunction(
                                    singleContract.name,
                                    singleFunction.selector,
                                  )
                                }
                              >
                                Disable {singleFunction.name}
                              </Button>
                            ) : (
                              <Button
                                className={styles.handlerButtonDisabled}
                                onClick={() =>
                                  addTrackedViewFunction(
                                    singleContract.name,
                                    singleFunction.selector,
                                  )
                                }
                              >
                                Enable {singleFunction.name}
                              </Button>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
              </div>
            ))}
        </>
      )}

      {/* Page 2 - Results */}
      {pageNumber == 2 && (
        <>
          <div>
            {allFuzzerContracts?.length > 0 && (
              <>
                <Button onClick={() => downloadAllContracts()}>
                  Download All Files
                </Button>
                <p>Put the downloaded folder in /test/</p>
                {allFuzzerContracts?.map((boilerplate) => (
                  <div key={boilerplate.name}>
                    <h2>{boilerplate.name}</h2>
                    <h3>{boilerplate.path}</h3>
                    <AppCode code={boilerplate.data} language="javascript" />
                  </div>
                ))}
              </>
            )}
            {reconSettingsFiles && (
              <>
                <div>
                  <h2>Recon Settings File</h2>
                  <AppCode
                    code={JSON.stringify(reconSettingsFiles, null, 2)}
                    language="json"
                  />
                </div>
              </>
            )}
          </div>
        </>
      )}

      {/* Page 3 - Help */}
      {pageNumber == 3 && (
        <div>
          <h3>Install Chimera</h3>
          <AppCode
            code={"forge install Recon-Fuzz/chimera --no-commit"}
            language="bash"
          />

          <h3>Add Chimera To Mappings</h3>
          <p>For example, change the remappings to</p>
          <AppCode
            code={`
remappings = [
    'forge-std/=lib/forge-std/src/',
    '@chimera/=lib/chimera/src/'
]
`}
            language="toml"
          />

          <h3>Add all Recon Files To /test</h3>

          <h3>Manually Fix the Constructors</h3>

          <h3>You&apos;re done!</h3>
        </div>
      )}
    </div>
  );
}
