"use client";

import { AppListCheckItem } from "@/app/components/app-list-check-item";
import { AppPageTitle } from "@/app/components/app-page-title";

import { AllRecipes } from "./AllRecipes";
import { CreateUpdateRecipeForm } from "./CreateUpdateRecipe";

export default function Campaigns() {
  const renderListItem = (text) => {
    return <AppListCheckItem text={text} key={text} />;
  };

  return (
    <div className="pl-[45px] pt-[45px]">
      <AppPageTitle>Recipes</AppPageTitle>

      <p className="my-[20px] text-[20px] leading-[25px] text-textSecondary">
        Recipes are Job Configurations.
        <br />
        They can be used as:
      </p>

      <ul className="text-[16px] leading-[26px]">
        {["Presets in the Jobs Page", "Template for Campaigns"].map(
          renderListItem,
        )}
      </ul>

      <p className="mt-[40px] text-[15px] leading-[18px] text-textSecondary">
        Recipes appear as buttons in the Jobs Page
        <br />
        And can be used with Campaigns to automatically run on PR or Commit
      </p>

      <div>
        <CreateUpdateRecipeForm />
        <AllRecipes />
      </div>
    </div>
  );
}
