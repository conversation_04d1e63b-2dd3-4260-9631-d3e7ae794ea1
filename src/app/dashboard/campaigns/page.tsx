"use client";

import { AppListCheckItem } from "@/app/components/app-list-check-item";
import { AppPageTitle } from "@/app/components/app-page-title";

import { AllCampaigns } from "./AllCampaigns";
import { CreateCampaign } from "./CreateCampaign";

// Create a Campaign
// -> orgName, repoName, ref
// List all Campaigns

export default function Campaigns() {
  const renderListItem = (text) => {
    return <AppListCheckItem text={text} key={text} />;
  };

  return (
    <div className="pl-[45px] pt-[45px]">
      <AppPageTitle>Campaigns</AppPageTitle>

      <p className="my-[20px] text-[20px] leading-[25px] text-textSecondary">
        Create a campaign to automatically run jobs on PR Push and Creation
      </p>

      <ul className="text-[16px] leading-[26px]">
        {["Create a Recipe", "Create a Campaign"].map(renderListItem)}
      </ul>

      <p className="mt-[40px] text-[15px] leading-[18px] text-textSecondary">
        Once you have created a Recipe, you can create a Campaign to re-use the
        recipe on changes
        <br />
        All fields from a Pull Request are ORd with the Recipe data
        <br />
        Meaning you can re-use the same recipe with different repositories
      </p>

      <div>
        <CreateCampaign />
        <AllCampaigns />
      </div>
    </div>
  );
}
