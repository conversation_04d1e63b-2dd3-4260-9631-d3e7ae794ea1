"use client";

import axios from "axios";
import Link from "next/link";
import { useEffect, useState } from "react";

import { AppPageTitle } from "@/app/components/app-page-title";
import SingleJobContainer from "@/app/components/SingleJobContainer";
import type { Job } from "@/app/services/jobs.hooks";

export default function SingleRecurringJobPage({
  params: { recurringJobId },
}: {
  params: { recurringJobId: string };
}) {
  const [message, setMessage] = useState("");
  const [jobsData, setJobsData] = useState<Job[] | null>(null);
  const [isLoading, setLoading] = useState(false);

  useEffect(() => {
    async function getJobInfo() {
      if (!recurringJobId) {
        return;
      }

      setLoading(true);
      try {
        const res = await axios.get(`/api/recurring/${recurringJobId}`);
        console.log("res", res);
        setJobsData(res?.data?.data?.jobs);
      } catch (e) {
        console.log("exception e", e);
        if (e.response?.data?.message) {
          setMessage(e.response?.data?.message);
        } else {
          setMessage("Exception, check console");
          console.log(e);
        }
      }

      setLoading(false);
    }
    getJobInfo();
  }, [recurringJobId]);

  // TODO Only show last by default
  // Others, just link to the JobId

  const lastJob = jobsData?.[0];
  const rest = jobsData?.slice(1);

  console.log("");

  return (
    <div className="min-h-[100vh] grow overflow-y-auto bg-dashboardBG">
      <div className="mb-[45px] pl-[45px] pt-[45px]">
        <AppPageTitle>Recurring Job Page</AppPageTitle>
        <p className="text-textPrimary">
          Below are listed all jobs that were run
        </p>
      </div>

      {lastJob && (
        <SingleJobContainer
          isJobInfoLoading={isLoading}
          shareInfo={null}
          key={lastJob?.id}
          jobId={lastJob?.id}
          reloadShares={() => {}}
          jobData={lastJob}
          isLoading={isLoading}
        />
      )}

      <div className="mb-[45px] pl-[45px] pt-[45px]">
        <AppPageTitle>Previous Runs</AppPageTitle>
        {rest?.map((jobData) => (
          <div key={jobData.id} className="text-textPrimary">
            <Link target="_blank" href={`/dashboard/jobs/${jobData.id}`}>
              View older Job
            </Link>
          </div>
        ))}
      </div>
    </div>
  );
}
