"use client";

import Link from "next/link";

import { Button } from "@/app/components/Button";
import { Recon } from "@/app/components/Svgs";

import styles from "./page.module.scss";

export default function Vision() {
  return (
    <div className="min-h-[100vh] grow overflow-y-auto bg-dashboardBG text-textPrimary">
      <Link href="/">
        <div className={styles.recon}>
          <Recon />
        </div>
      </Link>
      <main className={styles.section}>
        <h1>The Recon Demo</h1>
        <p>Recon scaffolds invariant testing via Medusa and Echidna</p>
        <p>Recon is compatible with any Foundry Project</p>
        <Link href="https://book.getrecon.xyz/" target="_blank">
          <Button>
            <p>Written Tutorials</p>
          </Button>
        </Link>
        <h2>Demo</h2>
        <p>
          <iframe
            width="560"
            height="315"
            src="https://www.youtube.com/embed/z0sktBuJfEI"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
            allowFullScreen
          ></iframe>
        </p>

        <h2>5 Minutes Tutorial</h2>
        <p>
          <iframe
            width="560"
            height="315"
            src="https://www.youtube.com/embed/RwfiPrxbdBg"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
            allowFullScreen
          ></iframe>
        </p>
        <Link href="/dashboard">
          <h2>To use Recon, you need to create an account</h2>
          <Button>Create a Free Account!</Button>
        </Link>
      </main>
    </div>
  );
}
