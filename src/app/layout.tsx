import "./globals.css";

import type { Metadata } from "next";
import localFont from "next/font/local";
import type { ReactNode } from "react";

import { AppProviders } from "./app-providers";
import AuthProvider from "./context/AuthProvider";
import QueryProvider from "./services/QueryProvider";

export const metadata: Metadata = {
  title: "Recon",
  description: "Recon helps you build and run invariant tests",
};

const blenderPro = localFont({
  src: [
    {
      path: "fonts/BlenderPro-Bold.woff2",
      weight: "700",
      style: "normal",
    },
    {
      path: "fonts/BlenderPro-Medium.woff2",
      weight: "500",
      style: "normal",
    },
    {
      path: "fonts/BlenderPro-Thin.woff2",
      weight: "100",
      style: "normal",
    },
  ],
});

export default function RootLayout({ children }: { children: ReactNode }) {
  return (
    <html lang="en">
      <AuthProvider>
        <QueryProvider>
          <body className={`${blenderPro.className}`}>
            <AppProviders>
              {children}
            </AppProviders>
          </body>
        </QueryProvider>
      </AuthProvider>
    </html>
  );
}
