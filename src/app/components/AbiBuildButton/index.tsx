import type { <PERSON> } from "react";

import styles from "./button.module.scss";

interface Props {
  org: string;
  repo: string;
  branch: string;
}

export const Button: FC<Props> = ({ org, repo, branch }) => {
  // Check if already built
  // If already built, we link to handler

  // If not built, check if job
  // If job, show pending timer

  // If not job
  // Allow to click to queue job

  return <button className={styles.button} type={"button"}></button>;
};
