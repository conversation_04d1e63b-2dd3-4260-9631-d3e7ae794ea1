export const Recon = () => (
  <svg
    id="Group_2"
    data-name="Group 2"
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
    width="173.793"
    height="101.024"
    viewBox="0 0 173.793 101.024"
  >
    <defs>
      <clipPath id="clip-path">
        <rect
          id="Rectangle_1"
          data-name="Rectangle 1"
          width="173.793"
          height="101.024"
          fill="#fff"
        />
      </clipPath>
    </defs>
    <g id="Group_1" data-name="Group 1" clipPath="url(#clip-path)">
      <path
        id="Path_1"
        data-name="Path 1"
        d="M24.243,120.561a4.39,4.39,0,0,1-4.377,4.377H17.408l6.835,8.148v.5h-4.31l-7.273-8.653H4.377a5.067,5.067,0,0,1-.742-.067v8.72H0V109.382H19.866a4.39,4.39,0,0,1,4.377,4.377Zm-4.377.74a.747.747,0,0,0,.74-.74v-6.8a.747.747,0,0,0-.74-.741H4.377a.748.748,0,0,0-.742.741v6.8a.748.748,0,0,0,.742.74Z"
        transform="translate(0 -32.567)"
        fill="#fff"
      />
      <path
        id="Path_2"
        data-name="Path 2"
        d="M76.3,112.971H57.714v6.667h14.95v3.636H57.714v6.667H76.3v3.636H54.078V109.334H76.3Z"
        transform="translate(-16.101 -32.553)"
        fill="#fff"
      />
      <path
        id="Path_3"
        data-name="Path 3"
        d="M109.464,112.97a.748.748,0,0,0-.741.741V129.2a.748.748,0,0,0,.741.741h19.8v3.636h-19.8a4.369,4.369,0,0,1-4.377-4.377V113.711a4.369,4.369,0,0,1,4.377-4.377h19.8v3.636Z"
        transform="translate(-31.288 -32.553)"
        fill="#fff"
      />
      <path
        id="Path_4"
        data-name="Path 4"
        d="M178.647,109.334a4.369,4.369,0,0,1,4.377,4.377V129.2a4.369,4.369,0,0,1-4.377,4.377H163.158a4.369,4.369,0,0,1-4.377-4.377V113.711a4.369,4.369,0,0,1,4.377-4.377Zm0,20.606a.748.748,0,0,0,.741-.741V113.711a.748.748,0,0,0-.741-.741H163.158a.748.748,0,0,0-.741.741V129.2a.748.748,0,0,0,.741.741Z"
        transform="translate(-47.275 -32.553)"
        fill="#fff"
      />
      <path
        id="Path_5"
        data-name="Path 5"
        d="M233.561,109.335H237.2v24.242h-4.31l-16.3-19.394v19.394h-3.636V109.335h4.31l16.3,19.394Z"
        transform="translate(-63.405 -32.553)"
        fill="#fff"
      />
      <path
        id="Path_6"
        data-name="Path 6"
        d="M139.714,16.954c0,3.171.06,6.345-.044,9.514a3.707,3.707,0,0,1-.931,2.268q-12.65,13.06-25.4,26.028c-1.317,1.346-1.445,1.341-2.8-.046Q97.992,41.883,85.428,29.058A3.86,3.86,0,0,1,84.2,26.087c.055-6.189.02-12.379.055-18.569a7.142,7.142,0,0,1,.43-1.7,6.647,6.647,0,0,1,1.617.882c2.074,2.047,4.129,4.119,6.093,6.271a3.65,3.65,0,0,1,.878,2.134c.107,2.5-.022,5.014.083,7.516a3.552,3.552,0,0,0,.863,2.151c5.541,5.74,11.158,11.407,16.714,17.134.832.858,1.361.73,2.119-.049,5.563-5.719,11.172-11.395,16.712-17.137a3.314,3.314,0,0,0,.756-2.033c.091-2.5-.016-5.013.078-7.517a3.406,3.406,0,0,1,.777-2.034c2.115-2.292,4.319-4.5,6.53-6.7a4.65,4.65,0,0,1,1.28-.645,4.416,4.416,0,0,1,.484,1.339c.041,3.273.022,6.547.022,9.821h.022"
        transform="translate(-25.07 -1.725)"
        fill="#fff"
      />
      <path
        id="Path_7"
        data-name="Path 7"
        d="M114.737,0c6.439,0,12.877-.013,19.316.02a9.557,9.557,0,0,1,1.984.43,9.791,9.791,0,0,1-1.1,1.85c-2.03,2.158-4.094,4.287-6.214,6.357a3.521,3.521,0,0,0-1.136,2.844c.07,2.6.077,5.214-.013,7.816a3.329,3.329,0,0,1-.784,2.027c-3.627,3.811-7.344,7.538-10.992,11.33-.844.877-1.46.829-2.277-.019-3.618-3.751-7.3-7.441-10.887-11.221a3.563,3.563,0,0,1-.832-2.155c-.1-2.6-.077-5.212-.016-7.817A3.363,3.363,0,0,0,100.7,8.755c-2.121-2.067-4.183-4.2-6.212-6.358A11.536,11.536,0,0,1,93.265.443a11.451,11.451,0,0,1,2.156-.42C101.86-.01,108.3,0,114.737,0"
        transform="translate(-27.769 0)"
        fill="#fff"
      />
    </g>
  </svg>
);
