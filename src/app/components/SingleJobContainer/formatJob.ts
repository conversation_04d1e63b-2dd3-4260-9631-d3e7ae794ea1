import type { Job } from "@/app/services/jobs.hooks";

function formatFor(
  fuzzer: string,
  fuzzerArgs: any,
  testDuration: string
): string {
  if (testDuration) {
    return ` for ${testDuration}`; // TODO: This is currently formatted by the API, if this becomes MS we need to format it
  }

  if (!fuzzerArgs || (fuzzer != "ECHIDNA" && fuzzer != "MEDUSA")) {
    return "";
  }

  // NOTE: The extra space
  if (fuzzer == "ECHIDNA") {
    return fuzzerArgs?.testLimit ? ` for ${fuzzerArgs?.testLimit} tests` : "";
  }

  if (fuzzer == "MEDUSA") {
    return fuzzerArgs?.timeout ? ` for ${fuzzerArgs?.timeout} seconds` : "";
  }

  return "";
}

/*
  QUEUED // when the user creates a job, for example through the UI
  STARTED // when the `runner-starter` starts a new ECS task
  RUNNING // when the `runner` starts the fuzzer
  SUCCESS // when the fuzzer finishes succesfully
  ERROR // when the fuzzer finds an error
  STOPPED // when the user manually stops a job
*/
export default function formatJobs(job: Job): string {
  if (!job?.status) {
    return "";
  }

  switch (job.status) {
    case "QUEUED":
      return `Preparing to run ${job.fuzzer}`;
    // RUNNING
    case "STARTED":
      return `Running ${job.fuzzer}`;
    case "RUNNING":
      return `Running ${job.fuzzer}`;

    // DONE
    case "SUCCESS":
      return `Ran ${job.fuzzer}${formatFor(
        job.fuzzer,
        job?.fuzzerArgs,
        job?.testsDuration
      )}`;
    case "ERROR":
      return `Ran ${job.fuzzer}${formatFor(
        job.fuzzer,
        job?.fuzzerArgs,
        job?.testsDuration
      )}`;
    case "STOPPED":
      return `Ran ${job.fuzzer}${formatFor(
        job.fuzzer,
        job?.fuzzerArgs,
        job?.testsDuration
      )}`;
  }

  return "";
}
