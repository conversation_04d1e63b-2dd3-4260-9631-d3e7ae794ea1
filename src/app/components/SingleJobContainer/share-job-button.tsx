"use client";

import axios from "axios";
import { useState } from "react";
import { FaCheck, FaLink } from "react-icons/fa";

import { AppButton } from "@/app/components/app-button";
import { AppSpinner } from "@/app/components/app-spinner";

export const ShareJobButton = ({ jobId, reloadShares, shareInfo }) => {
  const [loading, setLoading] = useState(false);

  async function shareTheJob() {
    if (!jobId) {
      return;
    }

    setLoading(true);

    try {
      await axios({
        method: "POST",
        url: "/api/shares",
        data: {
          jobId,
        },
      });
    } catch (e) {
      console.log(e);
      alert(`Something went wrong: ${e.response.data.message}`);
    }

    reloadShares();
    setLoading(false);
  }

  // TODO: if shareInfo
  // Then turn it into clipboard copy tool

  const [copied, setCopied] = useState(false)

  function handleCopy() {
    navigator.clipboard.writeText(`${window.location.origin}/shares/${shareInfo.id}`);
    setCopied(true)
  }

  if(shareInfo) {
    return (
      <AppButton
      onClick={handleCopy}
      className=" bg-[#171717] px-[40px] py-[10px] text-[18px] leading-[21px]"
    >
      <p>Copy Share URL </p>
      {copied ? <FaCheck /> : <FaLink />} 
    </AppButton>
    )
  }

  return (
    <AppButton
      className=" bg-[#171717] px-[40px] py-[10px] text-[18px] leading-[21px]"
      disabled={loading}
      onClick={shareTheJob}
    >
      {loading ? (
        <AppSpinner />
      ) : (
        <>
          <span>Share Job Results</span>
          <FaLink />
        </>
      )}
    </AppButton>
  );
};
