import React from "react";
import { useGetStats } from "../../services/stats.hook";
import VideoPlayer from "../VideoPlayer/VideoPlayer";
import GenericButton from "../GenericButton/GenericButton";
import Link from "next/link";

export default function ReconProCTA() {
  return (
    <div className="flex flex-col items-center rounded-lg border border-[#D0BCFF] p-4 lg:p-6">
      <div className="grid size-full grid-cols-3 gap-2 border-b-2 border-[#D0BCFF] text-center lg:gap-10">
        <div className="w-auto">
          <h3 className="sub-title-custom font-bold tracking-normal sm:text-[64px] md:text-[72px] lg:text-[100px]">
            22
          </h3>
          <h4 className="mb-5 font-thin leading-[16px] tracking-normal text-white lg:mb-[32px] lg:text-[32px] lg:leading-[33px]">
            Pro Accounts
          </h4>
        </div>
        <div className="w-auto">
          <h3 className="sub-title-custom font-bold tracking-normal sm:text-[64px] md:text-[72px] lg:text-[100px]">
            10k+
          </h3>
          <h4 className="mb-5 font-thin leading-[16px] tracking-normal text-white lg:mb-[32px] lg:text-[32px] lg:leading-[33px]">
            Jobs run in the cloud
          </h4>
        </div>
        <div className="w-auto">
          <h3 className="sub-title-custom font-bold tracking-normal sm:text-[64px] md:text-[72px] lg:text-[100px]">
            108k+
          </h3>
          <h4 className="mb-5 font-thin leading-[16px] tracking-normal text-white lg:mb-[32px] lg:text-[32px] lg:leading-[33px]">
            Properties Broken
          </h4>
        </div>
      </div>
      <div className="mt-10 flex w-full flex-col">
        <div className="mb-2 rounded-lg bg-[#171225] p-5 lg:mb-4">
          <h3 className="uppercase text-white lg:text-[40px]">It's never been easier</h3>
          <p className="font-thin text-white lg:text-[24px]">3 click to run Medusa, Echidna, Halmos or Kontrol in the cloud, works with private repos</p>
        </div>
        <div className="mb-2 rounded-lg bg-[#171225] p-5 lg:mb-4">
          <h3 className="uppercase text-white lg:text-[40px]">One click sharing and Corpus Reuse</h3>
          <p className="font-thin text-white lg:text-[24px]">Make your result public in one click with automatic reports and repro for all fuzzers</p>
        </div>
        <div className="mb-2 rounded-lg bg-[#171225] p-5 lg:mb-4">
          <h3 className="uppercase text-white lg:text-[40px]">Ready for Automation</h3>
          <p className="font-thin text-white lg:text-[24px]">Run on PR, Commit or via API, trigger alerts on broken properties</p>
        </div>
      </div>
      <Link
        href="/pro/"
        className="m-0 flex flex-row items-center justify-center p-0"
        target="_blank"
        rel="noopener noreferrer"
      >
        <GenericButton className={"button_cta m-0 p-0"}>
          Learn more about Pro
        </GenericButton>
      </Link>
    </div>
  );
}
