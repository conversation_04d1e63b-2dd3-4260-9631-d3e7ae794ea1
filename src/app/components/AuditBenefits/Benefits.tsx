import React from "react";
import Benefit from "./Benefit/Benefit";

const BenefitsArray = [
  {
    title: "THE MISSING PIECE",
    description:
      "Invariant Testing is often the missing piece to reduce the number of bugs protocols go to audit with",
    upcoming: false,
    experimental: false,
  },
  {
    title: "CODE THAT GROWS WITH YOU",
    description:
      "Invariant Tests help you specify your system behaviour, helping increase it's predictability",
    upcoming: false,
    experimental: false,
  },
  {
    title: "NEVER MAKE THE SAME MISTAKE AGAIN",
    description:
      "Invariant Tests can run on every edit, meaning once you fix a bug, they'll check against it",
    upcoming: false,
    experimental: false,
  },
  {
    title: "WORLD CLASS MANUAL REVIEW",
    description: "Recon Audits are lead exclusively by seasoned vetrans, we only take audits for which we have an edge",
    upcoming: false,
    experimental: false,
  },
  {
    title: "RECON PRO",
    description:
    "Our fully developed cloud platform saves you time with features nobody else has built. Recon Pro is included in every engagement",
    upcoming: false,
    experimental: false,
  },
  {
    title: "LIVE MONITORING",
    description:
      "Recon suites are easily reusable for live monitoring, these tests try to predict exploits instead of",
    upcoming: false,
    experimental: false,
  },
];

export default function Benefits() {
  return (
    <div className="flex items-center justify-center px-4 py-16">
      <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
        {BenefitsArray.map((benefit, index) => {
          return (
            <Benefit
              key={index}
              title={benefit.title}
              description={benefit.description}
              upcoming={benefit.upcoming}
              experimental={benefit.experimental}
            />
          );
        })}
      </div>
    </div>
  );
}
