import { forwardRef, useId } from "react";
import type { IconType } from "react-icons/lib";

import { cn } from "../helpers/cn";

type Option = {
  value: string | number;
  label: string;
};

type AppSelectProps = {
  className?: string;
  disabled?: boolean;
  onChange?: (e: any) => void;
  value?: string | number;
  options: Option[];
  placeholder?: string;
  label?: string;
  containerClassName?: string;
  error?: string;
  disableError?: boolean;
};

export const AppSelect = forwardRef(
  (
    {
      onChange,
      disabled,
      value,
      options,
      placeholder = "",
      className = "",
      label,
      containerClassName = "",
      error,
      disableError = false,
      ...rest
    }: AppSelectProps,
    ref: any,
  ) => {
    const id = useId();
    return (
      <div className={cn("relative", containerClassName)}>
        {label && (
          <label
            htmlFor={id}
            className="mb-[3px] block text-[15px] leading-[18px] text-textSecondary"
          >
            {label}
          </label>
        )}
        <div className={cn("relative")}>
          <select
            id={id}
            className={cn(
              "h-[41px] border border-border rounded-[4px] min-w-[230px] border-divider bg-inputBg pl-[10px] w-[100%] text-textSecondary italic outline-none",
              className,
            )}
            value={value}
            onChange={onChange}
            disabled={disabled}
            ref={ref}
            {...rest}
          >
            {placeholder && (
              <option value="" disabled>
                {placeholder}
              </option>
            )}
            {options.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>

        {!disableError && error && (
          <span className="mt-[3px] block h-[14px] text-[12px] text-error">
            {error}
          </span>
        )}
      </div>
    );
  },
);

AppSelect.displayName = "AppSelect";
