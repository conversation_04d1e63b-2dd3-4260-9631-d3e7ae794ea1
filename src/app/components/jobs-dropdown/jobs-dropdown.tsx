"use client";
import {
  MdOutlineKeyboardArrowDown,
  MdOutlineKeyboardArrowUp,
} from "react-icons/md";

import { useGetAbiJobs } from "@/app/services/abi-jobs-service";
import { useBooleanState } from "@/app/services/helpers";
import { useGetJobs } from "@/app/services/jobs.hooks";

import { JobStatusTracker } from "./jobs-status-tracker";

export default function JobsDropDown() {
  const [isMinimized, { toggle }] = useBooleanState(true);

  const { data: jobs, isLoading: jobsLoading } = useGetJobs();
  const { data: abiJobs, isLoading: abiJobsLoading } = useGetAbiJobs();

  const ChevronIcon = !isMinimized
    ? MdOutlineKeyboardArrowDown
    : MdOutlineKeyboardArrowUp;

  const loading = jobsLoading || abiJobsLoading;

  if ((!jobs?.length && !abiJobs?.length) || loading) return null;

  return (
    <div className="fixed bottom-0 right-[14px] w-[280px]">
      <div className="flex items-center gap-[19px] rounded-t-[10px] bg-aside px-[15px] py-[8px]">
        <button
          className="flex size-[30px] items-center justify-center rounded-full bg-dashboardBG"
          onClick={toggle}
        >
          <ChevronIcon className="text-textPrimary" />
        </button>
        <span className="text-[16px] leading-[19px] text-textPrimary">
          {isMinimized ? "Jobs Info" : "Minimize"}
        </span>
      </div>
      {!isMinimized && (
        <JobStatusTracker
          {...{
            jobs,
            abiJobs,
          }}
        />
      )}
    </div>
  );
}
