"use client";
import { useRouter } from "next/navigation";
import { IoMdArrowRoundBack } from "react-icons/io";

export const AppBackButton = () => {
  const router = useRouter();

  const onClick = () => {
    router.back();
  };

  return (
    <div
      className="gradient-dark-bg flex size-[30px] cursor-pointer items-center justify-center rounded-full bg-blockBg text-[15px]"
      onClick={onClick}
    >
      <IoMdArrowRoundBack />
    </div>
  );
};
