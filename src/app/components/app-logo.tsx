export const AppLogo = () => {
  return (
    <svg
      width="158"
      height="24"
      viewBox="0 0 158 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M60.6114 11.7265C60.6091 12.4746 60.3103 13.1914 59.7802 13.7204C59.25 14.2494 58.5316 14.5475 57.7818 14.5498H56.1929L60.6114 19.8053V20.1279H57.8252L53.1235 14.5465H47.769C47.6082 14.5439 47.4479 14.5294 47.2893 14.5033V20.1279H44.9395V4.51587H57.7818C58.5316 4.51808 59.25 4.81624 59.7802 5.34522C60.3103 5.8742 60.6091 6.59102 60.6114 7.33911V11.7265ZM57.7818 12.2038C57.9084 12.2026 58.0293 12.152 58.1188 12.0627C58.2083 11.9735 58.259 11.8527 58.2602 11.7265V7.3404C58.2592 7.21406 58.2085 7.09317 58.119 7.00377C58.0296 6.91437 57.9085 6.86362 57.7818 6.86244H47.769C47.6422 6.86345 47.5209 6.91411 47.4311 7.0035C47.3414 7.0929 47.2905 7.21389 47.2893 7.3404V11.7265C47.2907 11.8529 47.3417 11.9737 47.4314 12.063C47.5211 12.1522 47.6423 12.2028 47.769 12.2038H57.7818Z"
        fill="var(--logoColor)"
      />
      <path
        d="M83.8552 6.83989H71.8402V11.1402H81.5047V13.4855H71.8402V17.7858H83.8552V20.1311H69.4897V4.49396H83.8552V6.83989Z"
        fill="var(--logoColor)"
      />
      <path
        d="M95.4765 6.83925C95.3498 6.84042 95.2287 6.89115 95.1391 6.98053C95.0495 7.06992 94.9987 7.1908 94.9975 7.3172V17.3079C94.9987 17.4343 95.0495 17.5552 95.1391 17.6445C95.2287 17.7339 95.3498 17.7846 95.4765 17.7858H108.276V20.1311H95.4765C95.1047 20.1318 94.7365 20.0592 94.3929 19.9176C94.0493 19.776 93.7371 19.568 93.4742 19.3057C93.2113 19.0434 93.0029 18.7319 92.861 18.3891C92.719 18.0462 92.6463 17.6788 92.647 17.3079V7.3172C92.6463 6.94626 92.719 6.57883 92.861 6.23599C93.0029 5.89316 93.2113 5.58165 93.4742 5.31935C93.7371 5.05705 94.0493 4.84912 94.3929 4.70748C94.7365 4.56584 95.1047 4.49328 95.4765 4.49396H108.276V6.83925H95.4765Z"
        fill="var(--logoColor)"
      />
      <path
        d="M129.865 4.49396C130.237 4.49328 130.605 4.56584 130.949 4.70748C131.292 4.84912 131.605 5.05705 131.867 5.31935C132.13 5.58165 132.339 5.89316 132.481 6.23599C132.623 6.57883 132.695 6.94626 132.695 7.3172V17.3079C132.695 17.6788 132.623 18.0462 132.481 18.3891C132.339 18.7319 132.13 19.0434 131.867 19.3057C131.605 19.568 131.292 19.776 130.949 19.9176C130.605 20.0592 130.237 20.1318 129.865 20.1311H119.852C119.48 20.1318 119.112 20.0592 118.769 19.9176C118.425 19.776 118.113 19.568 117.85 19.3057C117.587 19.0434 117.379 18.7319 117.237 18.3891C117.095 18.0462 117.022 17.6788 117.023 17.3079V7.3172C117.022 6.94626 117.095 6.57883 117.237 6.23599C117.379 5.89316 117.587 5.58165 117.85 5.31935C118.113 5.05705 118.425 4.84912 118.769 4.70748C119.112 4.56584 119.48 4.49328 119.852 4.49396H129.865ZM129.865 17.7852C129.992 17.784 130.113 17.7333 130.203 17.6439C130.292 17.5545 130.343 17.4336 130.344 17.3072V7.3172C130.343 7.1908 130.292 7.06992 130.203 6.98053C130.113 6.89115 129.992 6.84042 129.865 6.83925H119.852C119.726 6.84042 119.604 6.89115 119.515 6.98053C119.425 7.06992 119.374 7.1908 119.373 7.3172V17.3079C119.374 17.4343 119.425 17.5552 119.515 17.6445C119.604 17.7339 119.726 17.7846 119.852 17.7858L129.865 17.7852Z"
        fill="var(--logoColor)"
      />
      <path
        d="M154.937 4.49457H157.289V20.1311H154.503L143.966 7.62161V20.1311H141.615V4.49457H144.402L154.939 17.004L154.937 4.49457Z"
        fill="var(--logoColor)"
      />
      <path
        d="M24.709 6.76416C24.709 8.17236 24.7357 9.58189 24.6894 10.9892C24.6698 11.3621 24.5237 11.7173 24.2751 11.9964C20.5216 15.8629 16.7533 19.7158 12.9702 23.5551C12.3841 24.1528 12.3271 24.1506 11.724 23.5346C8.00112 19.7347 4.2757 15.9363 0.547764 12.1394C0.366992 11.9716 0.224597 11.7668 0.13028 11.5392C0.0359636 11.3115 -0.0080607 11.0662 0.00121193 10.82C0.025691 8.07155 0.0101135 5.32266 0.0256911 2.57376C0.0581636 2.31522 0.122442 2.06166 0.217073 1.81881C0.474023 1.91542 0.716244 2.04724 0.936759 2.2105C1.85984 3.11954 2.77447 4.03969 3.64859 4.99536C3.87813 5.26078 4.01525 5.5933 4.03937 5.94305C4.08699 7.05326 4.02958 8.16969 4.07631 9.2808C4.0937 9.6331 4.22894 9.96943 4.46041 10.236C6.92656 12.7851 9.42654 15.3017 11.8994 17.845C12.2697 18.226 12.5051 18.1692 12.8425 17.8232C15.3184 15.2835 17.8148 12.7629 20.2806 10.2129C20.491 9.95817 20.6095 9.64019 20.617 9.31011C20.6575 8.19989 20.6099 7.0839 20.6517 5.97191C20.6649 5.64118 20.7863 5.32387 20.9976 5.06864C21.9389 4.05079 22.9198 3.07025 23.9039 2.09326C24.0778 1.96898 24.27 1.87235 24.4736 1.80682C24.5772 1.9921 24.6499 2.19292 24.689 2.40146C24.7073 3.85495 24.6988 5.30889 24.6988 6.76283H24.7086"
        fill="var(--logoColor)"
      />
      <path
        d="M12.3911 0.00112112C15.2569 0.00112112 18.1223 -0.004652 20.9881 0.0100029C21.2884 0.0421941 21.5845 0.106208 21.8712 0.20096C21.7387 0.491933 21.5745 0.767451 21.3816 1.02252C20.4781 1.98086 19.5595 2.92632 18.6159 3.84558C18.4417 4.00452 18.3058 4.20066 18.2183 4.41935C18.1307 4.63804 18.0938 4.87364 18.1103 5.10856C18.1414 6.26319 18.1446 7.42403 18.1045 8.57954C18.0924 8.9101 17.9695 9.22705 17.7556 9.4797C16.1413 11.1721 14.4869 12.8272 12.8633 14.5112C12.4877 14.9007 12.2135 14.8794 11.8499 14.5028C10.2396 12.837 8.60085 11.1983 7.00437 9.51967C6.77784 9.25055 6.64756 8.91385 6.63407 8.56266C6.58956 7.40804 6.5998 6.24809 6.62695 5.09124C6.642 4.86751 6.60636 4.64327 6.52268 4.43516C6.43901 4.22705 6.30944 4.04042 6.14359 3.8891C5.19959 2.97117 4.28185 2.02394 3.3788 1.0656C3.16877 0.795207 2.98642 0.504508 2.83447 0.197851C3.1476 0.105143 3.46894 0.0426832 3.79405 0.0113351C6.65988 -0.00331974 9.52616 0.00112112 12.3911 0.00112112Z"
        fill="var(--logoColor)"
      />
    </svg>
  );
};
