.button {
  background-color: var(--primary-color);
  padding: 12px 16px;
  color: var(--light-text-color);
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  border-radius: 6px;
  border: none;
  cursor: pointer;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  gap: 10px;
}
.button_login {
  background-color: transparent;
  border: 1px solid white;
  padding: 12px 16px;
  border-radius: 4px;
  margin: 10px;
}

.button_cta {
  background-color: #D0BCFF;
  border: 1px solid white;
  padding: 12px 16px;
  border-radius: 4px;
  margin: 10px;
  color: black;
}
@media (max-width: 980px) {
  .button,
  .button_cta,
  .button_login {
    width: 80%;
  }
}
.button:hover {
  transform: scale(1.05);
}
