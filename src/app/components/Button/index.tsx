import type { HTMLProps } from "react";
import { forwardRef } from "react";

import styles from "./button.module.scss";

interface ButtonProps extends HTMLProps<HTMLButtonElement> {
  type?: "button" | "submit" | "reset";
}

export const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  (props, ref) => {
    const { children, type, className, ...otherProps } = props;
    const _className = `${styles.button} ${className}`;
    return (
      <button
        className={_className}
        ref={ref}
        type={type || "button"}
        {...otherProps}
      >
        {children}
      </button>
    );
  },
);

Button.displayName = "Button";
