import React from "react";
import Benefit from "./Benefit/Benefit";

const BenefitsArray = [
  {
    title: "THE MISSING PIECE",
    description:
      "Invariant Testing is often the missing piece to reduce the number of bugs protocols go to audit with",
    upcoming: false,
    experimental: false,
  },
  {
    title: "NEVER STUCK IN QUEUE",
    description:
      "Recon Pro allows an unlimited number of parallel runs, we cap the total hours of usage, not the number of concurrent runs",
    upcoming: false,
    experimental: false,
  },
  {
    title: "ONE INTERFACE, ALL OF THE TOOLS",
    description:
      "Echidna, Medusa, Foundry, Halmos and Kontrol, the Recon panel abstracts away the complexity of handling infra, shareable runs, reusable corpus, rpc forks",
    upcoming: false,
    experimental: false,
  },
  {
    title: "DYNAMIC REPLACEMENT",
    description: "Replace values in your tester before running your suites",
    upcoming: false,
    experimental: true,
  },
  {
    title: "LIVE MONITORING",
    description:
      "Convert your Invariant Testing Properties into tests that are checked against each block",
    upcoming: false,
    experimental: true,
  },
  {
    title: "GOVERNANCE FUZZING",
    description:
      "Trigger an invariant testing suite against all of your onChain Proposals.",
    upcoming: false,
    experimental: true,
  },
];

export default function Benefits() {
  return (
    <div className="flex items-center justify-center px-4 py-16">
      <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
        {BenefitsArray.map((benefit, index) => {
          return (
            <Benefit
              key={index}
              title={benefit.title}
              description={benefit.description}
              upcoming={benefit.upcoming}
              experimental={benefit.experimental}
            />
          );
        })}
      </div>
    </div>
  );
}