"use client";

import Image from "next/image";
import { redirect } from "next/navigation";
import { useSession } from "next-auth/react";
import { useState } from "react";

import type { Organization } from "@/app/services/organization.hooks";
import { OrgStatus } from "@/app/services/organization.hooks";

import { AppSpinner } from "../app-spinner";

const ORG_STATUS_MAP = {
  [OrgStatus.FREE]: "Recon Public Good",
  [OrgStatus.PAID]: "Recon PRO",
  [OrgStatus.TRIAL]: "Recon PRO Trial",
  [OrgStatus.NOORG]: "Please create an account!",
};

type DashboardUserProps = {
  organization: Organization;
  isLoading: boolean;
  orgStatus: OrgStatus;
};

export function DashboardUser({
  organization,
  isLoading,
  orgStatus,
}: DashboardUserProps) {
  const { data: session, status } = useSession({
    required: true,
    onUnauthenticated() {
      redirect("/api/auth/signin?callbackUrl=/dashboard");
    },
  });

  const [showExtra, setShowExtra] = useState(false);

  const renderOrgStatus = () => {
    if (isLoading)
      return (
        <p className="text-[15px] leading-[18px] text-textSecondary">
          Loading ORG...
        </p>
      );
    if (organization) {
      return (
        <p
          onClick={() => setShowExtra(!showExtra)}
          className="text-[15px] leading-[18px] text-textSecondary"
        >
          {ORG_STATUS_MAP[orgStatus]}
        </p>
      );
    }
  };

  if (!session || !session.user) return null;

  return (
    <>
      {status === "loading" && <AppSpinner />}
      {session.user.image ? (
        <Image
          src={session.user.image}
          priority
          alt="Profile"
          width={50}
          height={50}
          className="rounded-full object-cover"
        />
      ) : null}
      <div>
        <span className="text-[15px] leading-[18px] text-textPrimary">
          {session.user.name}
        </span>
        {/* Check if they have ORG */}
        {/* YES -> Show FREE | PRO -> Click -> orgId */}
        {/* NO -> Show button to allow them to register */}
        {/* !organization -> They need to onboard */}
        {/* TODO: MAKE ORG STATUS HERE AS WELL, SAME AS MENU */}
        {renderOrgStatus()}
        {showExtra && (
          <div className="text-textSecondary">
            {organization && <>Org ID: {organization.id}</>}
          </div>
        )}
      </div>
    </>
  );
}
