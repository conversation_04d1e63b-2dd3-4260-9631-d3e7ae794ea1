import { useMemo } from "react";
import {
  FaCheckCircle,
  FaFolderOpen,
  FaGlobe,
  FaLink,
  FaLock,
  FaScroll,
} from "react-icons/fa";
import { FaArrowRotateRight, FaPeopleGroup } from "react-icons/fa6";
import { IoIosSwitch } from "react-icons/io";
import { IoBagSharp, IoShieldCheckmarkSharp } from "react-icons/io5";
import { GiMagicHat } from "react-icons/gi";
import { PiFlowArrowFill, PiLightningBold } from "react-icons/pi";

import { useGetAbi } from "@/app/services/abi.hook";
import { OrgStatus, useGetMyOrg } from "@/app/services/organization.hooks";
import type { AbiApiData } from "@/app/types/abi.api";

const mapChildItems = (abis: AbiApiData[]) =>
  abis.map((project) => ({
    label: project.identifier,
    href: `/dashboard/handlers/${project.identifier}`,
    icon: FaFolderOpen,
  }));

const makeFreeSidebarLinks = (abis: AbiApiData[]) => {
  const items = [
  {
    label: "Build your Handlers (new)",
    icon: FaFolderOpen,
    type: "link",
    href: "/dashboard/build",
  },
  {
    label: "Saved Handlers",
    icon: IoIosSwitch,
    type: "button",
    href: "/dashboard/handlers",
    childItems: mapChildItems(abis),
  },
  {
    label: "Get PRO",
    icon: IoShieldCheckmarkSharp,
    type: "link",
    href: "/dashboard/pro",
  },
];
  // Remove Saved Handlers for new accounts
  if (abis.length == 0) {
    items.filter((item) => item.label !== "Saved Handlers");
  }
  return items;
};

const makeProSidebarLinks = (abis: AbiApiData[]) => {
  const items = [
  {
    label: "Build your Handlers (new)",
    icon: FaFolderOpen,
    type: "link",
    href: "/dashboard/build",
  },
  {
    label: "Saved Handlers",
    icon: IoIosSwitch,
    type: "button",
    childItems: mapChildItems(abis),
  },
  {
    label: "Jobs",
    icon: IoBagSharp,
    type: "link",
    href: "/dashboard/jobs",
  },
  {
    label: "Private Repos",
    icon: FaLock,
    type: "link",
    href: "/dashboard/installs",
  },
  {
    label: "Dynamic replacement",
    icon: PiLightningBold,
    type: "link",
    href: "/dashboard/dynamic-replacement",
  },
  {
    label: "Recipes & Alerts",
    icon: FaScroll,
    type: "link",
    href: "/dashboard/recipes",
  },
  {
    label: "Campaigns",
    icon: FaGlobe,
    type: "link",
    href: "/dashboard/campaigns",
  },
  {
    label: "Recurring Jobs",
    icon: FaArrowRotateRight,
    type: "link",
    href: "/dashboard/recurring",
  },
  {
    label: "Live Monitoring",
    icon: FaCheckCircle,
    type: "link",
    href: "/dashboard/monitoring",
  },
  {
    label: "Governance Fuzzing",
    icon: GiMagicHat,
    type: "link",
    href: "/dashboard/governance-fuzzing",
  },
  {
    label: "Shares",
    icon: FaLink,
    type: "link",
    href: "/dashboard/shares",
  },
  {
    label: "Invite Colleagues",
    icon: FaPeopleGroup,
    type: "link",
    href: "/dashboard/settings",
    },
  ];

  // Remove Saved Handlers for new accounts
  if (abis.length == 0) {
    items.filter((item) => item.label !== "Saved Handlers");
  }
  return items;
};

const makeNoOrgSidebarLinks = () => [
  {
    label: "Create an Account",
    icon: PiFlowArrowFill,
    type: "link",
    href: "/dashboard/onboard",
  },
];

export const useGetSidebarLinks = () => {
  const { orgStatus } = useGetMyOrg();
  const { data: abiData, isLoading: isChildItemsLoading } = useGetAbi();

  const items = useMemo(() => {
    const abiDataFinal = abiData ?? [];

    const MAP = {
      [OrgStatus.PAID]: makeProSidebarLinks(abiDataFinal),
      [OrgStatus.TRIAL]: makeProSidebarLinks(abiDataFinal),
      [OrgStatus.FREE]: makeFreeSidebarLinks(abiDataFinal),
    };

    return !orgStatus ? makeNoOrgSidebarLinks() : MAP[orgStatus] || [];
  }, [abiData, orgStatus]);

  return { items, isChildItemsLoading };
};
