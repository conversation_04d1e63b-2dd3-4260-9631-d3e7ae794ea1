import { forwardRef, useId } from "react";

import { cn } from "../helpers/cn";

type SelectInputProps = {
  className?: string;
  defaultValue?: string;
  disabled?: boolean;
  onChange?: (e: React.ChangeEvent<HTMLSelectElement>) => void;
  value?: string;
  placeholder?: string;
  label?: string;
  containerClassName?: string;
  error?: string;
  disableError?: boolean;
  options: { label: string; value: string }[];
};

export const AppSelectInput = forwardRef<HTMLSelectElement, SelectInputProps>(
  (
    {
      onChange,
      disabled,
      value,
      placeholder = "",
      className = "",
      label,
      containerClassName = "",
      error,
      defaultValue,
      disableError = false,
      options,
      ...rest
    },
    ref,
  ) => {
    const id = useId();

    return (
      <div className={cn("relative", containerClassName)}>
        {label && (
          <label
            htmlFor={id}
            className="mb-[3px] block text-[15px] leading-[18px] text-textSecondary"
          >
            {label}
          </label>
        )}
        <select
          id={id}
          className={cn(
            "h-[41px] border border-border rounded-[4px] min-w-[230px] border-divider bg-inputBg pl-[10px] w-[100%] text-textSecondary outline-none",
            className,
          )}
          value={value}
          onChange={onChange}
          disabled={disabled}
          ref={ref}
          {...rest}
        >
          {options.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
        {!disableError && (
          <span className="mt-[3px] block h-[14px] text-[12px] text-error">
            {error}
          </span>
        )}
      </div>
    );
  },
);

AppSelectInput.displayName = "SelectInput";
