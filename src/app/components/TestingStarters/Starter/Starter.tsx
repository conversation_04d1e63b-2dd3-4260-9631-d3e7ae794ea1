import React from 'react'
import Image from "next/image";
import Link from "next/link";
import { FaGithub } from "react-icons/fa";

interface StarterProps {
  image: string
  name: string
  description: string
  github: string
}

export default function Starter({ image, name, description, github }: StarterProps) {
  return (
    <div
      className="mr-7 flex min-h-[272px] w-[300px] snap-center flex-col justify-between rounded-lg bg-[#6650a43d] p-4 leading-[24px] text-white shadow-lg md:w-[370px] lg:w-[370px]"
    >
      <div>
        <div className="mb-4 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Image
              src={image}
              alt="project logo"
              height={32}
              width={32}
            />
            <p className="text-[26px] uppercase">{name}</p>
          </div>
        </div>
        <div className="mb-4 mt-2">
          <p className="text-[16px]">{description}</p>
        </div>
      </div>

      <div className="mt-auto">
        <Link href={github} className="size-8">
          <FaGithub className="text-[34px] text-[#D8C7FF]" />
        </Link>
      </div>
    </div>
  )
}
