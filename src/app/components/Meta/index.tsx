import Head from "next/head";

export default function MetaTag() {
  return (
    <Head>
      <title>Recon - Run invariants today</title>
      <meta property="og:site_name" content="Recon" />
      <meta property="og:title" content={`Recon - Run invariants today`} />
      <meta
        property="og:description"
        content={`Recon - Run invariants today`}
      />
      <meta
        property="og:image"
        content="https://media.publit.io/file/Recon.jpg"
      />

      <meta name="twitter:card" content="summary_large_image" />
      <meta property="twitter:title" content="Recon - Run invariants today" />
      <meta
        property="twitter:description"
        content={`Recon - Run invariants today`}
      />
      <meta
        property="twitter:image"
        content="https://media.publit.io/file/Recon.jpg"
      />

      <meta property="twitter:creator" content="@GalloDaSballo" />
      <meta property="twitter:creator" content="@agfviggiano" />
    </Head>
  );
}
