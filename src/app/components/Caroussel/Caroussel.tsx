import { useState } from "react";
import { FiChevronLeft, FiChevronRight } from "react-icons/fi";

interface Props {
  data: any[];
  children: React.ReactNode;
}

export default function Caroussel({ data, children }: Props) {
  const [currentIndex, setCurrentIndex] = useState(0);

  const handlePrev = () => {
    setCurrentIndex((prevIndex) =>
      prevIndex === 0 ? data.length - Math.floor(data.length / 2) : prevIndex - 1
    );
  };

  const handleNext = () => {
    setCurrentIndex((prevIndex) =>
      prevIndex === data.length - Math.floor(data.length / 2)
        ? 0
        : prevIndex + 1
    );
  };

  return (
    <div className="scrollbar-hide relative flex w-full items-center justify-center">
      <button
        onClick={handlePrev}
        className="absolute right-12 top-[-50px] z-10 rounded-full border border-[#D0BCFF] bg-gray-900 p-2 text-white"
      >
        <FiChevronLeft size={24} />
      </button>
      <button
        onClick={handleNext}
        className="absolute right-0 top-[-50px] z-10 rounded-full border border-[#D0BCFF] bg-gray-900 p-2 text-white"
      >
        <FiChevronRight size={24} />
      </button>
      <div className="flex w-full snap-x snap-mandatory space-x-4 overflow-x-scroll">
        <div
          className="flex overflow-visible transition-transform duration-300"
          style={{ transform: `translateX(-${currentIndex * 20}%)` }}
        >
        {children}
        </div>
      </div>
    </div>
  );
}
