import { forwardRef, useId } from "react";
import type { IconType } from "react-icons/lib";

import { cn } from "../helpers/cn";

type AppTextareaProps = {
  type?: "text" | "password";
  className?: string;
  disabled?: boolean;
  onChange?: (e: any) => void;
  value?: string | number;
  placeholder?: string;
  label?: string;
  containerClassName?: string;
  error?: string;
  disableError?: boolean;
  icon?: IconType;
};
export const AppTextarea = forwardRef(
  (
    {
      type,
      onChange,
      disabled,
      value,
      placeholder = "",
      className = "",
      label,
      containerClassName = "",
      error,
      icon: Icon,
      disableError = false,
      ...rest
    }: AppTextareaProps,
    ref: any,
  ) => {
    const id = useId();
    return (
      <div className={cn("relative", containerClassName)}>
        {label && (
          <label
            htmlFor={id}
            className="mb-[3px] block text-[15px] leading-[18px] text-textSecondary"
          >
            {label}
          </label>
        )}
        {!!Icon && (
          <Icon className="absolute left-2 top-1/2 -translate-y-1/2 text-textSecondary" />
        )}
        <textarea
          rows={10}
          id={id}
          className={cn(
            " border border-border rounded-[4px] min-w-[230px] border-divider bg-inputBg pl-[10px] w-[100%] text-textSecondary italic outline-none",
            {
              "pl-[30px]": !!Icon,
            },
            className,
          )}
          {...{
            placeholder,
            type,
            value,
            onChange,
            disabled,
            ref,
            ...rest,
          }}
        />

        {!disableError && (
          <span className="mt-[3px] block h-[14px] text-[12px] text-error">
            {error}
          </span>
        )}
      </div>
    );
  },
);

AppTextarea.displayName = "AppTextarea";
