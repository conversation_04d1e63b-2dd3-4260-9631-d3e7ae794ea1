import React, { useState } from "react";

interface VideoPlayerProps {
  link: string;
  overlayText?: string;
  forcedHeight?: number;
  forcedWidth?: number;
}

export default function VideoPlayer({ link, overlayText, forcedHeight, forcedWidth }: VideoPlayerProps) {
  const [isOverlayVisible, setOverlayVisible] = useState(true);
  const handleOverlayClick = () => {
    setOverlayVisible(false);
  };

  return (
    //     <div className={`relative my-5 h-[${forcedHeight ? `${forcedHeight}px` : "450px"}] ${forcedWidth ? `w-[${forcedWidth}px]` : "w-full"} lg:h-[ overflow-hidden rounded-lg lg:w-[${forcedHeight ? `${forcedHeight}px` : "600px"}]`}>

    <div className="relative my-5 h-[450px] w-full overflow-hidden rounded-lg lg:h-[450px]">
      <iframe
        className="absolute inset-0 size-full"
        src={`${link}?modestbranding=1"${!isOverlayVisible ? "&autoplay=1" : ""}&rel=0`}
        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
        allowFullScreen
      ></iframe>
      {isOverlayVisible && (
        <div
          className="absolute inset-0 flex cursor-pointer items-center justify-center bg-black bg-opacity-50 bg-gradient-to-r from-[rgba(30,13,66,0.65)] to-[rgba(23,23,23,0.65)] "
          onClick={handleOverlayClick}
        >
          <div className="text-2xl font-bold text-white lg:text-4xl ">
            {overlayText}
          </div>
        </div>
      )}
    </div>
  );
}
