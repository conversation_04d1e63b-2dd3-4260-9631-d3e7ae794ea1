import { useFormContext } from "react-hook-form";

import { AppInput } from "../app-input";
import { PreInstallItem } from "./preinstall-item";

export const SubFormMedusa = () => {
  const { register } = useFormContext();

  return (
    <>
      <h3 className="mb-[20px] text-[28px] leading-[33px] text-textPrimary">
        Configure Custom Parameters for Medusa here:
      </h3>

      <div className="w-[230px]">
        <AppInput
          className="mb-[8px]"
          label="Medusa config filename"
          {...register("medusaConfig")}
          type="text"
        />

        <AppInput
          className="mb-[8px]"
          label="Test Time Limit"
          {...register("timeout")}
          type="text"
        />

        <AppInput
          className="mb-[8px]"
          label="Corpus Re-use Job ID"
          {...register("targetCorpus")}
          type="text"
        />

        <PreInstallItem />
      </div>
    </>
  );
};
