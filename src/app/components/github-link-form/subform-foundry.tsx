import { useEffect } from "react";
import { useFormContext } from "react-hook-form";

import { AppInput } from "../app-input";
import { AppSelectInput } from "../app-select-input";
import { PreInstallItem } from "./preinstall-item";

export const SubFormFoundry = () => {
  const { register, watch, setValue } = useFormContext();

  const forkMode = watch("forkMode");
  const testCommand = watch("testCommand");

  useEffect(() => {
  }, [forkMode, testCommand, setValue]);

  return (
    <>
      <h3 className="mb-[20px] text-[28px] leading-[33px] text-textPrimary">
        Configure Custom Parameters for Foundry here:
      </h3>
      <div className="w-[230px]">
        <AppInput
          className="mb-[8px]"
          label="Tester Contract Name"
          {...register("contract")}
          type="text"
        />

        <AppInput
          className="mb-[8px]"
          label="Runs"
          {...register("runs")}
          type="text"
        />

        <AppInput
          className="mb-[8px]"
          label="Seed"
          {...register("seed")}
          type="text"
        />

        <AppSelectInput
          className="mb-[8px]"
          label="Select test command"
          {...register("testCommand")}
          // TODO: standardize where we store and fetch these options
          options = {[
            { label: "None", value: "" },
            { label: "Match Test", value: "--match-test" },
            // Can choose to support more
          ]}
        />

        {testCommand && testCommand !== "" && (
          <AppInput
            className="mb-[8px]"
            label="Target Test"
            {...register("testTarget")}
            type="text"
          />
        )}

        <AppSelectInput
          className="mb-[8px]"
          label="Select verbosity"
          {...register("verbosity")}
          // TODO: standardize where we store and fetch these options
          options = {[
            { label: "-vv", value: "-vv" },
            { label: "-vvv", value: "-vvv" },
            { label: "-vvvv", value: "-vvvv" },
            { label: "-vvvvv", value: "-vvvvv" },
          ]}
        />

        <AppSelectInput
          className="mb-[8px]"
          label="Select Fork Mode"
          {...register("forkMode")}
          // TODO: standardize where we store and fetch these options
          options = {[
            { label: "Non-Forked", value: "NONE" },
            { label: "Mainnet", value: "MAINNET" },
            { label: "Optimism", value: "OPTIMISM" },
            { label: "Arbitrum", value: "ARBITRUM" },
            { label: "Polygon", value: "POLYGON" },
            { label: "Base", value: "BASE" }
          ]}
        />

        {forkMode && forkMode === "CUSTOM" && (
          <AppInput
            className="mb-[8px]"
            label="RPC URL"
            {...register("rpcUrl")}
            type="text"
            defaultValue=""
          />
        )}

        {forkMode && forkMode !== "NONE" && (
          <AppInput
            className="mb-[8px]"
            label="Fork Block"
            {...register("forkBlock")}
            type="text"
            defaultValue="LATEST"
          />
        )}

        <PreInstallItem />

      </div>
    </>
  );
};
