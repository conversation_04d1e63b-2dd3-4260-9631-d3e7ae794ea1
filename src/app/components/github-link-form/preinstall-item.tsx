import React from "react";
import { useFormContext } from "react-hook-form";

import { AppSelect } from "../app-select";

export const selectOptions = [
  {
    label: "No preprocess",
    value: "",
  },
  {
    label: "yarn install --ignore-scripts",
    value: "yarn install --ignore-scripts",
  },
];

export const PreInstallItem = () => {
  const { register } = useFormContext();

  return (
    <AppSelect
      className="mb-[8px]"
      options={selectOptions}
      label="Custom pre-install process"
      {...register("preprocess")}
    />
  );
};
