// NOTE: no longer used
// We'll need to port these over manually

// Hardcoded DATA for Convenience
// We will eventually add recipes on the Server Side
export const SMILEE_PROD_ID = "b34bd5ea-ff0d-46a6-a3b3-bd09b4dda758";

function makeSmileeConfig(label: string, preprocess: string): any {
  return {
    env: "echidna",
    label,
    directory: ".",
    preprocess,
    fuzzerArgs: {
      config: "test/invariants/system/conf.yaml",
      contract: "CryticTester",
      corpusDir: "echidna",
      testLimit: "300000",
      pathToTester: "test/invariants/system/CryticTester.sol",
    },
  };
}
// Weekly Slippage 1 and 2

export const SMILEE_CONFIGS = [
  makeSmileeConfig(
    "Smilee 0",
    "forge install && cp test/invariants/utils/scenarios/Parameters_0.sol test/invariants/utils/scenarios/Parameters.sol",
  ),
  makeSmileeConfig(
    "Smilee 1",
    "forge install && cp test/invariants/utils/scenarios/Parameters_1.sol test/invariants/utils/scenarios/Parameters.sol",
  ),
  makeSmileeConfig(
    "Smilee 2",
    "forge install && cp test/invariants/utils/scenarios/Parameters_2.sol test/invariants/utils/scenarios/Parameters.sol",
  ),
  makeSmileeConfig(
    "Smilee 3",
    "forge install && cp test/invariants/utils/scenarios/Parameters_3.sol test/invariants/utils/scenarios/Parameters.sol",
  ),
  makeSmileeConfig(
    "Smilee 4",
    "forge install && cp test/invariants/utils/scenarios/Parameters_4.sol test/invariants/utils/scenarios/Parameters.sol",
  ),
  makeSmileeConfig(
    "Smilee 5",
    "forge install && cp test/invariants/utils/scenarios/Parameters_5.sol test/invariants/utils/scenarios/Parameters.sol",
  ),
  makeSmileeConfig(
    "Smilee 6",
    "forge install && cp test/invariants/utils/scenarios/Parameters_6.sol test/invariants/utils/scenarios/Parameters.sol",
  ),

  // Weekly
  makeSmileeConfig(
    "Smilee Weekly 1",
    "forge install && cp test/invariants/utils/scenarios/Parameters_1_weekly.sol test/invariants/utils/scenarios/Parameters.sol",
  ),
  makeSmileeConfig(
    "Smilee Weekly 2",
    "forge install && cp test/invariants/utils/scenarios/Parameters_2_weekly.sol test/invariants/utils/scenarios/Parameters.sol",
  ),

  // Weekly Slippage
  makeSmileeConfig(
    "Smilee Weekly Slippage 1",
    "forge install && cp test/invariants/utils/scenarios/Parameters_1_weekly_slippage.sol test/invariants/utils/scenarios/Parameters.sol",
  ),
  makeSmileeConfig(
    "Smilee Weekly Slippage 2",
    "forge install && cp test/invariants/utils/scenarios/Parameters_2_weekly_slippage.sol test/invariants/utils/scenarios/Parameters.sol",
  ),
];

export const BADGER_PROD_ID = "340d69a2-76e5-4cd6-8af4-9a0b21d6ee59";

export const EBTC_CONFIGS = [
  {
    env: "medusa",
    label: "eBTC Medusa",
    directory: "packages/contracts",
    preprocess: "yarn install --ignore-scripts",
    fuzzerArgs: {
      timeout: "14400",
    },
  },
  {
    env: "echidna",
    label: "eBTC Echidna",
    directory: "packages/contracts",
    preprocess: "yarn install --ignore-scripts",
    fuzzerArgs: {
      config: "fuzzTests/echidna_config.yaml",
      contract: "EchidnaTester",
      corpusDir: "",
      testLimit: "300000",
      preprocess: "yarn install --ignore-scripts",
      pathToTester:
        "contracts/TestContracts/invariants/echidna/EchidnaTester.sol",
    },
  },
];

export const ALEX_PROD_ID = "545d63d6-d092-4da6-ab45-c53599442196";
export const ALEX_LOCAL_TEST = "170e7577-2d1f-4d92-b788-0bc1116c8e14";
