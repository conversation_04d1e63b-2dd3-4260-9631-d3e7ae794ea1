import { useEffect } from "react";
import { useFormContext } from "react-hook-form";

import { AppInput } from "../app-input";
import { AppSelectInput } from "../app-select-input";
import { PreInstallItem } from "./preinstall-item";

export const SubFormKontrol = () => {
  const { register, watch, setValue } = useFormContext();

  const forkMode = watch("forkMode");

  useEffect(() => {}, [forkMode, setValue]);

  return (
    <>
      <h3 className="mb-[20px] text-[28px] leading-[33px] text-textPrimary">
        Configure Custom Parameters for Kontrol here:
      </h3>
      <div className="w-[230px]">

        <AppInput
          className="mb-[8px]"
          label="Target Test"
          {...register("kontrolTest")}
          type="text"
        />

        <PreInstallItem />
      </div>
    </>
  );
};
