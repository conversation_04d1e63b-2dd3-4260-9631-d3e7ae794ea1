import React, { useState } from "react";
import VideoPlayer from "../VideoPlayer/VideoPlayer";

// TODO 0XSI
// Placeholder for now
const videos = [
  {
    title: "Run a job in the cloud",
    url: "https://www.youtube.com/embed/s4ci9zgIHiI",
  },
  {
    title: "Create and re-use recipes",
    url: "https://www.youtube.com/embed/bXT8Ye2EaGs",
  },
  {
    title: "Run jobs on PR and commit",
    url: "https://www.youtube.com/embed/Fnz4P5kxAD0",
  },
];

export default function VideoCaroussel() {
  const [currentVideo, setCurrentVideo] = useState(0);
  return (
    <div className="grid w-full grid-cols-1 gap-4 p-[30px] lg:grid-cols-[1fr_2fr]">
      <div className="order-2 flex flex-col justify-center space-y-4 lg:order-1">
        {videos.map((video, index) => {
          const isSelected = index === currentVideo;
          return (
            <button
              key={index}
              onClick={() => setCurrentVideo(index)}
              className={`rounded-lg px-4 py-2 text-left text-lg font-semibold uppercase transition-colors duration-200 ${
                isSelected ? "text-white" : "text-gray-400"
              }`}
            >
              {isSelected ? "—— " : ""}
              {video.title}
            </button>
          );
        })}
      </div>

      <div className="order-1 flex flex-col items-center justify-center text-left lg:order-2">
        <h3 className="sub-title-custom font-bold uppercase tracking-normal text-white lg:text-[40px]">
          Recon PRO Workflows
        </h3>
        <VideoPlayer link={videos[currentVideo].url} />
      </div>
    </div>
  );
}
