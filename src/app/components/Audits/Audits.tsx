import Audit from "./Audit/Audit";
import <PERSON><PERSON><PERSON> from "../Caroussel/Caroussel";

const AuditsList = [
  {
    project: "Liquity",
    githubURL: "https://github.com/Recon-Fuzz/audits/blob/main/bold-report.md",
    privateReport: false,
  },
  {
    project: "Beraborrow",
    githubURL:
      "https://1570492309-files.gitbook.io/~/files/v0/b/gitbook-x-prod.appspot.com/o/spaces%2FffzDCMBDa391vIMqruBP%2Fuploads%2FDLz8dzCO2O7SWGHBDJkj%2FRecon%20Beraborrow.pdf?alt=media&token=6b328d0d-d65f-4f27-913b-d4a867889af7",
    privateReport: false,
  },
  {
    project: "All reports",
    githubURL: "https://github.com/Recon-Fuzz/audits",
    privateReport: false,
  },
  {
    project: "Quill Finance",
    githubURL:
      "https://github.com/Recon-Fuzz/audits/blob/main/Quill_Finance_Report.md",
    privateReport: false,
  },
  {
    project: "Balancer DAO",
    githubURL:
      "https://github.com/Recon-Fuzz/audits/blob/main/Balancer_Report.md",
    privateReport: false,
  },
  {
    project: "Kleidi",
    githubURL:
      "https://github.com/Recon-Fuzz/audits/blob/main/Kleidi_Report.md",
    privateReport: false,
  },
  {
    project: "Apollon",
    githubURL:
      "https://github.com/Recon-Fuzz/audits/blob/main/Apollon_Report.md",
    privateReport: false,
  },
  {
    project: "Credit Coop",
    githubURL: "https://github.com/Recon-Fuzz/audits/blob/main/README.md",
    privateReport: true,
  },

];

export default function Audits() {
  return (
    <Caroussel data={AuditsList}>
      {AuditsList.map((audit, index) => (
        <Audit
          key={index}
          project={audit.project}
          githubURL={audit.githubURL}
          privateReport={audit.privateReport}
        />
      ))}
    </Caroussel>
  );
}
