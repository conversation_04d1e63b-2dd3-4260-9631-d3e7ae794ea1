import React from "react";
import { FaGithub } from "react-icons/fa";
interface TrophyProps {
  project: string;
  githubURL: string;
  privateReport: boolean;
}

export default function Trophy({
  project,
  githubURL,
  privateReport,
}: TrophyProps) {
  return (
    <div className="mr-7 min-h-[272px] w-[300px] flex-none snap-center rounded-lg bg-[#6750A41F] from-purple-800 to-gray-900 p-4 text-white shadow-lg md:w-[370px] lg:w-[370px]">
      <div className="flex h-full flex-col justify-between">
        <div className="grid grid-cols-[3fr_1fr] items-start gap-4">
          <div>
            <p className="text-left text-2xl font-bold">{project}</p>
          </div>
          <div className="flex items-start justify-center">
            <FaGithub className="text-[34px] text-[#D8C7FF]" />
          </div>
        </div>

        <div className=" mt-4 flex flex-col justify-center ">
          <p className="text-xl">Audit</p>
          {privateReport === true ? (
            <p className="text-xl">Private Report</p>
          ) : (
            <a
              href={githubURL}
              target="_blank"
              rel="noreferrer"
              className="block text-lg font-semibold text-[#D0BCFF] underline"
            >
              Report link &gt;
            </a>
          )}
        </div>
      </div>
    </div>
  );
}
