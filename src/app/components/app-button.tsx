"use client";

import noop from "lodash/noop";
import type { ReactNode } from "react";

import { cn } from "../helpers/cn";
// import { useTheme } from "next-themes";

type AppButtonProps = {
  variant?: "default" | "primary";
  className?: string;
  children: ReactNode;
  disabled?: boolean;
  onClick?: (e?: any) => void;
  type?: "button" | "submit" | "reset";
};

export const AppButton = ({
  children,
  className = "",
  variant = "primary",
  disabled = false,
  onClick = noop,
  type = "button",
}: AppButtonProps) => {
  // const { theme } = useTheme();
  // const isDark = theme === "dark";

  return (
    <button
      {...{
        disabled,
        onClick,
        type,
      }}
      className={cn(
        "flex gap-4 items-center leading-[17px] text-[19px] text-center font-bold  py-[13px] px-[47px] rounded-[10px] ",
        {
          "text-white bg-primary": variant === "primary",
          "text-black bg-white": variant === "default",
        },
        className,
      )}
    >
      {children}
    </button>
  );
};
