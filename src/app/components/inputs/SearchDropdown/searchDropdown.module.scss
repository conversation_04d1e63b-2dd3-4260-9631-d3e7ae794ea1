.select {
  display: flex;
  flex-direction: column;
  border: solid 1px var(--primary-color);
  border-radius: 4px;
  width: 274px;
  max-height: 190px;
  overflow: hidden;

  ul {
    z-index: 2;
    list-style: none;
    margin: 0px;
    padding: 10px 0px;
    overflow-y: auto;
    overflow-x: hidden;

    &::-webkit-scrollbar {
      width: 8px;
    }

    &::-webkit-scrollbar-track {
      background: var(--accent-color);
    }

    &::-webkit-scrollbar-thumb {
      background: var(--primary-color);
      border-radius: 4px;
    }
    background-color: white;
  }

  li {
    font-size: 15px;
    font-weight: 500;
    line-height: 18px;
    letter-spacing: 0em;
    text-align: left;
    padding: 5px 7px;

    cursor: pointer;

    &:hover {
      background-color: var(--accent-color);
    }
  }
}

.selected {
  // background-color: transparent;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 5px 7px;
  cursor: pointer;
  width: 100%;
  border: none;
  color: var(--dark-text-color);
  font-size: 15px;
  font-weight: 500;
  line-height: 18px;
  letter-spacing: 0em;
  text-align: left;
  border-bottom: solid 1px var(--primary-color);
}

.selectedClose {
  border: none;
}

.searchInput {
  border-bottom: solid 1px #c0c4d3;
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 5px 7px;
  color: #c0c4d3;
  background-color: white;
  input {
    border: none;

    font-size: 15px;
    font-weight: 500;
    line-height: 18px;
    letter-spacing: 0em;
    text-align: left;
    width: 100%;

    &:focus {
      outline: none;
    }
  }
}
.close {
  display: none;
}
