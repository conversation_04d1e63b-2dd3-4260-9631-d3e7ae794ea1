import {
  faCaretDown,
  faCaretUp,
  faSearch,
} from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import type { FC } from "react";
import { useEffect, useMemo, useRef, useState } from "react";

import styles from "./searchDropdown.module.scss";

interface Item {
  label: string;
  value: string | number;
}

interface Props {
  items: Item[];
  onChange: (value: string | number) => void;
  searchPlaceholder: string;
}

//TODO - add keyboard support

export const SearchDropdown: FC<Props> = ({
  items,
  onChange,
  searchPlaceholder,
}) => {
  const ref = useRef<HTMLDivElement>(null);
  const [selectedItem, setSelectedItem] = useState(null);
  const [isOpen, setIsOpen] = useState(false);
  const [search, setSearch] = useState("");

  useEffect(() => {
    if (!isOpen) return;
    const onClickOutside = (e: MouseEvent) => {
      if (ref.current && !ref.current.contains(e.target as Node)) {
        setIsOpen(false);
      }
    };
    document.addEventListener("click", onClickOutside, true);

    return () => {
      document.removeEventListener("click", onClickOutside, true);
    };
  }, [isOpen]);

  const toggleOpen = () => {
    setIsOpen((prev) => !prev);
    if (isOpen) setSearch("");
  };

  const selectItem = (item: Item) => {
    setSelectedItem(item);
    onChange(item.value);
    setIsOpen(false);
  };

  const filteredItems = useMemo(() => {
    if (!search) return items;
    return items.filter((item) => new RegExp(search, "i").test(item.label));
  }, [search, items]);

  return (
    <div ref={ref} role="select" className={styles.select}>
      <button
        className={`${styles.selected} ${
          !isOpen ? styles.selectedClose : ""
        } min-h-[50px] bg-slate-100`}
        onClick={toggleOpen}
      >
        {selectedItem?.label ? selectedItem.label : searchPlaceholder}
        <FontAwesomeIcon icon={isOpen ? faCaretDown : faCaretUp} />
      </button>
      <label
        htmlFor="search"
        className={`${styles.searchInput} ${!isOpen ? styles.close : ""}`}
      >
        <FontAwesomeIcon icon={faSearch} />
        <input
          onChange={(e) => setSearch(e.target.value)}
          value={search}
          name="search"
          type="text"
          placeholder={searchPlaceholder}
        />
      </label>
      <ul className={`${!isOpen ? styles.close : ""} overflow-auto`}>
        {filteredItems.map((item) => (
          <li
            onClick={() => selectItem(item)}
            key={item.value}
            role="option"
            aria-selected
          >
            {item.label}
          </li>
        ))}
      </ul>
    </div>
  );
};
