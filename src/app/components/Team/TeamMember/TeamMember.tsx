import React from "react";
import Image from "next/image";
import { FaGithub } from "react-icons/fa";
import { FaXTwitter } from "react-icons/fa6";
import Link from "next/link";

interface Props {
  title: string;
  name: string;
  twitter: string;
  github: string;
  image: string;
  description: string;
}

export default function TeamMember({
  image,
  name,
  twitter,
  github,
  title,
  description,
}: Props) {
  return (
    <div className="mr-7 flex w-[300px] flex-col overflow-hidden rounded-lg border border-[#D0BCFF] bg-[#6750A41F] text-white shadow-lg md:w-[370px] lg:w-[370px]">
      <div className="flex items-center justify-center">
        <Image
          src={image}
          alt={name}
          height={160}
          width={300}
          className="w-full object-cover"
        />
      </div>

      <div className="flex grow flex-col justify-center p-4">
        <div className="grow">
          <p className="text-xl font-bold uppercase">{name}</p>
          {/* <p className="font-thin">{title}</p> */}
          <p className="font-thin">{description}</p>

        </div>
        <div className="mt-2 flex space-x-4">
          <div className="mb-[20px] mt-2 flex space-x-4">
            {twitter && (
              <Link href={twitter} className="mr-4 size-8">
                <div className="flex size-[50px] items-center justify-center rounded-full bg-[#D0BCFF]">
                  <FaXTwitter className="text-[30px] text-white" />
                </div>
              </Link>
            )}
            {github && (
              <Link href={github} className="size-8">
                <div className="flex size-[50px] items-center justify-center rounded-full bg-[#D0BCFF]">
                  <FaGithub className="text-[30px] text-white" />
                </div>
              </Link>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
