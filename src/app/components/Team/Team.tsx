import React from 'react'
import <PERSON><PERSON><PERSON> from "../Caroussel/Caroussel";
import TeamMember from './TeamMember/TeamMember';

const TeamMembers = [
  {
    name: "<PERSON>",
    title: "Security researcher",
    twitter: "https://x.com/GalloDaSballo",
    github: "https://github.com/GalloDaSballo",
    image: "/team/Alex.jpeg",
    description: "Top C4 Judge, Former Badger Lead Dev, Code that handled hundreds of millions of dollars, Bug findings in most protocols you use every day",
  },
  {
    name: "<PERSON>",
    title: "Security researcher",
    twitter: "https://x.com/agfviggiano",
    github: "https://github.com/aviggiano",
    image: "/team/Aviggiano.jpeg",
    description: "Creator of EchidnaToFoundry, Author of some of the most read articles on Invariant Testing, Advisor to multiple high profile projects",
  },
  // {
  //   name: "Al<PERSON><PERSON>",
  //   title: "Security Advisor",
  //   twitter: "https://x.com/alcueca",
  //   github: "https://github.com/alcueca",
  //   image: "/team/Alcueca.jpeg",
  //   description: "Co-Founder and CTO at Yield Protocol, co-Author ERC4626 (Tokenized Vaults), ERC3156 (Flash Loans) and ERC7266 (Oracles), Judge @ C4 and Cantina. Author of many popular articles on smart contract development.",
  // },
  {
    name: "Nican0r",
    title: "Invariants Engineer",
    twitter: "https://x.com/nican0r",
    github: "https://github.com/nican0r",
    image: "/team/Nicanor.jpeg",
    description: "Up and coming talented researcher behind most of our articles and invariant testing starters",
  },
  {
    name: "0xsi",
    title: "Software engineer && Invariants Engineer",
    twitter: "https://x.com/_0xsi",
    github: "https://github.com/Simon-Busch",
    image: "/team/0xsi.jpg",
    description: "Lead Software Engineer && Fuzzing engineer behind a lot of Recon features",
  },
  // {
  //   name: "Lourens",
  //   title: "Fuzzing expert",
  //   twitter: "https://x.com/LourensLinde",
  //   github: "https://github.com/lokithe5th",
  //   image: "/team/Lourens.png",
  //   description: "Multiple bugs in Security Contests, Behind many Invariant Testing suites",
  // },
  {
    name: "Kn0t",
    title: "Invariants Engineer",
    twitter: "https://x.com/0xknot",
    github: "https://github.com/0xknot",
    image: "/team/kn0t.jpeg",
    description: "Senior Full-Stack Developer, Security Researcher & Fuzzing Engineer, driving R&D and future tools at Recon",
  },
]

export default function Team() {
  return (
    <Caroussel data={TeamMembers}>
      {
        TeamMembers.map((member, index) => {
          return (
            <TeamMember
              key={index}
              name={member.name}
              title={member.title}
              twitter={member.twitter}
              github={member.github}
              image={member.image}
              description={member.description}
            />
          )
        })
      }
    </Caroussel>
  )
}
