import { ABIActionsRow } from "@/app/dashboard/handlers/[identifier]/abi-actions-row";
import { AB<PERSON>rovider } from "@/app/dashboard/handlers/[identifier]/abi-context";
import { BeforeAfterContracts } from "@/app/dashboard/handlers/[identifier]/before-after-contracts";
import { ResultsPage } from "@/app/dashboard/handlers/[identifier]/results-page";
import { SelectContracts } from "@/app/dashboard/handlers/[identifier]/select-contracts";
import React, { useState } from "react";
import { AppButton } from "../app-button";
import { AppTabs, AppTabPane } from "../app-tabs";
import styles from "./sandboxBuilder.module.scss";

export default function SandboxBuilder() {
  const [abiInputs, setAbiInputs] = useState([
    { content: "", name: "Your_contract" },
  ]);
  const [preparedAbis, setPreparedAbis] = useState(null);
  const [errors, setErrors] = useState([]);

  const addNewAbi = () => {
    setAbiInputs([...abiInputs, { content: "", name: "Your_contract" }]);
  };

  const handleAbiChange = (index: number, value: string) => {
    const newInputs = [...abiInputs];
    newInputs[index].content = value;
    setAbiInputs(newInputs);
  };

  const handleNameChange = (index: number, value: string) => {
    const newInputs = [...abiInputs];
    // Capitalize value
    value = value.charAt(0).toUpperCase() + value.slice(1);
    newInputs[index].name = value;
    setAbiInputs(newInputs);
    const parsed = newInputs.map((input, index) => {
      const obj = JSON.parse(input.content);
      return {
        name: input.name,
        abi: obj?.abi ? obj.abi : obj,
        abiPath: `src${index}`,
      };
    });

    setPreparedAbis(parsed);
  };

  const parseAbis = () => {
    try {
      const parsed = abiInputs.map((input, index) => {
        const obj = JSON.parse(input.content);
        return {
          name: input.name,
          abi: obj?.abi ? obj.abi : obj,
          abiPath: `src${index}`,
        };
      });
      console.log(parsed);

      setPreparedAbis(parsed);
      setErrors([]);
    } catch (e) {
      setErrors(["Invalid JSON in one or more ABIs"]);
    }
  };
  return (
    <div>
      {abiInputs.map((input, index) => (
        <div key={index} className={styles.inputContainer}>
          <textarea
            value={input.content}
            onChange={(e) => handleAbiChange(index, e.target.value)}
            placeholder={`Paste ABI ${index + 1} here`}
          />
          <input
            type="text"
            value={input.name}
            onChange={(e) => handleNameChange(index, e.target.value)}
            placeholder="Contract name"
            className={styles.contractNameInput}
          />
        </div>
      ))}

      <div className="flex justify-center gap-4 items-center">
        <AppButton onClick={addNewAbi}>Add Another ABI</AppButton>
        <AppButton onClick={parseAbis}>Parse ABIs</AppButton>
      </div>

      {errors.length > 0 && (
        <div className={styles.errorContainer}>
          <p>{errors.join(", ")}</p>
        </div>
      )}

      {preparedAbis ? (
        <ABIProvider identifier={preparedAbis} forced={true}>
          <div className="pl-[45px] pr-[80px] pt-[45px]">
            <h1 className="mb-[20px] text-[28px] leading-[33px] text-textPrimary">
              Build your Handlers
            </h1>
            <div className="flex gap-[40px]">
              <AppTabs
                defaultActiveKey="select-contacts"
                className="min-w-[400px]"
              >
                <AppTabPane tab="select-contacts" label="Contracts">
                  <SelectContracts />
                </AppTabPane>
                <AppTabPane
                  tab="before-after-contacts"
                  label="Before and after trackers"
                >
                  <BeforeAfterContracts />
                </AppTabPane>
              </AppTabs>
              <div className="min-w-[500px] grow">
                <ABIActionsRow />
                <ResultsPage />
              </div>
            </div>
          </div>
        </ABIProvider>
      ) : (
        ""
      )}
    </div>
  );
}
