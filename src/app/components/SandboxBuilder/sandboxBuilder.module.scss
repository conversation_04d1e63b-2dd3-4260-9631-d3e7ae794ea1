.inputContainer {
  display: flex;
  flex-direction: column;
  margin-bottom: 1rem;
  align-items: center;
}

.inputContainer textarea {
  width: 400px;
  height: 80px;
  white-space: pre-wrap;
  margin-bottom: 16px;
  color: white;
  background-color: black;
  border: 1px solid #ccc;
  border-radius: 4px;
  padding: 4px;
  box-shadow: 4px 4px 8px rgba(103, 103, 103, 0.2);
}

.inputContainer textarea:focus {
  outline: none;
}

.errorContainer {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.errorContainer p {
  color: red;
  font-size: 1.5rem;
  margin-bottom: 1rem;
}
.contractNameInputContainer {
  width: 50%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.contractNameInput {
  width: 200px;
  border-radius: 8px;
  border: 1px solid white;
  margin-bottom: 8px;
  padding: 8px;
}
