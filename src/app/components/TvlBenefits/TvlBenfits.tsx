import React from "react";
import Benefit from "./Benefit/Benefit";

const BenefitsArray = [
  {
    title: "$1BLN+",
    description:
      "Aggregate TVL of our Customers",
    upcoming: false,
    experimental: false,
  },
  {
    title: "$200MLN+",
    description:
      "TVL Protected with Live Monitoring",
    upcoming: false,
    experimental: false,
  },
  {
    title: "10k+",
    description:
      "Cloud Fuzzing Runs done with Recon Pro",
    upcoming: false,
    experimental: false,
  }
];

export default function TvlBenefits() {
  return (
    <div className="flex items-center justify-center px-4 py-16">
      <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
        {BenefitsArray.map((benefit, index) => {
          return (
            <Benefit
              key={index}
              title={benefit.title}
              description={benefit.description}
              upcoming={benefit.upcoming}
              experimental={benefit.experimental}
            />
          );
        })}
      </div>
    </div>
  );
}
