"use client";

import noop from "lodash/noop";
import { useCallback } from "react";

import { cn } from "../helpers/cn";

type AppSwitchProps = {
  onChange?: (enabled: boolean) => void;
  className?: string;
  enabled?: boolean;
};

export const AppSwitch = ({
  enabled = false,
  onChange = noop,
  className = "",
}: AppSwitchProps) => {
  const onSwitchChange = useCallback(() => {
    onChange(!enabled);
  }, [enabled, onChange]);

  return (
    <label className={cn("flex items-center cursor-pointer", className)}>
      <div className="relative">
        <input
          type="checkbox"
          className="sr-only"
          checked={enabled}
          onChange={onSwitchChange}
        />
        <div
          className={`block h-[20px] w-[36px] rounded-full bg-gray-600 shadow-inner ${
            enabled ? "bg-primary" : "bg-[#534F5C]"
          }`}
        />
        <div
          className={cn(
            `dot absolute left-1 top-[3px] size-[14px] rounded-full bg-white transition-transform shadow`,
            {
              "translate-x-full border-primary": enabled,
              "translate-x-0 border-[#534F5C]": !enabled,
            },
          )}
          style={{ transition: "transform 0.2s" }}
        />
      </div>
    </label>
  );
};
