import type { ABI } from "@recon-fuzz/abi-to-invariants-ts/src/types";

export interface abiEntry {
  name: string;
  abi: ABI[];
  abiPath: string;
}

export interface AbiApiData {
  identifier: string;
  abiData: string; // TODO  MAKE IT COMPATIBLE? | PROB JSON abiEntry[]; // TODO: Security discussionss
  updatedAt: string;
  id: string;
  identifier: string;
}

export interface ParsedAbiApiData {
  identifier: string;
  abiData: abiEntry[];
  updatedAt: string;
}
