import type { Metadata } from "next";
import BytecodeToInterfaceInternal from "./internal";

export const metadata: Metadata = {
  title: "Bytecode To Interface Reverse Engineering Tool",
  description: "Paste bytecode or provide an address, this tool will reverse engineer an interface for you to interact with it",
};

export default function BytecodeToInterfacePage() {
  return <BytecodeToInterfaceInternal />
}
