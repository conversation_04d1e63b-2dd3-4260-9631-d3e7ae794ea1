"use client";
import Link from "next/link";
import { useState } from "react";

import { ENV_TYPE } from "@/app/app.constants";
import { AppInput } from "@/app/components/app-input";
import { AppLogo } from "@/app/components/app-logo";
import { AppPageTitle } from "@/app/components/app-page-title";
import { AppTextarea } from "@/app/components/app-textarea";
import LogComponent from "@/app/components/LogComponent/LogComponent";
import Footer from "@/app/components/Footer/Footer";


export default function MedusaParserInternal() {
  const [logs, setLogs] = useState("");
  const [prefix, setPrefix] = useState("");


  return (
    <div className="bg-dashboardBG min-h-screen">
      <div className="gradient-dark-bg bg-blockBg flex items-center justify-between px-[40px] py-[20px]">
        <Link href="/dashboard" className="cursor-pointer">
          <AppLogo />
        </Link>
      </div>

      <div className="p-[45px]">
        <div className="mb-[20px]">
          <AppPageTitle className="mb-[3px]">Medusa Logs Scraper</AppPageTitle>

          <p className="text-textSecondary mb-[3px] block text-[15px] leading-[18px]">
            This tool allows to scrape medusa logs for broken properties repros
          </p>
          <p className="text-textSecondary mb-[3px] block text-[15px] leading-[18px]">
            Paste your raw medusa logs, and the tool will generate foundry repros for you
          </p>
        </div>

        <AppInput
          className="mb-[8px]"
          label="Add a Prefix for your convenience"
          value={prefix}
          onChange={(e) => setPrefix(e.target.value)}
          type="text"
        />

        <AppTextarea
          className="mb-[8px]"
          label="Paste Medusa Logs Here"
          value={logs}
          onChange={(e) => setLogs(e.target.value)}
          type="text"
        />

        <LogComponent fuzzer={ENV_TYPE.MEDUSA} logs={logs} prefix={prefix} />
      </div>

      <Footer />
    </div>
  );
}
