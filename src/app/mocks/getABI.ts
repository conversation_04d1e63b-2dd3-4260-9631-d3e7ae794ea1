export const getABIMock = [
  {
    id: "6fa1a2a3-90d0-4ab0-8509-6469857b17e7",
    identifier: "GalloDaSballo_recon-demo-3_main",
    commit: "3c64ecc1ab15b3c129bfbff90c53c7b16465441e",
    abiData:
      '[{"name":"IMulticall3","abi":[{"type":"function","name":"aggregate","inputs":[{"name":"calls","type":"tuple[]","internalType":"struct IMulticall3.Call[]","components":[{"name":"target","type":"address","internalType":"address"},{"name":"callData","type":"bytes","internalType":"bytes"}]}],"outputs":[{"name":"blockNumber","type":"uint256","internalType":"uint256"},{"name":"returnData","type":"bytes[]","internalType":"bytes[]"}],"stateMutability":"payable"},{"type":"function","name":"aggregate3","inputs":[{"name":"calls","type":"tuple[]","internalType":"struct IMulticall3.Call3[]","components":[{"name":"target","type":"address","internalType":"address"},{"name":"allowFailure","type":"bool","internalType":"bool"},{"name":"callData","type":"bytes","internalType":"bytes"}]}],"outputs":[{"name":"returnData","type":"tuple[]","internalType":"struct IMulticall3.Result[]","components":[{"name":"success","type":"bool","internalType":"bool"},{"name":"returnData","type":"bytes","internalType":"bytes"}]}],"stateMutability":"payable"},{"type":"function","name":"aggregate3Value","inputs":[{"name":"calls","type":"tuple[]","internalType":"struct IMulticall3.Call3Value[]","components":[{"name":"target","type":"address","internalType":"address"},{"name":"allowFailure","type":"bool","internalType":"bool"},{"name":"value","type":"uint256","internalType":"uint256"},{"name":"callData","type":"bytes","internalType":"bytes"}]}],"outputs":[{"name":"returnData","type":"tuple[]","internalType":"struct IMulticall3.Result[]","components":[{"name":"success","type":"bool","internalType":"bool"},{"name":"returnData","type":"bytes","internalType":"bytes"}]}],"stateMutability":"payable"},{"type":"function","name":"blockAndAggregate","inputs":[{"name":"calls","type":"tuple[]","internalType":"struct IMulticall3.Call[]","components":[{"name":"target","type":"address","internalType":"address"},{"name":"callData","type":"bytes","internalType":"bytes"}]}],"outputs":[{"name":"blockNumber","type":"uint256","internalType":"uint256"},{"name":"blockHash","type":"bytes32","internalType":"bytes32"},{"name":"returnData","type":"tuple[]","internalType":"struct IMulticall3.Result[]","components":[{"name":"success","type":"bool","internalType":"bool"},{"name":"returnData","type":"bytes","internalType":"bytes"}]}],"stateMutability":"payable"},{"type":"function","name":"getBasefee","inputs":[],"outputs":[{"name":"basefee","type":"uint256","internalType":"uint256"}],"stateMutability":"view"},{"type":"function","name":"getBlockHash","inputs":[{"name":"blockNumber","type":"uint256","internalType":"uint256"}],"outputs":[{"name":"blockHash","type":"bytes32","internalType":"bytes32"}],"stateMutability":"view"},{"type":"function","name":"getBlockNumber","inputs":[],"outputs":[{"name":"blockNumber","type":"uint256","internalType":"uint256"}],"stateMutability":"view"},{"type":"function","name":"getChainId","inputs":[],"outputs":[{"name":"chainid","type":"uint256","internalType":"uint256"}],"stateMutability":"view"},{"type":"function","name":"getCurrentBlockCoinbase","inputs":[],"outputs":[{"name":"coinbase","type":"address","internalType":"address"}],"stateMutability":"view"},{"type":"function","name":"getCurrentBlockDifficulty","inputs":[],"outputs":[{"name":"difficulty","type":"uint256","internalType":"uint256"}],"stateMutability":"view"},{"type":"function","name":"getCurrentBlockGasLimit","inputs":[],"outputs":[{"name":"gaslimit","type":"uint256","internalType":"uint256"}],"stateMutability":"view"},{"type":"function","name":"getCurrentBlockTimestamp","inputs":[],"outputs":[{"name":"timestamp","type":"uint256","internalType":"uint256"}],"stateMutability":"view"},{"type":"function","name":"getEthBalance","inputs":[{"name":"addr","type":"address","internalType":"address"}],"outputs":[{"name":"balance","type":"uint256","internalType":"uint256"}],"stateMutability":"view"},{"type":"function","name":"getLastBlockHash","inputs":[],"outputs":[{"name":"blockHash","type":"bytes32","internalType":"bytes32"}],"stateMutability":"view"},{"type":"function","name":"tryAggregate","inputs":[{"name":"requireSuccess","type":"bool","internalType":"bool"},{"name":"calls","type":"tuple[]","internalType":"struct IMulticall3.Call[]","components":[{"name":"target","type":"address","internalType":"address"},{"name":"callData","type":"bytes","internalType":"bytes"}]}],"outputs":[{"name":"returnData","type":"tuple[]","internalType":"struct IMulticall3.Result[]","components":[{"name":"success","type":"bool","internalType":"bool"},{"name":"returnData","type":"bytes","internalType":"bytes"}]}],"stateMutability":"payable"},{"type":"function","name":"tryBlockAndAggregate","inputs":[{"name":"requireSuccess","type":"bool","internalType":"bool"},{"name":"calls","type":"tuple[]","internalType":"struct IMulticall3.Call[]","components":[{"name":"target","type":"address","internalType":"address"},{"name":"callData","type":"bytes","internalType":"bytes"}]}],"outputs":[{"name":"blockNumber","type":"uint256","internalType":"uint256"},{"name":"blockHash","type":"bytes32","internalType":"bytes32"},{"name":"returnData","type":"tuple[]","internalType":"struct IMulticall3.Result[]","components":[{"name":"success","type":"bool","internalType":"bool"},{"name":"returnData","type":"bytes","internalType":"bytes"}]}],"stateMutability":"payable"}],"abiPath":"lib/forge-std/src/interfaces/IMulticall3.sol"},{"name":"TargetFunctions","abi":[{"type":"function","name":"counter_increment","inputs":[],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"counter_setNumber","inputs":[{"name":"newNumber","type":"uint64","internalType":"uint64"}],"outputs":[],"stateMutability":"nonpayable"}],"abiPath":"test/recon/TargetFunctions.sol"},{"name":"MockERC20","abi":[{"type":"function","name":"DOMAIN_SEPARATOR","inputs":[],"outputs":[{"name":"","type":"bytes32","internalType":"bytes32"}],"stateMutability":"view"},{"type":"function","name":"allowance","inputs":[{"name":"","type":"address","internalType":"address"},{"name":"","type":"address","internalType":"address"}],"outputs":[{"name":"","type":"uint256","internalType":"uint256"}],"stateMutability":"view"},{"type":"function","name":"approve","inputs":[{"name":"spender","type":"address","internalType":"address"},{"name":"amount","type":"uint256","internalType":"uint256"}],"outputs":[{"name":"","type":"bool","internalType":"bool"}],"stateMutability":"nonpayable"},{"type":"function","name":"balanceOf","inputs":[{"name":"","type":"address","internalType":"address"}],"outputs":[{"name":"","type":"uint256","internalType":"uint256"}],"stateMutability":"view"},{"type":"function","name":"decimals","inputs":[],"outputs":[{"name":"","type":"uint8","internalType":"uint8"}],"stateMutability":"view"},{"type":"function","name":"initialize","inputs":[{"name":"_name","type":"string","internalType":"string"},{"name":"_symbol","type":"string","internalType":"string"},{"name":"_decimals","type":"uint8","internalType":"uint8"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"name","inputs":[],"outputs":[{"name":"","type":"string","internalType":"string"}],"stateMutability":"view"},{"type":"function","name":"nonces","inputs":[{"name":"","type":"address","internalType":"address"}],"outputs":[{"name":"","type":"uint256","internalType":"uint256"}],"stateMutability":"view"},{"type":"function","name":"permit","inputs":[{"name":"owner","type":"address","internalType":"address"},{"name":"spender","type":"address","internalType":"address"},{"name":"value","type":"uint256","internalType":"uint256"},{"name":"deadline","type":"uint256","internalType":"uint256"},{"name":"v","type":"uint8","internalType":"uint8"},{"name":"r","type":"bytes32","internalType":"bytes32"},{"name":"s","type":"bytes32","internalType":"bytes32"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"symbol","inputs":[],"outputs":[{"name":"","type":"string","internalType":"string"}],"stateMutability":"view"},{"type":"function","name":"totalSupply","inputs":[],"outputs":[{"name":"","type":"uint256","internalType":"uint256"}],"stateMutability":"view"},{"type":"function","name":"transfer","inputs":[{"name":"to","type":"address","internalType":"address"},{"name":"amount","type":"uint256","internalType":"uint256"}],"outputs":[{"name":"","type":"bool","internalType":"bool"}],"stateMutability":"nonpayable"},{"type":"function","name":"transferFrom","inputs":[{"name":"from","type":"address","internalType":"address"},{"name":"to","type":"address","internalType":"address"},{"name":"amount","type":"uint256","internalType":"uint256"}],"outputs":[{"name":"","type":"bool","internalType":"bool"}],"stateMutability":"nonpayable"},{"type":"event","name":"Approval","inputs":[{"name":"owner","type":"address","indexed":true,"internalType":"address"},{"name":"spender","type":"address","indexed":true,"internalType":"address"},{"name":"amount","type":"uint256","indexed":false,"internalType":"uint256"}],"anonymous":false},{"type":"event","name":"Transfer","inputs":[{"name":"from","type":"address","indexed":true,"internalType":"address"},{"name":"to","type":"address","indexed":true,"internalType":"address"},{"name":"amount","type":"uint256","indexed":false,"internalType":"uint256"}],"anonymous":false}],"abiPath":"lib/forge-std/src/mocks/MockERC20.sol"},{"name":"FoundryAsserts","abi":[{"type":"function","name":"IS_TEST","inputs":[],"outputs":[{"name":"","type":"bool","internalType":"bool"}],"stateMutability":"view"},{"type":"function","name":"excludeArtifacts","inputs":[],"outputs":[{"name":"excludedArtifacts_","type":"string[]","internalType":"string[]"}],"stateMutability":"view"},{"type":"function","name":"excludeContracts","inputs":[],"outputs":[{"name":"excludedContracts_","type":"address[]","internalType":"address[]"}],"stateMutability":"view"},{"type":"function","name":"excludeSenders","inputs":[],"outputs":[{"name":"excludedSenders_","type":"address[]","internalType":"address[]"}],"stateMutability":"view"},{"type":"function","name":"failed","inputs":[],"outputs":[{"name":"","type":"bool","internalType":"bool"}],"stateMutability":"nonpayable"},{"type":"function","name":"targetArtifactSelectors","inputs":[],"outputs":[{"name":"targetedArtifactSelectors_","type":"tuple[]","internalType":"struct StdInvariant.FuzzSelector[]","components":[{"name":"addr","type":"address","internalType":"address"},{"name":"selectors","type":"bytes4[]","internalType":"bytes4[]"}]}],"stateMutability":"view"},{"type":"function","name":"targetArtifacts","inputs":[],"outputs":[{"name":"targetedArtifacts_","type":"string[]","internalType":"string[]"}],"stateMutability":"view"},{"type":"function","name":"targetContracts","inputs":[],"outputs":[{"name":"targetedContracts_","type":"address[]","internalType":"address[]"}],"stateMutability":"view"},{"type":"function","name":"targetInterfaces","inputs":[],"outputs":[{"name":"targetedInterfaces_","type":"tuple[]","internalType":"struct StdInvariant.FuzzInterface[]","components":[{"name":"addr","type":"address","internalType":"address"},{"name":"artifacts","type":"string[]","internalType":"string[]"}]}],"stateMutability":"view"},{"type":"function","name":"targetSelectors","inputs":[],"outputs":[{"name":"targetedSelectors_","type":"tuple[]","internalType":"struct StdInvariant.FuzzSelector[]","components":[{"name":"addr","type":"address","internalType":"address"},{"name":"selectors","type":"bytes4[]","internalType":"bytes4[]"}]}],"stateMutability":"view"},{"type":"function","name":"targetSenders","inputs":[],"outputs":[{"name":"targetedSenders_","type":"address[]","internalType":"address[]"}],"stateMutability":"view"},{"type":"event","name":"log","inputs":[{"name":"","type":"string","indexed":false,"internalType":"string"}],"anonymous":false},{"type":"event","name":"log_address","inputs":[{"name":"","type":"address","indexed":false,"internalType":"address"}],"anonymous":false},{"type":"event","name":"log_array","inputs":[{"name":"val","type":"uint256[]","indexed":false,"internalType":"uint256[]"}],"anonymous":false},{"type":"event","name":"log_array","inputs":[{"name":"val","type":"int256[]","indexed":false,"internalType":"int256[]"}],"anonymous":false},{"type":"event","name":"log_array","inputs":[{"name":"val","type":"address[]","indexed":false,"internalType":"address[]"}],"anonymous":false},{"type":"event","name":"log_bytes","inputs":[{"name":"","type":"bytes","indexed":false,"internalType":"bytes"}],"anonymous":false},{"type":"event","name":"log_bytes32","inputs":[{"name":"","type":"bytes32","indexed":false,"internalType":"bytes32"}],"anonymous":false},{"type":"event","name":"log_int","inputs":[{"name":"","type":"int256","indexed":false,"internalType":"int256"}],"anonymous":false},{"type":"event","name":"log_named_address","inputs":[{"name":"key","type":"string","indexed":false,"internalType":"string"},{"name":"val","type":"address","indexed":false,"internalType":"address"}],"anonymous":false},{"type":"event","name":"log_named_array","inputs":[{"name":"key","type":"string","indexed":false,"internalType":"string"},{"name":"val","type":"uint256[]","indexed":false,"internalType":"uint256[]"}],"anonymous":false},{"type":"event","name":"log_named_array","inputs":[{"name":"key","type":"string","indexed":false,"internalType":"string"},{"name":"val","type":"int256[]","indexed":false,"internalType":"int256[]"}],"anonymous":false},{"type":"event","name":"log_named_array","inputs":[{"name":"key","type":"string","indexed":false,"internalType":"string"},{"name":"val","type":"address[]","indexed":false,"internalType":"address[]"}],"anonymous":false},{"type":"event","name":"log_named_bytes","inputs":[{"name":"key","type":"string","indexed":false,"internalType":"string"},{"name":"val","type":"bytes","indexed":false,"internalType":"bytes"}],"anonymous":false},{"type":"event","name":"log_named_bytes32","inputs":[{"name":"key","type":"string","indexed":false,"internalType":"string"},{"name":"val","type":"bytes32","indexed":false,"internalType":"bytes32"}],"anonymous":false},{"type":"event","name":"log_named_decimal_int","inputs":[{"name":"key","type":"string","indexed":false,"internalType":"string"},{"name":"val","type":"int256","indexed":false,"internalType":"int256"},{"name":"decimals","type":"uint256","indexed":false,"internalType":"uint256"}],"anonymous":false},{"type":"event","name":"log_named_decimal_uint","inputs":[{"name":"key","type":"string","indexed":false,"internalType":"string"},{"name":"val","type":"uint256","indexed":false,"internalType":"uint256"},{"name":"decimals","type":"uint256","indexed":false,"internalType":"uint256"}],"anonymous":false},{"type":"event","name":"log_named_int","inputs":[{"name":"key","type":"string","indexed":false,"internalType":"string"},{"name":"val","type":"int256","indexed":false,"internalType":"int256"}],"anonymous":false},{"type":"event","name":"log_named_string","inputs":[{"name":"key","type":"string","indexed":false,"internalType":"string"},{"name":"val","type":"string","indexed":false,"internalType":"string"}],"anonymous":false},{"type":"event","name":"log_named_uint","inputs":[{"name":"key","type":"string","indexed":false,"internalType":"string"},{"name":"val","type":"uint256","indexed":false,"internalType":"uint256"}],"anonymous":false},{"type":"event","name":"log_string","inputs":[{"name":"","type":"string","indexed":false,"internalType":"string"}],"anonymous":false},{"type":"event","name":"log_uint","inputs":[{"name":"","type":"uint256","indexed":false,"internalType":"uint256"}],"anonymous":false},{"type":"event","name":"logs","inputs":[{"name":"","type":"bytes","indexed":false,"internalType":"bytes"}],"anonymous":false}],"abiPath":"lib/chimera/src/FoundryAsserts.sol"},{"name":"Vm","abi":[{"type":"function","name":"accesses","inputs":[{"name":"target","type":"address","internalType":"address"}],"outputs":[{"name":"readSlots","type":"bytes32[]","internalType":"bytes32[]"},{"name":"writeSlots","type":"bytes32[]","internalType":"bytes32[]"}],"stateMutability":"nonpayable"},{"type":"function","name":"activeFork","inputs":[],"outputs":[{"name":"forkId","type":"uint256","internalType":"uint256"}],"stateMutability":"view"},{"type":"function","name":"addr","inputs":[{"name":"privateKey","type":"uint256","internalType":"uint256"}],"outputs":[{"name":"keyAddr","type":"address","internalType":"address"}],"stateMutability":"pure"},{"type":"function","name":"allowCheatcodes","inputs":[{"name":"account","type":"address","internalType":"address"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"assume","inputs":[{"name":"condition","type":"bool","internalType":"bool"}],"outputs":[],"stateMutability":"pure"},{"type":"function","name":"breakpoint","inputs":[{"name":"char","type":"string","internalType":"string"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"breakpoint","inputs":[{"name":"char","type":"string","internalType":"string"},{"name":"value","type":"bool","internalType":"bool"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"broadcast","inputs":[],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"broadcast","inputs":[{"name":"signer","type":"address","internalType":"address"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"broadcast","inputs":[{"name":"privateKey","type":"uint256","internalType":"uint256"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"chainId","inputs":[{"name":"newChainId","type":"uint256","internalType":"uint256"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"clearMockedCalls","inputs":[],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"closeFile","inputs":[{"name":"path","type":"string","internalType":"string"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"coinbase","inputs":[{"name":"newCoinbase","type":"address","internalType":"address"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"computeCreate2Address","inputs":[{"name":"salt","type":"bytes32","internalType":"bytes32"},{"name":"initCodeHash","type":"bytes32","internalType":"bytes32"}],"outputs":[{"name":"","type":"address","internalType":"address"}],"stateMutability":"pure"},{"type":"function","name":"computeCreate2Address","inputs":[{"name":"salt","type":"bytes32","internalType":"bytes32"},{"name":"initCodeHash","type":"bytes32","internalType":"bytes32"},{"name":"deployer","type":"address","internalType":"address"}],"outputs":[{"name":"","type":"address","internalType":"address"}],"stateMutability":"pure"},{"type":"function","name":"computeCreateAddress","inputs":[{"name":"deployer","type":"address","internalType":"address"},{"name":"nonce","type":"uint256","internalType":"uint256"}],"outputs":[{"name":"","type":"address","internalType":"address"}],"stateMutability":"pure"},{"type":"function","name":"copyFile","inputs":[{"name":"from","type":"string","internalType":"string"},{"name":"to","type":"string","internalType":"string"}],"outputs":[{"name":"copied","type":"uint64","internalType":"uint64"}],"stateMutability":"nonpayable"},{"type":"function","name":"createDir","inputs":[{"name":"path","type":"string","internalType":"string"},{"name":"recursive","type":"bool","internalType":"bool"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"createFork","inputs":[{"name":"urlOrAlias","type":"string","internalType":"string"}],"outputs":[{"name":"forkId","type":"uint256","internalType":"uint256"}],"stateMutability":"nonpayable"},{"type":"function","name":"createFork","inputs":[{"name":"urlOrAlias","type":"string","internalType":"string"},{"name":"blockNumber","type":"uint256","internalType":"uint256"}],"outputs":[{"name":"forkId","type":"uint256","internalType":"uint256"}],"stateMutability":"nonpayable"},{"type":"function","name":"createFork","inputs":[{"name":"urlOrAlias","type":"string","internalType":"string"},{"name":"txHash","type":"bytes32","internalType":"bytes32"}],"outputs":[{"name":"forkId","type":"uint256","internalType":"uint256"}],"stateMutability":"nonpayable"},{"type":"function","name":"createSelectFork","inputs":[{"name":"urlOrAlias","type":"string","internalType":"string"},{"name":"blockNumber","type":"uint256","internalType":"uint256"}],"outputs":[{"name":"forkId","type":"uint256","internalType":"uint256"}],"stateMutability":"nonpayable"},{"type":"function","name":"createSelectFork","inputs":[{"name":"urlOrAlias","type":"string","internalType":"string"},{"name":"txHash","type":"bytes32","internalType":"bytes32"}],"outputs":[{"name":"forkId","type":"uint256","internalType":"uint256"}],"stateMutability":"nonpayable"},{"type":"function","name":"createSelectFork","inputs":[{"name":"urlOrAlias","type":"string","internalType":"string"}],"outputs":[{"name":"forkId","type":"uint256","internalType":"uint256"}],"stateMutability":"nonpayable"},{"type":"function","name":"createWallet","inputs":[{"name":"walletLabel","type":"string","internalType":"string"}],"outputs":[{"name":"wallet","type":"tuple","internalType":"struct VmSafe.Wallet","components":[{"name":"addr","type":"address","internalType":"address"},{"name":"publicKeyX","type":"uint256","internalType":"uint256"},{"name":"publicKeyY","type":"uint256","internalType":"uint256"},{"name":"privateKey","type":"uint256","internalType":"uint256"}]}],"stateMutability":"nonpayable"},{"type":"function","name":"createWallet","inputs":[{"name":"privateKey","type":"uint256","internalType":"uint256"}],"outputs":[{"name":"wallet","type":"tuple","internalType":"struct VmSafe.Wallet","components":[{"name":"addr","type":"address","internalType":"address"},{"name":"publicKeyX","type":"uint256","internalType":"uint256"},{"name":"publicKeyY","type":"uint256","internalType":"uint256"},{"name":"privateKey","type":"uint256","internalType":"uint256"}]}],"stateMutability":"nonpayable"},{"type":"function","name":"createWallet","inputs":[{"name":"privateKey","type":"uint256","internalType":"uint256"},{"name":"walletLabel","type":"string","internalType":"string"}],"outputs":[{"name":"wallet","type":"tuple","internalType":"struct VmSafe.Wallet","components":[{"name":"addr","type":"address","internalType":"address"},{"name":"publicKeyX","type":"uint256","internalType":"uint256"},{"name":"publicKeyY","type":"uint256","internalType":"uint256"},{"name":"privateKey","type":"uint256","internalType":"uint256"}]}],"stateMutability":"nonpayable"},{"type":"function","name":"deal","inputs":[{"name":"account","type":"address","internalType":"address"},{"name":"newBalance","type":"uint256","internalType":"uint256"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"deleteSnapshot","inputs":[{"name":"snapshotId","type":"uint256","internalType":"uint256"}],"outputs":[{"name":"success","type":"bool","internalType":"bool"}],"stateMutability":"nonpayable"},{"type":"function","name":"deleteSnapshots","inputs":[],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"deriveKey","inputs":[{"name":"mnemonic","type":"string","internalType":"string"},{"name":"derivationPath","type":"string","internalType":"string"},{"name":"index","type":"uint32","internalType":"uint32"},{"name":"language","type":"string","internalType":"string"}],"outputs":[{"name":"privateKey","type":"uint256","internalType":"uint256"}],"stateMutability":"pure"},{"type":"function","name":"deriveKey","inputs":[{"name":"mnemonic","type":"string","internalType":"string"},{"name":"index","type":"uint32","internalType":"uint32"},{"name":"language","type":"string","internalType":"string"}],"outputs":[{"name":"privateKey","type":"uint256","internalType":"uint256"}],"stateMutability":"pure"},{"type":"function","name":"deriveKey","inputs":[{"name":"mnemonic","type":"string","internalType":"string"},{"name":"index","type":"uint32","internalType":"uint32"}],"outputs":[{"name":"privateKey","type":"uint256","internalType":"uint256"}],"stateMutability":"pure"},{"type":"function","name":"deriveKey","inputs":[{"name":"mnemonic","type":"string","internalType":"string"},{"name":"derivationPath","type":"string","internalType":"string"},{"name":"index","type":"uint32","internalType":"uint32"}],"outputs":[{"name":"privateKey","type":"uint256","internalType":"uint256"}],"stateMutability":"pure"},{"type":"function","name":"difficulty","inputs":[{"name":"newDifficulty","type":"uint256","internalType":"uint256"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"dumpState","inputs":[{"name":"pathToStateJson","type":"string","internalType":"string"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"envAddress","inputs":[{"name":"name","type":"string","internalType":"string"}],"outputs":[{"name":"value","type":"address","internalType":"address"}],"stateMutability":"view"},{"type":"function","name":"envAddress","inputs":[{"name":"name","type":"string","internalType":"string"},{"name":"delim","type":"string","internalType":"string"}],"outputs":[{"name":"value","type":"address[]","internalType":"address[]"}],"stateMutability":"view"},{"type":"function","name":"envBool","inputs":[{"name":"name","type":"string","internalType":"string"}],"outputs":[{"name":"value","type":"bool","internalType":"bool"}],"stateMutability":"view"},{"type":"function","name":"envBool","inputs":[{"name":"name","type":"string","internalType":"string"},{"name":"delim","type":"string","internalType":"string"}],"outputs":[{"name":"value","type":"bool[]","internalType":"bool[]"}],"stateMutability":"view"},{"type":"function","name":"envBytes","inputs":[{"name":"name","type":"string","internalType":"string"}],"outputs":[{"name":"value","type":"bytes","internalType":"bytes"}],"stateMutability":"view"},{"type":"function","name":"envBytes","inputs":[{"name":"name","type":"string","internalType":"string"},{"name":"delim","type":"string","internalType":"string"}],"outputs":[{"name":"value","type":"bytes[]","internalType":"bytes[]"}],"stateMutability":"view"},{"type":"function","name":"envBytes32","inputs":[{"name":"name","type":"string","internalType":"string"},{"name":"delim","type":"string","internalType":"string"}],"outputs":[{"name":"value","type":"bytes32[]","internalType":"bytes32[]"}],"stateMutability":"view"},{"type":"function","name":"envBytes32","inputs":[{"name":"name","type":"string","internalType":"string"}],"outputs":[{"name":"value","type":"bytes32","internalType":"bytes32"}],"stateMutability":"view"},{"type":"function","name":"envInt","inputs":[{"name":"name","type":"string","internalType":"string"},{"name":"delim","type":"string","internalType":"string"}],"outputs":[{"name":"value","type":"int256[]","internalType":"int256[]"}],"stateMutability":"view"},{"type":"function","name":"envInt","inputs":[{"name":"name","type":"string","internalType":"string"}],"outputs":[{"name":"value","type":"int256","internalType":"int256"}],"stateMutability":"view"},{"type":"function","name":"envOr","inputs":[{"name":"name","type":"string","internalType":"string"},{"name":"delim","type":"string","internalType":"string"},{"name":"defaultValue","type":"bytes32[]","internalType":"bytes32[]"}],"outputs":[{"name":"value","type":"bytes32[]","internalType":"bytes32[]"}],"stateMutability":"view"},{"type":"function","name":"envOr","inputs":[{"name":"name","type":"string","internalType":"string"},{"name":"delim","type":"string","internalType":"string"},{"name":"defaultValue","type":"int256[]","internalType":"int256[]"}],"outputs":[{"name":"value","type":"int256[]","internalType":"int256[]"}],"stateMutability":"view"},{"type":"function","name":"envOr","inputs":[{"name":"name","type":"string","internalType":"string"},{"name":"defaultValue","type":"bool","internalType":"bool"}],"outputs":[{"name":"value","type":"bool","internalType":"bool"}],"stateMutability":"view"},{"type":"function","name":"envOr","inputs":[{"name":"name","type":"string","internalType":"string"},{"name":"defaultValue","type":"address","internalType":"address"}],"outputs":[{"name":"value","type":"address","internalType":"address"}],"stateMutability":"view"},{"type":"function","name":"envOr","inputs":[{"name":"name","type":"string","internalType":"string"},{"name":"defaultValue","type":"uint256","internalType":"uint256"}],"outputs":[{"name":"value","type":"uint256","internalType":"uint256"}],"stateMutability":"view"},{"type":"function","name":"envOr","inputs":[{"name":"name","type":"string","internalType":"string"},{"name":"delim","type":"string","internalType":"string"},{"name":"defaultValue","type":"bytes[]","internalType":"bytes[]"}],"outputs":[{"name":"value","type":"bytes[]","internalType":"bytes[]"}],"stateMutability":"view"},{"type":"function","name":"envOr","inputs":[{"name":"name","type":"string","internalType":"string"},{"name":"delim","type":"string","internalType":"string"},{"name":"defaultValue","type":"uint256[]","internalType":"uint256[]"}],"outputs":[{"name":"value","type":"uint256[]","internalType":"uint256[]"}],"stateMutability":"view"},{"type":"function","name":"envOr","inputs":[{"name":"name","type":"string","internalType":"string"},{"name":"delim","type":"string","internalType":"string"},{"name":"defaultValue","type":"string[]","internalType":"string[]"}],"outputs":[{"name":"value","type":"string[]","internalType":"string[]"}],"stateMutability":"view"},{"type":"function","name":"envOr","inputs":[{"name":"name","type":"string","internalType":"string"},{"name":"defaultValue","type":"bytes","internalType":"bytes"}],"outputs":[{"name":"value","type":"bytes","internalType":"bytes"}],"stateMutability":"view"},{"type":"function","name":"envOr","inputs":[{"name":"name","type":"string","internalType":"string"},{"name":"defaultValue","type":"bytes32","internalType":"bytes32"}],"outputs":[{"name":"value","type":"bytes32","internalType":"bytes32"}],"stateMutability":"view"},{"type":"function","name":"envOr","inputs":[{"name":"name","type":"string","internalType":"string"},{"name":"defaultValue","type":"int256","internalType":"int256"}],"outputs":[{"name":"value","type":"int256","internalType":"int256"}],"stateMutability":"view"},{"type":"function","name":"envOr","inputs":[{"name":"name","type":"string","internalType":"string"},{"name":"delim","type":"string","internalType":"string"},{"name":"defaultValue","type":"address[]","internalType":"address[]"}],"outputs":[{"name":"value","type":"address[]","internalType":"address[]"}],"stateMutability":"view"},{"type":"function","name":"envOr","inputs":[{"name":"name","type":"string","internalType":"string"},{"name":"defaultValue","type":"string","internalType":"string"}],"outputs":[{"name":"value","type":"string","internalType":"string"}],"stateMutability":"view"},{"type":"function","name":"envOr","inputs":[{"name":"name","type":"string","internalType":"string"},{"name":"delim","type":"string","internalType":"string"},{"name":"defaultValue","type":"bool[]","internalType":"bool[]"}],"outputs":[{"name":"value","type":"bool[]","internalType":"bool[]"}],"stateMutability":"view"},{"type":"function","name":"envString","inputs":[{"name":"name","type":"string","internalType":"string"},{"name":"delim","type":"string","internalType":"string"}],"outputs":[{"name":"value","type":"string[]","internalType":"string[]"}],"stateMutability":"view"},{"type":"function","name":"envString","inputs":[{"name":"name","type":"string","internalType":"string"}],"outputs":[{"name":"value","type":"string","internalType":"string"}],"stateMutability":"view"},{"type":"function","name":"envUint","inputs":[{"name":"name","type":"string","internalType":"string"}],"outputs":[{"name":"value","type":"uint256","internalType":"uint256"}],"stateMutability":"view"},{"type":"function","name":"envUint","inputs":[{"name":"name","type":"string","internalType":"string"},{"name":"delim","type":"string","internalType":"string"}],"outputs":[{"name":"value","type":"uint256[]","internalType":"uint256[]"}],"stateMutability":"view"},{"type":"function","name":"etch","inputs":[{"name":"target","type":"address","internalType":"address"},{"name":"newRuntimeBytecode","type":"bytes","internalType":"bytes"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"eth_getLogs","inputs":[{"name":"fromBlock","type":"uint256","internalType":"uint256"},{"name":"toBlock","type":"uint256","internalType":"uint256"},{"name":"target","type":"address","internalType":"address"},{"name":"topics","type":"bytes32[]","internalType":"bytes32[]"}],"outputs":[{"name":"logs","type":"tuple[]","internalType":"struct VmSafe.EthGetLogs[]","components":[{"name":"emitter","type":"address","internalType":"address"},{"name":"topics","type":"bytes32[]","internalType":"bytes32[]"},{"name":"data","type":"bytes","internalType":"bytes"},{"name":"blockHash","type":"bytes32","internalType":"bytes32"},{"name":"blockNumber","type":"uint64","internalType":"uint64"},{"name":"transactionHash","type":"bytes32","internalType":"bytes32"},{"name":"transactionIndex","type":"uint64","internalType":"uint64"},{"name":"logIndex","type":"uint256","internalType":"uint256"},{"name":"removed","type":"bool","internalType":"bool"}]}],"stateMutability":"nonpayable"},{"type":"function","name":"exists","inputs":[{"name":"path","type":"string","internalType":"string"}],"outputs":[{"name":"result","type":"bool","internalType":"bool"}],"stateMutability":"nonpayable"},{"type":"function","name":"expectCall","inputs":[{"name":"callee","type":"address","internalType":"address"},{"name":"msgValue","type":"uint256","internalType":"uint256"},{"name":"gas","type":"uint64","internalType":"uint64"},{"name":"data","type":"bytes","internalType":"bytes"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"expectCall","inputs":[{"name":"callee","type":"address","internalType":"address"},{"name":"msgValue","type":"uint256","internalType":"uint256"},{"name":"gas","type":"uint64","internalType":"uint64"},{"name":"data","type":"bytes","internalType":"bytes"},{"name":"count","type":"uint64","internalType":"uint64"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"expectCall","inputs":[{"name":"callee","type":"address","internalType":"address"},{"name":"msgValue","type":"uint256","internalType":"uint256"},{"name":"data","type":"bytes","internalType":"bytes"},{"name":"count","type":"uint64","internalType":"uint64"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"expectCall","inputs":[{"name":"callee","type":"address","internalType":"address"},{"name":"data","type":"bytes","internalType":"bytes"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"expectCall","inputs":[{"name":"callee","type":"address","internalType":"address"},{"name":"data","type":"bytes","internalType":"bytes"},{"name":"count","type":"uint64","internalType":"uint64"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"expectCall","inputs":[{"name":"callee","type":"address","internalType":"address"},{"name":"msgValue","type":"uint256","internalType":"uint256"},{"name":"data","type":"bytes","internalType":"bytes"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"expectCallMinGas","inputs":[{"name":"callee","type":"address","internalType":"address"},{"name":"msgValue","type":"uint256","internalType":"uint256"},{"name":"minGas","type":"uint64","internalType":"uint64"},{"name":"data","type":"bytes","internalType":"bytes"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"expectCallMinGas","inputs":[{"name":"callee","type":"address","internalType":"address"},{"name":"msgValue","type":"uint256","internalType":"uint256"},{"name":"minGas","type":"uint64","internalType":"uint64"},{"name":"data","type":"bytes","internalType":"bytes"},{"name":"count","type":"uint64","internalType":"uint64"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"expectEmit","inputs":[],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"expectEmit","inputs":[{"name":"checkTopic1","type":"bool","internalType":"bool"},{"name":"checkTopic2","type":"bool","internalType":"bool"},{"name":"checkTopic3","type":"bool","internalType":"bool"},{"name":"checkData","type":"bool","internalType":"bool"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"expectEmit","inputs":[{"name":"checkTopic1","type":"bool","internalType":"bool"},{"name":"checkTopic2","type":"bool","internalType":"bool"},{"name":"checkTopic3","type":"bool","internalType":"bool"},{"name":"checkData","type":"bool","internalType":"bool"},{"name":"emitter","type":"address","internalType":"address"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"expectEmit","inputs":[{"name":"emitter","type":"address","internalType":"address"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"expectRevert","inputs":[{"name":"revertData","type":"bytes4","internalType":"bytes4"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"expectRevert","inputs":[{"name":"revertData","type":"bytes","internalType":"bytes"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"expectRevert","inputs":[],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"expectSafeMemory","inputs":[{"name":"min","type":"uint64","internalType":"uint64"},{"name":"max","type":"uint64","internalType":"uint64"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"expectSafeMemoryCall","inputs":[{"name":"min","type":"uint64","internalType":"uint64"},{"name":"max","type":"uint64","internalType":"uint64"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"fee","inputs":[{"name":"newBasefee","type":"uint256","internalType":"uint256"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"ffi","inputs":[{"name":"commandInput","type":"string[]","internalType":"string[]"}],"outputs":[{"name":"result","type":"bytes","internalType":"bytes"}],"stateMutability":"nonpayable"},{"type":"function","name":"fsMetadata","inputs":[{"name":"path","type":"string","internalType":"string"}],"outputs":[{"name":"metadata","type":"tuple","internalType":"struct VmSafe.FsMetadata","components":[{"name":"isDir","type":"bool","internalType":"bool"},{"name":"isSymlink","type":"bool","internalType":"bool"},{"name":"length","type":"uint256","internalType":"uint256"},{"name":"readOnly","type":"bool","internalType":"bool"},{"name":"modified","type":"uint256","internalType":"uint256"},{"name":"accessed","type":"uint256","internalType":"uint256"},{"name":"created","type":"uint256","internalType":"uint256"}]}],"stateMutability":"view"},{"type":"function","name":"getBlockNumber","inputs":[],"outputs":[{"name":"height","type":"uint256","internalType":"uint256"}],"stateMutability":"view"},{"type":"function","name":"getBlockTimestamp","inputs":[],"outputs":[{"name":"timestamp","type":"uint256","internalType":"uint256"}],"stateMutability":"view"},{"type":"function","name":"getCode","inputs":[{"name":"artifactPath","type":"string","internalType":"string"}],"outputs":[{"name":"creationBytecode","type":"bytes","internalType":"bytes"}],"stateMutability":"view"},{"type":"function","name":"getDeployedCode","inputs":[{"name":"artifactPath","type":"string","internalType":"string"}],"outputs":[{"name":"runtimeBytecode","type":"bytes","internalType":"bytes"}],"stateMutability":"view"},{"type":"function","name":"getLabel","inputs":[{"name":"account","type":"address","internalType":"address"}],"outputs":[{"name":"currentLabel","type":"string","internalType":"string"}],"stateMutability":"view"},{"type":"function","name":"getMappingKeyAndParentOf","inputs":[{"name":"target","type":"address","internalType":"address"},{"name":"elementSlot","type":"bytes32","internalType":"bytes32"}],"outputs":[{"name":"found","type":"bool","internalType":"bool"},{"name":"key","type":"bytes32","internalType":"bytes32"},{"name":"parent","type":"bytes32","internalType":"bytes32"}],"stateMutability":"nonpayable"},{"type":"function","name":"getMappingLength","inputs":[{"name":"target","type":"address","internalType":"address"},{"name":"mappingSlot","type":"bytes32","internalType":"bytes32"}],"outputs":[{"name":"length","type":"uint256","internalType":"uint256"}],"stateMutability":"nonpayable"},{"type":"function","name":"getMappingSlotAt","inputs":[{"name":"target","type":"address","internalType":"address"},{"name":"mappingSlot","type":"bytes32","internalType":"bytes32"},{"name":"idx","type":"uint256","internalType":"uint256"}],"outputs":[{"name":"value","type":"bytes32","internalType":"bytes32"}],"stateMutability":"nonpayable"},{"type":"function","name":"getNonce","inputs":[{"name":"account","type":"address","internalType":"address"}],"outputs":[{"name":"nonce","type":"uint64","internalType":"uint64"}],"stateMutability":"view"},{"type":"function","name":"getNonce","inputs":[{"name":"wallet","type":"tuple","internalType":"struct VmSafe.Wallet","components":[{"name":"addr","type":"address","internalType":"address"},{"name":"publicKeyX","type":"uint256","internalType":"uint256"},{"name":"publicKeyY","type":"uint256","internalType":"uint256"},{"name":"privateKey","type":"uint256","internalType":"uint256"}]}],"outputs":[{"name":"nonce","type":"uint64","internalType":"uint64"}],"stateMutability":"nonpayable"},{"type":"function","name":"getRecordedLogs","inputs":[],"outputs":[{"name":"logs","type":"tuple[]","internalType":"struct VmSafe.Log[]","components":[{"name":"topics","type":"bytes32[]","internalType":"bytes32[]"},{"name":"data","type":"bytes","internalType":"bytes"},{"name":"emitter","type":"address","internalType":"address"}]}],"stateMutability":"nonpayable"},{"type":"function","name":"isDir","inputs":[{"name":"path","type":"string","internalType":"string"}],"outputs":[{"name":"result","type":"bool","internalType":"bool"}],"stateMutability":"nonpayable"},{"type":"function","name":"isFile","inputs":[{"name":"path","type":"string","internalType":"string"}],"outputs":[{"name":"result","type":"bool","internalType":"bool"}],"stateMutability":"nonpayable"},{"type":"function","name":"isPersistent","inputs":[{"name":"account","type":"address","internalType":"address"}],"outputs":[{"name":"persistent","type":"bool","internalType":"bool"}],"stateMutability":"view"},{"type":"function","name":"keyExists","inputs":[{"name":"json","type":"string","internalType":"string"},{"name":"key","type":"string","internalType":"string"}],"outputs":[{"name":"","type":"bool","internalType":"bool"}],"stateMutability":"view"},{"type":"function","name":"label","inputs":[{"name":"account","type":"address","internalType":"address"},{"name":"newLabel","type":"string","internalType":"string"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"load","inputs":[{"name":"target","type":"address","internalType":"address"},{"name":"slot","type":"bytes32","internalType":"bytes32"}],"outputs":[{"name":"data","type":"bytes32","internalType":"bytes32"}],"stateMutability":"view"},{"type":"function","name":"loadAllocs","inputs":[{"name":"pathToAllocsJson","type":"string","internalType":"string"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"makePersistent","inputs":[{"name":"accounts","type":"address[]","internalType":"address[]"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"makePersistent","inputs":[{"name":"account0","type":"address","internalType":"address"},{"name":"account1","type":"address","internalType":"address"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"makePersistent","inputs":[{"name":"account","type":"address","internalType":"address"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"makePersistent","inputs":[{"name":"account0","type":"address","internalType":"address"},{"name":"account1","type":"address","internalType":"address"},{"name":"account2","type":"address","internalType":"address"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"mockCall","inputs":[{"name":"callee","type":"address","internalType":"address"},{"name":"msgValue","type":"uint256","internalType":"uint256"},{"name":"data","type":"bytes","internalType":"bytes"},{"name":"returnData","type":"bytes","internalType":"bytes"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"mockCall","inputs":[{"name":"callee","type":"address","internalType":"address"},{"name":"data","type":"bytes","internalType":"bytes"},{"name":"returnData","type":"bytes","internalType":"bytes"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"mockCallRevert","inputs":[{"name":"callee","type":"address","internalType":"address"},{"name":"msgValue","type":"uint256","internalType":"uint256"},{"name":"data","type":"bytes","internalType":"bytes"},{"name":"revertData","type":"bytes","internalType":"bytes"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"mockCallRevert","inputs":[{"name":"callee","type":"address","internalType":"address"},{"name":"data","type":"bytes","internalType":"bytes"},{"name":"revertData","type":"bytes","internalType":"bytes"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"parseAddress","inputs":[{"name":"stringifiedValue","type":"string","internalType":"string"}],"outputs":[{"name":"parsedValue","type":"address","internalType":"address"}],"stateMutability":"pure"},{"type":"function","name":"parseBool","inputs":[{"name":"stringifiedValue","type":"string","internalType":"string"}],"outputs":[{"name":"parsedValue","type":"bool","internalType":"bool"}],"stateMutability":"pure"},{"type":"function","name":"parseBytes","inputs":[{"name":"stringifiedValue","type":"string","internalType":"string"}],"outputs":[{"name":"parsedValue","type":"bytes","internalType":"bytes"}],"stateMutability":"pure"},{"type":"function","name":"parseBytes32","inputs":[{"name":"stringifiedValue","type":"string","internalType":"string"}],"outputs":[{"name":"parsedValue","type":"bytes32","internalType":"bytes32"}],"stateMutability":"pure"},{"type":"function","name":"parseInt","inputs":[{"name":"stringifiedValue","type":"string","internalType":"string"}],"outputs":[{"name":"parsedValue","type":"int256","internalType":"int256"}],"stateMutability":"pure"},{"type":"function","name":"parseJson","inputs":[{"name":"json","type":"string","internalType":"string"}],"outputs":[{"name":"abiEncodedData","type":"bytes","internalType":"bytes"}],"stateMutability":"pure"},{"type":"function","name":"parseJson","inputs":[{"name":"json","type":"string","internalType":"string"},{"name":"key","type":"string","internalType":"string"}],"outputs":[{"name":"abiEncodedData","type":"bytes","internalType":"bytes"}],"stateMutability":"pure"},{"type":"function","name":"parseJsonAddress","inputs":[{"name":"json","type":"string","internalType":"string"},{"name":"key","type":"string","internalType":"string"}],"outputs":[{"name":"","type":"address","internalType":"address"}],"stateMutability":"pure"},{"type":"function","name":"parseJsonAddressArray","inputs":[{"name":"json","type":"string","internalType":"string"},{"name":"key","type":"string","internalType":"string"}],"outputs":[{"name":"","type":"address[]","internalType":"address[]"}],"stateMutability":"pure"},{"type":"function","name":"parseJsonBool","inputs":[{"name":"json","type":"string","internalType":"string"},{"name":"key","type":"string","internalType":"string"}],"outputs":[{"name":"","type":"bool","internalType":"bool"}],"stateMutability":"pure"},{"type":"function","name":"parseJsonBoolArray","inputs":[{"name":"json","type":"string","internalType":"string"},{"name":"key","type":"string","internalType":"string"}],"outputs":[{"name":"","type":"bool[]","internalType":"bool[]"}],"stateMutability":"pure"},{"type":"function","name":"parseJsonBytes","inputs":[{"name":"json","type":"string","internalType":"string"},{"name":"key","type":"string","internalType":"string"}],"outputs":[{"name":"","type":"bytes","internalType":"bytes"}],"stateMutability":"pure"},{"type":"function","name":"parseJsonBytes32","inputs":[{"name":"json","type":"string","internalType":"string"},{"name":"key","type":"string","internalType":"string"}],"outputs":[{"name":"","type":"bytes32","internalType":"bytes32"}],"stateMutability":"pure"},{"type":"function","name":"parseJsonBytes32Array","inputs":[{"name":"json","type":"string","internalType":"string"},{"name":"key","type":"string","internalType":"string"}],"outputs":[{"name":"","type":"bytes32[]","internalType":"bytes32[]"}],"stateMutability":"pure"},{"type":"function","name":"parseJsonBytesArray","inputs":[{"name":"json","type":"string","internalType":"string"},{"name":"key","type":"string","internalType":"string"}],"outputs":[{"name":"","type":"bytes[]","internalType":"bytes[]"}],"stateMutability":"pure"},{"type":"function","name":"parseJsonInt","inputs":[{"name":"json","type":"string","internalType":"string"},{"name":"key","type":"string","internalType":"string"}],"outputs":[{"name":"","type":"int256","internalType":"int256"}],"stateMutability":"pure"},{"type":"function","name":"parseJsonIntArray","inputs":[{"name":"json","type":"string","internalType":"string"},{"name":"key","type":"string","internalType":"string"}],"outputs":[{"name":"","type":"int256[]","internalType":"int256[]"}],"stateMutability":"pure"},{"type":"function","name":"parseJsonKeys","inputs":[{"name":"json","type":"string","internalType":"string"},{"name":"key","type":"string","internalType":"string"}],"outputs":[{"name":"keys","type":"string[]","internalType":"string[]"}],"stateMutability":"pure"},{"type":"function","name":"parseJsonString","inputs":[{"name":"json","type":"string","internalType":"string"},{"name":"key","type":"string","internalType":"string"}],"outputs":[{"name":"","type":"string","internalType":"string"}],"stateMutability":"pure"},{"type":"function","name":"parseJsonStringArray","inputs":[{"name":"json","type":"string","internalType":"string"},{"name":"key","type":"string","internalType":"string"}],"outputs":[{"name":"","type":"string[]","internalType":"string[]"}],"stateMutability":"pure"},{"type":"function","name":"parseJsonUint","inputs":[{"name":"json","type":"string","internalType":"string"},{"name":"key","type":"string","internalType":"string"}],"outputs":[{"name":"","type":"uint256","internalType":"uint256"}],"stateMutability":"pure"},{"type":"function","name":"parseJsonUintArray","inputs":[{"name":"json","type":"string","internalType":"string"},{"name":"key","type":"string","internalType":"string"}],"outputs":[{"name":"","type":"uint256[]","internalType":"uint256[]"}],"stateMutability":"pure"},{"type":"function","name":"parseUint","inputs":[{"name":"stringifiedValue","type":"string","internalType":"string"}],"outputs":[{"name":"parsedValue","type":"uint256","internalType":"uint256"}],"stateMutability":"pure"},{"type":"function","name":"pauseGasMetering","inputs":[],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"prank","inputs":[{"name":"msgSender","type":"address","internalType":"address"},{"name":"txOrigin","type":"address","internalType":"address"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"prank","inputs":[{"name":"msgSender","type":"address","internalType":"address"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"prevrandao","inputs":[{"name":"newPrevrandao","type":"bytes32","internalType":"bytes32"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"projectRoot","inputs":[],"outputs":[{"name":"path","type":"string","internalType":"string"}],"stateMutability":"view"},{"type":"function","name":"readCallers","inputs":[],"outputs":[{"name":"callerMode","type":"uint8","internalType":"enum VmSafe.CallerMode"},{"name":"msgSender","type":"address","internalType":"address"},{"name":"txOrigin","type":"address","internalType":"address"}],"stateMutability":"nonpayable"},{"type":"function","name":"readDir","inputs":[{"name":"path","type":"string","internalType":"string"},{"name":"maxDepth","type":"uint64","internalType":"uint64"}],"outputs":[{"name":"entries","type":"tuple[]","internalType":"struct VmSafe.DirEntry[]","components":[{"name":"errorMessage","type":"string","internalType":"string"},{"name":"path","type":"string","internalType":"string"},{"name":"depth","type":"uint64","internalType":"uint64"},{"name":"isDir","type":"bool","internalType":"bool"},{"name":"isSymlink","type":"bool","internalType":"bool"}]}],"stateMutability":"view"},{"type":"function","name":"readDir","inputs":[{"name":"path","type":"string","internalType":"string"},{"name":"maxDepth","type":"uint64","internalType":"uint64"},{"name":"followLinks","type":"bool","internalType":"bool"}],"outputs":[{"name":"entries","type":"tuple[]","internalType":"struct VmSafe.DirEntry[]","components":[{"name":"errorMessage","type":"string","internalType":"string"},{"name":"path","type":"string","internalType":"string"},{"name":"depth","type":"uint64","internalType":"uint64"},{"name":"isDir","type":"bool","internalType":"bool"},{"name":"isSymlink","type":"bool","internalType":"bool"}]}],"stateMutability":"view"},{"type":"function","name":"readDir","inputs":[{"name":"path","type":"string","internalType":"string"}],"outputs":[{"name":"entries","type":"tuple[]","internalType":"struct VmSafe.DirEntry[]","components":[{"name":"errorMessage","type":"string","internalType":"string"},{"name":"path","type":"string","internalType":"string"},{"name":"depth","type":"uint64","internalType":"uint64"},{"name":"isDir","type":"bool","internalType":"bool"},{"name":"isSymlink","type":"bool","internalType":"bool"}]}],"stateMutability":"view"},{"type":"function","name":"readFile","inputs":[{"name":"path","type":"string","internalType":"string"}],"outputs":[{"name":"data","type":"string","internalType":"string"}],"stateMutability":"view"},{"type":"function","name":"readFileBinary","inputs":[{"name":"path","type":"string","internalType":"string"}],"outputs":[{"name":"data","type":"bytes","internalType":"bytes"}],"stateMutability":"view"},{"type":"function","name":"readLine","inputs":[{"name":"path","type":"string","internalType":"string"}],"outputs":[{"name":"line","type":"string","internalType":"string"}],"stateMutability":"view"},{"type":"function","name":"readLink","inputs":[{"name":"linkPath","type":"string","internalType":"string"}],"outputs":[{"name":"targetPath","type":"string","internalType":"string"}],"stateMutability":"view"},{"type":"function","name":"record","inputs":[],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"recordLogs","inputs":[],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"rememberKey","inputs":[{"name":"privateKey","type":"uint256","internalType":"uint256"}],"outputs":[{"name":"keyAddr","type":"address","internalType":"address"}],"stateMutability":"nonpayable"},{"type":"function","name":"removeDir","inputs":[{"name":"path","type":"string","internalType":"string"},{"name":"recursive","type":"bool","internalType":"bool"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"removeFile","inputs":[{"name":"path","type":"string","internalType":"string"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"resetNonce","inputs":[{"name":"account","type":"address","internalType":"address"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"resumeGasMetering","inputs":[],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"revertTo","inputs":[{"name":"snapshotId","type":"uint256","internalType":"uint256"}],"outputs":[{"name":"success","type":"bool","internalType":"bool"}],"stateMutability":"nonpayable"},{"type":"function","name":"revertToAndDelete","inputs":[{"name":"snapshotId","type":"uint256","internalType":"uint256"}],"outputs":[{"name":"success","type":"bool","internalType":"bool"}],"stateMutability":"nonpayable"},{"type":"function","name":"revokePersistent","inputs":[{"name":"accounts","type":"address[]","internalType":"address[]"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"revokePersistent","inputs":[{"name":"account","type":"address","internalType":"address"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"roll","inputs":[{"name":"newHeight","type":"uint256","internalType":"uint256"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"rollFork","inputs":[{"name":"txHash","type":"bytes32","internalType":"bytes32"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"rollFork","inputs":[{"name":"forkId","type":"uint256","internalType":"uint256"},{"name":"blockNumber","type":"uint256","internalType":"uint256"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"rollFork","inputs":[{"name":"blockNumber","type":"uint256","internalType":"uint256"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"rollFork","inputs":[{"name":"forkId","type":"uint256","internalType":"uint256"},{"name":"txHash","type":"bytes32","internalType":"bytes32"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"rpc","inputs":[{"name":"method","type":"string","internalType":"string"},{"name":"params","type":"string","internalType":"string"}],"outputs":[{"name":"data","type":"bytes","internalType":"bytes"}],"stateMutability":"nonpayable"},{"type":"function","name":"rpcUrl","inputs":[{"name":"rpcAlias","type":"string","internalType":"string"}],"outputs":[{"name":"json","type":"string","internalType":"string"}],"stateMutability":"view"},{"type":"function","name":"rpcUrlStructs","inputs":[],"outputs":[{"name":"urls","type":"tuple[]","internalType":"struct VmSafe.Rpc[]","components":[{"name":"key","type":"string","internalType":"string"},{"name":"url","type":"string","internalType":"string"}]}],"stateMutability":"view"},{"type":"function","name":"rpcUrls","inputs":[],"outputs":[{"name":"urls","type":"string[2][]","internalType":"string[2][]"}],"stateMutability":"view"},{"type":"function","name":"selectFork","inputs":[{"name":"forkId","type":"uint256","internalType":"uint256"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"serializeAddress","inputs":[{"name":"objectKey","type":"string","internalType":"string"},{"name":"valueKey","type":"string","internalType":"string"},{"name":"values","type":"address[]","internalType":"address[]"}],"outputs":[{"name":"json","type":"string","internalType":"string"}],"stateMutability":"nonpayable"},{"type":"function","name":"serializeAddress","inputs":[{"name":"objectKey","type":"string","internalType":"string"},{"name":"valueKey","type":"string","internalType":"string"},{"name":"value","type":"address","internalType":"address"}],"outputs":[{"name":"json","type":"string","internalType":"string"}],"stateMutability":"nonpayable"},{"type":"function","name":"serializeBool","inputs":[{"name":"objectKey","type":"string","internalType":"string"},{"name":"valueKey","type":"string","internalType":"string"},{"name":"values","type":"bool[]","internalType":"bool[]"}],"outputs":[{"name":"json","type":"string","internalType":"string"}],"stateMutability":"nonpayable"},{"type":"function","name":"serializeBool","inputs":[{"name":"objectKey","type":"string","internalType":"string"},{"name":"valueKey","type":"string","internalType":"string"},{"name":"value","type":"bool","internalType":"bool"}],"outputs":[{"name":"json","type":"string","internalType":"string"}],"stateMutability":"nonpayable"},{"type":"function","name":"serializeBytes","inputs":[{"name":"objectKey","type":"string","internalType":"string"},{"name":"valueKey","type":"string","internalType":"string"},{"name":"values","type":"bytes[]","internalType":"bytes[]"}],"outputs":[{"name":"json","type":"string","internalType":"string"}],"stateMutability":"nonpayable"},{"type":"function","name":"serializeBytes","inputs":[{"name":"objectKey","type":"string","internalType":"string"},{"name":"valueKey","type":"string","internalType":"string"},{"name":"value","type":"bytes","internalType":"bytes"}],"outputs":[{"name":"json","type":"string","internalType":"string"}],"stateMutability":"nonpayable"},{"type":"function","name":"serializeBytes32","inputs":[{"name":"objectKey","type":"string","internalType":"string"},{"name":"valueKey","type":"string","internalType":"string"},{"name":"values","type":"bytes32[]","internalType":"bytes32[]"}],"outputs":[{"name":"json","type":"string","internalType":"string"}],"stateMutability":"nonpayable"},{"type":"function","name":"serializeBytes32","inputs":[{"name":"objectKey","type":"string","internalType":"string"},{"name":"valueKey","type":"string","internalType":"string"},{"name":"value","type":"bytes32","internalType":"bytes32"}],"outputs":[{"name":"json","type":"string","internalType":"string"}],"stateMutability":"nonpayable"},{"type":"function","name":"serializeInt","inputs":[{"name":"objectKey","type":"string","internalType":"string"},{"name":"valueKey","type":"string","internalType":"string"},{"name":"value","type":"int256","internalType":"int256"}],"outputs":[{"name":"json","type":"string","internalType":"string"}],"stateMutability":"nonpayable"},{"type":"function","name":"serializeInt","inputs":[{"name":"objectKey","type":"string","internalType":"string"},{"name":"valueKey","type":"string","internalType":"string"},{"name":"values","type":"int256[]","internalType":"int256[]"}],"outputs":[{"name":"json","type":"string","internalType":"string"}],"stateMutability":"nonpayable"},{"type":"function","name":"serializeJson","inputs":[{"name":"objectKey","type":"string","internalType":"string"},{"name":"value","type":"string","internalType":"string"}],"outputs":[{"name":"json","type":"string","internalType":"string"}],"stateMutability":"nonpayable"},{"type":"function","name":"serializeString","inputs":[{"name":"objectKey","type":"string","internalType":"string"},{"name":"valueKey","type":"string","internalType":"string"},{"name":"values","type":"string[]","internalType":"string[]"}],"outputs":[{"name":"json","type":"string","internalType":"string"}],"stateMutability":"nonpayable"},{"type":"function","name":"serializeString","inputs":[{"name":"objectKey","type":"string","internalType":"string"},{"name":"valueKey","type":"string","internalType":"string"},{"name":"value","type":"string","internalType":"string"}],"outputs":[{"name":"json","type":"string","internalType":"string"}],"stateMutability":"nonpayable"},{"type":"function","name":"serializeUint","inputs":[{"name":"objectKey","type":"string","internalType":"string"},{"name":"valueKey","type":"string","internalType":"string"},{"name":"value","type":"uint256","internalType":"uint256"}],"outputs":[{"name":"json","type":"string","internalType":"string"}],"stateMutability":"nonpayable"},{"type":"function","name":"serializeUint","inputs":[{"name":"objectKey","type":"string","internalType":"string"},{"name":"valueKey","type":"string","internalType":"string"},{"name":"values","type":"uint256[]","internalType":"uint256[]"}],"outputs":[{"name":"json","type":"string","internalType":"string"}],"stateMutability":"nonpayable"},{"type":"function","name":"setEnv","inputs":[{"name":"name","type":"string","internalType":"string"},{"name":"value","type":"string","internalType":"string"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"setNonce","inputs":[{"name":"account","type":"address","internalType":"address"},{"name":"newNonce","type":"uint64","internalType":"uint64"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"setNonceUnsafe","inputs":[{"name":"account","type":"address","internalType":"address"},{"name":"newNonce","type":"uint64","internalType":"uint64"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"sign","inputs":[{"name":"wallet","type":"tuple","internalType":"struct VmSafe.Wallet","components":[{"name":"addr","type":"address","internalType":"address"},{"name":"publicKeyX","type":"uint256","internalType":"uint256"},{"name":"publicKeyY","type":"uint256","internalType":"uint256"},{"name":"privateKey","type":"uint256","internalType":"uint256"}]},{"name":"digest","type":"bytes32","internalType":"bytes32"}],"outputs":[{"name":"v","type":"uint8","internalType":"uint8"},{"name":"r","type":"bytes32","internalType":"bytes32"},{"name":"s","type":"bytes32","internalType":"bytes32"}],"stateMutability":"nonpayable"},{"type":"function","name":"sign","inputs":[{"name":"privateKey","type":"uint256","internalType":"uint256"},{"name":"digest","type":"bytes32","internalType":"bytes32"}],"outputs":[{"name":"v","type":"uint8","internalType":"uint8"},{"name":"r","type":"bytes32","internalType":"bytes32"},{"name":"s","type":"bytes32","internalType":"bytes32"}],"stateMutability":"pure"},{"type":"function","name":"signP256","inputs":[{"name":"privateKey","type":"uint256","internalType":"uint256"},{"name":"digest","type":"bytes32","internalType":"bytes32"}],"outputs":[{"name":"r","type":"bytes32","internalType":"bytes32"},{"name":"s","type":"bytes32","internalType":"bytes32"}],"stateMutability":"pure"},{"type":"function","name":"skip","inputs":[{"name":"skipTest","type":"bool","internalType":"bool"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"sleep","inputs":[{"name":"duration","type":"uint256","internalType":"uint256"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"snapshot","inputs":[],"outputs":[{"name":"snapshotId","type":"uint256","internalType":"uint256"}],"stateMutability":"nonpayable"},{"type":"function","name":"startBroadcast","inputs":[],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"startBroadcast","inputs":[{"name":"signer","type":"address","internalType":"address"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"startBroadcast","inputs":[{"name":"privateKey","type":"uint256","internalType":"uint256"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"startMappingRecording","inputs":[],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"startPrank","inputs":[{"name":"msgSender","type":"address","internalType":"address"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"startPrank","inputs":[{"name":"msgSender","type":"address","internalType":"address"},{"name":"txOrigin","type":"address","internalType":"address"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"startStateDiffRecording","inputs":[],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"stopAndReturnStateDiff","inputs":[],"outputs":[{"name":"accountAccesses","type":"tuple[]","internalType":"struct VmSafe.AccountAccess[]","components":[{"name":"chainInfo","type":"tuple","internalType":"struct VmSafe.ChainInfo","components":[{"name":"forkId","type":"uint256","internalType":"uint256"},{"name":"chainId","type":"uint256","internalType":"uint256"}]},{"name":"kind","type":"uint8","internalType":"enum VmSafe.AccountAccessKind"},{"name":"account","type":"address","internalType":"address"},{"name":"accessor","type":"address","internalType":"address"},{"name":"initialized","type":"bool","internalType":"bool"},{"name":"oldBalance","type":"uint256","internalType":"uint256"},{"name":"newBalance","type":"uint256","internalType":"uint256"},{"name":"deployedCode","type":"bytes","internalType":"bytes"},{"name":"value","type":"uint256","internalType":"uint256"},{"name":"data","type":"bytes","internalType":"bytes"},{"name":"reverted","type":"bool","internalType":"bool"},{"name":"storageAccesses","type":"tuple[]","internalType":"struct VmSafe.StorageAccess[]","components":[{"name":"account","type":"address","internalType":"address"},{"name":"slot","type":"bytes32","internalType":"bytes32"},{"name":"isWrite","type":"bool","internalType":"bool"},{"name":"previousValue","type":"bytes32","internalType":"bytes32"},{"name":"newValue","type":"bytes32","internalType":"bytes32"},{"name":"reverted","type":"bool","internalType":"bool"}]}]}],"stateMutability":"nonpayable"},{"type":"function","name":"stopBroadcast","inputs":[],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"stopMappingRecording","inputs":[],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"stopPrank","inputs":[],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"store","inputs":[{"name":"target","type":"address","internalType":"address"},{"name":"slot","type":"bytes32","internalType":"bytes32"},{"name":"value","type":"bytes32","internalType":"bytes32"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"toBase64","inputs":[{"name":"data","type":"string","internalType":"string"}],"outputs":[{"name":"","type":"string","internalType":"string"}],"stateMutability":"pure"},{"type":"function","name":"toBase64","inputs":[{"name":"data","type":"bytes","internalType":"bytes"}],"outputs":[{"name":"","type":"string","internalType":"string"}],"stateMutability":"pure"},{"type":"function","name":"toBase64URL","inputs":[{"name":"data","type":"string","internalType":"string"}],"outputs":[{"name":"","type":"string","internalType":"string"}],"stateMutability":"pure"},{"type":"function","name":"toBase64URL","inputs":[{"name":"data","type":"bytes","internalType":"bytes"}],"outputs":[{"name":"","type":"string","internalType":"string"}],"stateMutability":"pure"},{"type":"function","name":"toString","inputs":[{"name":"value","type":"address","internalType":"address"}],"outputs":[{"name":"stringifiedValue","type":"string","internalType":"string"}],"stateMutability":"pure"},{"type":"function","name":"toString","inputs":[{"name":"value","type":"uint256","internalType":"uint256"}],"outputs":[{"name":"stringifiedValue","type":"string","internalType":"string"}],"stateMutability":"pure"},{"type":"function","name":"toString","inputs":[{"name":"value","type":"bytes","internalType":"bytes"}],"outputs":[{"name":"stringifiedValue","type":"string","internalType":"string"}],"stateMutability":"pure"},{"type":"function","name":"toString","inputs":[{"name":"value","type":"bool","internalType":"bool"}],"outputs":[{"name":"stringifiedValue","type":"string","internalType":"string"}],"stateMutability":"pure"},{"type":"function","name":"toString","inputs":[{"name":"value","type":"int256","internalType":"int256"}],"outputs":[{"name":"stringifiedValue","type":"string","internalType":"string"}],"stateMutability":"pure"},{"type":"function","name":"toString","inputs":[{"name":"value","type":"bytes32","internalType":"bytes32"}],"outputs":[{"name":"stringifiedValue","type":"string","internalType":"string"}],"stateMutability":"pure"},{"type":"function","name":"transact","inputs":[{"name":"forkId","type":"uint256","internalType":"uint256"},{"name":"txHash","type":"bytes32","internalType":"bytes32"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"transact","inputs":[{"name":"txHash","type":"bytes32","internalType":"bytes32"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"tryFfi","inputs":[{"name":"commandInput","type":"string[]","internalType":"string[]"}],"outputs":[{"name":"result","type":"tuple","internalType":"struct VmSafe.FfiResult","components":[{"name":"exitCode","type":"int32","internalType":"int32"},{"name":"stdout","type":"bytes","internalType":"bytes"},{"name":"stderr","type":"bytes","internalType":"bytes"}]}],"stateMutability":"nonpayable"},{"type":"function","name":"txGasPrice","inputs":[{"name":"newGasPrice","type":"uint256","internalType":"uint256"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"unixTime","inputs":[],"outputs":[{"name":"milliseconds","type":"uint256","internalType":"uint256"}],"stateMutability":"nonpayable"},{"type":"function","name":"warp","inputs":[{"name":"newTimestamp","type":"uint256","internalType":"uint256"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"writeFile","inputs":[{"name":"path","type":"string","internalType":"string"},{"name":"data","type":"string","internalType":"string"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"writeFileBinary","inputs":[{"name":"path","type":"string","internalType":"string"},{"name":"data","type":"bytes","internalType":"bytes"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"writeJson","inputs":[{"name":"json","type":"string","internalType":"string"},{"name":"path","type":"string","internalType":"string"},{"name":"valueKey","type":"string","internalType":"string"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"writeJson","inputs":[{"name":"json","type":"string","internalType":"string"},{"name":"path","type":"string","internalType":"string"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"writeLine","inputs":[{"name":"path","type":"string","internalType":"string"},{"name":"data","type":"string","internalType":"string"}],"outputs":[],"stateMutability":"nonpayable"}],"abiPath":"lib/forge-std/src/Vm.sol"},{"name":"VmSafe","abi":[{"type":"function","name":"accesses","inputs":[{"name":"target","type":"address","internalType":"address"}],"outputs":[{"name":"readSlots","type":"bytes32[]","internalType":"bytes32[]"},{"name":"writeSlots","type":"bytes32[]","internalType":"bytes32[]"}],"stateMutability":"nonpayable"},{"type":"function","name":"addr","inputs":[{"name":"privateKey","type":"uint256","internalType":"uint256"}],"outputs":[{"name":"keyAddr","type":"address","internalType":"address"}],"stateMutability":"pure"},{"type":"function","name":"assume","inputs":[{"name":"condition","type":"bool","internalType":"bool"}],"outputs":[],"stateMutability":"pure"},{"type":"function","name":"breakpoint","inputs":[{"name":"char","type":"string","internalType":"string"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"breakpoint","inputs":[{"name":"char","type":"string","internalType":"string"},{"name":"value","type":"bool","internalType":"bool"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"broadcast","inputs":[],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"broadcast","inputs":[{"name":"signer","type":"address","internalType":"address"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"broadcast","inputs":[{"name":"privateKey","type":"uint256","internalType":"uint256"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"closeFile","inputs":[{"name":"path","type":"string","internalType":"string"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"computeCreate2Address","inputs":[{"name":"salt","type":"bytes32","internalType":"bytes32"},{"name":"initCodeHash","type":"bytes32","internalType":"bytes32"}],"outputs":[{"name":"","type":"address","internalType":"address"}],"stateMutability":"pure"},{"type":"function","name":"computeCreate2Address","inputs":[{"name":"salt","type":"bytes32","internalType":"bytes32"},{"name":"initCodeHash","type":"bytes32","internalType":"bytes32"},{"name":"deployer","type":"address","internalType":"address"}],"outputs":[{"name":"","type":"address","internalType":"address"}],"stateMutability":"pure"},{"type":"function","name":"computeCreateAddress","inputs":[{"name":"deployer","type":"address","internalType":"address"},{"name":"nonce","type":"uint256","internalType":"uint256"}],"outputs":[{"name":"","type":"address","internalType":"address"}],"stateMutability":"pure"},{"type":"function","name":"copyFile","inputs":[{"name":"from","type":"string","internalType":"string"},{"name":"to","type":"string","internalType":"string"}],"outputs":[{"name":"copied","type":"uint64","internalType":"uint64"}],"stateMutability":"nonpayable"},{"type":"function","name":"createDir","inputs":[{"name":"path","type":"string","internalType":"string"},{"name":"recursive","type":"bool","internalType":"bool"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"createWallet","inputs":[{"name":"walletLabel","type":"string","internalType":"string"}],"outputs":[{"name":"wallet","type":"tuple","internalType":"struct VmSafe.Wallet","components":[{"name":"addr","type":"address","internalType":"address"},{"name":"publicKeyX","type":"uint256","internalType":"uint256"},{"name":"publicKeyY","type":"uint256","internalType":"uint256"},{"name":"privateKey","type":"uint256","internalType":"uint256"}]}],"stateMutability":"nonpayable"},{"type":"function","name":"createWallet","inputs":[{"name":"privateKey","type":"uint256","internalType":"uint256"}],"outputs":[{"name":"wallet","type":"tuple","internalType":"struct VmSafe.Wallet","components":[{"name":"addr","type":"address","internalType":"address"},{"name":"publicKeyX","type":"uint256","internalType":"uint256"},{"name":"publicKeyY","type":"uint256","internalType":"uint256"},{"name":"privateKey","type":"uint256","internalType":"uint256"}]}],"stateMutability":"nonpayable"},{"type":"function","name":"createWallet","inputs":[{"name":"privateKey","type":"uint256","internalType":"uint256"},{"name":"walletLabel","type":"string","internalType":"string"}],"outputs":[{"name":"wallet","type":"tuple","internalType":"struct VmSafe.Wallet","components":[{"name":"addr","type":"address","internalType":"address"},{"name":"publicKeyX","type":"uint256","internalType":"uint256"},{"name":"publicKeyY","type":"uint256","internalType":"uint256"},{"name":"privateKey","type":"uint256","internalType":"uint256"}]}],"stateMutability":"nonpayable"},{"type":"function","name":"deriveKey","inputs":[{"name":"mnemonic","type":"string","internalType":"string"},{"name":"derivationPath","type":"string","internalType":"string"},{"name":"index","type":"uint32","internalType":"uint32"},{"name":"language","type":"string","internalType":"string"}],"outputs":[{"name":"privateKey","type":"uint256","internalType":"uint256"}],"stateMutability":"pure"},{"type":"function","name":"deriveKey","inputs":[{"name":"mnemonic","type":"string","internalType":"string"},{"name":"index","type":"uint32","internalType":"uint32"},{"name":"language","type":"string","internalType":"string"}],"outputs":[{"name":"privateKey","type":"uint256","internalType":"uint256"}],"stateMutability":"pure"},{"type":"function","name":"deriveKey","inputs":[{"name":"mnemonic","type":"string","internalType":"string"},{"name":"index","type":"uint32","internalType":"uint32"}],"outputs":[{"name":"privateKey","type":"uint256","internalType":"uint256"}],"stateMutability":"pure"},{"type":"function","name":"deriveKey","inputs":[{"name":"mnemonic","type":"string","internalType":"string"},{"name":"derivationPath","type":"string","internalType":"string"},{"name":"index","type":"uint32","internalType":"uint32"}],"outputs":[{"name":"privateKey","type":"uint256","internalType":"uint256"}],"stateMutability":"pure"},{"type":"function","name":"envAddress","inputs":[{"name":"name","type":"string","internalType":"string"}],"outputs":[{"name":"value","type":"address","internalType":"address"}],"stateMutability":"view"},{"type":"function","name":"envAddress","inputs":[{"name":"name","type":"string","internalType":"string"},{"name":"delim","type":"string","internalType":"string"}],"outputs":[{"name":"value","type":"address[]","internalType":"address[]"}],"stateMutability":"view"},{"type":"function","name":"envBool","inputs":[{"name":"name","type":"string","internalType":"string"}],"outputs":[{"name":"value","type":"bool","internalType":"bool"}],"stateMutability":"view"},{"type":"function","name":"envBool","inputs":[{"name":"name","type":"string","internalType":"string"},{"name":"delim","type":"string","internalType":"string"}],"outputs":[{"name":"value","type":"bool[]","internalType":"bool[]"}],"stateMutability":"view"},{"type":"function","name":"envBytes","inputs":[{"name":"name","type":"string","internalType":"string"}],"outputs":[{"name":"value","type":"bytes","internalType":"bytes"}],"stateMutability":"view"},{"type":"function","name":"envBytes","inputs":[{"name":"name","type":"string","internalType":"string"},{"name":"delim","type":"string","internalType":"string"}],"outputs":[{"name":"value","type":"bytes[]","internalType":"bytes[]"}],"stateMutability":"view"},{"type":"function","name":"envBytes32","inputs":[{"name":"name","type":"string","internalType":"string"},{"name":"delim","type":"string","internalType":"string"}],"outputs":[{"name":"value","type":"bytes32[]","internalType":"bytes32[]"}],"stateMutability":"view"},{"type":"function","name":"envBytes32","inputs":[{"name":"name","type":"string","internalType":"string"}],"outputs":[{"name":"value","type":"bytes32","internalType":"bytes32"}],"stateMutability":"view"},{"type":"function","name":"envInt","inputs":[{"name":"name","type":"string","internalType":"string"},{"name":"delim","type":"string","internalType":"string"}],"outputs":[{"name":"value","type":"int256[]","internalType":"int256[]"}],"stateMutability":"view"},{"type":"function","name":"envInt","inputs":[{"name":"name","type":"string","internalType":"string"}],"outputs":[{"name":"value","type":"int256","internalType":"int256"}],"stateMutability":"view"},{"type":"function","name":"envOr","inputs":[{"name":"name","type":"string","internalType":"string"},{"name":"delim","type":"string","internalType":"string"},{"name":"defaultValue","type":"bytes32[]","internalType":"bytes32[]"}],"outputs":[{"name":"value","type":"bytes32[]","internalType":"bytes32[]"}],"stateMutability":"view"},{"type":"function","name":"envOr","inputs":[{"name":"name","type":"string","internalType":"string"},{"name":"delim","type":"string","internalType":"string"},{"name":"defaultValue","type":"int256[]","internalType":"int256[]"}],"outputs":[{"name":"value","type":"int256[]","internalType":"int256[]"}],"stateMutability":"view"},{"type":"function","name":"envOr","inputs":[{"name":"name","type":"string","internalType":"string"},{"name":"defaultValue","type":"bool","internalType":"bool"}],"outputs":[{"name":"value","type":"bool","internalType":"bool"}],"stateMutability":"view"},{"type":"function","name":"envOr","inputs":[{"name":"name","type":"string","internalType":"string"},{"name":"defaultValue","type":"address","internalType":"address"}],"outputs":[{"name":"value","type":"address","internalType":"address"}],"stateMutability":"view"},{"type":"function","name":"envOr","inputs":[{"name":"name","type":"string","internalType":"string"},{"name":"defaultValue","type":"uint256","internalType":"uint256"}],"outputs":[{"name":"value","type":"uint256","internalType":"uint256"}],"stateMutability":"view"},{"type":"function","name":"envOr","inputs":[{"name":"name","type":"string","internalType":"string"},{"name":"delim","type":"string","internalType":"string"},{"name":"defaultValue","type":"bytes[]","internalType":"bytes[]"}],"outputs":[{"name":"value","type":"bytes[]","internalType":"bytes[]"}],"stateMutability":"view"},{"type":"function","name":"envOr","inputs":[{"name":"name","type":"string","internalType":"string"},{"name":"delim","type":"string","internalType":"string"},{"name":"defaultValue","type":"uint256[]","internalType":"uint256[]"}],"outputs":[{"name":"value","type":"uint256[]","internalType":"uint256[]"}],"stateMutability":"view"},{"type":"function","name":"envOr","inputs":[{"name":"name","type":"string","internalType":"string"},{"name":"delim","type":"string","internalType":"string"},{"name":"defaultValue","type":"string[]","internalType":"string[]"}],"outputs":[{"name":"value","type":"string[]","internalType":"string[]"}],"stateMutability":"view"},{"type":"function","name":"envOr","inputs":[{"name":"name","type":"string","internalType":"string"},{"name":"defaultValue","type":"bytes","internalType":"bytes"}],"outputs":[{"name":"value","type":"bytes","internalType":"bytes"}],"stateMutability":"view"},{"type":"function","name":"envOr","inputs":[{"name":"name","type":"string","internalType":"string"},{"name":"defaultValue","type":"bytes32","internalType":"bytes32"}],"outputs":[{"name":"value","type":"bytes32","internalType":"bytes32"}],"stateMutability":"view"},{"type":"function","name":"envOr","inputs":[{"name":"name","type":"string","internalType":"string"},{"name":"defaultValue","type":"int256","internalType":"int256"}],"outputs":[{"name":"value","type":"int256","internalType":"int256"}],"stateMutability":"view"},{"type":"function","name":"envOr","inputs":[{"name":"name","type":"string","internalType":"string"},{"name":"delim","type":"string","internalType":"string"},{"name":"defaultValue","type":"address[]","internalType":"address[]"}],"outputs":[{"name":"value","type":"address[]","internalType":"address[]"}],"stateMutability":"view"},{"type":"function","name":"envOr","inputs":[{"name":"name","type":"string","internalType":"string"},{"name":"defaultValue","type":"string","internalType":"string"}],"outputs":[{"name":"value","type":"string","internalType":"string"}],"stateMutability":"view"},{"type":"function","name":"envOr","inputs":[{"name":"name","type":"string","internalType":"string"},{"name":"delim","type":"string","internalType":"string"},{"name":"defaultValue","type":"bool[]","internalType":"bool[]"}],"outputs":[{"name":"value","type":"bool[]","internalType":"bool[]"}],"stateMutability":"view"},{"type":"function","name":"envString","inputs":[{"name":"name","type":"string","internalType":"string"},{"name":"delim","type":"string","internalType":"string"}],"outputs":[{"name":"value","type":"string[]","internalType":"string[]"}],"stateMutability":"view"},{"type":"function","name":"envString","inputs":[{"name":"name","type":"string","internalType":"string"}],"outputs":[{"name":"value","type":"string","internalType":"string"}],"stateMutability":"view"},{"type":"function","name":"envUint","inputs":[{"name":"name","type":"string","internalType":"string"}],"outputs":[{"name":"value","type":"uint256","internalType":"uint256"}],"stateMutability":"view"},{"type":"function","name":"envUint","inputs":[{"name":"name","type":"string","internalType":"string"},{"name":"delim","type":"string","internalType":"string"}],"outputs":[{"name":"value","type":"uint256[]","internalType":"uint256[]"}],"stateMutability":"view"},{"type":"function","name":"eth_getLogs","inputs":[{"name":"fromBlock","type":"uint256","internalType":"uint256"},{"name":"toBlock","type":"uint256","internalType":"uint256"},{"name":"target","type":"address","internalType":"address"},{"name":"topics","type":"bytes32[]","internalType":"bytes32[]"}],"outputs":[{"name":"logs","type":"tuple[]","internalType":"struct VmSafe.EthGetLogs[]","components":[{"name":"emitter","type":"address","internalType":"address"},{"name":"topics","type":"bytes32[]","internalType":"bytes32[]"},{"name":"data","type":"bytes","internalType":"bytes"},{"name":"blockHash","type":"bytes32","internalType":"bytes32"},{"name":"blockNumber","type":"uint64","internalType":"uint64"},{"name":"transactionHash","type":"bytes32","internalType":"bytes32"},{"name":"transactionIndex","type":"uint64","internalType":"uint64"},{"name":"logIndex","type":"uint256","internalType":"uint256"},{"name":"removed","type":"bool","internalType":"bool"}]}],"stateMutability":"nonpayable"},{"type":"function","name":"exists","inputs":[{"name":"path","type":"string","internalType":"string"}],"outputs":[{"name":"result","type":"bool","internalType":"bool"}],"stateMutability":"nonpayable"},{"type":"function","name":"ffi","inputs":[{"name":"commandInput","type":"string[]","internalType":"string[]"}],"outputs":[{"name":"result","type":"bytes","internalType":"bytes"}],"stateMutability":"nonpayable"},{"type":"function","name":"fsMetadata","inputs":[{"name":"path","type":"string","internalType":"string"}],"outputs":[{"name":"metadata","type":"tuple","internalType":"struct VmSafe.FsMetadata","components":[{"name":"isDir","type":"bool","internalType":"bool"},{"name":"isSymlink","type":"bool","internalType":"bool"},{"name":"length","type":"uint256","internalType":"uint256"},{"name":"readOnly","type":"bool","internalType":"bool"},{"name":"modified","type":"uint256","internalType":"uint256"},{"name":"accessed","type":"uint256","internalType":"uint256"},{"name":"created","type":"uint256","internalType":"uint256"}]}],"stateMutability":"view"},{"type":"function","name":"getBlockNumber","inputs":[],"outputs":[{"name":"height","type":"uint256","internalType":"uint256"}],"stateMutability":"view"},{"type":"function","name":"getBlockTimestamp","inputs":[],"outputs":[{"name":"timestamp","type":"uint256","internalType":"uint256"}],"stateMutability":"view"},{"type":"function","name":"getCode","inputs":[{"name":"artifactPath","type":"string","internalType":"string"}],"outputs":[{"name":"creationBytecode","type":"bytes","internalType":"bytes"}],"stateMutability":"view"},{"type":"function","name":"getDeployedCode","inputs":[{"name":"artifactPath","type":"string","internalType":"string"}],"outputs":[{"name":"runtimeBytecode","type":"bytes","internalType":"bytes"}],"stateMutability":"view"},{"type":"function","name":"getLabel","inputs":[{"name":"account","type":"address","internalType":"address"}],"outputs":[{"name":"currentLabel","type":"string","internalType":"string"}],"stateMutability":"view"},{"type":"function","name":"getMappingKeyAndParentOf","inputs":[{"name":"target","type":"address","internalType":"address"},{"name":"elementSlot","type":"bytes32","internalType":"bytes32"}],"outputs":[{"name":"found","type":"bool","internalType":"bool"},{"name":"key","type":"bytes32","internalType":"bytes32"},{"name":"parent","type":"bytes32","internalType":"bytes32"}],"stateMutability":"nonpayable"},{"type":"function","name":"getMappingLength","inputs":[{"name":"target","type":"address","internalType":"address"},{"name":"mappingSlot","type":"bytes32","internalType":"bytes32"}],"outputs":[{"name":"length","type":"uint256","internalType":"uint256"}],"stateMutability":"nonpayable"},{"type":"function","name":"getMappingSlotAt","inputs":[{"name":"target","type":"address","internalType":"address"},{"name":"mappingSlot","type":"bytes32","internalType":"bytes32"},{"name":"idx","type":"uint256","internalType":"uint256"}],"outputs":[{"name":"value","type":"bytes32","internalType":"bytes32"}],"stateMutability":"nonpayable"},{"type":"function","name":"getNonce","inputs":[{"name":"account","type":"address","internalType":"address"}],"outputs":[{"name":"nonce","type":"uint64","internalType":"uint64"}],"stateMutability":"view"},{"type":"function","name":"getNonce","inputs":[{"name":"wallet","type":"tuple","internalType":"struct VmSafe.Wallet","components":[{"name":"addr","type":"address","internalType":"address"},{"name":"publicKeyX","type":"uint256","internalType":"uint256"},{"name":"publicKeyY","type":"uint256","internalType":"uint256"},{"name":"privateKey","type":"uint256","internalType":"uint256"}]}],"outputs":[{"name":"nonce","type":"uint64","internalType":"uint64"}],"stateMutability":"nonpayable"},{"type":"function","name":"getRecordedLogs","inputs":[],"outputs":[{"name":"logs","type":"tuple[]","internalType":"struct VmSafe.Log[]","components":[{"name":"topics","type":"bytes32[]","internalType":"bytes32[]"},{"name":"data","type":"bytes","internalType":"bytes"},{"name":"emitter","type":"address","internalType":"address"}]}],"stateMutability":"nonpayable"},{"type":"function","name":"isDir","inputs":[{"name":"path","type":"string","internalType":"string"}],"outputs":[{"name":"result","type":"bool","internalType":"bool"}],"stateMutability":"nonpayable"},{"type":"function","name":"isFile","inputs":[{"name":"path","type":"string","internalType":"string"}],"outputs":[{"name":"result","type":"bool","internalType":"bool"}],"stateMutability":"nonpayable"},{"type":"function","name":"keyExists","inputs":[{"name":"json","type":"string","internalType":"string"},{"name":"key","type":"string","internalType":"string"}],"outputs":[{"name":"","type":"bool","internalType":"bool"}],"stateMutability":"view"},{"type":"function","name":"label","inputs":[{"name":"account","type":"address","internalType":"address"},{"name":"newLabel","type":"string","internalType":"string"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"load","inputs":[{"name":"target","type":"address","internalType":"address"},{"name":"slot","type":"bytes32","internalType":"bytes32"}],"outputs":[{"name":"data","type":"bytes32","internalType":"bytes32"}],"stateMutability":"view"},{"type":"function","name":"parseAddress","inputs":[{"name":"stringifiedValue","type":"string","internalType":"string"}],"outputs":[{"name":"parsedValue","type":"address","internalType":"address"}],"stateMutability":"pure"},{"type":"function","name":"parseBool","inputs":[{"name":"stringifiedValue","type":"string","internalType":"string"}],"outputs":[{"name":"parsedValue","type":"bool","internalType":"bool"}],"stateMutability":"pure"},{"type":"function","name":"parseBytes","inputs":[{"name":"stringifiedValue","type":"string","internalType":"string"}],"outputs":[{"name":"parsedValue","type":"bytes","internalType":"bytes"}],"stateMutability":"pure"},{"type":"function","name":"parseBytes32","inputs":[{"name":"stringifiedValue","type":"string","internalType":"string"}],"outputs":[{"name":"parsedValue","type":"bytes32","internalType":"bytes32"}],"stateMutability":"pure"},{"type":"function","name":"parseInt","inputs":[{"name":"stringifiedValue","type":"string","internalType":"string"}],"outputs":[{"name":"parsedValue","type":"int256","internalType":"int256"}],"stateMutability":"pure"},{"type":"function","name":"parseJson","inputs":[{"name":"json","type":"string","internalType":"string"}],"outputs":[{"name":"abiEncodedData","type":"bytes","internalType":"bytes"}],"stateMutability":"pure"},{"type":"function","name":"parseJson","inputs":[{"name":"json","type":"string","internalType":"string"},{"name":"key","type":"string","internalType":"string"}],"outputs":[{"name":"abiEncodedData","type":"bytes","internalType":"bytes"}],"stateMutability":"pure"},{"type":"function","name":"parseJsonAddress","inputs":[{"name":"json","type":"string","internalType":"string"},{"name":"key","type":"string","internalType":"string"}],"outputs":[{"name":"","type":"address","internalType":"address"}],"stateMutability":"pure"},{"type":"function","name":"parseJsonAddressArray","inputs":[{"name":"json","type":"string","internalType":"string"},{"name":"key","type":"string","internalType":"string"}],"outputs":[{"name":"","type":"address[]","internalType":"address[]"}],"stateMutability":"pure"},{"type":"function","name":"parseJsonBool","inputs":[{"name":"json","type":"string","internalType":"string"},{"name":"key","type":"string","internalType":"string"}],"outputs":[{"name":"","type":"bool","internalType":"bool"}],"stateMutability":"pure"},{"type":"function","name":"parseJsonBoolArray","inputs":[{"name":"json","type":"string","internalType":"string"},{"name":"key","type":"string","internalType":"string"}],"outputs":[{"name":"","type":"bool[]","internalType":"bool[]"}],"stateMutability":"pure"},{"type":"function","name":"parseJsonBytes","inputs":[{"name":"json","type":"string","internalType":"string"},{"name":"key","type":"string","internalType":"string"}],"outputs":[{"name":"","type":"bytes","internalType":"bytes"}],"stateMutability":"pure"},{"type":"function","name":"parseJsonBytes32","inputs":[{"name":"json","type":"string","internalType":"string"},{"name":"key","type":"string","internalType":"string"}],"outputs":[{"name":"","type":"bytes32","internalType":"bytes32"}],"stateMutability":"pure"},{"type":"function","name":"parseJsonBytes32Array","inputs":[{"name":"json","type":"string","internalType":"string"},{"name":"key","type":"string","internalType":"string"}],"outputs":[{"name":"","type":"bytes32[]","internalType":"bytes32[]"}],"stateMutability":"pure"},{"type":"function","name":"parseJsonBytesArray","inputs":[{"name":"json","type":"string","internalType":"string"},{"name":"key","type":"string","internalType":"string"}],"outputs":[{"name":"","type":"bytes[]","internalType":"bytes[]"}],"stateMutability":"pure"},{"type":"function","name":"parseJsonInt","inputs":[{"name":"json","type":"string","internalType":"string"},{"name":"key","type":"string","internalType":"string"}],"outputs":[{"name":"","type":"int256","internalType":"int256"}],"stateMutability":"pure"},{"type":"function","name":"parseJsonIntArray","inputs":[{"name":"json","type":"string","internalType":"string"},{"name":"key","type":"string","internalType":"string"}],"outputs":[{"name":"","type":"int256[]","internalType":"int256[]"}],"stateMutability":"pure"},{"type":"function","name":"parseJsonKeys","inputs":[{"name":"json","type":"string","internalType":"string"},{"name":"key","type":"string","internalType":"string"}],"outputs":[{"name":"keys","type":"string[]","internalType":"string[]"}],"stateMutability":"pure"},{"type":"function","name":"parseJsonString","inputs":[{"name":"json","type":"string","internalType":"string"},{"name":"key","type":"string","internalType":"string"}],"outputs":[{"name":"","type":"string","internalType":"string"}],"stateMutability":"pure"},{"type":"function","name":"parseJsonStringArray","inputs":[{"name":"json","type":"string","internalType":"string"},{"name":"key","type":"string","internalType":"string"}],"outputs":[{"name":"","type":"string[]","internalType":"string[]"}],"stateMutability":"pure"},{"type":"function","name":"parseJsonUint","inputs":[{"name":"json","type":"string","internalType":"string"},{"name":"key","type":"string","internalType":"string"}],"outputs":[{"name":"","type":"uint256","internalType":"uint256"}],"stateMutability":"pure"},{"type":"function","name":"parseJsonUintArray","inputs":[{"name":"json","type":"string","internalType":"string"},{"name":"key","type":"string","internalType":"string"}],"outputs":[{"name":"","type":"uint256[]","internalType":"uint256[]"}],"stateMutability":"pure"},{"type":"function","name":"parseUint","inputs":[{"name":"stringifiedValue","type":"string","internalType":"string"}],"outputs":[{"name":"parsedValue","type":"uint256","internalType":"uint256"}],"stateMutability":"pure"},{"type":"function","name":"pauseGasMetering","inputs":[],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"projectRoot","inputs":[],"outputs":[{"name":"path","type":"string","internalType":"string"}],"stateMutability":"view"},{"type":"function","name":"readDir","inputs":[{"name":"path","type":"string","internalType":"string"},{"name":"maxDepth","type":"uint64","internalType":"uint64"}],"outputs":[{"name":"entries","type":"tuple[]","internalType":"struct VmSafe.DirEntry[]","components":[{"name":"errorMessage","type":"string","internalType":"string"},{"name":"path","type":"string","internalType":"string"},{"name":"depth","type":"uint64","internalType":"uint64"},{"name":"isDir","type":"bool","internalType":"bool"},{"name":"isSymlink","type":"bool","internalType":"bool"}]}],"stateMutability":"view"},{"type":"function","name":"readDir","inputs":[{"name":"path","type":"string","internalType":"string"},{"name":"maxDepth","type":"uint64","internalType":"uint64"},{"name":"followLinks","type":"bool","internalType":"bool"}],"outputs":[{"name":"entries","type":"tuple[]","internalType":"struct VmSafe.DirEntry[]","components":[{"name":"errorMessage","type":"string","internalType":"string"},{"name":"path","type":"string","internalType":"string"},{"name":"depth","type":"uint64","internalType":"uint64"},{"name":"isDir","type":"bool","internalType":"bool"},{"name":"isSymlink","type":"bool","internalType":"bool"}]}],"stateMutability":"view"},{"type":"function","name":"readDir","inputs":[{"name":"path","type":"string","internalType":"string"}],"outputs":[{"name":"entries","type":"tuple[]","internalType":"struct VmSafe.DirEntry[]","components":[{"name":"errorMessage","type":"string","internalType":"string"},{"name":"path","type":"string","internalType":"string"},{"name":"depth","type":"uint64","internalType":"uint64"},{"name":"isDir","type":"bool","internalType":"bool"},{"name":"isSymlink","type":"bool","internalType":"bool"}]}],"stateMutability":"view"},{"type":"function","name":"readFile","inputs":[{"name":"path","type":"string","internalType":"string"}],"outputs":[{"name":"data","type":"string","internalType":"string"}],"stateMutability":"view"},{"type":"function","name":"readFileBinary","inputs":[{"name":"path","type":"string","internalType":"string"}],"outputs":[{"name":"data","type":"bytes","internalType":"bytes"}],"stateMutability":"view"},{"type":"function","name":"readLine","inputs":[{"name":"path","type":"string","internalType":"string"}],"outputs":[{"name":"line","type":"string","internalType":"string"}],"stateMutability":"view"},{"type":"function","name":"readLink","inputs":[{"name":"linkPath","type":"string","internalType":"string"}],"outputs":[{"name":"targetPath","type":"string","internalType":"string"}],"stateMutability":"view"},{"type":"function","name":"record","inputs":[],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"recordLogs","inputs":[],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"rememberKey","inputs":[{"name":"privateKey","type":"uint256","internalType":"uint256"}],"outputs":[{"name":"keyAddr","type":"address","internalType":"address"}],"stateMutability":"nonpayable"},{"type":"function","name":"removeDir","inputs":[{"name":"path","type":"string","internalType":"string"},{"name":"recursive","type":"bool","internalType":"bool"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"removeFile","inputs":[{"name":"path","type":"string","internalType":"string"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"resumeGasMetering","inputs":[],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"rpc","inputs":[{"name":"method","type":"string","internalType":"string"},{"name":"params","type":"string","internalType":"string"}],"outputs":[{"name":"data","type":"bytes","internalType":"bytes"}],"stateMutability":"nonpayable"},{"type":"function","name":"rpcUrl","inputs":[{"name":"rpcAlias","type":"string","internalType":"string"}],"outputs":[{"name":"json","type":"string","internalType":"string"}],"stateMutability":"view"},{"type":"function","name":"rpcUrlStructs","inputs":[],"outputs":[{"name":"urls","type":"tuple[]","internalType":"struct VmSafe.Rpc[]","components":[{"name":"key","type":"string","internalType":"string"},{"name":"url","type":"string","internalType":"string"}]}],"stateMutability":"view"},{"type":"function","name":"rpcUrls","inputs":[],"outputs":[{"name":"urls","type":"string[2][]","internalType":"string[2][]"}],"stateMutability":"view"},{"type":"function","name":"serializeAddress","inputs":[{"name":"objectKey","type":"string","internalType":"string"},{"name":"valueKey","type":"string","internalType":"string"},{"name":"values","type":"address[]","internalType":"address[]"}],"outputs":[{"name":"json","type":"string","internalType":"string"}],"stateMutability":"nonpayable"},{"type":"function","name":"serializeAddress","inputs":[{"name":"objectKey","type":"string","internalType":"string"},{"name":"valueKey","type":"string","internalType":"string"},{"name":"value","type":"address","internalType":"address"}],"outputs":[{"name":"json","type":"string","internalType":"string"}],"stateMutability":"nonpayable"},{"type":"function","name":"serializeBool","inputs":[{"name":"objectKey","type":"string","internalType":"string"},{"name":"valueKey","type":"string","internalType":"string"},{"name":"values","type":"bool[]","internalType":"bool[]"}],"outputs":[{"name":"json","type":"string","internalType":"string"}],"stateMutability":"nonpayable"},{"type":"function","name":"serializeBool","inputs":[{"name":"objectKey","type":"string","internalType":"string"},{"name":"valueKey","type":"string","internalType":"string"},{"name":"value","type":"bool","internalType":"bool"}],"outputs":[{"name":"json","type":"string","internalType":"string"}],"stateMutability":"nonpayable"},{"type":"function","name":"serializeBytes","inputs":[{"name":"objectKey","type":"string","internalType":"string"},{"name":"valueKey","type":"string","internalType":"string"},{"name":"values","type":"bytes[]","internalType":"bytes[]"}],"outputs":[{"name":"json","type":"string","internalType":"string"}],"stateMutability":"nonpayable"},{"type":"function","name":"serializeBytes","inputs":[{"name":"objectKey","type":"string","internalType":"string"},{"name":"valueKey","type":"string","internalType":"string"},{"name":"value","type":"bytes","internalType":"bytes"}],"outputs":[{"name":"json","type":"string","internalType":"string"}],"stateMutability":"nonpayable"},{"type":"function","name":"serializeBytes32","inputs":[{"name":"objectKey","type":"string","internalType":"string"},{"name":"valueKey","type":"string","internalType":"string"},{"name":"values","type":"bytes32[]","internalType":"bytes32[]"}],"outputs":[{"name":"json","type":"string","internalType":"string"}],"stateMutability":"nonpayable"},{"type":"function","name":"serializeBytes32","inputs":[{"name":"objectKey","type":"string","internalType":"string"},{"name":"valueKey","type":"string","internalType":"string"},{"name":"value","type":"bytes32","internalType":"bytes32"}],"outputs":[{"name":"json","type":"string","internalType":"string"}],"stateMutability":"nonpayable"},{"type":"function","name":"serializeInt","inputs":[{"name":"objectKey","type":"string","internalType":"string"},{"name":"valueKey","type":"string","internalType":"string"},{"name":"value","type":"int256","internalType":"int256"}],"outputs":[{"name":"json","type":"string","internalType":"string"}],"stateMutability":"nonpayable"},{"type":"function","name":"serializeInt","inputs":[{"name":"objectKey","type":"string","internalType":"string"},{"name":"valueKey","type":"string","internalType":"string"},{"name":"values","type":"int256[]","internalType":"int256[]"}],"outputs":[{"name":"json","type":"string","internalType":"string"}],"stateMutability":"nonpayable"},{"type":"function","name":"serializeJson","inputs":[{"name":"objectKey","type":"string","internalType":"string"},{"name":"value","type":"string","internalType":"string"}],"outputs":[{"name":"json","type":"string","internalType":"string"}],"stateMutability":"nonpayable"},{"type":"function","name":"serializeString","inputs":[{"name":"objectKey","type":"string","internalType":"string"},{"name":"valueKey","type":"string","internalType":"string"},{"name":"values","type":"string[]","internalType":"string[]"}],"outputs":[{"name":"json","type":"string","internalType":"string"}],"stateMutability":"nonpayable"},{"type":"function","name":"serializeString","inputs":[{"name":"objectKey","type":"string","internalType":"string"},{"name":"valueKey","type":"string","internalType":"string"},{"name":"value","type":"string","internalType":"string"}],"outputs":[{"name":"json","type":"string","internalType":"string"}],"stateMutability":"nonpayable"},{"type":"function","name":"serializeUint","inputs":[{"name":"objectKey","type":"string","internalType":"string"},{"name":"valueKey","type":"string","internalType":"string"},{"name":"value","type":"uint256","internalType":"uint256"}],"outputs":[{"name":"json","type":"string","internalType":"string"}],"stateMutability":"nonpayable"},{"type":"function","name":"serializeUint","inputs":[{"name":"objectKey","type":"string","internalType":"string"},{"name":"valueKey","type":"string","internalType":"string"},{"name":"values","type":"uint256[]","internalType":"uint256[]"}],"outputs":[{"name":"json","type":"string","internalType":"string"}],"stateMutability":"nonpayable"},{"type":"function","name":"setEnv","inputs":[{"name":"name","type":"string","internalType":"string"},{"name":"value","type":"string","internalType":"string"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"sign","inputs":[{"name":"wallet","type":"tuple","internalType":"struct VmSafe.Wallet","components":[{"name":"addr","type":"address","internalType":"address"},{"name":"publicKeyX","type":"uint256","internalType":"uint256"},{"name":"publicKeyY","type":"uint256","internalType":"uint256"},{"name":"privateKey","type":"uint256","internalType":"uint256"}]},{"name":"digest","type":"bytes32","internalType":"bytes32"}],"outputs":[{"name":"v","type":"uint8","internalType":"uint8"},{"name":"r","type":"bytes32","internalType":"bytes32"},{"name":"s","type":"bytes32","internalType":"bytes32"}],"stateMutability":"nonpayable"},{"type":"function","name":"sign","inputs":[{"name":"privateKey","type":"uint256","internalType":"uint256"},{"name":"digest","type":"bytes32","internalType":"bytes32"}],"outputs":[{"name":"v","type":"uint8","internalType":"uint8"},{"name":"r","type":"bytes32","internalType":"bytes32"},{"name":"s","type":"bytes32","internalType":"bytes32"}],"stateMutability":"pure"},{"type":"function","name":"signP256","inputs":[{"name":"privateKey","type":"uint256","internalType":"uint256"},{"name":"digest","type":"bytes32","internalType":"bytes32"}],"outputs":[{"name":"r","type":"bytes32","internalType":"bytes32"},{"name":"s","type":"bytes32","internalType":"bytes32"}],"stateMutability":"pure"},{"type":"function","name":"sleep","inputs":[{"name":"duration","type":"uint256","internalType":"uint256"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"startBroadcast","inputs":[],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"startBroadcast","inputs":[{"name":"signer","type":"address","internalType":"address"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"startBroadcast","inputs":[{"name":"privateKey","type":"uint256","internalType":"uint256"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"startMappingRecording","inputs":[],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"startStateDiffRecording","inputs":[],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"stopAndReturnStateDiff","inputs":[],"outputs":[{"name":"accountAccesses","type":"tuple[]","internalType":"struct VmSafe.AccountAccess[]","components":[{"name":"chainInfo","type":"tuple","internalType":"struct VmSafe.ChainInfo","components":[{"name":"forkId","type":"uint256","internalType":"uint256"},{"name":"chainId","type":"uint256","internalType":"uint256"}]},{"name":"kind","type":"uint8","internalType":"enum VmSafe.AccountAccessKind"},{"name":"account","type":"address","internalType":"address"},{"name":"accessor","type":"address","internalType":"address"},{"name":"initialized","type":"bool","internalType":"bool"},{"name":"oldBalance","type":"uint256","internalType":"uint256"},{"name":"newBalance","type":"uint256","internalType":"uint256"},{"name":"deployedCode","type":"bytes","internalType":"bytes"},{"name":"value","type":"uint256","internalType":"uint256"},{"name":"data","type":"bytes","internalType":"bytes"},{"name":"reverted","type":"bool","internalType":"bool"},{"name":"storageAccesses","type":"tuple[]","internalType":"struct VmSafe.StorageAccess[]","components":[{"name":"account","type":"address","internalType":"address"},{"name":"slot","type":"bytes32","internalType":"bytes32"},{"name":"isWrite","type":"bool","internalType":"bool"},{"name":"previousValue","type":"bytes32","internalType":"bytes32"},{"name":"newValue","type":"bytes32","internalType":"bytes32"},{"name":"reverted","type":"bool","internalType":"bool"}]}]}],"stateMutability":"nonpayable"},{"type":"function","name":"stopBroadcast","inputs":[],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"stopMappingRecording","inputs":[],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"toBase64","inputs":[{"name":"data","type":"string","internalType":"string"}],"outputs":[{"name":"","type":"string","internalType":"string"}],"stateMutability":"pure"},{"type":"function","name":"toBase64","inputs":[{"name":"data","type":"bytes","internalType":"bytes"}],"outputs":[{"name":"","type":"string","internalType":"string"}],"stateMutability":"pure"},{"type":"function","name":"toBase64URL","inputs":[{"name":"data","type":"string","internalType":"string"}],"outputs":[{"name":"","type":"string","internalType":"string"}],"stateMutability":"pure"},{"type":"function","name":"toBase64URL","inputs":[{"name":"data","type":"bytes","internalType":"bytes"}],"outputs":[{"name":"","type":"string","internalType":"string"}],"stateMutability":"pure"},{"type":"function","name":"toString","inputs":[{"name":"value","type":"address","internalType":"address"}],"outputs":[{"name":"stringifiedValue","type":"string","internalType":"string"}],"stateMutability":"pure"},{"type":"function","name":"toString","inputs":[{"name":"value","type":"uint256","internalType":"uint256"}],"outputs":[{"name":"stringifiedValue","type":"string","internalType":"string"}],"stateMutability":"pure"},{"type":"function","name":"toString","inputs":[{"name":"value","type":"bytes","internalType":"bytes"}],"outputs":[{"name":"stringifiedValue","type":"string","internalType":"string"}],"stateMutability":"pure"},{"type":"function","name":"toString","inputs":[{"name":"value","type":"bool","internalType":"bool"}],"outputs":[{"name":"stringifiedValue","type":"string","internalType":"string"}],"stateMutability":"pure"},{"type":"function","name":"toString","inputs":[{"name":"value","type":"int256","internalType":"int256"}],"outputs":[{"name":"stringifiedValue","type":"string","internalType":"string"}],"stateMutability":"pure"},{"type":"function","name":"toString","inputs":[{"name":"value","type":"bytes32","internalType":"bytes32"}],"outputs":[{"name":"stringifiedValue","type":"string","internalType":"string"}],"stateMutability":"pure"},{"type":"function","name":"tryFfi","inputs":[{"name":"commandInput","type":"string[]","internalType":"string[]"}],"outputs":[{"name":"result","type":"tuple","internalType":"struct VmSafe.FfiResult","components":[{"name":"exitCode","type":"int32","internalType":"int32"},{"name":"stdout","type":"bytes","internalType":"bytes"},{"name":"stderr","type":"bytes","internalType":"bytes"}]}],"stateMutability":"nonpayable"},{"type":"function","name":"unixTime","inputs":[],"outputs":[{"name":"milliseconds","type":"uint256","internalType":"uint256"}],"stateMutability":"nonpayable"},{"type":"function","name":"writeFile","inputs":[{"name":"path","type":"string","internalType":"string"},{"name":"data","type":"string","internalType":"string"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"writeFileBinary","inputs":[{"name":"path","type":"string","internalType":"string"},{"name":"data","type":"bytes","internalType":"bytes"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"writeJson","inputs":[{"name":"json","type":"string","internalType":"string"},{"name":"path","type":"string","internalType":"string"},{"name":"valueKey","type":"string","internalType":"string"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"writeJson","inputs":[{"name":"json","type":"string","internalType":"string"},{"name":"path","type":"string","internalType":"string"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"writeLine","inputs":[{"name":"path","type":"string","internalType":"string"},{"name":"data","type":"string","internalType":"string"}],"outputs":[],"stateMutability":"nonpayable"}],"abiPath":"lib/forge-std/src/Vm.sol"},{"name":"StdInvariant","abi":[{"type":"function","name":"excludeArtifacts","inputs":[],"outputs":[{"name":"excludedArtifacts_","type":"string[]","internalType":"string[]"}],"stateMutability":"view"},{"type":"function","name":"excludeContracts","inputs":[],"outputs":[{"name":"excludedContracts_","type":"address[]","internalType":"address[]"}],"stateMutability":"view"},{"type":"function","name":"excludeSenders","inputs":[],"outputs":[{"name":"excludedSenders_","type":"address[]","internalType":"address[]"}],"stateMutability":"view"},{"type":"function","name":"targetArtifactSelectors","inputs":[],"outputs":[{"name":"targetedArtifactSelectors_","type":"tuple[]","internalType":"struct StdInvariant.FuzzSelector[]","components":[{"name":"addr","type":"address","internalType":"address"},{"name":"selectors","type":"bytes4[]","internalType":"bytes4[]"}]}],"stateMutability":"view"},{"type":"function","name":"targetArtifacts","inputs":[],"outputs":[{"name":"targetedArtifacts_","type":"string[]","internalType":"string[]"}],"stateMutability":"view"},{"type":"function","name":"targetContracts","inputs":[],"outputs":[{"name":"targetedContracts_","type":"address[]","internalType":"address[]"}],"stateMutability":"view"},{"type":"function","name":"targetInterfaces","inputs":[],"outputs":[{"name":"targetedInterfaces_","type":"tuple[]","internalType":"struct StdInvariant.FuzzInterface[]","components":[{"name":"addr","type":"address","internalType":"address"},{"name":"artifacts","type":"string[]","internalType":"string[]"}]}],"stateMutability":"view"},{"type":"function","name":"targetSelectors","inputs":[],"outputs":[{"name":"targetedSelectors_","type":"tuple[]","internalType":"struct StdInvariant.FuzzSelector[]","components":[{"name":"addr","type":"address","internalType":"address"},{"name":"selectors","type":"bytes4[]","internalType":"bytes4[]"}]}],"stateMutability":"view"},{"type":"function","name":"targetSenders","inputs":[],"outputs":[{"name":"targetedSenders_","type":"address[]","internalType":"address[]"}],"stateMutability":"view"}],"abiPath":"lib/forge-std/src/StdInvariant.sol"},{"name":"stdStorageSafe","abi":[{"type":"event","name":"SlotFound","inputs":[{"name":"who","type":"address","indexed":false,"internalType":"address"},{"name":"fsig","type":"bytes4","indexed":false,"internalType":"bytes4"},{"name":"keysHash","type":"bytes32","indexed":false,"internalType":"bytes32"},{"name":"slot","type":"uint256","indexed":false,"internalType":"uint256"}],"anonymous":false},{"type":"event","name":"WARNING_UninitedSlot","inputs":[{"name":"who","type":"address","indexed":false,"internalType":"address"},{"name":"slot","type":"uint256","indexed":false,"internalType":"uint256"}],"anonymous":false}],"abiPath":"lib/forge-std/src/StdStorage.sol"},{"name":"Counter","abi":[{"type":"function","name":"increment","inputs":[],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"number","inputs":[],"outputs":[{"name":"","type":"uint128","internalType":"uint128"}],"stateMutability":"view"},{"type":"function","name":"setNumber","inputs":[{"name":"newNumber","type":"uint64","internalType":"uint64"}],"outputs":[],"stateMutability":"nonpayable"}],"abiPath":"src/Counter.sol"},{"name":"CryticAsserts","abi":[{"type":"event","name":"Log","inputs":[{"name":"","type":"string","indexed":false,"internalType":"string"}],"anonymous":false}],"abiPath":"lib/chimera/src/CryticAsserts.sol"},{"name":"IHevm","abi":[{"type":"function","name":"addr","inputs":[{"name":"privateKey","type":"uint256","internalType":"uint256"}],"outputs":[{"name":"account","type":"address","internalType":"address"}],"stateMutability":"nonpayable"},{"type":"function","name":"ffi","inputs":[{"name":"inputs","type":"string[]","internalType":"string[]"}],"outputs":[{"name":"result","type":"bytes","internalType":"bytes"}],"stateMutability":"nonpayable"},{"type":"function","name":"load","inputs":[{"name":"where","type":"address","internalType":"address"},{"name":"slot","type":"bytes32","internalType":"bytes32"}],"outputs":[{"name":"","type":"bytes32","internalType":"bytes32"}],"stateMutability":"nonpayable"},{"type":"function","name":"prank","inputs":[{"name":"newSender","type":"address","internalType":"address"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"roll","inputs":[{"name":"newNumber","type":"uint256","internalType":"uint256"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"sign","inputs":[{"name":"privateKey","type":"uint256","internalType":"uint256"},{"name":"digest","type":"bytes32","internalType":"bytes32"}],"outputs":[{"name":"r","type":"uint8","internalType":"uint8"},{"name":"v","type":"bytes32","internalType":"bytes32"},{"name":"s","type":"bytes32","internalType":"bytes32"}],"stateMutability":"nonpayable"},{"type":"function","name":"store","inputs":[{"name":"where","type":"address","internalType":"address"},{"name":"slot","type":"bytes32","internalType":"bytes32"},{"name":"value","type":"bytes32","internalType":"bytes32"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"warp","inputs":[{"name":"newTimestamp","type":"uint256","internalType":"uint256"}],"outputs":[],"stateMutability":"nonpayable"}],"abiPath":"lib/chimera/src/Hevm.sol"},{"name":"Test","abi":[{"type":"function","name":"IS_TEST","inputs":[],"outputs":[{"name":"","type":"bool","internalType":"bool"}],"stateMutability":"view"},{"type":"function","name":"excludeArtifacts","inputs":[],"outputs":[{"name":"excludedArtifacts_","type":"string[]","internalType":"string[]"}],"stateMutability":"view"},{"type":"function","name":"excludeContracts","inputs":[],"outputs":[{"name":"excludedContracts_","type":"address[]","internalType":"address[]"}],"stateMutability":"view"},{"type":"function","name":"excludeSenders","inputs":[],"outputs":[{"name":"excludedSenders_","type":"address[]","internalType":"address[]"}],"stateMutability":"view"},{"type":"function","name":"failed","inputs":[],"outputs":[{"name":"","type":"bool","internalType":"bool"}],"stateMutability":"nonpayable"},{"type":"function","name":"targetArtifactSelectors","inputs":[],"outputs":[{"name":"targetedArtifactSelectors_","type":"tuple[]","internalType":"struct StdInvariant.FuzzSelector[]","components":[{"name":"addr","type":"address","internalType":"address"},{"name":"selectors","type":"bytes4[]","internalType":"bytes4[]"}]}],"stateMutability":"view"},{"type":"function","name":"targetArtifacts","inputs":[],"outputs":[{"name":"targetedArtifacts_","type":"string[]","internalType":"string[]"}],"stateMutability":"view"},{"type":"function","name":"targetContracts","inputs":[],"outputs":[{"name":"targetedContracts_","type":"address[]","internalType":"address[]"}],"stateMutability":"view"},{"type":"function","name":"targetInterfaces","inputs":[],"outputs":[{"name":"targetedInterfaces_","type":"tuple[]","internalType":"struct StdInvariant.FuzzInterface[]","components":[{"name":"addr","type":"address","internalType":"address"},{"name":"artifacts","type":"string[]","internalType":"string[]"}]}],"stateMutability":"view"},{"type":"function","name":"targetSelectors","inputs":[],"outputs":[{"name":"targetedSelectors_","type":"tuple[]","internalType":"struct StdInvariant.FuzzSelector[]","components":[{"name":"addr","type":"address","internalType":"address"},{"name":"selectors","type":"bytes4[]","internalType":"bytes4[]"}]}],"stateMutability":"view"},{"type":"function","name":"targetSenders","inputs":[],"outputs":[{"name":"targetedSenders_","type":"address[]","internalType":"address[]"}],"stateMutability":"view"},{"type":"event","name":"log","inputs":[{"name":"","type":"string","indexed":false,"internalType":"string"}],"anonymous":false},{"type":"event","name":"log_address","inputs":[{"name":"","type":"address","indexed":false,"internalType":"address"}],"anonymous":false},{"type":"event","name":"log_array","inputs":[{"name":"val","type":"uint256[]","indexed":false,"internalType":"uint256[]"}],"anonymous":false},{"type":"event","name":"log_array","inputs":[{"name":"val","type":"int256[]","indexed":false,"internalType":"int256[]"}],"anonymous":false},{"type":"event","name":"log_array","inputs":[{"name":"val","type":"address[]","indexed":false,"internalType":"address[]"}],"anonymous":false},{"type":"event","name":"log_bytes","inputs":[{"name":"","type":"bytes","indexed":false,"internalType":"bytes"}],"anonymous":false},{"type":"event","name":"log_bytes32","inputs":[{"name":"","type":"bytes32","indexed":false,"internalType":"bytes32"}],"anonymous":false},{"type":"event","name":"log_int","inputs":[{"name":"","type":"int256","indexed":false,"internalType":"int256"}],"anonymous":false},{"type":"event","name":"log_named_address","inputs":[{"name":"key","type":"string","indexed":false,"internalType":"string"},{"name":"val","type":"address","indexed":false,"internalType":"address"}],"anonymous":false},{"type":"event","name":"log_named_array","inputs":[{"name":"key","type":"string","indexed":false,"internalType":"string"},{"name":"val","type":"uint256[]","indexed":false,"internalType":"uint256[]"}],"anonymous":false},{"type":"event","name":"log_named_array","inputs":[{"name":"key","type":"string","indexed":false,"internalType":"string"},{"name":"val","type":"int256[]","indexed":false,"internalType":"int256[]"}],"anonymous":false},{"type":"event","name":"log_named_array","inputs":[{"name":"key","type":"string","indexed":false,"internalType":"string"},{"name":"val","type":"address[]","indexed":false,"internalType":"address[]"}],"anonymous":false},{"type":"event","name":"log_named_bytes","inputs":[{"name":"key","type":"string","indexed":false,"internalType":"string"},{"name":"val","type":"bytes","indexed":false,"internalType":"bytes"}],"anonymous":false},{"type":"event","name":"log_named_bytes32","inputs":[{"name":"key","type":"string","indexed":false,"internalType":"string"},{"name":"val","type":"bytes32","indexed":false,"internalType":"bytes32"}],"anonymous":false},{"type":"event","name":"log_named_decimal_int","inputs":[{"name":"key","type":"string","indexed":false,"internalType":"string"},{"name":"val","type":"int256","indexed":false,"internalType":"int256"},{"name":"decimals","type":"uint256","indexed":false,"internalType":"uint256"}],"anonymous":false},{"type":"event","name":"log_named_decimal_uint","inputs":[{"name":"key","type":"string","indexed":false,"internalType":"string"},{"name":"val","type":"uint256","indexed":false,"internalType":"uint256"},{"name":"decimals","type":"uint256","indexed":false,"internalType":"uint256"}],"anonymous":false},{"type":"event","name":"log_named_int","inputs":[{"name":"key","type":"string","indexed":false,"internalType":"string"},{"name":"val","type":"int256","indexed":false,"internalType":"int256"}],"anonymous":false},{"type":"event","name":"log_named_string","inputs":[{"name":"key","type":"string","indexed":false,"internalType":"string"},{"name":"val","type":"string","indexed":false,"internalType":"string"}],"anonymous":false},{"type":"event","name":"log_named_uint","inputs":[{"name":"key","type":"string","indexed":false,"internalType":"string"},{"name":"val","type":"uint256","indexed":false,"internalType":"uint256"}],"anonymous":false},{"type":"event","name":"log_string","inputs":[{"name":"","type":"string","indexed":false,"internalType":"string"}],"anonymous":false},{"type":"event","name":"log_uint","inputs":[{"name":"","type":"uint256","indexed":false,"internalType":"uint256"}],"anonymous":false},{"type":"event","name":"logs","inputs":[{"name":"","type":"bytes","indexed":false,"internalType":"bytes"}],"anonymous":false}],"abiPath":"lib/forge-std/src/Test.sol"},{"name":"DSTest","abi":[{"type":"function","name":"IS_TEST","inputs":[],"outputs":[{"name":"","type":"bool","internalType":"bool"}],"stateMutability":"view"},{"type":"function","name":"failed","inputs":[],"outputs":[{"name":"","type":"bool","internalType":"bool"}],"stateMutability":"nonpayable"},{"type":"event","name":"log","inputs":[{"name":"","type":"string","indexed":false,"internalType":"string"}],"anonymous":false},{"type":"event","name":"log_address","inputs":[{"name":"","type":"address","indexed":false,"internalType":"address"}],"anonymous":false},{"type":"event","name":"log_bytes","inputs":[{"name":"","type":"bytes","indexed":false,"internalType":"bytes"}],"anonymous":false},{"type":"event","name":"log_bytes32","inputs":[{"name":"","type":"bytes32","indexed":false,"internalType":"bytes32"}],"anonymous":false},{"type":"event","name":"log_int","inputs":[{"name":"","type":"int256","indexed":false,"internalType":"int256"}],"anonymous":false},{"type":"event","name":"log_named_address","inputs":[{"name":"key","type":"string","indexed":false,"internalType":"string"},{"name":"val","type":"address","indexed":false,"internalType":"address"}],"anonymous":false},{"type":"event","name":"log_named_bytes","inputs":[{"name":"key","type":"string","indexed":false,"internalType":"string"},{"name":"val","type":"bytes","indexed":false,"internalType":"bytes"}],"anonymous":false},{"type":"event","name":"log_named_bytes32","inputs":[{"name":"key","type":"string","indexed":false,"internalType":"string"},{"name":"val","type":"bytes32","indexed":false,"internalType":"bytes32"}],"anonymous":false},{"type":"event","name":"log_named_decimal_int","inputs":[{"name":"key","type":"string","indexed":false,"internalType":"string"},{"name":"val","type":"int256","indexed":false,"internalType":"int256"},{"name":"decimals","type":"uint256","indexed":false,"internalType":"uint256"}],"anonymous":false},{"type":"event","name":"log_named_decimal_uint","inputs":[{"name":"key","type":"string","indexed":false,"internalType":"string"},{"name":"val","type":"uint256","indexed":false,"internalType":"uint256"},{"name":"decimals","type":"uint256","indexed":false,"internalType":"uint256"}],"anonymous":false},{"type":"event","name":"log_named_int","inputs":[{"name":"key","type":"string","indexed":false,"internalType":"string"},{"name":"val","type":"int256","indexed":false,"internalType":"int256"}],"anonymous":false},{"type":"event","name":"log_named_string","inputs":[{"name":"key","type":"string","indexed":false,"internalType":"string"},{"name":"val","type":"string","indexed":false,"internalType":"string"}],"anonymous":false},{"type":"event","name":"log_named_uint","inputs":[{"name":"key","type":"string","indexed":false,"internalType":"string"},{"name":"val","type":"uint256","indexed":false,"internalType":"uint256"}],"anonymous":false},{"type":"event","name":"log_string","inputs":[{"name":"","type":"string","indexed":false,"internalType":"string"}],"anonymous":false},{"type":"event","name":"log_uint","inputs":[{"name":"","type":"uint256","indexed":false,"internalType":"uint256"}],"anonymous":false},{"type":"event","name":"logs","inputs":[{"name":"","type":"bytes","indexed":false,"internalType":"bytes"}],"anonymous":false}],"abiPath":"lib/forge-std/lib/ds-test/src/test.sol"},{"name":"CryticTester","abi":[{"type":"constructor","inputs":[],"stateMutability":"payable"},{"type":"function","name":"counter_increment","inputs":[],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"counter_setNumber","inputs":[{"name":"newNumber","type":"uint64","internalType":"uint64"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"event","name":"Log","inputs":[{"name":"","type":"string","indexed":false,"internalType":"string"}],"anonymous":false}],"abiPath":"test/recon/CryticTester.sol"},{"name":"StdAssertions","abi":[{"type":"function","name":"IS_TEST","inputs":[],"outputs":[{"name":"","type":"bool","internalType":"bool"}],"stateMutability":"view"},{"type":"function","name":"failed","inputs":[],"outputs":[{"name":"","type":"bool","internalType":"bool"}],"stateMutability":"nonpayable"},{"type":"event","name":"log","inputs":[{"name":"","type":"string","indexed":false,"internalType":"string"}],"anonymous":false},{"type":"event","name":"log_address","inputs":[{"name":"","type":"address","indexed":false,"internalType":"address"}],"anonymous":false},{"type":"event","name":"log_array","inputs":[{"name":"val","type":"uint256[]","indexed":false,"internalType":"uint256[]"}],"anonymous":false},{"type":"event","name":"log_array","inputs":[{"name":"val","type":"int256[]","indexed":false,"internalType":"int256[]"}],"anonymous":false},{"type":"event","name":"log_array","inputs":[{"name":"val","type":"address[]","indexed":false,"internalType":"address[]"}],"anonymous":false},{"type":"event","name":"log_bytes","inputs":[{"name":"","type":"bytes","indexed":false,"internalType":"bytes"}],"anonymous":false},{"type":"event","name":"log_bytes32","inputs":[{"name":"","type":"bytes32","indexed":false,"internalType":"bytes32"}],"anonymous":false},{"type":"event","name":"log_int","inputs":[{"name":"","type":"int256","indexed":false,"internalType":"int256"}],"anonymous":false},{"type":"event","name":"log_named_address","inputs":[{"name":"key","type":"string","indexed":false,"internalType":"string"},{"name":"val","type":"address","indexed":false,"internalType":"address"}],"anonymous":false},{"type":"event","name":"log_named_array","inputs":[{"name":"key","type":"string","indexed":false,"internalType":"string"},{"name":"val","type":"uint256[]","indexed":false,"internalType":"uint256[]"}],"anonymous":false},{"type":"event","name":"log_named_array","inputs":[{"name":"key","type":"string","indexed":false,"internalType":"string"},{"name":"val","type":"int256[]","indexed":false,"internalType":"int256[]"}],"anonymous":false},{"type":"event","name":"log_named_array","inputs":[{"name":"key","type":"string","indexed":false,"internalType":"string"},{"name":"val","type":"address[]","indexed":false,"internalType":"address[]"}],"anonymous":false},{"type":"event","name":"log_named_bytes","inputs":[{"name":"key","type":"string","indexed":false,"internalType":"string"},{"name":"val","type":"bytes","indexed":false,"internalType":"bytes"}],"anonymous":false},{"type":"event","name":"log_named_bytes32","inputs":[{"name":"key","type":"string","indexed":false,"internalType":"string"},{"name":"val","type":"bytes32","indexed":false,"internalType":"bytes32"}],"anonymous":false},{"type":"event","name":"log_named_decimal_int","inputs":[{"name":"key","type":"string","indexed":false,"internalType":"string"},{"name":"val","type":"int256","indexed":false,"internalType":"int256"},{"name":"decimals","type":"uint256","indexed":false,"internalType":"uint256"}],"anonymous":false},{"type":"event","name":"log_named_decimal_uint","inputs":[{"name":"key","type":"string","indexed":false,"internalType":"string"},{"name":"val","type":"uint256","indexed":false,"internalType":"uint256"},{"name":"decimals","type":"uint256","indexed":false,"internalType":"uint256"}],"anonymous":false},{"type":"event","name":"log_named_int","inputs":[{"name":"key","type":"string","indexed":false,"internalType":"string"},{"name":"val","type":"int256","indexed":false,"internalType":"int256"}],"anonymous":false},{"type":"event","name":"log_named_string","inputs":[{"name":"key","type":"string","indexed":false,"internalType":"string"},{"name":"val","type":"string","indexed":false,"internalType":"string"}],"anonymous":false},{"type":"event","name":"log_named_uint","inputs":[{"name":"key","type":"string","indexed":false,"internalType":"string"},{"name":"val","type":"uint256","indexed":false,"internalType":"uint256"}],"anonymous":false},{"type":"event","name":"log_string","inputs":[{"name":"","type":"string","indexed":false,"internalType":"string"}],"anonymous":false},{"type":"event","name":"log_uint","inputs":[{"name":"","type":"uint256","indexed":false,"internalType":"uint256"}],"anonymous":false},{"type":"event","name":"logs","inputs":[{"name":"","type":"bytes","indexed":false,"internalType":"bytes"}],"anonymous":false}],"abiPath":"lib/forge-std/src/StdAssertions.sol"},{"name":"stdError","abi":[{"type":"function","name":"arithmeticError","inputs":[],"outputs":[{"name":"","type":"bytes","internalType":"bytes"}],"stateMutability":"view"},{"type":"function","name":"assertionError","inputs":[],"outputs":[{"name":"","type":"bytes","internalType":"bytes"}],"stateMutability":"view"},{"type":"function","name":"divisionError","inputs":[],"outputs":[{"name":"","type":"bytes","internalType":"bytes"}],"stateMutability":"view"},{"type":"function","name":"encodeStorageError","inputs":[],"outputs":[{"name":"","type":"bytes","internalType":"bytes"}],"stateMutability":"view"},{"type":"function","name":"enumConversionError","inputs":[],"outputs":[{"name":"","type":"bytes","internalType":"bytes"}],"stateMutability":"view"},{"type":"function","name":"indexOOBError","inputs":[],"outputs":[{"name":"","type":"bytes","internalType":"bytes"}],"stateMutability":"view"},{"type":"function","name":"memOverflowError","inputs":[],"outputs":[{"name":"","type":"bytes","internalType":"bytes"}],"stateMutability":"view"},{"type":"function","name":"popError","inputs":[],"outputs":[{"name":"","type":"bytes","internalType":"bytes"}],"stateMutability":"view"},{"type":"function","name":"zeroVarError","inputs":[],"outputs":[{"name":"","type":"bytes","internalType":"bytes"}],"stateMutability":"view"}],"abiPath":"lib/forge-std/src/StdError.sol"},{"name":"IERC721TokenReceiver","abi":[{"type":"function","name":"onERC721Received","inputs":[{"name":"","type":"address","internalType":"address"},{"name":"","type":"address","internalType":"address"},{"name":"","type":"uint256","internalType":"uint256"},{"name":"","type":"bytes","internalType":"bytes"}],"outputs":[{"name":"","type":"bytes4","internalType":"bytes4"}],"stateMutability":"nonpayable"}],"abiPath":"lib/forge-std/src/mocks/MockERC721.sol"},{"name":"MockERC721","abi":[{"type":"function","name":"approve","inputs":[{"name":"spender","type":"address","internalType":"address"},{"name":"id","type":"uint256","internalType":"uint256"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"balanceOf","inputs":[{"name":"owner","type":"address","internalType":"address"}],"outputs":[{"name":"","type":"uint256","internalType":"uint256"}],"stateMutability":"view"},{"type":"function","name":"getApproved","inputs":[{"name":"","type":"uint256","internalType":"uint256"}],"outputs":[{"name":"","type":"address","internalType":"address"}],"stateMutability":"view"},{"type":"function","name":"initialize","inputs":[{"name":"_name","type":"string","internalType":"string"},{"name":"_symbol","type":"string","internalType":"string"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"isApprovedForAll","inputs":[{"name":"","type":"address","internalType":"address"},{"name":"","type":"address","internalType":"address"}],"outputs":[{"name":"","type":"bool","internalType":"bool"}],"stateMutability":"view"},{"type":"function","name":"name","inputs":[],"outputs":[{"name":"","type":"string","internalType":"string"}],"stateMutability":"view"},{"type":"function","name":"ownerOf","inputs":[{"name":"id","type":"uint256","internalType":"uint256"}],"outputs":[{"name":"owner","type":"address","internalType":"address"}],"stateMutability":"view"},{"type":"function","name":"safeTransferFrom","inputs":[{"name":"from","type":"address","internalType":"address"},{"name":"to","type":"address","internalType":"address"},{"name":"id","type":"uint256","internalType":"uint256"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"safeTransferFrom","inputs":[{"name":"from","type":"address","internalType":"address"},{"name":"to","type":"address","internalType":"address"},{"name":"id","type":"uint256","internalType":"uint256"},{"name":"data","type":"bytes","internalType":"bytes"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"setApprovalForAll","inputs":[{"name":"operator","type":"address","internalType":"address"},{"name":"approved","type":"bool","internalType":"bool"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"supportsInterface","inputs":[{"name":"interfaceId","type":"bytes4","internalType":"bytes4"}],"outputs":[{"name":"","type":"bool","internalType":"bool"}],"stateMutability":"pure"},{"type":"function","name":"symbol","inputs":[],"outputs":[{"name":"","type":"string","internalType":"string"}],"stateMutability":"view"},{"type":"function","name":"tokenURI","inputs":[{"name":"id","type":"uint256","internalType":"uint256"}],"outputs":[{"name":"","type":"string","internalType":"string"}],"stateMutability":"view"},{"type":"function","name":"transferFrom","inputs":[{"name":"from","type":"address","internalType":"address"},{"name":"to","type":"address","internalType":"address"},{"name":"id","type":"uint256","internalType":"uint256"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"event","name":"Approval","inputs":[{"name":"owner","type":"address","indexed":true,"internalType":"address"},{"name":"spender","type":"address","indexed":true,"internalType":"address"},{"name":"id","type":"uint256","indexed":true,"internalType":"uint256"}],"anonymous":false},{"type":"event","name":"ApprovalForAll","inputs":[{"name":"owner","type":"address","indexed":true,"internalType":"address"},{"name":"operator","type":"address","indexed":true,"internalType":"address"},{"name":"approved","type":"bool","indexed":false,"internalType":"bool"}],"anonymous":false},{"type":"event","name":"Transfer","inputs":[{"name":"from","type":"address","indexed":true,"internalType":"address"},{"name":"to","type":"address","indexed":true,"internalType":"address"},{"name":"id","type":"uint256","indexed":true,"internalType":"uint256"}],"anonymous":false}],"abiPath":"lib/forge-std/src/mocks/MockERC721.sol"},{"name":"CryticToFoundry","abi":[{"type":"function","name":"IS_TEST","inputs":[],"outputs":[{"name":"","type":"bool","internalType":"bool"}],"stateMutability":"view"},{"type":"function","name":"counter_increment","inputs":[],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"counter_setNumber","inputs":[{"name":"newNumber","type":"uint64","internalType":"uint64"}],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"excludeArtifacts","inputs":[],"outputs":[{"name":"excludedArtifacts_","type":"string[]","internalType":"string[]"}],"stateMutability":"view"},{"type":"function","name":"excludeContracts","inputs":[],"outputs":[{"name":"excludedContracts_","type":"address[]","internalType":"address[]"}],"stateMutability":"view"},{"type":"function","name":"excludeSenders","inputs":[],"outputs":[{"name":"excludedSenders_","type":"address[]","internalType":"address[]"}],"stateMutability":"view"},{"type":"function","name":"failed","inputs":[],"outputs":[{"name":"","type":"bool","internalType":"bool"}],"stateMutability":"nonpayable"},{"type":"function","name":"setUp","inputs":[],"outputs":[],"stateMutability":"nonpayable"},{"type":"function","name":"targetArtifactSelectors","inputs":[],"outputs":[{"name":"targetedArtifactSelectors_","type":"tuple[]","internalType":"struct StdInvariant.FuzzSelector[]","components":[{"name":"addr","type":"address","internalType":"address"},{"name":"selectors","type":"bytes4[]","internalType":"bytes4[]"}]}],"stateMutability":"view"},{"type":"function","name":"targetArtifacts","inputs":[],"outputs":[{"name":"targetedArtifacts_","type":"string[]","internalType":"string[]"}],"stateMutability":"view"},{"type":"function","name":"targetContracts","inputs":[],"outputs":[{"name":"targetedContracts_","type":"address[]","internalType":"address[]"}],"stateMutability":"view"},{"type":"function","name":"targetInterfaces","inputs":[],"outputs":[{"name":"targetedInterfaces_","type":"tuple[]","internalType":"struct StdInvariant.FuzzInterface[]","components":[{"name":"addr","type":"address","internalType":"address"},{"name":"artifacts","type":"string[]","internalType":"string[]"}]}],"stateMutability":"view"},{"type":"function","name":"targetSelectors","inputs":[],"outputs":[{"name":"targetedSelectors_","type":"tuple[]","internalType":"struct StdInvariant.FuzzSelector[]","components":[{"name":"addr","type":"address","internalType":"address"},{"name":"selectors","type":"bytes4[]","internalType":"bytes4[]"}]}],"stateMutability":"view"},{"type":"function","name":"targetSenders","inputs":[],"outputs":[{"name":"targetedSenders_","type":"address[]","internalType":"address[]"}],"stateMutability":"view"},{"type":"function","name":"testDemo","inputs":[],"outputs":[],"stateMutability":"nonpayable"},{"type":"event","name":"log","inputs":[{"name":"","type":"string","indexed":false,"internalType":"string"}],"anonymous":false},{"type":"event","name":"log_address","inputs":[{"name":"","type":"address","indexed":false,"internalType":"address"}],"anonymous":false},{"type":"event","name":"log_array","inputs":[{"name":"val","type":"uint256[]","indexed":false,"internalType":"uint256[]"}],"anonymous":false},{"type":"event","name":"log_array","inputs":[{"name":"val","type":"int256[]","indexed":false,"internalType":"int256[]"}],"anonymous":false},{"type":"event","name":"log_array","inputs":[{"name":"val","type":"address[]","indexed":false,"internalType":"address[]"}],"anonymous":false},{"type":"event","name":"log_bytes","inputs":[{"name":"","type":"bytes","indexed":false,"internalType":"bytes"}],"anonymous":false},{"type":"event","name":"log_bytes32","inputs":[{"name":"","type":"bytes32","indexed":false,"internalType":"bytes32"}],"anonymous":false},{"type":"event","name":"log_int","inputs":[{"name":"","type":"int256","indexed":false,"internalType":"int256"}],"anonymous":false},{"type":"event","name":"log_named_address","inputs":[{"name":"key","type":"string","indexed":false,"internalType":"string"},{"name":"val","type":"address","indexed":false,"internalType":"address"}],"anonymous":false},{"type":"event","name":"log_named_array","inputs":[{"name":"key","type":"string","indexed":false,"internalType":"string"},{"name":"val","type":"uint256[]","indexed":false,"internalType":"uint256[]"}],"anonymous":false},{"type":"event","name":"log_named_array","inputs":[{"name":"key","type":"string","indexed":false,"internalType":"string"},{"name":"val","type":"int256[]","indexed":false,"internalType":"int256[]"}],"anonymous":false},{"type":"event","name":"log_named_array","inputs":[{"name":"key","type":"string","indexed":false,"internalType":"string"},{"name":"val","type":"address[]","indexed":false,"internalType":"address[]"}],"anonymous":false},{"type":"event","name":"log_named_bytes","inputs":[{"name":"key","type":"string","indexed":false,"internalType":"string"},{"name":"val","type":"bytes","indexed":false,"internalType":"bytes"}],"anonymous":false},{"type":"event","name":"log_named_bytes32","inputs":[{"name":"key","type":"string","indexed":false,"internalType":"string"},{"name":"val","type":"bytes32","indexed":false,"internalType":"bytes32"}],"anonymous":false},{"type":"event","name":"log_named_decimal_int","inputs":[{"name":"key","type":"string","indexed":false,"internalType":"string"},{"name":"val","type":"int256","indexed":false,"internalType":"int256"},{"name":"decimals","type":"uint256","indexed":false,"internalType":"uint256"}],"anonymous":false},{"type":"event","name":"log_named_decimal_uint","inputs":[{"name":"key","type":"string","indexed":false,"internalType":"string"},{"name":"val","type":"uint256","indexed":false,"internalType":"uint256"},{"name":"decimals","type":"uint256","indexed":false,"internalType":"uint256"}],"anonymous":false},{"type":"event","name":"log_named_int","inputs":[{"name":"key","type":"string","indexed":false,"internalType":"string"},{"name":"val","type":"int256","indexed":false,"internalType":"int256"}],"anonymous":false},{"type":"event","name":"log_named_string","inputs":[{"name":"key","type":"string","indexed":false,"internalType":"string"},{"name":"val","type":"string","indexed":false,"internalType":"string"}],"anonymous":false},{"type":"event","name":"log_named_uint","inputs":[{"name":"key","type":"string","indexed":false,"internalType":"string"},{"name":"val","type":"uint256","indexed":false,"internalType":"uint256"}],"anonymous":false},{"type":"event","name":"log_string","inputs":[{"name":"","type":"string","indexed":false,"internalType":"string"}],"anonymous":false},{"type":"event","name":"log_uint","inputs":[{"name":"","type":"uint256","indexed":false,"internalType":"uint256"}],"anonymous":false},{"type":"event","name":"logs","inputs":[{"name":"","type":"bytes","indexed":false,"internalType":"bytes"}],"anonymous":false}],"abiPath":"test/recon/CryticToFoundry.sol"}]',
    organizationId: "170e7577-2d1f-4d92-b788-0bc1116c8e14",
    createdAt: "2024-03-01T12:02:50.318Z",
    updatedAt: "2024-03-01T12:02:50.318Z",
  },
];
