export const getInstallsMock = [
  {
    id: 733450817,
    node_id: "R_kgDOK7eSQQ",
    name: "yieldbox-rebase",
    full_name: "GalloDaSballo/yieldbox-rebase",
    private: true,
    owner: {
      login: "GalloDaSballo",
      id: 13383782,
      node_id: "MDQ6VXNlcjEzMzgzNzgy",
      avatar_url: "https://avatars.githubusercontent.com/u/13383782?v=4",
      gravatar_id: "",
      url: "https://api.github.com/users/GalloDaSballo",
      html_url: "https://github.com/GalloDaSballo",
      followers_url: "https://api.github.com/users/GalloDaSballo/followers",
      following_url:
        "https://api.github.com/users/GalloDaSballo/following{/other_user}",
      gists_url: "https://api.github.com/users/GalloDaSballo/gists{/gist_id}",
      starred_url:
        "https://api.github.com/users/GalloDaSballo/starred{/owner}{/repo}",
      subscriptions_url:
        "https://api.github.com/users/GalloDaSballo/subscriptions",
      organizations_url: "https://api.github.com/users/GalloDaSballo/orgs",
      repos_url: "https://api.github.com/users/GalloDaSballo/repos",
      events_url: "https://api.github.com/users/GalloDaSballo/events{/privacy}",
      received_events_url:
        "https://api.github.com/users/GalloDaSballo/received_events",
      type: "User",
      site_admin: false,
    },
    html_url: "https://github.com/GalloDaSballo/yieldbox-rebase",
    description: null,
    fork: false,
    url: "https://api.github.com/repos/GalloDaSballo/yieldbox-rebase",
    forks_url:
      "https://api.github.com/repos/GalloDaSballo/yieldbox-rebase/forks",
    keys_url:
      "https://api.github.com/repos/GalloDaSballo/yieldbox-rebase/keys{/key_id}",
    collaborators_url:
      "https://api.github.com/repos/GalloDaSballo/yieldbox-rebase/collaborators{/collaborator}",
    teams_url:
      "https://api.github.com/repos/GalloDaSballo/yieldbox-rebase/teams",
    hooks_url:
      "https://api.github.com/repos/GalloDaSballo/yieldbox-rebase/hooks",
    issue_events_url:
      "https://api.github.com/repos/GalloDaSballo/yieldbox-rebase/issues/events{/number}",
    events_url:
      "https://api.github.com/repos/GalloDaSballo/yieldbox-rebase/events",
    assignees_url:
      "https://api.github.com/repos/GalloDaSballo/yieldbox-rebase/assignees{/user}",
    branches_url:
      "https://api.github.com/repos/GalloDaSballo/yieldbox-rebase/branches{/branch}",
    tags_url: "https://api.github.com/repos/GalloDaSballo/yieldbox-rebase/tags",
    blobs_url:
      "https://api.github.com/repos/GalloDaSballo/yieldbox-rebase/git/blobs{/sha}",
    git_tags_url:
      "https://api.github.com/repos/GalloDaSballo/yieldbox-rebase/git/tags{/sha}",
    git_refs_url:
      "https://api.github.com/repos/GalloDaSballo/yieldbox-rebase/git/refs{/sha}",
    trees_url:
      "https://api.github.com/repos/GalloDaSballo/yieldbox-rebase/git/trees{/sha}",
    statuses_url:
      "https://api.github.com/repos/GalloDaSballo/yieldbox-rebase/statuses/{sha}",
    languages_url:
      "https://api.github.com/repos/GalloDaSballo/yieldbox-rebase/languages",
    stargazers_url:
      "https://api.github.com/repos/GalloDaSballo/yieldbox-rebase/stargazers",
    contributors_url:
      "https://api.github.com/repos/GalloDaSballo/yieldbox-rebase/contributors",
    subscribers_url:
      "https://api.github.com/repos/GalloDaSballo/yieldbox-rebase/subscribers",
    subscription_url:
      "https://api.github.com/repos/GalloDaSballo/yieldbox-rebase/subscription",
    commits_url:
      "https://api.github.com/repos/GalloDaSballo/yieldbox-rebase/commits{/sha}",
    git_commits_url:
      "https://api.github.com/repos/GalloDaSballo/yieldbox-rebase/git/commits{/sha}",
    comments_url:
      "https://api.github.com/repos/GalloDaSballo/yieldbox-rebase/comments{/number}",
    issue_comment_url:
      "https://api.github.com/repos/GalloDaSballo/yieldbox-rebase/issues/comments{/number}",
    contents_url:
      "https://api.github.com/repos/GalloDaSballo/yieldbox-rebase/contents/{+path}",
    compare_url:
      "https://api.github.com/repos/GalloDaSballo/yieldbox-rebase/compare/{base}...{head}",
    merges_url:
      "https://api.github.com/repos/GalloDaSballo/yieldbox-rebase/merges",
    archive_url:
      "https://api.github.com/repos/GalloDaSballo/yieldbox-rebase/{archive_format}{/ref}",
    downloads_url:
      "https://api.github.com/repos/GalloDaSballo/yieldbox-rebase/downloads",
    issues_url:
      "https://api.github.com/repos/GalloDaSballo/yieldbox-rebase/issues{/number}",
    pulls_url:
      "https://api.github.com/repos/GalloDaSballo/yieldbox-rebase/pulls{/number}",
    milestones_url:
      "https://api.github.com/repos/GalloDaSballo/yieldbox-rebase/milestones{/number}",
    notifications_url:
      "https://api.github.com/repos/GalloDaSballo/yieldbox-rebase/notifications{?since,all,participating}",
    labels_url:
      "https://api.github.com/repos/GalloDaSballo/yieldbox-rebase/labels{/name}",
    releases_url:
      "https://api.github.com/repos/GalloDaSballo/yieldbox-rebase/releases{/id}",
    deployments_url:
      "https://api.github.com/repos/GalloDaSballo/yieldbox-rebase/deployments",
    created_at: "2023-12-19T11:03:22Z",
    updated_at: "2023-12-19T11:04:13Z",
    pushed_at: "2024-01-03T15:34:33Z",
    git_url: "git://github.com/GalloDaSballo/yieldbox-rebase.git",
    ssh_url: "**************:GalloDaSballo/yieldbox-rebase.git",
    clone_url: "https://github.com/GalloDaSballo/yieldbox-rebase.git",
    svn_url: "https://github.com/GalloDaSballo/yieldbox-rebase",
    homepage: null,
    size: 44,
    stargazers_count: 0,
    watchers_count: 0,
    language: "Solidity",
    has_issues: true,
    has_projects: true,
    has_downloads: true,
    has_wiki: false,
    has_pages: false,
    has_discussions: false,
    forks_count: 0,
    mirror_url: null,
    archived: false,
    disabled: false,
    open_issues_count: 0,
    license: null,
    allow_forking: true,
    is_template: false,
    web_commit_signoff_required: false,
    topics: [],
    visibility: "private",
    forks: 0,
    open_issues: 0,
    watchers: 0,
    default_branch: "main",
    permissions: {
      admin: false,
      maintain: false,
      push: false,
      triage: false,
      pull: false,
    },
    installation_id: 47004340,
  },
  {
    id: 761909186,
    node_id: "R_kgDOLWnPwg",
    name: "recon-demo-3",
    full_name: "GalloDaSballo/recon-demo-3",
    private: false,
    owner: {
      login: "GalloDaSballo",
      id: 13383782,
      node_id: "MDQ6VXNlcjEzMzgzNzgy",
      avatar_url: "https://avatars.githubusercontent.com/u/13383782?v=4",
      gravatar_id: "",
      url: "https://api.github.com/users/GalloDaSballo",
      html_url: "https://github.com/GalloDaSballo",
      followers_url: "https://api.github.com/users/GalloDaSballo/followers",
      following_url:
        "https://api.github.com/users/GalloDaSballo/following{/other_user}",
      gists_url: "https://api.github.com/users/GalloDaSballo/gists{/gist_id}",
      starred_url:
        "https://api.github.com/users/GalloDaSballo/starred{/owner}{/repo}",
      subscriptions_url:
        "https://api.github.com/users/GalloDaSballo/subscriptions",
      organizations_url: "https://api.github.com/users/GalloDaSballo/orgs",
      repos_url: "https://api.github.com/users/GalloDaSballo/repos",
      events_url: "https://api.github.com/users/GalloDaSballo/events{/privacy}",
      received_events_url:
        "https://api.github.com/users/GalloDaSballo/received_events",
      type: "User",
      site_admin: false,
    },
    html_url: "https://github.com/GalloDaSballo/recon-demo-3",
    description: null,
    fork: false,
    url: "https://api.github.com/repos/GalloDaSballo/recon-demo-3",
    forks_url: "https://api.github.com/repos/GalloDaSballo/recon-demo-3/forks",
    keys_url:
      "https://api.github.com/repos/GalloDaSballo/recon-demo-3/keys{/key_id}",
    collaborators_url:
      "https://api.github.com/repos/GalloDaSballo/recon-demo-3/collaborators{/collaborator}",
    teams_url: "https://api.github.com/repos/GalloDaSballo/recon-demo-3/teams",
    hooks_url: "https://api.github.com/repos/GalloDaSballo/recon-demo-3/hooks",
    issue_events_url:
      "https://api.github.com/repos/GalloDaSballo/recon-demo-3/issues/events{/number}",
    events_url:
      "https://api.github.com/repos/GalloDaSballo/recon-demo-3/events",
    assignees_url:
      "https://api.github.com/repos/GalloDaSballo/recon-demo-3/assignees{/user}",
    branches_url:
      "https://api.github.com/repos/GalloDaSballo/recon-demo-3/branches{/branch}",
    tags_url: "https://api.github.com/repos/GalloDaSballo/recon-demo-3/tags",
    blobs_url:
      "https://api.github.com/repos/GalloDaSballo/recon-demo-3/git/blobs{/sha}",
    git_tags_url:
      "https://api.github.com/repos/GalloDaSballo/recon-demo-3/git/tags{/sha}",
    git_refs_url:
      "https://api.github.com/repos/GalloDaSballo/recon-demo-3/git/refs{/sha}",
    trees_url:
      "https://api.github.com/repos/GalloDaSballo/recon-demo-3/git/trees{/sha}",
    statuses_url:
      "https://api.github.com/repos/GalloDaSballo/recon-demo-3/statuses/{sha}",
    languages_url:
      "https://api.github.com/repos/GalloDaSballo/recon-demo-3/languages",
    stargazers_url:
      "https://api.github.com/repos/GalloDaSballo/recon-demo-3/stargazers",
    contributors_url:
      "https://api.github.com/repos/GalloDaSballo/recon-demo-3/contributors",
    subscribers_url:
      "https://api.github.com/repos/GalloDaSballo/recon-demo-3/subscribers",
    subscription_url:
      "https://api.github.com/repos/GalloDaSballo/recon-demo-3/subscription",
    commits_url:
      "https://api.github.com/repos/GalloDaSballo/recon-demo-3/commits{/sha}",
    git_commits_url:
      "https://api.github.com/repos/GalloDaSballo/recon-demo-3/git/commits{/sha}",
    comments_url:
      "https://api.github.com/repos/GalloDaSballo/recon-demo-3/comments{/number}",
    issue_comment_url:
      "https://api.github.com/repos/GalloDaSballo/recon-demo-3/issues/comments{/number}",
    contents_url:
      "https://api.github.com/repos/GalloDaSballo/recon-demo-3/contents/{+path}",
    compare_url:
      "https://api.github.com/repos/GalloDaSballo/recon-demo-3/compare/{base}...{head}",
    merges_url:
      "https://api.github.com/repos/GalloDaSballo/recon-demo-3/merges",
    archive_url:
      "https://api.github.com/repos/GalloDaSballo/recon-demo-3/{archive_format}{/ref}",
    downloads_url:
      "https://api.github.com/repos/GalloDaSballo/recon-demo-3/downloads",
    issues_url:
      "https://api.github.com/repos/GalloDaSballo/recon-demo-3/issues{/number}",
    pulls_url:
      "https://api.github.com/repos/GalloDaSballo/recon-demo-3/pulls{/number}",
    milestones_url:
      "https://api.github.com/repos/GalloDaSballo/recon-demo-3/milestones{/number}",
    notifications_url:
      "https://api.github.com/repos/GalloDaSballo/recon-demo-3/notifications{?since,all,participating}",
    labels_url:
      "https://api.github.com/repos/GalloDaSballo/recon-demo-3/labels{/name}",
    releases_url:
      "https://api.github.com/repos/GalloDaSballo/recon-demo-3/releases{/id}",
    deployments_url:
      "https://api.github.com/repos/GalloDaSballo/recon-demo-3/deployments",
    created_at: "2024-02-22T18:01:46Z",
    updated_at: "2024-02-25T17:20:25Z",
    pushed_at: "2024-02-29T17:01:48Z",
    git_url: "git://github.com/GalloDaSballo/recon-demo-3.git",
    ssh_url: "**************:GalloDaSballo/recon-demo-3.git",
    clone_url: "https://github.com/GalloDaSballo/recon-demo-3.git",
    svn_url: "https://github.com/GalloDaSballo/recon-demo-3",
    homepage: null,
    size: 1366,
    stargazers_count: 0,
    watchers_count: 0,
    language: "HTML",
    has_issues: true,
    has_projects: true,
    has_downloads: true,
    has_wiki: true,
    has_pages: false,
    has_discussions: false,
    forks_count: 0,
    mirror_url: null,
    archived: false,
    disabled: false,
    open_issues_count: 1,
    license: null,
    allow_forking: true,
    is_template: false,
    web_commit_signoff_required: false,
    topics: [],
    visibility: "public",
    forks: 0,
    open_issues: 1,
    watchers: 0,
    default_branch: "main",
    permissions: {
      admin: false,
      maintain: false,
      push: false,
      triage: false,
      pull: false,
    },
    installation_id: 47004340,
  },
];
