export const getJobsMock = [
  {
    id: "3a3c510c-344f-44c2-9cb5-19a3d7ed84ea",
    orgName: "GalloDaSballo",
    repoName: "recon-demo-3",
    ref: "cc3a7b727c07800d650251fd489894f42291a97d",
    fuzzer: "MEDUSA",
    directory: ".",
    preprocess: null,
    duration: null,
    fuzzerArgs: null,
    taskArn: null,
    corpusUrl: null,
    coverageUrl: null,
    logsUrl: null,
    status: "QUEUED",
    organizationId: "170e7577-2d1f-4d92-b788-0bc1116c8e14",
    experimentId: null,
    createdAt: "2024-02-29T08:43:51.124Z",
    updatedAt: "2024-02-29T08:43:51.124Z",
  },
  {
    id: "29c92342-c729-4e93-8ffd-3ede030a7d28",
    orgName: "GalloDaSballo",
    repoName: "recon-demo-3",
    ref: "8094a4aca45683b3f03e0a1583d1420deb6b2f8d",
    fuzzer: "MEDUSA",
    directory: ".",
    preprocess: null,
    duration: null,
    fuzzerArgs: null,
    taskArn: null,
    corpusUrl: null,
    coverageUrl: null,
    logsUrl: null,
    status: "QUEUED",
    organizationId: "170e7577-2d1f-4d92-b788-0bc1116c8e14",
    experimentId: null,
    createdAt: "2024-02-26T19:31:17.621Z",
    updatedAt: "2024-02-26T19:31:17.621Z",
  },
  {
    id: "8f2e76d4-d1d4-46d9-b2f9-9af14e3548d1",
    orgName: "GalloDaSballo",
    repoName: "recon-demo-3",
    ref: "469c1e613ce043ea5d43f1a0c1471bbb87902c8e",
    fuzzer: "MEDUSA",
    directory: ".",
    preprocess: null,
    duration: null,
    fuzzerArgs: null,
    taskArn: null,
    corpusUrl: null,
    coverageUrl: null,
    logsUrl: null,
    status: "QUEUED",
    organizationId: "170e7577-2d1f-4d92-b788-0bc1116c8e14",
    experimentId: null,
    createdAt: "2024-02-26T19:30:55.244Z",
    updatedAt: "2024-02-26T19:30:55.244Z",
  },
  {
    id: "887e8033-7a62-4865-b521-18c29a7199bc",
    orgName: "GalloDaSballo",
    repoName: "recon-demo-3",
    ref: "ef338c4448d1f4421a0dfaac9b98109f87ddf519",
    fuzzer: "MEDUSA",
    directory: ".",
    preprocess: null,
    duration: null,
    fuzzerArgs: null,
    taskArn: null,
    corpusUrl: null,
    coverageUrl: null,
    logsUrl: null,
    status: "QUEUED",
    organizationId: "170e7577-2d1f-4d92-b788-0bc1116c8e14",
    experimentId: null,
    createdAt: "2024-02-26T19:25:58.691Z",
    updatedAt: "2024-02-26T19:25:58.691Z",
  },
  {
    id: "381547be-f531-48f8-93f6-1ed04dc1da5c",
    orgName: "GalloDaSballo",
    repoName: "foundry-recond-demo-2",
    ref: "main",
    fuzzer: "MEDUSA",
    directory: ".",
    preprocess: null,
    duration: null,
    fuzzerArgs: null,
    taskArn: null,
    corpusUrl: null,
    coverageUrl: null,
    logsUrl: null,
    status: "QUEUED",
    organizationId: "170e7577-2d1f-4d92-b788-0bc1116c8e14",
    experimentId: null,
    createdAt: "2024-02-26T08:36:35.277Z",
    updatedAt: "2024-02-26T08:36:35.277Z",
  },
  {
    id: "652b3b09-9a6c-4ba7-b0c8-06996dba2827",
    orgName: "GalloDaSballo",
    repoName: "twap-study",
    ref: "main",
    fuzzer: "MEDUSA",
    directory: ".",
    preprocess: null,
    duration: null,
    fuzzerArgs: null,
    taskArn: null,
    corpusUrl: null,
    coverageUrl: null,
    logsUrl: null,
    status: "QUEUED",
    organizationId: "170e7577-2d1f-4d92-b788-0bc1116c8e14",
    experimentId: null,
    createdAt: "2024-02-26T08:36:10.604Z",
    updatedAt: "2024-02-26T08:36:10.604Z",
  },
];
