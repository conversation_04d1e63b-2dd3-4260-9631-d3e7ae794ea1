import { useQuery } from "@tanstack/react-query";
import axios from "axios";
import { useEffect, useState } from "react";

// import { getABIMock } from "../mocks/getABI";
import type { AbiApiData } from "../types/abi.api";

export function useGetAbi(): {
  data: AbiApiData[];
  isLoading: boolean;
  refetch: any;
  isRefetching: boolean;
} {
  // TODO: We need to change this to also add the branch
  // Without branch this doesn't really make sense
  // @ts-expect-error note
  return useQuery<Project[], string>({
    queryKey: ["getAbi"],
    queryFn: async () => {
      try {
        const res = await axios.get("/api/abi");
        return res.data.data;
      } catch (e) {
        // return getABIMock;
        console.log("rev", e);
        throw "Unable to get projects";
      }
    },
  });
}

export function useGetAbiByIdentifier(identifier: string | AbiApiData[], forced?: boolean): {
  data: AbiApiData;
  isLoading: boolean;
} {
  const query = useGetAbi();

  const [project, setProject] = useState<AbiApiData>();
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (forced) {
      setProject({identifier: "", updatedAt: "", id:"",  abiData: JSON.stringify(identifier)});
      setIsLoading(false);
    } else if (query.data) {
      // Sort by date for latest
      // const sorted = query.data.sort((e)) // TODO

      // Return first found
      const project = query.data.find(
        (project) => String(project.identifier) === String(identifier),
      );
      setProject(project);
      setIsLoading(query.isLoading);
    }
  }, [query.data, identifier, forced, query.isLoading]);

  return {
    ...query,
    isLoading,
    data: project,
  };
}
