import { useQuery } from "@tanstack/react-query";
import axios from "axios";
import type { Recipe } from "./recipes.hook";
import type { Job } from "./jobs.hooks";

export interface GovernanceFuzzing {
  id: string;
  contractAddress: string;
  address: string;
  chainId: number;
  recipeId: string;
  eventDefinition: string;
  topic: string;
  recipe: Recipe;
  jobs: Job[];
  recipes: Recipe[];
}

export function useGetGovernanceFuzzing(): {
  data: GovernanceFuzzing[];
  isLoading: boolean;
  refetch: any;
  isRefetching: boolean;
} {
  return useQuery<GovernanceFuzzing[], string>({
    queryKey: ["getGovernanceFuzzing"],
    queryFn: async () => {
      try {
        const res = await axios.get("/api/governanceFuzzing");
        return res.data.data;
      } catch (e) {
        console.log("Gov fuzzing hook error", e);
        throw "Unable to get projects";
      }
    },
  });
}
