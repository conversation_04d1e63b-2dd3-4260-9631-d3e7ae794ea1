import { useQuery } from "@tanstack/react-query";
import axios from "axios";
import { useEffect, useState } from "react";

export interface Recipe {
  id: string;
  displayName: string;
  orgName: string;
  repoName: string;
  ref: string;
  fuzzer: string;
  fuzzerArgs: {
    target: string;
    prepareContracts: {
      target: string;
      replacement: string;
      targetContract: string;
    }[];
  };
}

export function useGetRecipes(): {
  data: Recipe[];
  isLoading: boolean;
  refetch: any;
  isRefetching: boolean;
} {
  // Without branch this doesn't really make sense
  return useQuery<Recipe[], string>({
    queryKey: ["getRecipes"],
    queryFn: async () => {
      try {
        const res = await axios.get("/api/recipes");
        console.log("getRecipes.data", res.data);
        return res.data.data;
      } catch (e) {
        // return getABIMock;
        console.log("rev", e);
        throw "Unable to get projects";
      }
    },
  });
}

export function useGetRecipeByIdentifier(identifier: string): {
  data: Recipe;
  isLoading: boolean;
} {
  const query = useGetRecipes();

  const [recipe, setRecipe] = useState<Recipe>();

  useEffect(() => {
    if (query.data) {
      // Return first found
      const found = query.data.find(
        (toTest) => String(toTest.id) === String(identifier),
      );
      setRecipe(found);
    }
  }, [query.data, identifier]);

  return {
    ...query,
    data: recipe,
  };
}
