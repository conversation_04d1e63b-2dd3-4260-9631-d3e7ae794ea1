import { useQuery } from "@tanstack/react-query";
import axios from "axios";

export function useGetDiscord(): { data: string; isLoading: boolean } {
  return useQuery<string, string>({
    queryKey: ["getDiscordUrl"],
    queryFn: async () => {
      try {
        const res = await axios.get("/api/discord");
        return res.data.data;
      } catch (e) {
        console.log("error - discord", e);
        throw "Unable to get Discord Link";
      }
    },
  });
}
