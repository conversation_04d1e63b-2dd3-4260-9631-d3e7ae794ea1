import { useQuery } from "@tanstack/react-query";
import axios from "axios";

// import { getInstallsMock } from "./../mocks/getInstalls";

export interface Installs {
  id: number;
  name: string; // NAME (without org) | Btw for user install, then the url is obvious
  full_name: string; // ORG/NAME
  html_url: string;
  license: {
    spdx_id: string; // Prob useful
  };
  default_branch: string; // To show
  allow_forking: boolean; // Can we fork?
}

export function useGetInstalls(): { data: Installs[]; isLoading: boolean } {
  return useQuery<Installs[], string>({
    queryKey: ["getInstalls"],
    queryFn: async () => {
      try {
        const res = await axios.get("/api/installs");
        return res.data.data;
      } catch {
        // return getInstallsMock;
        throw "Unable to get projects";
      }
    },
  });
}

// TODO: Get branches for a single install
