// No clue why this is broken
import { useQuery } from "@tanstack/react-query";
import axios from "axios";

export interface Webhook {
  id: string;
  organizationId: string;

  type: "PR_CREATION" | "PR_UPDATE" | "COMMIT";

  orgName: string;
  repoName: string;
  ref: string;

  // Date times
  createdAt: string;
  updatedAt: string;
}

export function useGetWebhooks(): {
  data: Webhook[];
  isLoading: boolean;
  refetch: any;
  isRefetching: boolean;
} {
  return useQuery<Webhook[], string>({
    queryKey: ["getWebhooks"],
    queryFn: async () => {
      try {
        const res = await axios.get("/api/webhooks");
        return res.data.data;
      } catch {
        throw "Unable to get webhooks";
      }
    },
  });
}
