import { useQuery } from "@tanstack/react-query";
import axios from "axios";

import type { Recipe } from "./recipes.hook";

export interface Campaign {
  id: string;
  displayName: string;
  comments: boolean;

  recipes: Recipe[];
}

export function useGetCampaigns(): {
  data: Campaign[];
  isLoading: boolean;
  refetch: any;
  isRefetching: boolean;
} {
  return useQuery<Campaign[], string>({
    queryKey: ["getCampaigns"],
    queryFn: async () => {
      try {
        const res = await axios.get("/api/campaigns");
        return res.data.data;
      } catch (e) {
        // return getABIMock;
        console.log("rev", e);
        throw "Unable to get Campaigns";
      }
    },
  });
}
