import {
  extractEchidnaPace,
  extractLastElapsedTimeFromMedusa,
  extractLastTestCountFromEchidna,
  formatSecondsToDaysHoursMinutes,
} from "@/lib/utils";
import {
  Fuzzer,
  type BrokenProperty,
} from "@recon-fuzz/log-parser";
import { useQuery } from "@tanstack/react-query";
import axios from "axios";
import { useEffect, useState } from "react";

// import { getJobsMock } from "../mocks/getJobs";

export interface Job {
  id: string;
  orgName: string;
  repoName: string;
  ref: string;
  fuzzer: string;
  directory: string;
  taskArn?: string;
  corpusUrl?: string;
  coverageUrl?: string;
  logsUrl?: string;
  status: string;
  createdAt: string;
  updatedAt: string;
  fuzzerArgs: any;
  label?: string;

  metadata: {
    commit: string;
    method: string;
    startedBy: string;
  };
  testsDuration: string;
  testsCoverage: number;
  testsFailed: number;
  testsPassed: number;
  numberOfTests: number;
  brokenProperties: BrokenProperty[];
  progress?: number;
  eta?: string;
}

export function useGetJobs(): {
  data: Job[];
  isLoading: boolean;
  refetch: any;
  isRefetching: boolean;
} {
  return useQuery<Job[], string>({
    queryKey: ["getJobs"],
    queryFn: async () => {
      try {
        const res = await axios.get("/api/jobs");
        for (const job in res.data.data) {
          const currJob = res.data.data[job];
          try {
            const { progress, eta } = await getJobProgress(currJob);
            if (progress) {
              currJob.progress = progress;
            }
            if (eta) {
              currJob.eta = eta;
            }
          } catch (err) {
            console.log("error getting job progress ", err);
          }
          res.data.data[job] = currJob;
        }
        return res.data.data;
      } catch {
        // return getJobsMock;
        throw "Unable to get projects";
      }
    },
  });
}

export function useGetJobById(identifier: string): {
  data: Job;
  isLoading: boolean;
} {
  const query = useGetJobs();
  console.log("query.data", query.data);

  const [job, setJob] = useState<Job>();

  useEffect(() => {
    if (query.data) {
      // Return first found
      const project = query.data.find(
        (project) => String(project.id) === String(identifier)
      );

      setJob(project);
    }
  }, [query.data, identifier]);

  return {
    ...query,
    data: job,
  };
}

interface JobProgress {
  progress: number;
  eta: string | null;
}

export const getJobProgress = async (job: Job): Promise<JobProgress | null> => {
  if (
    job.status === "ERROR" ||
    job.status === "STOPPED" ||
    job.status === "SUCCESS"
  ) {
    return {
      progress: 100,
      eta: null,
    };
  }
  if (job.status === "STARTED" || job.status === "QUEUED") {
    return {
      progress: 0,
      eta: null,
    };
  }
  if (!job.logsUrl) {
    return {
      progress: 0,
      eta: null,
    };
  }
  if (job.fuzzer === Fuzzer.ECHIDNA) {
    const jobLogsRaw = await axios({
      method: "POST",
      url: `/api/fetchLogs`,
      data: {
        logsUrl: job?.logsUrl,
      },
    });
    const progress = extractLastTestCountFromEchidna(jobLogsRaw.data);
    const remainingSeconds = extractEchidnaPace(jobLogsRaw.data);
    if (remainingSeconds === 0) {
      return {
        progress: 100,
        eta: null,
      };
    }
    const etaString = formatSecondsToDaysHoursMinutes(remainingSeconds);
    return {
      progress,
      eta: etaString ? etaString : null,
    };
  } else if (job.fuzzer === Fuzzer.MEDUSA) {
    const jobLogsRaw = await axios({
      method: "POST",
      url: `/api/fetchLogs`,
      data: {
        logsUrl: job?.logsUrl,
      },
    });

    const timeoutInSeconds = job.fuzzerArgs?.timeout || 0;
    const elapsedSeconds = extractLastElapsedTimeFromMedusa(jobLogsRaw.data);

    const remainingSeconds = timeoutInSeconds - elapsedSeconds;
    if (remainingSeconds <= 0) {
      return {
        progress: 100,
        eta: null,
      };
    }

    const etaString = formatSecondsToDaysHoursMinutes(remainingSeconds);
    const progress = (elapsedSeconds / timeoutInSeconds) * 100;

    return {
      progress: Math.min(Math.max(Math.round(progress), 0), 100),
      eta: etaString,
    };
  } else {
    return {
      progress: 100,
      eta: null,
    };
  }
};
