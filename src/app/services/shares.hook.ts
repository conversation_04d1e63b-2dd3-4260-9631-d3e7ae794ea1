// No clue why this is broken
import { useQuery } from "@tanstack/react-query";
import axios from "axios";
import { useEffect, useState } from "react";
import type { Job } from "./jobs.hooks";

export interface Share {
  id: string;
  jobId: string;
  shareId: string;
  job: {
    repoName: string,
    orgName: string
    ref: string
    label: string
  }
}

export function useGetShares(): {
  data: Share[];
  isLoading: boolean;
  refetch: any;
  isRefetching: boolean;
} {
  return useQuery<Share[], string>({
    queryKey: ["getShares"],
    queryFn: async () => {
      try {
        const res = await axios.get("/api/shares");
        const shares = res.data.data.map((share: any) => ({
          id: share.id,
          jobId: share.jobId,
          shareId: share.shareId,
          job: {
            repoName: share.job.repoName,
            orgName: share.job.orgName,
            ref: share.job.ref,
            label: share.job.label,
          },
        }));
        return shares as Share[];
      } catch {
        throw "Unable to get shares";
      }
    },
  });
}

export function useGetShareByJobId(jobId: string): {
  data: Share;
  isLoading: boolean;
  refetch: any;
  isRefetching: boolean;
} {
  const query = useGetShares();

  const [share, setShare] = useState<Share>();

  useEffect(() => {
    if (query.data) {
      // Return first found
      const project = query.data.find(
        (share) => String(share.jobId) === String(jobId),
      );

      setShare(project);
    }
  }, [query.data, jobId]);

  return {
    ...query,
    data: share,
  };
}
