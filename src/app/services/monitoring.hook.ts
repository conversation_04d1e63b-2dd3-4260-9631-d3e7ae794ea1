// No clue why this is broken
import { useQuery } from "@tanstack/react-query";
import axios from "axios";
import { useEffect, useState } from "react";

import type { Job } from "./jobs.hooks";

interface ServiceResponse {
  message: string;
  data: {
    block: number;
    properties: {
      [propertyName: string]: boolean;
    };
  };
  // alerts: Alert[]; // 0XSI - code for later when we want alerts on Monitoring
}

interface MonitoringCallResponse {
  id: string;
  data: ServiceResponse;
  status: number;
}

interface Alert {
  id: string;
  active: boolean;
  threshold: number;
  webhookUrl: string;
}

export function useGetMonitoring(): {
  data: MonitoringCallResponse[];
  isLoading: boolean;
  refetch: any;
  isRefetching: boolean;
} {
  return useQuery<MonitoringCallResponse[], string>({
    queryKey: ["getMonitoring"],
    queryFn: async () => {
      try {
        const res = await axios.get("/api/monitoring");
        console.log("hook:", res.data.data)
        return res.data.data;
      } catch {
        throw "Unable to get shares";
      }
    },
  });
}
