// No clue why this is broken
import { useQuery } from "@tanstack/react-query";
import axios from "axios";

import type { BrokenProperty } from "@recon-fuzz/log-parser";

export interface RecurringJob {
  id: string;
  label: string;

  // This allows us to toggle the job while still displaying it in the UI
  enabled: boolean;

  organizationId: string;
  recipeId: string;

  lastRun: string; // Date String
  frequencyInSeconds: number;

  createdAt: string;
  updatedAt: string;
  alerts: Alert[];
}

interface Alert {
  id: string;
  active: boolean;
  threshold: number;
  webhookUrl: string;
}

export function useGetRecurring(): {
  data: RecurringJob[];
  isLoading: boolean;
  refetch: any;
  isRefetching: boolean;
} {
  return useQuery<RecurringJob[], string>({
    queryKey: ["getRecurring"],
    queryFn: async () => {
      try {
        const res = await axios.get("/api/recurring") ;
        return res.data.data as RecurringJob[];
      } catch {
        throw "Unable to get shares";
      }
    },
  });
}
