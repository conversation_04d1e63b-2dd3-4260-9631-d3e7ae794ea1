import { useQuery } from "@tanstack/react-query";
import axios from "axios";

import type { Job } from "./jobs.hooks";

export interface AbiJob {
  id: string;
  orgName: string;
  repoName: string;
  branch: string;
  status: string;
  createdAt: string;
  updatedAt: string;
}

export function useGetAbiJobs(): {
  data: AbiJob[];
  isLoading: boolean;
  refetch: any;
  isRefetching: boolean;
} {
  return useQuery<any[], string>({
    queryKey: ["getAbiJobs"],
    queryFn: async () => {
      try {
        const res = await axios.get("/api/abiJobs");
        return res.data.data;
      } catch {
        throw "Unable to get projects";
      }
    },
  });
}

export const groupJobs = (jobs: AbiJob[] | Job[]) => {
  return jobs?.reduce(
    (acc, job) => {
      if (!acc[job.status]) {
        acc[job.status] = [];
      }

      acc[job.status].push(job);

      return acc;
    },
    {} as Record<string, Array<AbiJob | Job>>,
  );
};
