import { useQuery } from "@tanstack/react-query";
import axios from "axios";

interface StatsData {
  abiJobCount: number;
  userCount: number;
  abiDataCount: number;
}

export function useGetStats(): { data: StatsData; isLoading: boolean } {
  return useQuery<StatsData, string>({
    queryKey: ["getStats"],
    queryFn: async () => {
      try {
        const res = await axios.get("/api/stats");
        return res.data.data;
      } catch (e) {
        console.log("error - stats", e);
        // throw "Unable to get Discord Link";
      }
    },
  });
}
