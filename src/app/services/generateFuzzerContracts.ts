import fromProjectToAllBoilerplate from "@recon-fuzz/abi-to-invariants-ts/src/";
import processContract, {
  processABI,
} from "@recon-fuzz/abi-to-invariants-ts/src/process";
// NOTE: These are not prod imports
import type {
  ABI,
  ABIWithSelector,
  BoilerplateFile,
  ContractInstance,
  Mode,
  ProcessedContract,
  ReconProject,
} from "@recon-fuzz/abi-to-invariants-ts/src/types";

import type { abiEntry } from "../types/abi.api";

export enum ReconMode {
  normal = "NORMAL",
  fail = "FAIL",
  catch = "CATCH",
}

interface SkippedFunctions {
  [contractName: string]: string[]; // Selectors
}
interface TrackedViewFunctions {
  [contractName: string]: string[]; // Selectors
}

function fromNameToTracked(
  trackedContracts: string[],
  skippedFunctions: SkippedFunctions,
  trackedViewFunctions: TrackedViewFunctions,
): ContractInstance[] {
  // TODO: Find ignored given tracked
  // If found, add them
  return trackedContracts.map((contract) => {
    const foundToSkip = skippedFunctions[contract];
    const foundView = trackedViewFunctions[contract];
    return {
      contractIdentifier: contract,
      label: camelCase(contract),
      ignored: foundToSkip ? foundToSkip : [], // TODO
      beforeAfterTracked: foundView ? foundView : [],
    };
  });
}

// NOTE: May throw if you don't filter the functions first
export function processFunctions(functions: ABI[]): ABIWithSelector[] {
  const processed = functions.map((fn) => processABI(fn));

  return processed;
}
export function filterOutAllNonViewFunctions(functions: ABI[]): ABI[] {
  const onlyWriteFunctions = functions.filter(
    (fn) => fn.type == "function" && fn.stateMutability != "view",
  );

  return onlyWriteFunctions;
}
export function getAllViewFunctions(functions: ABI[]): ABI[] {
  const onlyViewFunctions = functions.filter(
    (fn) => fn.type == "function" && fn.stateMutability == "view",
  );

  return onlyViewFunctions;
}

function fromApiDataToUsable(abiData: abiEntry[]): ProcessedContract[] {
  console.log("fromApiDataToUsable abiData", abiData);
  // TODO: Is it appropriate to remove errors here?
  const removedErrors = abiData.map((abiEntry) => ({
    ...abiEntry,
    abi: abiEntry.abi.filter((abi) => abi.type == "function"),
  }));

  return removedErrors.map((abiEntry) => {
    return processContract({
      identifier: abiEntry.name, // projectIdentifier_path_to_file
      type: abiEntry.name, // E.g. TheVault | NOTE: Maybe wrong? TODO
      pathFromRoot: abiEntry.abiPath, // So we can properly fix imports
      functions: abiEntry.abi.filter(
        (singleAbi) => singleAbi.type === "function",
      ),
    });
  });
}

export function generateReconProjectData(
  allAbiData: abiEntry[],
  trackedContracts: string[],
  skippedFunctions: SkippedFunctions,
  trackedViewFunctions: TrackedViewFunctions,
  reconMode: ReconMode,
): ReconProject {
  //0XSI - the ReconProject interface will be updated with update with new abi-to-invariants version
  const settings = {
    projectIdentifier: "TODO The Project Identifier",
    filePostfix: "",
    label: "TODO Project Label",
    pragma: "pragma solidity ^0.8.0;",
    license: "// SPDX-License-Identifier: GPL-2.0",
    contracts: fromApiDataToUsable(allAbiData),
    instances: fromNameToTracked(
      trackedContracts,
      skippedFunctions,
      trackedViewFunctions,
    ),
    mode: reconMode as unknown as Mode, //TODO 0XSI check this
  };

  return settings;
}

export function generateFullFuzzerContracts(
  settings: ReconProject,
): BoilerplateFile[] {
  const boilerplate = fromProjectToAllBoilerplate(settings);
  return boilerplate;
}

function camelCase(str: string): string {
  return str
    .replace(/(?:^\w|[A-Z]|\b\w)/g, function (word, index) {
      return index === 0 ? word.toLowerCase() : word.toUpperCase();
    })
    .replace(/\s+/g, "");
}
