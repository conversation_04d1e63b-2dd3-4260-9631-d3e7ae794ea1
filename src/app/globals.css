@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  min-height: 100vh;
}

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
  /* --primary-fornt: "Inter", sans-serif; */

  --primary-color: #660fdb;
  --secondary-color: #171717;
  --accent-color: #f4eefc;
  --danger-color: #f54f4f;
  --success-color: #41d98d;
  --light-text-color: #fff;
  --dark-text-color: var(--secondary-color);

  --color-bg1: rgb(0, 0, 0);
  --color-bg2: rgb(0, 17, 82);
  --primary: #5c25d2;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 0, 0, 0;
    --background-start-rgb: 214, 219, 220;
    --background-end-rgb: 255, 255, 255;
  }
}

.light {
  /* TODO check it */
  --primary: #5c25d2;
  --primary-bg: #e7e0f6;
  --text-secondary: #2d2d2d;
  --grey-secondary: #8a8a8a;
  --block-bg: #eaedf4;
  --aside: #fff;
  --input-bg: #fcfcfc;
  --divider: #bec6da;
  --success: #259465;
  --error: #ff5252;
  --text-primary: #2b2b2b;
  --dashboard-bg: #f3f4f8;
  --logoColor: #5c25d2;
}

.dark {
  --primary: #5c25d2;
  --primary-bg: #261a40;
  --text-secondary: #737373;
  --grey-secondary: #8a8a8a;
  --block-bg: linear-gradient(95.95deg, #303030 35.62%, #1e1e1e 113.48%);
  --aside: #171717;
  --input-bg: #0e0e0e;
  --divider: #4e4e4e;
  --success: #259465;
  --error: #ff5252;
  --text-primary: #fff;
  --dashboard-bg: #1e1e1e;
  --logoColor: #fff;
}

body {
  color: rgb(var(--foreground-rgb));
  margin: 0;
  padding: 0;
}



.app-button-default {
  background: white;
  color: black;
}

.landing-info-block {
  background: linear-gradient(
    288deg,
    rgba(30, 13, 66, 0.67) -21.63%,
    rgba(23, 23, 23, 0.67) 92%
  );
}

.landing-footer-block {
  background: linear-gradient(96.89deg, #5c25d2 33.34%, #4700de 122.54%);
}

.dark .gradient-dark-bg {
  background-image: linear-gradient(95.95deg, #303030 35.62%, #1e1e1e 113.48%);
}

.aside-menu::-webkit-scrollbar {
  display: none;
}
.aside-menu {
  position: sticky;
  top: 0;
  max-height: 100vh;
  overflow-y: scroll;
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.main-container {
  background-color: black;
}

.main-title-custom {
  background: linear-gradient(277.21deg, #5100ff -13.33%, #ffffff 51.57%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.sub-title-custom {
  background: linear-gradient(277.04deg, #5100FF 16.04%, #FFFFFF 51.63%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  font-size: 40px;
  display: inline-block;
}



/*****************************
* PREVIOUS STYLE *
* DELETE WHEN MERGING INTO MAIN *
*****************************/

.main-title-custom-prev {
  background: linear-gradient(277.21deg, #5100ff -13.33%, #ffffff 51.57%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.landing-info-block-prev {
  background: linear-gradient(
    288deg,
    rgba(30, 13, 66, 0.67) -21.63%,
    rgba(23, 23, 23, 0.67) 92%
  );
}

.landing-footer-block-prev {
  background: linear-gradient(96.89deg, #5c25d2 33.34%, #4700de 122.54%);
}

.gradient-bg-prev {
  width: 100vw;
  min-height: 100vh;
  overflow: hidden;
  background: linear-gradient(40deg, var(--color-bg1), var(--color-bg2));
  top: 0;
  left: 0;
  z-index: 0;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
