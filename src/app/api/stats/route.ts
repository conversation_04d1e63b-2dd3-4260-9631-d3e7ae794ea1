import axios from "axios";
import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";

export async function GET() {
  let foundData;
  try {
    foundData = await axios({
      method: "GET",
      url: `${process.env.BACKEND_API_URL}/super/stats/public-digest`,
    });
  } catch (e) {
    // Axios error handling
    if (e?.response?.data) {
      return NextResponse.json(
        { data: {}, message: e.response.data.message },
        { status: e.response.status },
      );
    } else {
      return NextResponse.json(
        { data: {}, message: "Something went wrong" },
        { status: 500 },
      );
    }
  }

  return NextResponse.json(foundData.data);
}
