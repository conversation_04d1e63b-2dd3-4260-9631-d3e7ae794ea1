import axios from "axios";
import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import type { JWT } from "next-auth/jwt";
import { getToken } from "next-auth/jwt";

const secret = process.env.NEXTAUTH_SECRET;

export async function POST(req: NextRequest, res: NextResponse) {
  const sesh = await getServerSession();
  if (!sesh) {
    return NextResponse.json({ error: "Need Log in" }, { status: 401 });
  }

  const token: JWT = await getToken({ req, secret });
  const { id, chainId, address, eventDefinition, topic, prepareContracts } = await req.json();
  try {
    const foundData = await axios({
      method: "PUT",
      url: `${process.env.BACKEND_API_URL}/governanceFuzzing/`,
      headers: { Authorization: `Bearer ${token.access_token}` },
      data: {
        id,
        chainId,
        address,
        eventDefinition,
        topic,
        prepareContracts
      }
    });
    return NextResponse.json(foundData.data);
  } catch (e) {
    console.log(e)
    return NextResponse.json({ error: "Something went wrong" }, { status: 500 });
  }
}
