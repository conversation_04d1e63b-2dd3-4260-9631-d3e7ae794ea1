import axios from "axios";
import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import type { JWT } from "next-auth/jwt";
import { getToken } from "next-auth/jwt";

const secret = process.env.NEXTAUTH_SECRET;

export async function POST(req: NextRequest, res: NextResponse) {
  const sesh = await getServerSession();
  if (!sesh) {
    return NextResponse.json({ error: "Need Log in" }, { status: 401 });
  }

  // It's guaranteed to be there due to the check above
  const token: JWT = await getToken({ req, secret });

  // For some reason we need to do this
  // Dumb af
  const { contractAddress, topic, prepContract, recipeId, eventDefinition, chainId } = await req.json();
console.log({contractAddress, topic, prepContract, recipeId, eventDefinition, chainId})
  /// @audit NOTE: No validation here, needs to be done on the server

  let foundData;
  try {
    foundData = await axios({
      method: "POST",
      url: `${process.env.BACKEND_API_URL}/governanceFuzzing`,
      headers: { Authorization: `Bearer ${token.access_token}` },
      data: {
        contractAddress,
        prepContract,
        recipeId,
        eventDefinition,
        topic,
        chainId
      },
    });
  } catch (e) {
    console.log(e)
    // Axios error handling
    if (e?.response?.data) {
      return NextResponse.json(
        { data: {}, message: e.response.data.message },
        { status: e.response.status },
      );
    } else {
      return NextResponse.json(
        { data: {}, message: "Something went wrong" },
        { status: 500 },
      );
    }
  }

  // Returns an object with {data, message}
  return NextResponse.json(foundData.data);
}

export async function GET(req: NextRequest, res: NextResponse) {
  const sesh = await getServerSession();
  if (!sesh) {
    return NextResponse.json({ error: "Need Log in" }, { status: 401 });
  }

  const token: JWT = await getToken({ req, secret });
  try {
    const foundData = await axios({
      method: "GET",
      url: `${process.env.BACKEND_API_URL}/governanceFuzzing`,
      headers: { Authorization: `Bearer ${token.access_token}` },
    });
    return NextResponse.json(foundData.data);
  } catch (e) {
    console.log(e)
    return NextResponse.json({ error: "Something went wrong" }, { status: 500 });
  }
}

export async function DELETE(req: NextRequest, res: NextResponse) {
  const sesh = await getServerSession();
  if (!sesh) {
    return NextResponse.json({ error: "Need Log in" }, { status: 401 });
  }

  const token: JWT = await getToken({ req, secret });
  const { id } = await req.json();

  try {
    const foundData = await axios({
      method: "DELETE",
      url: `${process.env.BACKEND_API_URL}/governanceFuzzing/${id}`,
      headers: { Authorization: `Bearer ${token.access_token}` },
    });
    return NextResponse.json(foundData.data);
  } catch (e) {
    console.log(e)
    return NextResponse.json({ error: "Something went wrong" }, { status: 500 });
  }
}
