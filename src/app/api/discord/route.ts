import axios from "axios";
import { NextResponse } from "next/server";

const URL = "https://discord.gg/";

// NOTE: Could use ENV
const CODE = "6JnUcp33Yn";

const INVITE_URL = `${URL}${CODE}`;

const API_URL = `https://discord.com/api/invite/${CODE}`;

// NOTE: Could use ENV
// eslint-disable-next-line @typescript-eslint/no-loss-of-precision
const EXPECTED_GUILD = 1199312177727799336;

// Get all jobs running for the organization that the user belongs to
export async function GET() {
  //discord.gg/YtyC8zAc

  try {
    const inviteInfo = await axios({
      method: "GET",
      url: API_URL,
    });

    const foundInvite = inviteInfo.data;

    if (foundInvite.guild.id == EXPECTED_GUILD) {
      return NextResponse.json({ message: INVITE_URL, data: INVITE_URL });
    }
  } catch {
    return NextResponse.json(
      { message: "Issue With API", data: {} },
      { status: 500 },
    );
  }

  return NextResponse.json({ message: "Hijack", data: {} }, { status: 500 });
}
