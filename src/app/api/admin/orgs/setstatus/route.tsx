import axios from "axios";
import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import type { JWT } from "next-auth/jwt";
import { getToken } from "next-auth/jwt";

const secret = process.env.NEXTAUTH_SECRET;

// Get all jobs running for the organization that the user belongs to
export async function POST(req: NextRequest, res: NextResponse) {
  const sesh = await getServerSession();
  if (!sesh) {
    return NextResponse.json({ error: "Need Log in" }, { status: 401 });
  }
  // It's guaranteed to be there due to the check above
  const token: JWT = (await getToken({ req, secret })) as JWT;

  const { orgId, status } = await req.json();

  let url;
  switch (status) {
    case "UNPAID":
    case "REVOKED":
      console.log("unpaid && revoked");
      url = `${process.env.BACKEND_API_URL}/super/cancel`;
      break;
    case "PAID":
      console.log("paid");
      url = `${process.env.BACKEND_API_URL}/super/pro`;
      break;
    case "TRIAL":
      url = `${process.env.BACKEND_API_URL}/super/trial`;
      console.log("trial");
      break;
    default:
      console.log("default");
      break;
  }

  try {
    await axios({
      method: "POST",
      url: url,
      headers: { Authorization: `Bearer ${token.access_token}` },
      data: {
        orgId: orgId,
      },
    });
  } catch (e) {
    // Axios error handling
    if (e?.response?.data) {
      return NextResponse.json(
        { data: {}, message: e.response.data.message },
        { status: e.response.status }
      );
    } else {
      return NextResponse.json(
        { data: {}, message: "Something went wrong" },
        { status: 500 }
      );
    }
  }

  return NextResponse.json({ data: "", message: "success" }, { status: 200 });
}
