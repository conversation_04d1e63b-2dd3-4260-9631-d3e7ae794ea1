import axios from "axios";
import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import type { JWT } from "next-auth/jwt";
import { getToken } from "next-auth/jwt";

const secret = process.env.NEXTAUTH_SECRET;

export async function GET(req: NextRequest, res: NextResponse) {
  const sesh = await getServerSession();
  if (!sesh) {
    return NextResponse.json("Not logged in");
  }

  // It's guaranteed to be there due to the check above
  const token: JWT = await getToken({ req, secret });

  // Access Token
  // console.log(token?.account?.access_token);

  // console.log("token", token.access_token);

  const foundData = await axios({
    method: "GET",
    url: `${process.env.NEXT_PUBLIC_API_URL}/myInstalls`,
    headers: { Authorization: `Bearer ${token.access_token}` },
  });

  return NextResponse.json(foundData.data);
}
