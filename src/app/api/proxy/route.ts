import axios from "axios";
import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";

export async function POST(req: NextRequest) {
  try {
    const { method, endpoint, token, data } = await req.json();

    if (!method || !endpoint || !token) {
      return NextResponse.json(
        { message: "Missing required fields" },
        { status: 400 }
      );
    }

    const shouldIncludeData = 
      data !== undefined && 
      data !== null && 
      ['post', 'put'].includes(method.toLowerCase());

    const config = {
      method: method.toUpperCase(),
      url: `${process.env.BACKEND_API_URL}${endpoint}`,
      headers: { Authorization: `Bearer ${token}` },
      ...(shouldIncludeData && { data }),
    };

    const response = await axios(config);
    return NextResponse.json(response.data);
  } catch (e) {
    return NextResponse.json(
      { message: "Error processing request", error: e.message },
      { status: 500 }
    );
  }
}
