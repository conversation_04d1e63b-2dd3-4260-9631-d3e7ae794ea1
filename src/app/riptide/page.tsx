"use client";

import Image from "next/image";
import Link from "next/link";
import GenericButton from "../components/GenericButton/GenericButton";
import Benefits from "../components/AuditBenefits/Benefits";
import Trophies from "../components/Trophies/Trophies";
import Testimonials from "../components/Testimonials/Testimonials";
import Team from "../components/Team/Team";
import Service from "../components/Service/Service";
import Footer from "../components/Footer/Footer";
import Navbar from "../components/Navbar/Navbar";
import TvlBenefits from "../components/TvlBenefits/TvlBenfits";
import ReconProCTA from "../components/ReconProCTA/ReconProCTA";
import { FaTelegram } from "react-icons/fa";
import Audits from "../components/Audits/Audits";

export default function Home() {
  return (
    <div>
      <Navbar />
      <div className="main-container w-full overflow-x-hidden">
        <div className="absolute inset-0 z-0 bg-gradient-to-b from-transparent via-transparent to-black">
          <div
            className="size-full"
            style={{
              backgroundImage: `linear-gradient(0deg, #5C25D2, #5C25D2), url('/bg-recon.jpeg')`,
              backgroundBlendMode: "color, normal",
              backgroundSize: "cover",
              backgroundPosition: "center",
            }}
          />
        </div>
        <main className="relative z-10">
          <section className="mx-auto mb-5 flex w-[87.5%] flex-col items-center justify-center pt-[80px] lg:w-4/5 lg:pt-[180px]">
            <h1 className="main-title-custom mb-5 text-center text-[56px] font-bold leading-[48px] tracking-normal lg:text-[120px] lg:leading-[100px]">
              AUDITS POWERED BY INVARIANT TESTING
            </h1>
            <h2 className="mb-5 text-center font-thin tracking-normal text-white lg:my-[37px] lg:text-[37px] lg:leading-[33px]">
              World Class Audits powered by state of the art Invariant Tests
            </h2>
            <p className="mb-5 text-center font-thin tracking-normal text-white lg:my-[22px] lg:text-[22px] lg:leading-[22px]">
              $5k Discount on your first engagement by saying you were referred by &quot;RIPTIDE&quot;
            </p>
            <Link
              href="https://tally.so/r/w2Vqxb"
              className="m-0 flex flex-row items-center justify-center p-0 text-center"
              target="_blank"
              rel="noopener noreferrer"
            >
              <GenericButton className={"button_cta m-0 p-0"}>
                Send Audit Request
              </GenericButton>
            </Link>


            <div className="mt-4 flex size-full flex-col rounded-lg bg-gradient-to-r from-[rgba(30,13,66,0.65)] to-[rgba(23,23,23,0.65)] p-[16px] backdrop-blur-[3.85px]">
              <div className="mb-4 flex size-full flex-col items-center justify-center text-center lg:mb-0 lg:flex-row lg:items-start lg:justify-start">
                <p className="mb-4 w-full text-center text-[24px] font-thin leading-[24px] text-white">
                  Helping these projects deploy safely
                </p>
              </div>
              <div className="flex w-full flex-row items-center justify-around md:flex-wrap">
                <Link href="https://youtu.be/AT3fMhPDZFU" target="_blank">
                  <Image
                    src="/centrifuge-logo.svg"
                    alt="Centrifuge logo"
                    width={100}
                    height={30}
                    className="mr-0"
                  />
                </Link>
                <Link href="https://youtu.be/3pvWq_zBauY" target="_blank">
                  <Image
                    src="/badger-logo.svg"
                    alt="Badger logo"
                    width={100}
                    height={30}
                    className="mr-0"
                  />
                </Link>
                <Link href="" target="_blank">
                  <Image
                    src="/Corn.png"
                    alt="Corn Logo"
                    width={100}
                    height={30}
                    className="mr-0"
                  />
                </Link>
                <Link href="" target="_blank">
                  <Image
                    src="/Liquity.svg"
                    alt="Liquity Logo"
                    width={30}
                    height={30}
                    className="mr-0"
                  />
                </Link>
                <Link href="" target="_blank">
                  <Image
                    src="/Balancer.svg"
                    alt="Balancer Logo"
                    width={30}
                    height={30}
                    className="mr-0"
                  />
                </Link>
                <Link href="" target="_blank">
                  <Image
                    src="/credit-coop-logo.png"
                    alt="Credit coop Logo"
                    width={100}
                    height={40}
                    className="mr-0"
                  />
                </Link>
              </div>
            </div>
          </section>
          <section
            id="stats"
            className="mx-auto mb-5 flex w-[87.5%] flex-col items-center justify-center pt-[30px] lg:pt-[180px]"
          >
            <h3 className=" sub-title-custom text-center font-bold uppercase leading-[48px] tracking-normal lg:text-[100px] lg:leading-[100px]">
              TVL Protected
            </h3>
            <TvlBenefits />
          </section>

          <section
            id="testimonials"
            className="mx-auto mb-5 flex w-[87.5%] flex-col justify-center pt-[30px] lg:w-4/5 lg:pt-[180px]"
          >
            <h3 className=" sub-title-custom w-[650px] items-start font-bold uppercase leading-[48px] tracking-normal lg:text-[100px] lg:leading-[100px]">
              Testimonials
            </h3>
            <h4 className="mb-[40px] font-thin tracking-normal text-white md:mb-5 lg:mb-[32px] lg:text-[32px] lg:leading-[33px]">
              Our services are tailored to our customers
            </h4>
            <Testimonials />
          </section>

          <section
            id="benefits"
            className="mx-auto mb-5 flex w-[87.5%] flex-col items-center justify-center pt-[30px] lg:pt-[180px]"
          >
            <h3 className=" sub-title-custom text-center font-bold uppercase leading-[48px] tracking-normal lg:text-[100px] lg:leading-[100px]">
              Benefits
            </h3>
            <Benefits />
          </section>

          <section
            id="trophies"
            className="mx-auto mb-5 flex w-[87.5%] flex-col justify-center  pt-[30px] lg:w-4/5 lg:pt-[180px]"
          >
            <h3 className=" sub-title-custom w-[450px] items-start font-bold uppercase leading-[48px] tracking-normal lg:text-[100px] lg:leading-[100px]">
              Trophies
            </h3>
            <h4 className="mb-5 font-thin tracking-normal text-white lg:mb-[32px] lg:text-[32px] lg:leading-[33px]">
              Recon was used to find these bugs
            </h4>
            <Trophies />
          </section>

          <section
            id="audits"
            className="mx-auto mb-5 flex w-[87.5%] flex-col justify-center  pt-[30px] lg:w-4/5 lg:pt-[180px]"
          >
            <h3 className=" sub-title-custom w-[450px] items-start font-bold uppercase leading-[48px] tracking-normal lg:text-[100px] lg:leading-[100px]">
              Audits
            </h3>
            <h4 className="mb-5 font-thin tracking-normal text-white lg:mb-[32px] lg:text-[32px] lg:leading-[33px]">
              World Class Reviews, we open source every audit unless asked not to by our customers
            </h4>
            <Audits />
          </section>

          <section
            id="team"
            className="mx-auto mb-5 flex w-[87.5%] flex-col justify-center pt-[30px] lg:w-4/5 lg:pt-[180px]"
          >
            <h3 className=" sub-title-custom w-[250px] items-start font-bold uppercase leading-[48px] tracking-normal lg:text-[100px] lg:leading-[100px]">
              TEAM
            </h3>
            <Team />
          </section>
            
          {/* TODO:Change this to be specific and also specify HOW TO */}
          <section
            id="services"
            className="mx-auto mb-5 flex w-[87.5%] flex-col items-center justify-center pt-[30px] lg:w-4/5 lg:pt-[180px]"
          >
            <Service />
          </section>

          <section
            id="builder"
            className="mx-auto mb-5 flex w-[87.5%] flex-col justify-center pt-[30px] lg:w-4/5 lg:pt-[180px]"
          >
            <div className="mb-[40px] flex w-full flex-col items-center justify-between lg:flex-row">
              <h3 className=" sub-title-custom text-center font-bold uppercase leading-[48px] tracking-normal lg:text-[100px] lg:leading-[100px] ">
                RECON PRO
              </h3>
              <Link
                href="/pro"
                className="mb-5 text-center"
              >
                <GenericButton className={"button_cta m-0 p-0"}>
                  Learn More &gt;
                </GenericButton>
              </Link>
            </div>
            
            <ReconProCTA />
          </section>
        </main>
        <div className="w-full border-t border-t-[#FFFFFF]">
          <Footer />
        </div>
      </div>
    </div>
  );
}
