// SPDX-License-Identifier: GPL-2.0
pragma solidity ^0.8.0;

import {Test} from "forge-std/Test.sol";

/// @title HalmosDirect - Tests designed to fail with Halmos
/// @notice These tests are intentionally written to produce counter examples
/// @dev Run with: halmos --function check_ --solver-timeout-assertion 0
contract HalmosDirect is Test {
    
    // ===== Structs for testing =====
    struct SimpleStruct {
        uint256 value;
        bool flag;
    }
    
    struct ComplexStruct {
        uint256 id;
        address owner;
        uint256[] amounts;
        bool isActive;
    }

    // ===== Bool Counter Examples =====
    
    /// @notice This will fail when a == b
    function check_bool_xor_always_true(bool a, bool b) public pure {
        // XOR should not always be true
        assert(a != b);
    }
    
    /// @notice This will fail when both are true or both are false
    function check_bool_implication(bool a, bool b) public pure {
        // If a is true, b must be false (will fail)
        if (a) {
            assert(!b);
        }
    }

    // ===== Uint Counter Examples =====
    
    /// @notice This will fail when a + b overflows
    function check_uint_no_overflow(uint256 a, uint256 b) public pure {
        unchecked {
            uint256 sum = a + b;
            // This assertion will fail on overflow
            assert(sum >= a && sum >= b);
        }
    }
    
    /// @notice This will fail when a * b > type(uint256).max / 2
    function check_uint_multiplication_bound(uint256 a, uint256 b) public pure {
        // Assuming multiplication never exceeds half of max uint256
        assert(a * b <= type(uint256).max / 2);
    }
    
    /// @notice This will fail for many values
    function check_uint_complex_property(uint256 x, uint256 y, uint256 z) public pure {
        // Complex property that doesn't always hold
        if (x > 0 && y > 0 && z > 0) {
            assert(x + y + z > x * 2);
        }
    }

    // ===== Int Counter Examples =====
    
    /// @notice This will fail on int256 underflow
    function check_int_no_underflow(int256 a, int256 b) public pure {
        unchecked {
            int256 diff = a - b;
            // This will fail when underflow occurs
            assert(diff <= a);
        }
    }
    
    /// @notice This will fail for negative numbers
    function check_int_absolute_value(int256 a) public pure {
        // Incorrectly assumes we can always negate safely
        int256 absValue = a < 0 ? -a : a;
        assert(absValue >= 0);
    }
    
    /// @notice This will fail for specific combinations
    function check_int_division_property(int256 a, int256 b) public pure {
        if (b != 0) {
            int256 quotient = a / b;
            // This property doesn't always hold for integer division
            assert(quotient * b == a);
        }
    }

    // ===== Struct Counter Examples =====
    
    /// @notice This will fail when struct values don't match expected pattern
    function check_struct_simple_invariant(SimpleStruct memory s) public pure {
        // Assumes if flag is true, value must be > 100 (will fail)
        if (s.flag) {
            assert(s.value > 100);
        }
    }
    
    /// @notice This will fail for certain struct configurations
    function check_struct_complex_property(
        uint256 id,
        address owner,
        bool isActive
    ) public pure {
        ComplexStruct memory s;
        s.id = id;
        s.owner = owner;
        s.isActive = isActive;
        
        // Property that doesn't always hold
        if (s.isActive) {
            assert(s.owner != address(0) && s.id > 0);
        }
    }

    // ===== Array Counter Examples =====
    
    /// @notice This will fail when arrays have specific patterns
    function check_array_sum_property(uint256[] memory arr) public pure {
        if (arr.length == 0) return;
        
        uint256 sum = 0;
        for (uint256 i = 0; i < arr.length; i++) {
            sum += arr[i];
        }
        
        // Incorrectly assumes sum is always greater than length * first element
        assert(sum > arr.length * arr[0]);
    }
    
    /// @notice This will fail for non-sorted arrays
    function check_array_sorted(uint256[] memory arr) public pure {
        if (arr.length <= 1) return;
        
        // Assumes array is always sorted (will fail)
        for (uint256 i = 0; i < arr.length - 1; i++) {
            assert(arr[i] <= arr[i + 1]);
        }
    }
    
    /// @notice This will fail when array contains duplicates
    function check_array_unique_elements(uint256[] memory arr) public pure {
        // Assumes all elements are unique (will fail on duplicates)
        for (uint256 i = 0; i < arr.length; i++) {
            for (uint256 j = i + 1; j < arr.length; j++) {
                assert(arr[i] != arr[j]);
            }
        }
    }
    
    /// @notice This will fail for specific byte patterns
    function check_bytes_array_property(bytes memory data) public pure {
        if (data.length == 0) return;
        
        // Assumes first byte is never 0xFF (will fail)
        assert(data[0] != 0xFF);
    }

    // ===== Combined Type Counter Examples =====
    
    /// @notice Complex property mixing multiple types
    function check_mixed_types_property(
        uint256 x,
        int256 y,
        bool flag,
        uint256[] memory arr
    ) public pure {
        // Complex invariant that doesn't always hold
        if (flag && arr.length > 0) {
            assert(x > uint256(y) || arr[0] > 1000);
        }
    }
    
    /// @notice Testing address type properties
    function check_address_properties(address a, address b) public pure {
        // Assumes addresses are never equal (will fail)
        assert(a != b);
    }
    
    /// @notice Testing fixed-size array
    function check_fixed_array_property(uint256[3] memory arr) public pure {
        // Assumes sum of fixed array is always less than 1000 (will fail)
        uint256 sum = arr[0] + arr[1] + arr[2];
        assert(sum < 1000);
    }
    
    /// @notice Testing mapping-like behavior with parallel arrays
    function check_parallel_arrays_consistency(
        address[] memory keys,
        uint256[] memory values
    ) public pure {
        // Assumes arrays always have same length (might fail)
        assert(keys.length == values.length);
        
        if (keys.length > 0) {
            // Assumes first key is never zero address (will fail)
            assert(keys[0] != address(0));
        }
    }

    // ===== Edge Case Counter Examples =====
    
    /// @notice Tests for extreme values
    function check_extreme_values(uint8 small, uint256 large) public pure {
        // Will fail when small = 255 and large is max
        assert(uint256(small) + 1 < large);
    }
    
    /// @notice Tests for boundary conditions
    function check_boundary_conditions(uint256 x) public pure {
        // Incorrectly assumes x is never at boundaries
        assert(x != 0 && x != type(uint256).max);
    }
    
    /// @notice Tests for modulo arithmetic
    function check_modulo_property(uint256 a, uint256 b) public pure {
        if (b > 0) {
            uint256 remainder = a % b;
            // Incorrect assumption about modulo (will fail)
            assert(remainder <= b / 2);
        }
    }
}