// SPDX-License-Identifier: GPL-2.0
pragma solidity ^0.8.0;

import {Asserts} from "@chimera/Asserts.sol";
import {BeforeAfter} from "./BeforeAfter.sol";

abstract contract Properties is BeforeAfter, Asserts {

    function invariant_never_manager() public {
        t(theManager == address(0), "Always 0");
    }

    function invariant_isNeverManager() public {
        t(!state.isManager[theManager], "Never manager");
    }

    function invariant_amt_isBelow256() public {
        t(amt < 256, "Amt is below 256");
    }

    function invariant_amt_isAbove0() public {
        t(amt > 0, "Amt is above 0");
    }
    
}
