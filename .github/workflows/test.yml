name: Run Tests

on:
  pull_request:
    branches: [ '*' ]
  push:
    branches: [ '*' ]

jobs:
  test:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3

    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'yarn'

    - name: Install dependencies
      run: yarn install --frozen-lockfile

    - name: Run tests
      run: yarn test

    - name: Run tests with coverage
      run: yarn test --coverage
