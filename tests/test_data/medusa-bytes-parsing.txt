[1m[32m[0m[0m Reading the configuration file at: [1mmedusa.json[0m
 Compiling targets with crytic-compile
 Running with a timeout of 3600 seconds
 Initializing corpus
 Setting up base chain
 Initializing and validating corpus call sequences
 Fuzzing with 10 workers
 fuzz: elapsed: 0s, calls: 0 (0/sec), seq/s: 0, coverage: 0
 fuzz: elapsed: 3s, calls: 3339 (1110/sec), seq/s: 9, coverage: 26
 fuzz: elapsed: 6s, calls: 6702 (1118/sec), seq/s: 10, coverage: 29
 fuzz: elapsed: 9s, calls: 10131 (1137/sec), seq/s: 11, coverage: 29
 fuzz: elapsed: 12s, calls: 13579 (1144/sec), seq/s: 11, coverage: 29
 fuzz: elapsed: 15s, calls: 16980 (1132/sec), seq/s: 11, coverage: 29
 fuzz: elapsed: 18s, calls: 20304 (1107/sec), seq/s: 10, coverage: 29
 fuzz: elapsed: 21s, calls: 23773 (1150/sec), seq/s: 11, coverage: 29
 fuzz: elapsed: 24s, calls: 27150 (1125/sec), seq/s: 10, coverage: 30
 fuzz: elapsed: 27s, calls: 30519 (1121/sec), seq/s: 11, coverage: 30
 fuzz: elapsed: 30s, calls: 33995 (1156/sec), seq/s: 11, coverage: 30
 fuzz: elapsed: 33s, calls: 37424 (1141/sec), seq/s: 11, coverage: 30
 fuzz: elapsed: 36s, calls: 40846 (1138/sec), seq/s: 11, coverage: 30
 fuzz: elapsed: 39s, calls: 44245 (1126/sec), seq/s: 11, coverage: 30
 fuzz: elapsed: 42s, calls: 47722 (1157/sec), seq/s: 11, coverage: 30
 fuzz: elapsed: 45s, calls: 51052 (1104/sec), seq/s: 10, coverage: 30
 fuzz: elapsed: 48s, calls: 54216 (1054/sec), seq/s: 10, coverage: 30
 fuzz: elapsed: 51s, calls: 57627 (1135/sec), seq/s: 10, coverage: 30
 fuzz: elapsed: 54s, calls: 60890 (1083/sec), seq/s: 11, coverage: 30
 fuzz: elapsed: 57s, calls: 64285 (1125/sec), seq/s: 10, coverage: 30
 fuzz: elapsed: 1m0s, calls: 67768 (1155/sec), seq/s: 11, coverage: 30
 fuzz: elapsed: 1m3s, calls: 71163 (1128/sec), seq/s: 11, coverage: 30
 fuzz: elapsed: 1m6s, calls: 74603 (1140/sec), seq/s: 11, coverage: 30
 fuzz: elapsed: 1m9s, calls: 78008 (1132/sec), seq/s: 10, coverage: 30
 fuzz: elapsed: 1m12s, calls: 81438 (1142/sec), seq/s: 11, coverage: 30
 fuzz: elapsed: 1m15s, calls: 84866 (1139/sec), seq/s: 11, coverage: 30
 fuzz: elapsed: 1m18s, calls: 88281 (1135/sec), seq/s: 10, coverage: 30
 fuzz: elapsed: 1m21s, calls: 91642 (1120/sec), seq/s: 11, coverage: 30
 fuzz: elapsed: 1m24s, calls: 95037 (1127/sec), seq/s: 11, coverage: 30
 fuzz: elapsed: 1m27s, calls: 98449 (1134/sec), seq/s: 11, coverage: 30
 fuzz: elapsed: 1m30s, calls: 101946 (1164/sec), seq/s: 11, coverage: 30
 fuzz: elapsed: 1m33s, calls: 105300 (1115/sec), seq/s: 10, coverage: 30
 fuzz: elapsed: 1m36s, calls: 108630 (1106/sec), seq/s: 10, coverage: 30
 fuzz: elapsed: 1m39s, calls: 111985 (1112/sec), seq/s: 11, coverage: 30
 fuzz: elapsed: 1m42s, calls: 115391 (1134/sec), seq/s: 10, coverage: 30
 fuzz: elapsed: 1m45s, calls: 118796 (1133/sec), seq/s: 11, coverage: 30
 fuzz: elapsed: 1m48s, calls: 122084 (1092/sec), seq/s: 11, coverage: 30
 fuzz: elapsed: 1m51s, calls: 125421 (1105/sec), seq/s: 10, coverage: 30
 fuzz: elapsed: 1m54s, calls: 128864 (1144/sec), seq/s: 11, coverage: 30
 fuzz: elapsed: 1m57s, calls: 132263 (1113/sec), seq/s: 11, coverage: 30
 fuzz: elapsed: 2m0s, calls: 135693 (1131/sec), seq/s: 11, coverage: 30
 fuzz: elapsed: 2m3s, calls: 139115 (1138/sec), seq/s: 10, coverage: 30
 fuzz: elapsed: 2m6s, calls: 142492 (1114/sec), seq/s: 11, coverage: 30
 fuzz: elapsed: 2m9s, calls: 145899 (1134/sec), seq/s: 10, coverage: 30
 fuzz: elapsed: 2m13s, calls: 149391 (1137/sec), seq/s: 11, coverage: 30
 fuzz: elapsed: 2m16s, calls: 152791 (1130/sec), seq/s: 10, coverage: 30
 fuzz: elapsed: 2m19s, calls: 156170 (1125/sec), seq/s: 11, coverage: 30
 fuzz: elapsed: 2m22s, calls: 159552 (1126/sec), seq/s: 11, coverage: 30
 fuzz: elapsed: 2m25s, calls: 162902 (1116/sec), seq/s: 11, coverage: 30
 fuzz: elapsed: 2m28s, calls: 166302 (1128/sec), seq/s: 11, coverage: 30
 fuzz: elapsed: 2m31s, calls: 169704 (1132/sec), seq/s: 10, coverage: 30
 fuzz: elapsed: 2m34s, calls: 173109 (1133/sec), seq/s: 11, coverage: 30
 fuzz: elapsed: 2m37s, calls: 176542 (1143/sec), seq/s: 10, coverage: 30
 fuzz: elapsed: 2m40s, calls: 179996 (1148/sec), seq/s: 11, coverage: 30
 fuzz: elapsed: 2m43s, calls: 183362 (1118/sec), seq/s: 10, coverage: 30
 fuzz: elapsed: 2m46s, calls: 186799 (1139/sec), seq/s: 11, coverage: 30
 fuzz: elapsed: 2m49s, calls: 190101 (1097/sec), seq/s: 10, coverage: 30
 fuzz: elapsed: 2m52s, calls: 193500 (1131/sec), seq/s: 11, coverage: 30
 fuzz: elapsed: 2m55s, calls: 196967 (1153/sec), seq/s: 11, coverage: 30
 fuzz: elapsed: 2m58s, calls: 200403 (1142/sec), seq/s: 11, coverage: 30
 fuzz: elapsed: 3m1s, calls: 203863 (1152/sec), seq/s: 11, coverage: 30
 fuzz: elapsed: 3m4s, calls: 207229 (1115/sec), seq/s: 11, coverage: 30
 fuzz: elapsed: 3m7s, calls: 210624 (1130/sec), seq/s: 11, coverage: 30
 fuzz: elapsed: 3m10s, calls: 214030 (1131/sec), seq/s: 11, coverage: 30
 fuzz: elapsed: 3m13s, calls: 217382 (1111/sec), seq/s: 10, coverage: 30
 fuzz: elapsed: 3m16s, calls: 220751 (1118/sec), seq/s: 10, coverage: 30
 fuzz: elapsed: 3m19s, calls: 224113 (1101/sec), seq/s: 11, coverage: 30
 fuzz: elapsed: 3m22s, calls: 227511 (1130/sec), seq/s: 11, coverage: 30
 fuzz: elapsed: 3m25s, calls: 230926 (1134/sec), seq/s: 11, coverage: 30
 fuzz: elapsed: 3m28s, calls: 234283 (1115/sec), seq/s: 10, coverage: 30
 fuzz: elapsed: 3m31s, calls: 237659 (1124/sec), seq/s: 12, coverage: 30
 fuzz: elapsed: 3m34s, calls: 241043 (1125/sec), seq/s: 10, coverage: 30
 fuzz: elapsed: 3m37s, calls: 244492 (1145/sec), seq/s: 11, coverage: 30
 fuzz: elapsed: 3m40s, calls: 247885 (1129/sec), seq/s: 11, coverage: 30
 fuzz: elapsed: 3m43s, calls: 251404 (1172/sec), seq/s: 11, coverage: 30
 fuzz: elapsed: 3m46s, calls: 254872 (1151/sec), seq/s: 11, coverage: 30
 fuzz: elapsed: 3m49s, calls: 258063 (1060/sec), seq/s: 9, coverage: 30
 fuzz: elapsed: 3m52s, calls: 261421 (1117/sec), seq/s: 11, coverage: 31
 fuzz: elapsed: 3m55s, calls: 264786 (1115/sec), seq/s: 10, coverage: 31
 fuzz: elapsed: 3m58s, calls: 268169 (1121/sec), seq/s: 10, coverage: 31
 fuzz: elapsed: 4m1s, calls: 271584 (1134/sec), seq/s: 11, coverage: 31
 fuzz: elapsed: 4m4s, calls: 274944 (1119/sec), seq/s: 11, coverage: 31
 fuzz: elapsed: 4m7s, calls: 278304 (1094/sec), seq/s: 10, coverage: 31
 fuzz: elapsed: 4m10s, calls: 281659 (1115/sec), seq/s: 11, coverage: 31
 fuzz: elapsed: 4m13s, calls: 285026 (1120/sec), seq/s: 10, coverage: 31
 fuzz: elapsed: 4m16s, calls: 288357 (1108/sec), seq/s: 11, coverage: 31
 fuzz: elapsed: 4m19s, calls: 291821 (1149/sec), seq/s: 11, coverage: 31
 fuzz: elapsed: 4m22s, calls: 295225 (1132/sec), seq/s: 10, coverage: 31
 fuzz: elapsed: 4m25s, calls: 298569 (1113/sec), seq/s: 11, coverage: 31
 fuzz: elapsed: 4m28s, calls: 301962 (1126/sec), seq/s: 11, coverage: 31
 fuzz: elapsed: 4m31s, calls: 305331 (1121/sec), seq/s: 11, coverage: 31
 fuzz: elapsed: 4m34s, calls: 308712 (1126/sec), seq/s: 10, coverage: 31
 fuzz: elapsed: 4m37s, calls: 312021 (1099/sec), seq/s: 11, coverage: 31
 fuzz: elapsed: 4m40s, calls: 315360 (1110/sec), seq/s: 10, coverage: 31
 fuzz: elapsed: 4m43s, calls: 318720 (1102/sec), seq/s: 11, coverage: 31
 fuzz: elapsed: 4m46s, calls: 322134 (1134/sec), seq/s: 10, coverage: 31
 fuzz: elapsed: 4m49s, calls: 325394 (1083/sec), seq/s: 10, coverage: 31
 fuzz: elapsed: 4m52s, calls: 328747 (1117/sec), seq/s: 10, coverage: 31
 fuzz: elapsed: 4m55s, calls: 332080 (1107/sec), seq/s: 11, coverage: 31
 fuzz: elapsed: 4m58s, calls: 335521 (1140/sec), seq/s: 10, coverage: 31
 fuzz: elapsed: 5m1s, calls: 338911 (1124/sec), seq/s: 12, coverage: 31
 fuzz: elapsed: 5m4s, calls: 342290 (1122/sec), seq/s: 10, coverage: 31
 fuzz: elapsed: 5m7s, calls: 345695 (1127/sec), seq/s: 11, coverage: 31
 fuzz: elapsed: 5m10s, calls: 349043 (1115/sec), seq/s: 10, coverage: 31
 fuzz: elapsed: 5m13s, calls: 352398 (1117/sec), seq/s: 11, coverage: 31
 fuzz: elapsed: 5m16s, calls: 355779 (1122/sec), seq/s: 10, coverage: 31
 fuzz: elapsed: 5m19s, calls: 359099 (1105/sec), seq/s: 10, coverage: 31
 fuzz: elapsed: 5m22s, calls: 362487 (1128/sec), seq/s: 11, coverage: 31
 fuzz: elapsed: 5m25s, calls: 365854 (1120/sec), seq/s: 10, coverage: 31
 fuzz: elapsed: 5m28s, calls: 369132 (1091/sec), seq/s: 10, coverage: 31
 fuzz: elapsed: 5m31s, calls: 372554 (1130/sec), seq/s: 11, coverage: 31
 fuzz: elapsed: 5m34s, calls: 375944 (1129/sec), seq/s: 11, coverage: 31
 fuzz: elapsed: 5m37s, calls: 379346 (1126/sec), seq/s: 11, coverage: 31
 fuzz: elapsed: 5m40s, calls: 382676 (1105/sec), seq/s: 11, coverage: 31
 fuzz: elapsed: 5m43s, calls: 386007 (1108/sec), seq/s: 10, coverage: 31
 fuzz: elapsed: 5m46s, calls: 389424 (1135/sec), seq/s: 11, coverage: 31
 fuzz: elapsed: 5m49s, calls: 392670 (1078/sec), seq/s: 11, coverage: 31
 fuzz: elapsed: 5m52s, calls: 396093 (1139/sec), seq/s: 10, coverage: 31
 fuzz: elapsed: 5m55s, calls: 399474 (1126/sec), seq/s: 11, coverage: 31
 fuzz: elapsed: 5m58s, calls: 402894 (1135/sec), seq/s: 11, coverage: 31
 fuzz: elapsed: 6m1s, calls: 406246 (1110/sec), seq/s: 10, coverage: 31
 fuzz: elapsed: 6m4s, calls: 409590 (1098/sec), seq/s: 11, coverage: 31
 fuzz: elapsed: 6m7s, calls: 412997 (1133/sec), seq/s: 10, coverage: 31
 fuzz: elapsed: 6m10s, calls: 416282 (1088/sec), seq/s: 10, coverage: 32
 fuzz: elapsed: 6m13s, calls: 419525 (1077/sec), seq/s: 10, coverage: 32
 fuzz: elapsed: 6m17s, calls: 422789 (1084/sec), seq/s: 11, coverage: 32
 fuzz: elapsed: 6m20s, calls: 426057 (1088/sec), seq/s: 10, coverage: 32
 fuzz: elapsed: 6m23s, calls: 429338 (1093/sec), seq/s: 10, coverage: 32
 fuzz: elapsed: 6m26s, calls: 432629 (1096/sec), seq/s: 10, coverage: 32
 fuzz: elapsed: 6m29s, calls: 435937 (1099/sec), seq/s: 10, coverage: 32
 fuzz: elapsed: 6m32s, calls: 439199 (1083/sec), seq/s: 11, coverage: 32
 fuzz: elapsed: 6m35s, calls: 442508 (1099/sec), seq/s: 10, coverage: 32
 fuzz: elapsed: 6m38s, calls: 445837 (1107/sec), seq/s: 11, coverage: 32
 fuzz: elapsed: 6m41s, calls: 449170 (1109/sec), seq/s: 10, coverage: 32
 fuzz: elapsed: 6m44s, calls: 452511 (1108/sec), seq/s: 10, coverage: 32
 fuzz: elapsed: 6m47s, calls: 455776 (1084/sec), seq/s: 11, coverage: 32
 fuzz: elapsed: 6m50s, calls: 458973 (1065/sec), seq/s: 10, coverage: 32
 fuzz: elapsed: 6m53s, calls: 462221 (1078/sec), seq/s: 10, coverage: 32
 fuzz: elapsed: 6m56s, calls: 465538 (1105/sec), seq/s: 11, coverage: 32
 fuzz: elapsed: 6m59s, calls: 468799 (1084/sec), seq/s: 10, coverage: 32
 fuzz: elapsed: 7m2s, calls: 472102 (1094/sec), seq/s: 10, coverage: 32
 fuzz: elapsed: 7m5s, calls: 475373 (1085/sec), seq/s: 11, coverage: 32
 fuzz: elapsed: 7m8s, calls: 478664 (1096/sec), seq/s: 10, coverage: 32
 fuzz: elapsed: 7m11s, calls: 481916 (1082/sec), seq/s: 9, coverage: 32
 fuzz: elapsed: 7m14s, calls: 485166 (1078/sec), seq/s: 10, coverage: 32
 fuzz: elapsed: 7m17s, calls: 488466 (1099/sec), seq/s: 11, coverage: 32
 fuzz: elapsed: 7m20s, calls: 491763 (1093/sec), seq/s: 10, coverage: 32
 fuzz: elapsed: 7m23s, calls: 495045 (1092/sec), seq/s: 11, coverage: 32
 fuzz: elapsed: 7m26s, calls: 498322 (1085/sec), seq/s: 10, coverage: 32
 fuzz: elapsed: 7m29s, calls: 501597 (1090/sec), seq/s: 10, coverage: 32
 fuzz: elapsed: 7m32s, calls: 504943 (1112/sec), seq/s: 11, coverage: 32
 fuzz: elapsed: 7m35s, calls: 508157 (1067/sec), seq/s: 10, coverage: 32
 fuzz: elapsed: 7m38s, calls: 511457 (1099/sec), seq/s: 10, coverage: 32
 fuzz: elapsed: 7m41s, calls: 514726 (1083/sec), seq/s: 11, coverage: 32
 fuzz: elapsed: 7m44s, calls: 518007 (1093/sec), seq/s: 10, coverage: 32
 fuzz: elapsed: 7m47s, calls: 521348 (1109/sec), seq/s: 10, coverage: 32
 fuzz: elapsed: 7m50s, calls: 524559 (1068/sec), seq/s: 10, coverage: 32
 fuzz: elapsed: 7m53s, calls: 527809 (1080/sec), seq/s: 10, coverage: 32
 fuzz: elapsed: 7m56s, calls: 531175 (1116/sec), seq/s: 11, coverage: 32
 fuzz: elapsed: 7m59s, calls: 534546 (1119/sec), seq/s: 11, coverage: 32
 fuzz: elapsed: 8m2s, calls: 537859 (1104/sec), seq/s: 10, coverage: 32
 fuzz: elapsed: 8m5s, calls: 541219 (1118/sec), seq/s: 10, coverage: 32
 fuzz: elapsed: 8m8s, calls: 544586 (1109/sec), seq/s: 12, coverage: 32
 fuzz: elapsed: 8m11s, calls: 547946 (1116/sec), seq/s: 10, coverage: 32
 fuzz: elapsed: 8m14s, calls: 551277 (1107/sec), seq/s: 11, coverage: 32
 fuzz: elapsed: 8m17s, calls: 554593 (1102/sec), seq/s: 10, coverage: 32
 fuzz: elapsed: 8m20s, calls: 557898 (1098/sec), seq/s: 10, coverage: 32
 fuzz: elapsed: 8m23s, calls: 561266 (1115/sec), seq/s: 11, coverage: 32
 fuzz: elapsed: 8m26s, calls: 564538 (1090/sec), seq/s: 10, coverage: 32
 fuzz: elapsed: 8m29s, calls: 567938 (1131/sec), seq/s: 11, coverage: 32
 fuzz: elapsed: 8m32s, calls: 571168 (1075/sec), seq/s: 9, coverage: 33
 fuzz: elapsed: 8m35s, calls: 574356 (1060/sec), seq/s: 10, coverage: 33
 fuzz: elapsed: 8m38s, calls: 577506 (1044/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 8m41s, calls: 580777 (1084/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 8m44s, calls: 583953 (1057/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 8m47s, calls: 587132 (1059/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 8m50s, calls: 590160 (1008/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 8m53s, calls: 593243 (1026/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 8m56s, calls: 596383 (1043/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 8m59s, calls: 599474 (1026/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 9m2s, calls: 602644 (1052/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 9m5s, calls: 605825 (1054/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 9m8s, calls: 608934 (1035/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 9m11s, calls: 612130 (1062/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 9m14s, calls: 615315 (1050/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 9m17s, calls: 618440 (1040/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 9m20s, calls: 621620 (1057/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 9m23s, calls: 624789 (1051/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 9m26s, calls: 627946 (1051/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 9m29s, calls: 631105 (1051/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 9m32s, calls: 634266 (1049/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 9m35s, calls: 637506 (1074/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 9m38s, calls: 640706 (1049/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 9m41s, calls: 643877 (1055/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 9m44s, calls: 647119 (1074/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 9m47s, calls: 650340 (1073/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 9m50s, calls: 653396 (1016/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 9m53s, calls: 656478 (1026/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 9m56s, calls: 659713 (1076/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 9m59s, calls: 662829 (1038/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 10m2s, calls: 665981 (1049/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 10m5s, calls: 669144 (1052/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 10m8s, calls: 672310 (1052/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 10m11s, calls: 675457 (1045/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 10m14s, calls: 678693 (1066/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 10m17s, calls: 681767 (1019/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 10m20s, calls: 684966 (1061/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 10m23s, calls: 688252 (1095/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 10m26s, calls: 691481 (1074/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 10m29s, calls: 694750 (1082/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 10m32s, calls: 697930 (1059/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 10m35s, calls: 701103 (1057/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 10m38s, calls: 704319 (1070/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 10m41s, calls: 707461 (1046/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 10m44s, calls: 710664 (1066/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 10m47s, calls: 713756 (1021/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 10m50s, calls: 716867 (1033/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 10m53s, calls: 720075 (1063/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 10m56s, calls: 723229 (1045/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 10m59s, calls: 726381 (1049/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 11m2s, calls: 729558 (1058/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 11m6s, calls: 732721 (1047/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 11m9s, calls: 735875 (1050/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 11m12s, calls: 739023 (1045/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 11m15s, calls: 742237 (1066/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 11m18s, calls: 745352 (1038/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 11m21s, calls: 748557 (1063/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 11m24s, calls: 751826 (1082/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 11m27s, calls: 755022 (1061/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 11m30s, calls: 758197 (1057/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 11m33s, calls: 761360 (1053/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 11m36s, calls: 764484 (1035/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 11m39s, calls: 767660 (1054/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 11m42s, calls: 770792 (1043/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 11m45s, calls: 773999 (1067/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 11m48s, calls: 777063 (1017/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 11m51s, calls: 780220 (1047/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 11m54s, calls: 783414 (1061/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 11m57s, calls: 786717 (1094/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 12m0s, calls: 789890 (1054/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 12m3s, calls: 793046 (1051/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 12m6s, calls: 796257 (1065/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 12m9s, calls: 799398 (1044/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 12m12s, calls: 802523 (1040/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 12m15s, calls: 805709 (1057/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 12m18s, calls: 808855 (1045/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 12m21s, calls: 811988 (1041/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 12m24s, calls: 814970 (990/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 12m27s, calls: 818174 (1065/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 12m30s, calls: 821264 (1027/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 12m33s, calls: 824451 (1061/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 12m36s, calls: 827685 (1071/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 12m39s, calls: 830837 (1046/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 12m42s, calls: 833956 (1034/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 12m45s, calls: 837074 (1036/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 12m48s, calls: 840104 (1009/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 12m51s, calls: 843316 (1068/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 12m54s, calls: 846546 (1075/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 12m57s, calls: 849815 (1083/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 13m0s, calls: 853021 (1065/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 13m3s, calls: 856172 (1044/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 13m6s, calls: 859307 (1043/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 13m9s, calls: 862476 (1055/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 13m12s, calls: 865633 (1050/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 13m15s, calls: 868805 (1053/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 13m18s, calls: 872018 (1068/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 13m21s, calls: 875169 (1047/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 13m24s, calls: 878289 (1038/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 13m27s, calls: 881425 (1043/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 13m30s, calls: 884575 (1046/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 13m33s, calls: 887790 (1070/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 13m36s, calls: 890994 (1060/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 13m39s, calls: 894115 (1037/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 13m42s, calls: 897250 (1039/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 13m45s, calls: 900385 (1042/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 13m48s, calls: 903505 (1033/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 13m51s, calls: 906541 (1011/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 13m54s, calls: 909666 (1038/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 13m57s, calls: 912870 (1066/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 14m0s, calls: 916083 (1066/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 14m3s, calls: 919260 (1052/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 14m6s, calls: 922562 (1078/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 14m9s, calls: 925681 (1039/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 14m12s, calls: 928886 (1062/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 14m15s, calls: 932065 (1058/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 14m18s, calls: 935255 (1062/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 14m21s, calls: 938459 (1067/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 14m24s, calls: 941527 (1019/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 14m27s, calls: 944752 (1073/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 14m30s, calls: 947924 (1051/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 14m33s, calls: 951110 (1061/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 14m36s, calls: 954333 (1074/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 14m39s, calls: 957423 (1028/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 14m42s, calls: 960663 (1079/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 14m45s, calls: 963769 (1031/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 14m48s, calls: 966775 (1001/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 14m51s, calls: 969986 (1064/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 14m54s, calls: 973153 (1052/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 14m57s, calls: 976369 (1069/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 15m0s, calls: 979575 (1064/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 15m3s, calls: 982710 (1043/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 15m6s, calls: 985906 (1064/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 15m9s, calls: 988962 (1012/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 15m12s, calls: 992174 (1064/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 15m15s, calls: 995404 (1072/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 15m18s, calls: 998605 (1065/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 15m21s, calls: 1001829 (1074/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 15m24s, calls: 1005012 (1059/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 15m27s, calls: 1008158 (1047/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 15m30s, calls: 1011359 (1061/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 15m33s, calls: 1014596 (1073/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 15m36s, calls: 1017751 (1048/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 15m39s, calls: 1020882 (1041/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 15m42s, calls: 1024130 (1078/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 15m45s, calls: 1027335 (1053/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 15m48s, calls: 1030460 (1040/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 15m51s, calls: 1033652 (1059/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 15m54s, calls: 1036770 (1038/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 15m57s, calls: 1039880 (1034/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 16m1s, calls: 1043135 (1081/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 16m4s, calls: 1046250 (1037/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 16m7s, calls: 1049419 (1054/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 16m10s, calls: 1052606 (1060/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 16m13s, calls: 1055793 (1059/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 16m16s, calls: 1058998 (1064/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 16m19s, calls: 1062224 (1075/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 16m22s, calls: 1065483 (1082/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 16m25s, calls: 1068693 (1067/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 16m28s, calls: 1071936 (1078/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 16m31s, calls: 1075134 (1063/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 16m34s, calls: 1078320 (1058/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 16m37s, calls: 1081498 (1056/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 16m40s, calls: 1084642 (1044/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 16m43s, calls: 1087719 (1024/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 16m46s, calls: 1090892 (1051/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 16m49s, calls: 1093969 (1021/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 16m52s, calls: 1097131 (1051/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 16m55s, calls: 1100300 (1055/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 16m58s, calls: 1103547 (1080/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 17m1s, calls: 1106640 (1028/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 17m4s, calls: 1109833 (1060/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 17m7s, calls: 1113028 (1059/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 17m10s, calls: 1116105 (1022/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 17m13s, calls: 1119277 (1054/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 17m16s, calls: 1122435 (1049/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 17m19s, calls: 1125589 (1045/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 17m22s, calls: 1128748 (1038/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 17m25s, calls: 1131906 (1045/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 17m28s, calls: 1135166 (1085/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 17m31s, calls: 1138360 (1059/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 17m34s, calls: 1141553 (1050/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 17m37s, calls: 1144727 (1056/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 17m40s, calls: 1147977 (1077/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 17m43s, calls: 1151190 (1068/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 17m46s, calls: 1154302 (1031/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 17m49s, calls: 1157318 (1004/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 17m52s, calls: 1160426 (1035/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 17m55s, calls: 1163549 (1037/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 17m58s, calls: 1166738 (1060/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 18m1s, calls: 1169880 (1045/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 18m4s, calls: 1173019 (1042/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 18m7s, calls: 1176134 (1031/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 18m10s, calls: 1179339 (1064/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 18m13s, calls: 1182478 (1043/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 18m16s, calls: 1185661 (1054/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 18m19s, calls: 1188788 (1041/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 18m22s, calls: 1191951 (1053/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 18m25s, calls: 1195177 (1068/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 18m28s, calls: 1198426 (1071/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 18m31s, calls: 1201538 (1036/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 18m34s, calls: 1204748 (1069/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 18m37s, calls: 1207881 (1039/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 18m40s, calls: 1211035 (1048/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 18m43s, calls: 1214276 (1073/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 18m46s, calls: 1217464 (1061/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 18m49s, calls: 1220578 (1037/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 18m52s, calls: 1223826 (1079/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 18m55s, calls: 1227137 (1103/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 18m58s, calls: 1230346 (1069/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 19m1s, calls: 1233523 (1058/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 19m4s, calls: 1236645 (1036/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 19m7s, calls: 1239822 (1056/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 19m10s, calls: 1242935 (1035/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 19m13s, calls: 1246103 (1054/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 19m16s, calls: 1249393 (1094/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 19m19s, calls: 1252572 (1058/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 19m22s, calls: 1255848 (1088/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 19m25s, calls: 1259119 (1079/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 19m28s, calls: 1262386 (1085/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 19m31s, calls: 1265562 (1056/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 19m34s, calls: 1268722 (1053/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 19m37s, calls: 1271863 (1046/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 19m40s, calls: 1275071 (1067/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 19m43s, calls: 1278272 (1059/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 19m46s, calls: 1281498 (1073/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 19m49s, calls: 1284532 (1010/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 19m52s, calls: 1287740 (1068/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 19m55s, calls: 1290949 (1067/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 19m58s, calls: 1294166 (1072/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 20m1s, calls: 1297255 (1029/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 20m4s, calls: 1300427 (1053/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 20m7s, calls: 1303570 (1042/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 20m10s, calls: 1306751 (1057/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 20m13s, calls: 1309881 (1042/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 20m16s, calls: 1313014 (1043/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 20m19s, calls: 1316220 (1062/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 20m22s, calls: 1319393 (1053/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 20m25s, calls: 1322527 (1044/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 20m28s, calls: 1325732 (1062/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 20m31s, calls: 1328861 (1040/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 20m34s, calls: 1332032 (1056/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 20m37s, calls: 1335270 (1075/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 20m40s, calls: 1338463 (1041/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 20m43s, calls: 1341541 (1025/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 20m46s, calls: 1344686 (1048/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 20m50s, calls: 1347693 (996/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 20m53s, calls: 1350869 (1054/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 20m56s, calls: 1354038 (1053/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 20m59s, calls: 1357221 (1038/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 21m2s, calls: 1360462 (1077/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 21m5s, calls: 1363639 (1055/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 21m8s, calls: 1366793 (1046/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 21m11s, calls: 1370044 (1078/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 21m14s, calls: 1373222 (1052/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 21m17s, calls: 1376455 (1073/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 21m20s, calls: 1379607 (1045/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 21m23s, calls: 1382800 (1062/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 21m26s, calls: 1386036 (1075/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 21m29s, calls: 1389155 (1036/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 21m32s, calls: 1392312 (1051/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 21m35s, calls: 1395436 (1041/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 21m38s, calls: 1398547 (1030/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 21m41s, calls: 1401765 (1072/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 21m44s, calls: 1405005 (1077/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 21m47s, calls: 1408172 (1053/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 21m50s, calls: 1411245 (1021/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 21m53s, calls: 1414493 (1082/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 21m56s, calls: 1417687 (1063/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 21m59s, calls: 1420943 (1085/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 22m2s, calls: 1424148 (1063/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 22m5s, calls: 1427293 (1045/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 22m8s, calls: 1430383 (1027/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 22m11s, calls: 1433549 (1054/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 22m14s, calls: 1436749 (1064/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 22m17s, calls: 1439951 (1067/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 22m20s, calls: 1443140 (1062/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 22m23s, calls: 1446384 (1078/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 22m26s, calls: 1449543 (1049/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 22m29s, calls: 1452760 (1071/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 22m32s, calls: 1455976 (1057/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 22m35s, calls: 1459285 (1101/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 22m38s, calls: 1462552 (1067/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 22m41s, calls: 1465758 (1067/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 22m44s, calls: 1469012 (1079/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 22m47s, calls: 1472228 (1068/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 22m50s, calls: 1475285 (1015/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 22m53s, calls: 1478400 (1034/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 22m56s, calls: 1481585 (1061/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 22m59s, calls: 1484826 (1077/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 23m2s, calls: 1487942 (1035/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 23m5s, calls: 1491128 (1055/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 23m8s, calls: 1494255 (1039/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 23m11s, calls: 1497392 (1044/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 23m14s, calls: 1500485 (1027/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 23m17s, calls: 1503675 (1062/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 23m20s, calls: 1506829 (1050/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 23m23s, calls: 1509885 (1016/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 23m26s, calls: 1513074 (1061/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 23m29s, calls: 1516294 (1072/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 23m32s, calls: 1519378 (1023/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 23m35s, calls: 1522526 (1047/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 23m38s, calls: 1525734 (1068/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 23m41s, calls: 1528811 (1025/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 23m44s, calls: 1531963 (1048/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 23m47s, calls: 1534981 (1000/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 23m50s, calls: 1538072 (1026/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 23m53s, calls: 1541274 (1063/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 23m56s, calls: 1544441 (1053/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 23m59s, calls: 1547627 (1061/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 24m2s, calls: 1550775 (1045/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 24m5s, calls: 1553965 (1059/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 24m8s, calls: 1557167 (1066/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 24m11s, calls: 1560269 (1029/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 24m14s, calls: 1563427 (1047/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 24m17s, calls: 1566653 (1074/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 24m20s, calls: 1569801 (1048/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 24m23s, calls: 1572955 (1049/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 24m26s, calls: 1576039 (1026/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 24m29s, calls: 1579226 (1061/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 24m32s, calls: 1582426 (1063/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 24m35s, calls: 1585597 (1053/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 24m38s, calls: 1588744 (1048/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 24m41s, calls: 1591858 (1033/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 24m44s, calls: 1595023 (1054/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 24m47s, calls: 1598184 (1052/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 24m50s, calls: 1601234 (1012/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 24m53s, calls: 1604426 (1059/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 24m56s, calls: 1607634 (1068/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 24m59s, calls: 1610880 (1077/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 25m2s, calls: 1614051 (1054/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 25m5s, calls: 1617300 (1079/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 25m8s, calls: 1620536 (1071/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 25m11s, calls: 1623765 (1073/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 25m14s, calls: 1626944 (1053/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 25m17s, calls: 1630180 (1075/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 25m20s, calls: 1633315 (1043/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 25m23s, calls: 1636472 (1052/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 25m26s, calls: 1639721 (1076/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 25m29s, calls: 1642909 (1061/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 25m32s, calls: 1646216 (1098/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 25m35s, calls: 1649489 (1087/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 25m38s, calls: 1652720 (1076/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 25m41s, calls: 1655887 (1054/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 25m44s, calls: 1659060 (1054/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 25m48s, calls: 1662160 (1032/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 25m51s, calls: 1665250 (1024/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 25m54s, calls: 1668483 (1075/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 25m57s, calls: 1671692 (1053/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 26m0s, calls: 1674873 (1059/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 26m3s, calls: 1678022 (1045/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 26m6s, calls: 1681191 (1053/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 26m9s, calls: 1684299 (1034/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 26m12s, calls: 1687612 (1103/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 26m15s, calls: 1690731 (1038/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 26m18s, calls: 1693956 (1072/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 26m21s, calls: 1697086 (1028/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 26m24s, calls: 1700185 (1030/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 26m27s, calls: 1703391 (1068/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 26m30s, calls: 1706604 (1064/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 26m33s, calls: 1709790 (1058/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 26m36s, calls: 1712972 (1056/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 26m39s, calls: 1716170 (1064/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 26m42s, calls: 1719377 (1068/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 26m45s, calls: 1722516 (1039/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 26m48s, calls: 1725620 (1033/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 26m51s, calls: 1728726 (1033/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 26m54s, calls: 1731857 (1040/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 26m57s, calls: 1735087 (1070/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 27m0s, calls: 1738212 (1033/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 27m3s, calls: 1741418 (1063/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 27m6s, calls: 1744632 (1066/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 27m9s, calls: 1747831 (1063/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 27m12s, calls: 1751058 (1068/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 27m15s, calls: 1754227 (1054/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 27m18s, calls: 1757443 (1070/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 27m21s, calls: 1760609 (1054/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 27m24s, calls: 1763781 (1056/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 27m27s, calls: 1766919 (1044/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 27m30s, calls: 1770056 (1045/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 27m33s, calls: 1773246 (1061/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 27m36s, calls: 1776427 (1059/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 27m39s, calls: 1779549 (1038/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 27m42s, calls: 1782715 (1053/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 27m45s, calls: 1785924 (1066/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 27m48s, calls: 1789124 (1061/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 27m51s, calls: 1792180 (1016/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 27m54s, calls: 1795379 (1065/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 27m57s, calls: 1798522 (1047/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 28m0s, calls: 1801727 (1064/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 28m3s, calls: 1804907 (1059/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 28m6s, calls: 1808063 (1040/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 28m9s, calls: 1811252 (1060/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 28m12s, calls: 1814465 (1069/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 28m15s, calls: 1817660 (1064/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 28m18s, calls: 1820749 (1028/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 28m21s, calls: 1823928 (1056/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 28m24s, calls: 1827028 (1026/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 28m27s, calls: 1830158 (1043/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 28m30s, calls: 1833388 (1070/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 28m33s, calls: 1836529 (1045/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 28m36s, calls: 1839743 (1068/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 28m39s, calls: 1842927 (1060/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 28m42s, calls: 1846140 (1068/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 28m45s, calls: 1849335 (1061/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 28m48s, calls: 1852361 (1004/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 28m51s, calls: 1855530 (1052/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 28m54s, calls: 1858646 (1038/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 28m57s, calls: 1861849 (1062/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 29m0s, calls: 1865041 (1063/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 29m3s, calls: 1868135 (1028/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 29m6s, calls: 1871201 (1017/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 29m9s, calls: 1874409 (1065/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 29m12s, calls: 1877663 (1077/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 29m15s, calls: 1880772 (1032/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 29m18s, calls: 1883956 (1061/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 29m21s, calls: 1887147 (1060/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 29m24s, calls: 1890351 (1063/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 29m27s, calls: 1893446 (1030/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 29m30s, calls: 1896594 (1045/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 29m33s, calls: 1899709 (1037/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 29m36s, calls: 1902846 (1040/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 29m39s, calls: 1906057 (1070/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 29m42s, calls: 1909270 (1068/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 29m45s, calls: 1912373 (1031/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 29m48s, calls: 1915398 (1007/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 29m51s, calls: 1918588 (1058/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 29m54s, calls: 1921763 (1055/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 29m57s, calls: 1924954 (1061/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 30m0s, calls: 1928033 (1020/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 30m3s, calls: 1931209 (1051/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 30m6s, calls: 1934364 (1048/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 30m9s, calls: 1937570 (1065/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 30m12s, calls: 1940650 (1025/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 30m15s, calls: 1943677 (1006/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 30m18s, calls: 1946851 (1057/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 30m21s, calls: 1949962 (1036/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 30m24s, calls: 1953066 (1033/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 30m27s, calls: 1956191 (1037/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 30m30s, calls: 1959374 (1060/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 30m33s, calls: 1962584 (1065/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 30m36s, calls: 1965768 (1060/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 30m39s, calls: 1968895 (1038/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 30m43s, calls: 1972041 (1042/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 30m46s, calls: 1975175 (1043/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 30m49s, calls: 1978178 (1000/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 30m52s, calls: 1981393 (1069/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 30m55s, calls: 1984560 (1053/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 30m58s, calls: 1987715 (1050/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 31m1s, calls: 1990934 (1071/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 31m4s, calls: 1994136 (1066/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 31m7s, calls: 1997333 (1063/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 31m10s, calls: 2000490 (1052/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 31m13s, calls: 2003699 (1069/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 31m16s, calls: 2006861 (1050/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 31m19s, calls: 2010093 (1075/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 31m22s, calls: 2013254 (1053/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 31m25s, calls: 2016508 (1080/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 31m28s, calls: 2019702 (1063/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 31m31s, calls: 2022815 (1037/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 31m34s, calls: 2025932 (1035/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 31m37s, calls: 2029086 (1038/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 31m40s, calls: 2032299 (1059/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 31m43s, calls: 2035387 (1006/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 31m46s, calls: 2038533 (1045/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 31m49s, calls: 2041604 (1022/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 31m52s, calls: 2044852 (1075/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 31m55s, calls: 2048020 (1052/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 31m58s, calls: 2051176 (1047/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 32m1s, calls: 2054387 (1067/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 32m4s, calls: 2057638 (1076/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 32m7s, calls: 2060742 (1034/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 32m10s, calls: 2063876 (1041/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 32m13s, calls: 2066987 (1036/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 32m16s, calls: 2070129 (1046/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 32m19s, calls: 2073355 (1071/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 32m22s, calls: 2076548 (1063/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 32m25s, calls: 2079724 (1054/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 32m28s, calls: 2082880 (1046/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 32m31s, calls: 2086085 (1063/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 32m34s, calls: 2089302 (1071/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 32m37s, calls: 2092450 (1049/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 32m40s, calls: 2095649 (1059/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 32m43s, calls: 2098802 (1048/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 32m46s, calls: 2101857 (1016/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 32m49s, calls: 2104921 (1016/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 32m52s, calls: 2108135 (1070/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 32m55s, calls: 2111306 (1056/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 32m58s, calls: 2114442 (1044/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 33m1s, calls: 2117672 (1071/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 33m4s, calls: 2120785 (1036/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 33m7s, calls: 2123861 (1025/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 33m10s, calls: 2127028 (1055/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 33m13s, calls: 2130169 (1045/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 33m16s, calls: 2133230 (1018/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 33m19s, calls: 2136446 (1071/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 33m22s, calls: 2139646 (1063/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 33m25s, calls: 2142825 (1053/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 33m28s, calls: 2145898 (1015/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 33m31s, calls: 2149100 (1063/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 33m34s, calls: 2152280 (1059/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 33m37s, calls: 2155417 (1042/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 33m40s, calls: 2158644 (1068/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 33m43s, calls: 2161712 (1022/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 33m46s, calls: 2164930 (1047/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 33m49s, calls: 2168017 (1023/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 33m52s, calls: 2171271 (1083/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 33m55s, calls: 2174429 (1051/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 33m58s, calls: 2177590 (1048/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 34m1s, calls: 2180722 (1040/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 34m4s, calls: 2183920 (1065/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 34m7s, calls: 2187024 (1033/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 34m10s, calls: 2190296 (1090/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 34m13s, calls: 2193568 (1090/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 34m16s, calls: 2196702 (1044/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 34m19s, calls: 2199857 (1049/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 34m22s, calls: 2203077 (1070/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 34m25s, calls: 2206260 (1060/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 34m28s, calls: 2209521 (1085/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 34m31s, calls: 2212766 (1074/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 34m34s, calls: 2216016 (1079/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 34m37s, calls: 2219186 (1052/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 34m40s, calls: 2222356 (1052/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 34m43s, calls: 2225438 (1025/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 34m46s, calls: 2228641 (1064/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 34m49s, calls: 2231591 (982/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 34m52s, calls: 2234780 (1058/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 34m55s, calls: 2237925 (1046/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 34m58s, calls: 2241103 (1059/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 35m1s, calls: 2244282 (1056/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 35m4s, calls: 2247432 (1049/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 35m7s, calls: 2250533 (1029/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 35m10s, calls: 2253686 (1047/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 35m13s, calls: 2256785 (1032/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 35m16s, calls: 2260001 (1070/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 35m19s, calls: 2263135 (1041/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 35m22s, calls: 2266312 (1057/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 35m25s, calls: 2269486 (1057/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 35m28s, calls: 2272650 (1052/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 35m31s, calls: 2275844 (1059/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 35m34s, calls: 2279016 (1050/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 35m37s, calls: 2282188 (1056/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 35m41s, calls: 2285418 (1054/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 35m44s, calls: 2288605 (1061/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 35m47s, calls: 2291766 (1052/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 35m50s, calls: 2294800 (1005/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 35m53s, calls: 2297997 (1060/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 35m56s, calls: 2301121 (1039/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 35m59s, calls: 2304270 (1045/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 36m2s, calls: 2307444 (1057/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 36m5s, calls: 2310721 (1088/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 36m8s, calls: 2313783 (1020/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 36m11s, calls: 2316968 (1058/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 36m14s, calls: 2320184 (1070/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 36m17s, calls: 2323463 (1068/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 36m20s, calls: 2326580 (1034/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 36m23s, calls: 2329830 (1080/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 36m26s, calls: 2332947 (1035/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 36m29s, calls: 2336134 (1058/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 36m32s, calls: 2339281 (1043/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 36m35s, calls: 2342452 (1054/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 36m38s, calls: 2345651 (1061/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 36m41s, calls: 2348794 (1044/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 36m44s, calls: 2352084 (1095/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 36m47s, calls: 2355252 (1055/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 36m50s, calls: 2358329 (1025/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 36m53s, calls: 2361433 (1030/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 36m56s, calls: 2364547 (1035/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 36m59s, calls: 2367769 (1071/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 37m2s, calls: 2370884 (1033/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 37m5s, calls: 2374112 (1075/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 37m8s, calls: 2377322 (1068/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 37m11s, calls: 2380522 (1050/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 37m14s, calls: 2383730 (1069/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 37m17s, calls: 2386871 (1045/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 37m20s, calls: 2390146 (1072/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 37m23s, calls: 2393325 (1055/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 37m26s, calls: 2396524 (1065/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 37m29s, calls: 2399728 (1065/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 37m32s, calls: 2402930 (1065/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 37m35s, calls: 2406091 (1052/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 37m38s, calls: 2409321 (1075/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 37m41s, calls: 2412493 (1055/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 37m44s, calls: 2415720 (1071/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 37m47s, calls: 2418877 (1049/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 37m50s, calls: 2421903 (1006/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 37m53s, calls: 2425134 (1072/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 37m56s, calls: 2428270 (1045/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 37m59s, calls: 2431500 (1074/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 38m2s, calls: 2434757 (1083/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 38m5s, calls: 2437926 (1052/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 38m8s, calls: 2441084 (1049/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 38m11s, calls: 2444314 (1075/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 38m14s, calls: 2447431 (1035/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 38m17s, calls: 2450600 (1055/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 38m20s, calls: 2453768 (1053/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 38m23s, calls: 2456872 (1032/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 38m26s, calls: 2460027 (1049/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 38m29s, calls: 2463196 (1056/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 38m32s, calls: 2466325 (1042/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 38m35s, calls: 2469484 (1052/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 38m38s, calls: 2472711 (1069/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 38m41s, calls: 2475892 (1058/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 38m44s, calls: 2479050 (1051/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 38m47s, calls: 2482181 (1037/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 38m50s, calls: 2485219 (1008/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 38m53s, calls: 2488368 (1049/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 38m56s, calls: 2491579 (1067/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 38m59s, calls: 2494836 (1080/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 39m2s, calls: 2498041 (1066/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 39m5s, calls: 2501269 (1068/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 39m8s, calls: 2504469 (1062/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 39m11s, calls: 2507593 (1037/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 39m14s, calls: 2510733 (1045/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 39m17s, calls: 2513995 (1087/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 39m20s, calls: 2517167 (1056/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 39m23s, calls: 2520459 (1093/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 39m26s, calls: 2523743 (1079/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 39m29s, calls: 2526932 (1061/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 39m32s, calls: 2530130 (1064/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 39m35s, calls: 2533316 (1059/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 39m38s, calls: 2536526 (1063/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 39m41s, calls: 2539649 (1038/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 39m44s, calls: 2542769 (1035/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 39m47s, calls: 2545815 (1015/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 39m50s, calls: 2548858 (1009/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 39m53s, calls: 2551994 (1044/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 39m56s, calls: 2555213 (1070/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 39m59s, calls: 2558381 (1052/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 40m2s, calls: 2561555 (1053/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 40m5s, calls: 2564720 (1050/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 40m9s, calls: 2567914 (1056/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 40m12s, calls: 2571103 (1060/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 40m15s, calls: 2574310 (1065/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 40m18s, calls: 2577468 (1049/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 40m21s, calls: 2580591 (1038/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 40m24s, calls: 2583741 (1049/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 40m27s, calls: 2587022 (1090/sec), seq/s: 12, coverage: 34
 fuzz: elapsed: 40m30s, calls: 2590195 (1050/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 40m33s, calls: 2593389 (1058/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 40m36s, calls: 2596562 (1051/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 40m39s, calls: 2599690 (1040/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 40m42s, calls: 2602901 (1068/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 40m45s, calls: 2605911 (1003/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 40m48s, calls: 2608954 (1012/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 40m51s, calls: 2612092 (1044/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 40m54s, calls: 2615303 (1069/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 40m57s, calls: 2618635 (1103/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 41m0s, calls: 2621805 (1051/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 41m3s, calls: 2625081 (1065/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 41m6s, calls: 2628321 (1076/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 41m9s, calls: 2631460 (1041/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 41m12s, calls: 2634575 (1038/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 41m15s, calls: 2637776 (1066/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 41m18s, calls: 2641016 (1074/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 41m21s, calls: 2644199 (1059/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 41m24s, calls: 2647345 (1046/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 41m27s, calls: 2650588 (1074/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 41m30s, calls: 2653773 (1060/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 41m33s, calls: 2656947 (1057/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 41m36s, calls: 2660139 (1057/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 41m39s, calls: 2663334 (1057/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 41m42s, calls: 2666550 (1068/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 41m45s, calls: 2669671 (1040/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 41m48s, calls: 2672774 (1032/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 41m51s, calls: 2675917 (1040/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 41m54s, calls: 2679047 (1042/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 41m57s, calls: 2682189 (1044/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 42m0s, calls: 2685347 (1050/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 42m3s, calls: 2688429 (1022/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 42m6s, calls: 2691608 (1058/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 42m9s, calls: 2694812 (1067/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 42m12s, calls: 2697989 (1058/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 42m15s, calls: 2701169 (1055/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 42m18s, calls: 2704412 (1074/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 42m21s, calls: 2707679 (1084/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 42m24s, calls: 2710849 (1056/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 42m27s, calls: 2714029 (1057/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 42m30s, calls: 2717228 (1059/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 42m33s, calls: 2720425 (1064/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 42m36s, calls: 2723576 (1048/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 42m39s, calls: 2726808 (1074/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 42m42s, calls: 2729926 (1037/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 42m45s, calls: 2733173 (1077/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 42m48s, calls: 2736243 (1021/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 42m51s, calls: 2739395 (1048/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 42m54s, calls: 2742564 (1056/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 42m57s, calls: 2745703 (1044/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 43m0s, calls: 2748817 (1037/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 43m3s, calls: 2751934 (1037/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 43m6s, calls: 2755135 (1065/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 43m9s, calls: 2758299 (1050/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 43m12s, calls: 2761392 (1030/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 43m15s, calls: 2764539 (1042/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 43m18s, calls: 2767692 (1044/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 43m21s, calls: 2770818 (1041/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 43m24s, calls: 2773907 (1026/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 43m27s, calls: 2777088 (1056/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 43m30s, calls: 2780244 (1045/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 43m33s, calls: 2783464 (1071/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 43m36s, calls: 2786626 (1052/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 43m39s, calls: 2789754 (1037/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 43m42s, calls: 2792903 (1048/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 43m45s, calls: 2795958 (1018/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 43m48s, calls: 2799014 (1016/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 43m51s, calls: 2802221 (1067/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 43m54s, calls: 2805351 (1037/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 43m57s, calls: 2808477 (1038/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 44m0s, calls: 2811699 (1073/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 44m3s, calls: 2814932 (1074/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 44m6s, calls: 2818099 (1052/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 44m9s, calls: 2821149 (1015/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 44m12s, calls: 2824405 (1082/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 44m15s, calls: 2827741 (1107/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 44m18s, calls: 2830922 (1059/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 44m21s, calls: 2834010 (1023/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 44m24s, calls: 2837120 (1033/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 44m27s, calls: 2840305 (1060/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 44m30s, calls: 2843552 (1080/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 44m33s, calls: 2846776 (1070/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 44m36s, calls: 2850042 (1086/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 44m39s, calls: 2853240 (1064/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 44m42s, calls: 2856455 (1068/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 44m45s, calls: 2859606 (1043/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 44m48s, calls: 2862616 (1003/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 44m51s, calls: 2865849 (1076/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 44m54s, calls: 2869028 (1052/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 44m57s, calls: 2872182 (1050/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 45m0s, calls: 2875396 (1068/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 45m3s, calls: 2878694 (1096/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 45m6s, calls: 2881872 (1058/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 45m9s, calls: 2885079 (1068/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 45m12s, calls: 2888300 (1072/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 45m15s, calls: 2891478 (1058/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 45m18s, calls: 2894571 (1029/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 45m22s, calls: 2897770 (1063/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 45m25s, calls: 2900860 (1026/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 45m28s, calls: 2904040 (1058/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 45m31s, calls: 2907173 (1042/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 45m34s, calls: 2910379 (1068/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 45m37s, calls: 2913520 (1046/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 45m40s, calls: 2916676 (1051/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 45m43s, calls: 2919761 (1025/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 45m46s, calls: 2922852 (1028/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 45m49s, calls: 2925819 (988/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 45m52s, calls: 2928996 (1055/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 45m55s, calls: 2932301 (1100/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 45m58s, calls: 2935560 (1077/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 46m1s, calls: 2938717 (1049/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 46m4s, calls: 2941884 (1054/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 46m7s, calls: 2945014 (1039/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 46m10s, calls: 2948254 (1077/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 46m13s, calls: 2951456 (1066/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 46m16s, calls: 2954733 (1090/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 46m19s, calls: 2957864 (1040/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 46m22s, calls: 2961109 (1080/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 46m25s, calls: 2964248 (1030/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 46m28s, calls: 2967544 (1096/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 46m31s, calls: 2970775 (1074/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 46m34s, calls: 2973956 (1055/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 46m37s, calls: 2977196 (1075/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 46m40s, calls: 2980391 (1059/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 46m43s, calls: 2983618 (1070/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 46m46s, calls: 2986827 (1069/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 46m49s, calls: 2989785 (984/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 46m52s, calls: 2992966 (1058/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 46m55s, calls: 2996148 (1055/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 46m58s, calls: 2999374 (1074/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 47m1s, calls: 3002551 (1057/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 47m4s, calls: 3005794 (1077/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 47m7s, calls: 3008996 (1063/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 47m10s, calls: 3012236 (1074/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 47m13s, calls: 3015452 (1065/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 47m16s, calls: 3018708 (1083/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 47m19s, calls: 3021895 (1061/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 47m22s, calls: 3024998 (1029/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 47m25s, calls: 3028141 (1042/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 47m28s, calls: 3031344 (1067/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 47m31s, calls: 3034502 (1048/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 47m34s, calls: 3037699 (1065/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 47m37s, calls: 3040937 (1072/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 47m40s, calls: 3044094 (1042/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 47m43s, calls: 3047383 (1095/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 47m46s, calls: 3050590 (1067/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 47m49s, calls: 3053654 (1021/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 47m52s, calls: 3056811 (1048/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 47m55s, calls: 3060046 (1077/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 47m58s, calls: 3063166 (1037/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 48m1s, calls: 3066338 (1057/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 48m4s, calls: 3069477 (1045/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 48m7s, calls: 3072705 (1075/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 48m10s, calls: 3075859 (1051/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 48m13s, calls: 3078946 (1022/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 48m16s, calls: 3082197 (1080/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 48m19s, calls: 3085360 (1052/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 48m22s, calls: 3088574 (1068/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 48m25s, calls: 3091680 (1032/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 48m28s, calls: 3094859 (1059/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 48m31s, calls: 3098127 (1088/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 48m34s, calls: 3101268 (1044/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 48m37s, calls: 3104455 (1061/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 48m40s, calls: 3107608 (1050/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 48m43s, calls: 3110783 (1054/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 48m46s, calls: 3113974 (1057/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 48m49s, calls: 3117071 (1031/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 48m52s, calls: 3120283 (1069/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 48m55s, calls: 3123470 (1062/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 48m58s, calls: 3126697 (1072/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 49m1s, calls: 3129954 (1083/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 49m4s, calls: 3133162 (1066/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 49m7s, calls: 3136351 (1061/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 49m10s, calls: 3139587 (1078/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 49m13s, calls: 3142709 (1037/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 49m16s, calls: 3146009 (1099/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 49m19s, calls: 3149134 (1037/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 49m22s, calls: 3152226 (1024/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 49m25s, calls: 3155380 (1044/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 49m28s, calls: 3158601 (1066/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 49m31s, calls: 3161757 (1049/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 49m34s, calls: 3164932 (1054/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 49m37s, calls: 3168116 (1060/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 49m40s, calls: 3171355 (1072/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 49m43s, calls: 3174511 (1050/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 49m46s, calls: 3177777 (1085/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 49m49s, calls: 3180794 (1001/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 49m52s, calls: 3184062 (1086/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 49m55s, calls: 3187266 (1067/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 49m58s, calls: 3190458 (1059/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 50m1s, calls: 3193640 (1058/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 50m4s, calls: 3196778 (1044/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 50m7s, calls: 3200084 (1096/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 50m10s, calls: 3203219 (1042/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 50m13s, calls: 3206330 (1036/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 50m16s, calls: 3209572 (1079/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 50m19s, calls: 3212756 (1060/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 50m22s, calls: 3216010 (1084/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 50m25s, calls: 3219238 (1071/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 50m28s, calls: 3222440 (1063/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 50m31s, calls: 3225666 (1072/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 50m34s, calls: 3228787 (1040/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 50m37s, calls: 3231980 (1061/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 50m40s, calls: 3235069 (1025/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 50m43s, calls: 3238240 (1055/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 50m46s, calls: 3241451 (1070/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 50m50s, calls: 3244568 (1020/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 50m53s, calls: 3247733 (1049/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 50m56s, calls: 3250808 (1019/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 50m59s, calls: 3253930 (1040/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 51m2s, calls: 3257132 (1065/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 51m5s, calls: 3260435 (1100/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 51m8s, calls: 3263625 (1063/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 51m11s, calls: 3266836 (1067/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 51m14s, calls: 3270035 (1065/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 51m17s, calls: 3273179 (1047/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 51m20s, calls: 3276361 (1058/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 51m23s, calls: 3279609 (1082/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 51m26s, calls: 3282801 (1058/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 51m29s, calls: 3285979 (1057/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 51m32s, calls: 3289109 (1042/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 51m35s, calls: 3292337 (1075/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 51m38s, calls: 3295515 (1053/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 51m41s, calls: 3298719 (1063/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 51m44s, calls: 3301900 (1059/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 51m47s, calls: 3305099 (1064/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 51m50s, calls: 3308183 (1027/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 51m53s, calls: 3311250 (1019/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 51m56s, calls: 3314399 (1049/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 51m59s, calls: 3317575 (1054/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 52m2s, calls: 3320782 (1067/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 52m5s, calls: 3323954 (1054/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 52m8s, calls: 3327032 (1025/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 52m11s, calls: 3330230 (1047/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 52m14s, calls: 3333432 (1066/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 52m17s, calls: 3336452 (1005/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 52m20s, calls: 3339601 (1048/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 52m23s, calls: 3342720 (1036/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 52m26s, calls: 3345906 (1056/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 52m29s, calls: 3349082 (1055/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 52m32s, calls: 3352300 (1070/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 52m35s, calls: 3355438 (1044/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 52m38s, calls: 3358641 (1060/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 52m41s, calls: 3361881 (1079/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 52m44s, calls: 3365054 (1055/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 52m47s, calls: 3368198 (1046/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 52m50s, calls: 3371246 (1011/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 52m53s, calls: 3374407 (1051/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 52m56s, calls: 3377533 (1040/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 52m59s, calls: 3380744 (1063/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 53m2s, calls: 3383978 (1076/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 53m5s, calls: 3387104 (1035/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 53m8s, calls: 3390250 (1047/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 53m11s, calls: 3393485 (1077/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 53m14s, calls: 3396596 (1033/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 53m17s, calls: 3399788 (1062/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 53m20s, calls: 3402986 (1064/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 53m23s, calls: 3406165 (1056/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 53m26s, calls: 3409392 (1072/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 53m29s, calls: 3412439 (1014/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 53m32s, calls: 3415545 (1033/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 53m35s, calls: 3418724 (1057/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 53m38s, calls: 3421881 (1048/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 53m41s, calls: 3425012 (1023/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 53m44s, calls: 3428242 (1071/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 53m47s, calls: 3431474 (1071/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 53m50s, calls: 3434566 (1028/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 53m53s, calls: 3437730 (1052/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 53m56s, calls: 3440808 (1024/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 53m59s, calls: 3443869 (1017/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 54m2s, calls: 3447061 (1063/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 54m5s, calls: 3450306 (1077/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 54m8s, calls: 3453490 (1057/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 54m11s, calls: 3456559 (1019/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 54m14s, calls: 3459662 (1033/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 54m17s, calls: 3462874 (1064/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 54m20s, calls: 3466168 (1094/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 54m23s, calls: 3469346 (1055/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 54m26s, calls: 3472492 (1045/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 54m29s, calls: 3475679 (1057/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 54m32s, calls: 3478827 (1049/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 54m35s, calls: 3481911 (1023/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 54m38s, calls: 3485103 (1063/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 54m41s, calls: 3488297 (1061/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 54m44s, calls: 3491514 (1068/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 54m47s, calls: 3494741 (1067/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 54m50s, calls: 3497917 (1054/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 54m53s, calls: 3500996 (1024/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 54m56s, calls: 3504115 (1039/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 54m59s, calls: 3507349 (1076/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 55m2s, calls: 3510637 (1091/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 55m5s, calls: 3513883 (1076/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 55m8s, calls: 3517229 (1114/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 55m11s, calls: 3520399 (1052/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 55m14s, calls: 3523478 (1020/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 55m17s, calls: 3526647 (1055/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 55m20s, calls: 3529780 (1043/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 55m23s, calls: 3532906 (1039/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 55m26s, calls: 3536089 (1057/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 55m29s, calls: 3539246 (1052/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 55m32s, calls: 3542473 (1071/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 55m35s, calls: 3545585 (1031/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 55m38s, calls: 3548795 (1069/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 55m41s, calls: 3552059 (1086/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 55m44s, calls: 3555318 (1082/sec), seq/s: 11, coverage: 34
 fuzz: elapsed: 55m48s, calls: 3558484 (1052/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 55m51s, calls: 3561670 (1058/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 55m54s, calls: 3564654 (992/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 55m57s, calls: 3567518 (953/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 56m0s, calls: 3570459 (974/sec), seq/s: 8, coverage: 34
 fuzz: elapsed: 56m3s, calls: 3573350 (963/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 56m6s, calls: 3576245 (961/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 56m9s, calls: 3579119 (953/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 56m12s, calls: 3581930 (933/sec), seq/s: 8, coverage: 34
 fuzz: elapsed: 56m15s, calls: 3584778 (946/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 56m18s, calls: 3587513 (909/sec), seq/s: 8, coverage: 34
 fuzz: elapsed: 56m21s, calls: 3590287 (924/sec), seq/s: 8, coverage: 34
 fuzz: elapsed: 56m24s, calls: 3593139 (948/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 56m27s, calls: 3595955 (937/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 56m30s, calls: 3598740 (926/sec), seq/s: 8, coverage: 34
 fuzz: elapsed: 56m33s, calls: 3601547 (934/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 56m36s, calls: 3604411 (948/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 56m39s, calls: 3607307 (962/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 56m42s, calls: 3610130 (940/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 56m45s, calls: 3612972 (941/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 56m48s, calls: 3615748 (919/sec), seq/s: 8, coverage: 34
 fuzz: elapsed: 56m51s, calls: 3618503 (913/sec), seq/s: 8, coverage: 34
 fuzz: elapsed: 56m54s, calls: 3621336 (939/sec), seq/s: 8, coverage: 34
 fuzz: elapsed: 56m57s, calls: 3624187 (935/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 57m0s, calls: 3627037 (949/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 57m3s, calls: 3629892 (950/sec), seq/s: 8, coverage: 34
 fuzz: elapsed: 57m6s, calls: 3632721 (941/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 57m9s, calls: 3635600 (954/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 57m12s, calls: 3638479 (953/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 57m15s, calls: 3641285 (931/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 57m18s, calls: 3644103 (938/sec), seq/s: 8, coverage: 34
 fuzz: elapsed: 57m21s, calls: 3646909 (931/sec), seq/s: 8, coverage: 34
 fuzz: elapsed: 57m24s, calls: 3649792 (960/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 57m27s, calls: 3652599 (935/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 57m30s, calls: 3655381 (925/sec), seq/s: 8, coverage: 34
 fuzz: elapsed: 57m33s, calls: 3658227 (944/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 57m36s, calls: 3661043 (934/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 57m39s, calls: 3663841 (929/sec), seq/s: 8, coverage: 34
 fuzz: elapsed: 57m42s, calls: 3666660 (935/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 57m45s, calls: 3669463 (933/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 57m48s, calls: 3672261 (931/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 57m51s, calls: 3675127 (950/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 57m54s, calls: 3677831 (901/sec), seq/s: 8, coverage: 34
 fuzz: elapsed: 57m57s, calls: 3680625 (930/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 58m0s, calls: 3683446 (938/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 58m3s, calls: 3686288 (943/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 58m6s, calls: 3689113 (938/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 58m9s, calls: 3692043 (974/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 58m12s, calls: 3694885 (943/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 58m15s, calls: 3697702 (936/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 58m18s, calls: 3700579 (957/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 58m21s, calls: 3703360 (925/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 58m24s, calls: 3706182 (939/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 58m27s, calls: 3708990 (934/sec), seq/s: 8, coverage: 34
 fuzz: elapsed: 58m30s, calls: 3711878 (961/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 58m33s, calls: 3714721 (942/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 58m36s, calls: 3717591 (953/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 58m39s, calls: 3720450 (947/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 58m42s, calls: 3723267 (933/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 58m45s, calls: 3726104 (943/sec), seq/s: 8, coverage: 34
 fuzz: elapsed: 58m48s, calls: 3728830 (908/sec), seq/s: 8, coverage: 34
 fuzz: elapsed: 58m51s, calls: 3731641 (931/sec), seq/s: 8, coverage: 34
 fuzz: elapsed: 58m54s, calls: 3734463 (939/sec), seq/s: 8, coverage: 34
 fuzz: elapsed: 58m57s, calls: 3737171 (900/sec), seq/s: 8, coverage: 34
 fuzz: elapsed: 59m0s, calls: 3739932 (919/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 59m3s, calls: 3742753 (937/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 59m6s, calls: 3745631 (956/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 59m9s, calls: 3748467 (939/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 59m12s, calls: 3751261 (925/sec), seq/s: 8, coverage: 34
 fuzz: elapsed: 59m15s, calls: 3754101 (943/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 59m18s, calls: 3756986 (958/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 59m21s, calls: 3759841 (946/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 59m24s, calls: 3762592 (916/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 59m27s, calls: 3765492 (965/sec), seq/s: 8, coverage: 34
 fuzz: elapsed: 59m30s, calls: 3768297 (931/sec), seq/s: 8, coverage: 34
 fuzz: elapsed: 59m33s, calls: 3771105 (934/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 59m36s, calls: 3773987 (950/sec), seq/s: 10, coverage: 34
 fuzz: elapsed: 59m39s, calls: 3776930 (978/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 59m42s, calls: 3779806 (957/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 59m45s, calls: 3782744 (959/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 59m48s, calls: 3785385 (878/sec), seq/s: 7, coverage: 34
 fuzz: elapsed: 59m51s, calls: 3788198 (936/sec), seq/s: 9, coverage: 34
 fuzz: elapsed: 59m54s, calls: 3790992 (930/sec), seq/s: 8, coverage: 34
 fuzz: elapsed: 59m57s, calls: 3793868 (957/sec), seq/s: 9, coverage: 34
 [FAILED] Property Test: CryticTester.crytic_solvent_shares_bitcorn()
Test for method "CryticTester.crytic_solvent_shares_bitcorn()" failed after the following call sequence:
[Call Sequence]
1) CryticTester.cornSilo_deposit(91687770586880857492174702047381654035746670744311968002121780829686237431475, 10000) (block=11, time=25, gas=12500000, gasprice=1, value=0, sender=******************************************)
2) CryticTester.cornSilo_enableBridge() (block=11, time=25, gas=12500000, gasprice=1, value=0, sender=******************************************)
3) CryticTester.cornSilo_mintAndDepositBitcorn(492451759) (block=20, time=78, gas=12500000, gasprice=1, value=0, sender=******************************************)
4) CryticTester.cornSilo_bridgeAllTokens(******************************************, 60, 2, 1000000000000000000, 553fc125eb03fdd5cd52a215) (block=30, time=104, gas=12500000, gasprice=1, value=0, sender=******************************************)
[Execution Trace]
 => [call] CryticTester.cornSilo_bridgeAllTokens(******************************************, 60, 2, 1000000000000000000, 553fc125eb03fdd5cd52a215) (addr=******************************************, value=0, sender=******************************************)
 => [call] ERC1967Proxy.<unresolved method>(msg_data=6afc0c5f) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.getApprovedTokens() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return ([******************************************, ******************************************])]
 => [return (return_data=000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000020000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a690000000000000000000000009491f0dfb965bc45570dd449801432599f0542a0)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=13adf30b) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.bitcornMinterAsset() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (******************************************)]
 => [return (return_data=000000000000000000000000fc11d02bd1b627683935c7cd70b328df9af6275d)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=9a1ce881) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.isBridgeEnabled() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (true)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000001)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=6afc0c5f) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.getApprovedTokens() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return ([******************************************, ******************************************])]
 => [return (return_data=000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000020000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a690000000000000000000000009491f0dfb965bc45570dd449801432599f0542a0)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=13adf30b) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.bitcornMinterAsset() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (******************************************)]
 => [return (return_data=000000000000000000000000fc11d02bd1b627683935c7cd70b328df9af6275d)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=6afc0c5f) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.getApprovedTokens() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return ([******************************************, ******************************************])]
 => [return (return_data=000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000020000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a690000000000000000000000009491f0dfb965bc45570dd449801432599f0542a0)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=13adf30b) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.bitcornMinterAsset() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (******************************************)]
 => [return (return_data=000000000000000000000000fc11d02bd1b627683935c7cd70b328df9af6275d)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=6afc0c5f) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.getApprovedTokens() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return ([******************************************, ******************************************])]
 => [return (return_data=000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000020000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a690000000000000000000000009491f0dfb965bc45570dd449801432599f0542a0)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=13adf30b) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.bitcornMinterAsset() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (******************************************)]
 => [return (return_data=000000000000000000000000fc11d02bd1b627683935c7cd70b328df9af6275d)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=0a7292f5000000000000000000000000a647ff3c36cfab592509e13860ab8c4f28781a660000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a69) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.sharesOf(******************************************, ******************************************) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (10000)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000002710)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=0a7292f500000000000000000000000000000000000000000000000000000000001231230000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a69) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.sharesOf(******************************************, ******************************************) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (0)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000000)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=bf6b874e0000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a69) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.totalShares(******************************************) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (10000)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000002710)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=6afc0c5f) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.getApprovedTokens() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return ([******************************************, ******************************************])]
 => [return (return_data=000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000020000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a690000000000000000000000009491f0dfb965bc45570dd449801432599f0542a0)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=13adf30b) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.bitcornMinterAsset() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (******************************************)]
 => [return (return_data=000000000000000000000000fc11d02bd1b627683935c7cd70b328df9af6275d)]
 => [call] <unresolved contract>.<unresolved method>(msg_data=70a08231000000000000000000000000c27fbedbf5253341c9878f51a6457bf582ced511) (addr=******************************************, value=<nil>, sender=******************************************)
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000002710)]
 => [call] <unresolved contract>.<unresolved method>(msg_data=70a08231000000000000000000000000c27fbedbf5253341c9878f51a6457bf582ced511) (addr=******************************************, value=<nil>, sender=******************************************)
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000000)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=6afc0c5f) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.getApprovedTokens() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return ([******************************************, ******************************************])]
 => [return (return_data=000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000020000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a690000000000000000000000009491f0dfb965bc45570dd449801432599f0542a0)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=13adf30b) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.bitcornMinterAsset() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (******************************************)]
 => [return (return_data=000000000000000000000000fc11d02bd1b627683935c7cd70b328df9af6275d)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=0a7292f5000000000000000000000000a647ff3c36cfab592509e13860ab8c4f28781a660000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a69) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.sharesOf(******************************************, ******************************************) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (10000)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000002710)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=0a7292f500000000000000000000000000000000000000000000000000000000001231230000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a69) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.sharesOf(******************************************, ******************************************) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (0)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000000)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=0a7292f5000000000000000000000000a647ff3c36cfab592509e13860ab8c4f28781a660000000000000000000000009491f0dfb965bc45570dd449801432599f0542a0) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.sharesOf(******************************************, ******************************************) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (0)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000000)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=0a7292f500000000000000000000000000000000000000000000000000000000001231230000000000000000000000009491f0dfb965bc45570dd449801432599f0542a0) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.sharesOf(******************************************, ******************************************) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (0)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000000)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=9a1ce881) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.isBridgeEnabled() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (true)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000001)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=6afc0c5f) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.getApprovedTokens() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return ([******************************************, ******************************************])]
 => [return (return_data=000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000020000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a690000000000000000000000009491f0dfb965bc45570dd449801432599f0542a0)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=13adf30b) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.bitcornMinterAsset() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (******************************************)]
 => [return (return_data=000000000000000000000000fc11d02bd1b627683935c7cd70b328df9af6275d)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=6afc0c5f) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.getApprovedTokens() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return ([******************************************, ******************************************])]
 => [return (return_data=000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000020000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a690000000000000000000000009491f0dfb965bc45570dd449801432599f0542a0)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=13adf30b) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.bitcornMinterAsset() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (******************************************)]
 => [return (return_data=000000000000000000000000fc11d02bd1b627683935c7cd70b328df9af6275d)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=6afc0c5f) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.getApprovedTokens() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return ([******************************************, ******************************************])]
 => [return (return_data=000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000020000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a690000000000000000000000009491f0dfb965bc45570dd449801432599f0542a0)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=13adf30b) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.bitcornMinterAsset() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (******************************************)]
 => [return (return_data=000000000000000000000000fc11d02bd1b627683935c7cd70b328df9af6275d)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=0a7292f5000000000000000000000000a647ff3c36cfab592509e13860ab8c4f28781a660000000000000000000000009491f0dfb965bc45570dd449801432599f0542a0) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.sharesOf(******************************************, ******************************************) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (0)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000000)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=0a7292f500000000000000000000000000000000000000000000000000000000001231230000000000000000000000009491f0dfb965bc45570dd449801432599f0542a0) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.sharesOf(******************************************, ******************************************) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (0)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000000)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=bf6b874e0000000000000000000000009491f0dfb965bc45570dd449801432599f0542a0) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.totalShares(******************************************) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (0)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000000)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=6afc0c5f) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.getApprovedTokens() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return ([******************************************, ******************************************])]
 => [return (return_data=000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000020000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a690000000000000000000000009491f0dfb965bc45570dd449801432599f0542a0)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=13adf30b) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.bitcornMinterAsset() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (******************************************)]
 => [return (return_data=000000000000000000000000fc11d02bd1b627683935c7cd70b328df9af6275d)]
 => [call] <unresolved contract>.<unresolved method>(msg_data=70a08231000000000000000000000000c27fbedbf5253341c9878f51a6457bf582ced511) (addr=******************************************, value=<nil>, sender=******************************************)
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000002710)]
 => [call] <unresolved contract>.<unresolved method>(msg_data=70a08231000000000000000000000000c27fbedbf5253341c9878f51a6457bf582ced511) (addr=******************************************, value=<nil>, sender=******************************************)
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000000)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=6afc0c5f) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.getApprovedTokens() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return ([******************************************, ******************************************])]
 => [return (return_data=000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000020000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a690000000000000000000000009491f0dfb965bc45570dd449801432599f0542a0)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=13adf30b) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.bitcornMinterAsset() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (******************************************)]
 => [return (return_data=000000000000000000000000fc11d02bd1b627683935c7cd70b328df9af6275d)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=0a7292f5000000000000000000000000a647ff3c36cfab592509e13860ab8c4f28781a660000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a69) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.sharesOf(******************************************, ******************************************) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (10000)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000002710)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=0a7292f500000000000000000000000000000000000000000000000000000000001231230000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a69) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.sharesOf(******************************************, ******************************************) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (0)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000000)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=0a7292f5000000000000000000000000a647ff3c36cfab592509e13860ab8c4f28781a660000000000000000000000009491f0dfb965bc45570dd449801432599f0542a0) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.sharesOf(******************************************, ******************************************) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (0)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000000)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=0a7292f500000000000000000000000000000000000000000000000000000000001231230000000000000000000000009491f0dfb965bc45570dd449801432599f0542a0) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.sharesOf(******************************************, ******************************************) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (0)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000000)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=6afc0c5f) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.getApprovedTokens() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return ([******************************************, ******************************************])]
 => [return (return_data=000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000020000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a690000000000000000000000009491f0dfb965bc45570dd449801432599f0542a0)]
 => [call] <unresolved contract>.<unresolved method>(msg_data=70a08231000000000000000000000000c27fbedbf5253341c9878f51a6457bf582ced511) (addr=******************************************, value=<nil>, sender=******************************************)
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000002710)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=0a7292f5000000000000000000000000a647ff3c36cfab592509e13860ab8c4f28781a660000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a69) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.sharesOf(******************************************, ******************************************) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (10000)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000002710)]
 => [call] <unresolved contract>.<unresolved method>(msg_data=70a08231000000000000000000000000c27fbedbf5253341c9878f51a6457bf582ced511) (addr=******************************************, value=<nil>, sender=******************************************)
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000000)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=0a7292f5000000000000000000000000a647ff3c36cfab592509e13860ab8c4f28781a660000000000000000000000009491f0dfb965bc45570dd449801432599f0542a0) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.sharesOf(******************************************, ******************************************) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (0)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000000)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=0a7292f5000000000000000000000000a647ff3c36cfab592509e13860ab8c4f28781a66000000000000000000000000aa80b404c0c9c17a62129b00da58e257db87e1b2) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.sharesOf(******************************************, ******************************************) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (4924517590000000000)]
 => [return (return_data=000000000000000000000000000000000000000000000000445766a82a1cdc00)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=3d1698120000000000000000000000000000000000000000000000000000000000000032000000000000000000000000000000000000000000000000000000000000003c00000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000de0b6b3a764000000000000000000000000000000000000000000000000000000000000000000a0000000000000000000000000000000000000000000000000000000000000000c553fc125eb03fdd5cd52a2150000000000000000000000000000000000000000) (addr=******************************************, value=0, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.bridgeAllTokens(******************************************, 60, 2, 1000000000000000000, 553fc125eb03fdd5cd52a215) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [call] <unresolved contract>.<unresolved method>(msg_data=bda009fe0000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a69) (addr=******************************************, value=<nil>, sender=******************************************)
 => [return (return_data=000000000000000000000000e0a57d49baeb9ff12d3d3028cf09d1d2e1af0460)]
 => [call] <unresolved contract>.<unresolved method>(msg_data=dd62ed3e000000000000000000000000c27fbedbf5253341c9878f51a6457bf582ced511000000000000000000000000e0a57d49baeb9ff12d3d3028cf09d1d2e1af0460) (addr=******************************************, value=<nil>, sender=******************************************)
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000000)]
 => [call] <unresolved contract>.<unresolved method>(msg_data=095ea7b3000000000000000000000000e0a57d49baeb9ff12d3d3028cf09d1d2e1af04600000000000000000000000000000000000000000000000000000000000002710) (addr=******************************************, value=0, sender=******************************************)
 => [event] Approval(******************************************, ******************************************, 10000)
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000001)]
 => [call] <unresolved contract>.<unresolved method>(msg_data=bda009fe000000000000000000000000aa80b404c0c9c17a62129b00da58e257db87e1b2) (addr=******************************************, value=<nil>, sender=******************************************)
 => [return (return_data=000000000000000000000000e0a57d49baeb9ff12d3d3028cf09d1d2e1af0460)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=dd62ed3e000000000000000000000000c27fbedbf5253341c9878f51a6457bf582ced511000000000000000000000000e0a57d49baeb9ff12d3d3028cf09d1d2e1af0460) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> Bitcorn.allowance(******************************************, ******************************************) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (0)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000000)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=095ea7b3000000000000000000000000e0a57d49baeb9ff12d3d3028cf09d1d2e1af04600000000000000000000000000000000000000000000000001bc16d674ec80000) (addr=******************************************, value=0, sender=******************************************)
 => [proxy call] ERC1967Proxy -> Bitcorn.approve(******************************************, 2000000000000000000) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [event] Approval(******************************************, ******************************************, 2000000000000000000)
 => [return (true)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000001)]
 => [call] <unresolved contract>.<unresolved method>(msg_data=4fb1a07b0000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a6900000000000000000000000000000000000000000000000000000000000000320000000000000000000000000000000000000000000000000000000000000032000000000000000000000000000000000000000000000000000000000000271000000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000de0b6b3a764000000000000000000000000000000000000000000000000000000000000000000e0000000000000000000000000000000000000000000000000000000000000000c553fc125eb03fdd5cd52a2150000000000000000000000000000000000000000) (addr=******************************************, value=0, sender=******************************************)
 => [call] <unresolved contract>.<unresolved method>(msg_data=23b872dd000000000000000000000000c27fbedbf5253341c9878f51a6457bf582ced511000000000000000000000000e0a57d49baeb9ff12d3d3028cf09d1d2e1af04600000000000000000000000000000000000000000000000000000000000002710) (addr=******************************************, value=0, sender=******************************************)
 => [event] Approval(******************************************, ******************************************, 0)
 => [event] Transfer(******************************************, ******************************************, 10000)
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000001)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=23b872dd000000000000000000000000c27fbedbf5253341c9878f51a6457bf582ced511000000000000000000000000e0a57d49baeb9ff12d3d3028cf09d1d2e1af04600000000000000000000000000000000000000000000000001bc16d674ec80000) (addr=******************************************, value=0, sender=******************************************)
 => [proxy call] ERC1967Proxy -> Bitcorn.transferFrom(******************************************, ******************************************, 2000000000000000000) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [event] Approval(******************************************, ******************************************, 0)
 => [event] Transfer(******************************************, ******************************************, 2000000000000000000)
 => [return (true)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000001)]
 => [return (return_data=00000000000000000000000000000000000000000000000000000000000000200000000000000000000000000000000000000000000000000000000000000000)]
 => [event] TokenBridged(******************************************, ******************************************, ******************************************, 10000, 2, 1000000000000000000, 553fc125eb03fdd5cd52a215)
 => [call] <unresolved contract>.<unresolved method>(msg_data=a9059cbb00000000000000000000000049be013ba450ca2893b167aeac4044c873eae700000000000000000000000000000000000000000000000000000000000bebc200) (addr=******************************************, value=0, sender=******************************************)
 => [event] Transfer(******************************************, ******************************************, 200000000)
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000001)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=dd62ed3e000000000000000000000000c27fbedbf5253341c9878f51a6457bf582ced5110000000000000000000000002b28fb2641bd0c432d53d909e84d3a41aba92fff) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> Bitcorn.allowance(******************************************, ******************************************) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (0)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000000)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=095ea7b30000000000000000000000002b28fb2641bd0c432d53d909e84d3a41aba92fff0000000000000000000000000000000000000000000000002895f940db54dc00) (addr=******************************************, value=0, sender=******************************************)
 => [proxy call] ERC1967Proxy -> Bitcorn.approve(******************************************, 2924517590000000000) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [event] Approval(******************************************, ******************************************, 2924517590000000000)
 => [return (true)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000001)]
 => [call] <unresolved contract>.<unresolved method>(msg_data=549e8426000000000000000000000000a647ff3c36cfab592509e13860ab8c4f28781a660000000000000000000000000000000000000000000000000cd48bd98c8cdbc4000000000000000000000000000000000000000000000000000000000000003c000000000000000000000000a647ff3c36cfab592509e13860ab8c4f28781a66000000000000000000000000a647ff3c36cfab592509e13860ab8c4f28781a6600000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000de0b6b3a76400000000000000000000000000000000000000000000000000002895f940db54dc0000000000000000000000000000000000000000000000000000000000000001200000000000000000000000000000000000000000000000000000000000000000) (addr=******************************************, value=0, sender=******************************************)
 => [call] ERC1967Proxy.<unresolved method>(msg_data=23b872dd000000000000000000000000c27fbedbf5253341c9878f51a6457bf582ced5110000000000000000000000002b28fb2641bd0c432d53d909e84d3a41aba92fff0000000000000000000000000000000000000000000000002895f940db54dc00) (addr=******************************************, value=0, sender=******************************************)
 => [proxy call] ERC1967Proxy -> Bitcorn.transferFrom(******************************************, ******************************************, 2924517590000000000) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [event] Approval(******************************************, ******************************************, 0)
 => [event] Transfer(******************************************, ******************************************, 2924517590000000000)
 => [return (true)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000001)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=42966c680000000000000000000000000000000000000000000000002895f940db54dc00) (addr=******************************************, value=0, sender=******************************************)
 => [proxy call] ERC1967Proxy -> Bitcorn.burn(2924517590000000000) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [call] <unresolved contract>.<unresolved method>(msg_data=b70096130000000000000000000000002b28fb2641bd0c432d53d909e84d3a41aba92fff000000000000000000000000aa80b404c0c9c17a62129b00da58e257db87e1b242966c6800000000000000000000000000000000000000000000000000000000) (addr=******************************************, value=<nil>, sender=******************************************)
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000001)]
 => [event] Transfer(******************************************, ******************************************, 2924517590000000000)
 => [return ()]
 => [return]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000000)]
 => [event] TokenBridged(******************************************, ******************************************, ******************************************, 2924517590000000000, 2, 1000000000000000000, 553fc125eb03fdd5cd52a215)
 => [call] <unresolved contract>.<unresolved method>(msg_data=a9059cbb00000000000000000000000049be013ba450ca2893b167aeac4044c873eae70000000000000000000000000000000000000000000000000000000000116e75af) (addr=******************************************, value=0, sender=******************************************)
 => [event] Transfer(******************************************, ******************************************, 292451759)
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000001)]
 => [return ()]
 => [return]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=9a1ce881) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.isBridgeEnabled() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (true)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000001)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=6afc0c5f) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.getApprovedTokens() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return ([******************************************, ******************************************])]
 => [return (return_data=000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000020000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a690000000000000000000000009491f0dfb965bc45570dd449801432599f0542a0)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=13adf30b) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.bitcornMinterAsset() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (******************************************)]
 => [return (return_data=000000000000000000000000fc11d02bd1b627683935c7cd70b328df9af6275d)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=6afc0c5f) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.getApprovedTokens() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return ([******************************************, ******************************************])]
 => [return (return_data=000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000020000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a690000000000000000000000009491f0dfb965bc45570dd449801432599f0542a0)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=13adf30b) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.bitcornMinterAsset() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (******************************************)]
 => [return (return_data=000000000000000000000000fc11d02bd1b627683935c7cd70b328df9af6275d)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=6afc0c5f) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.getApprovedTokens() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return ([******************************************, ******************************************])]
 => [return (return_data=000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000020000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a690000000000000000000000009491f0dfb965bc45570dd449801432599f0542a0)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=13adf30b) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.bitcornMinterAsset() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (******************************************)]
 => [return (return_data=000000000000000000000000fc11d02bd1b627683935c7cd70b328df9af6275d)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=0a7292f5000000000000000000000000a647ff3c36cfab592509e13860ab8c4f28781a660000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a69) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.sharesOf(******************************************, ******************************************) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (0)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000000)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=0a7292f500000000000000000000000000000000000000000000000000000000001231230000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a69) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.sharesOf(******************************************, ******************************************) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (0)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000000)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=bf6b874e0000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a69) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.totalShares(******************************************) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (0)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000000)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=6afc0c5f) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.getApprovedTokens() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return ([******************************************, ******************************************])]
 => [return (return_data=000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000020000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a690000000000000000000000009491f0dfb965bc45570dd449801432599f0542a0)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=13adf30b) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.bitcornMinterAsset() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (******************************************)]
 => [return (return_data=000000000000000000000000fc11d02bd1b627683935c7cd70b328df9af6275d)]
 => [call] <unresolved contract>.<unresolved method>(msg_data=70a08231000000000000000000000000c27fbedbf5253341c9878f51a6457bf582ced511) (addr=******************************************, value=<nil>, sender=******************************************)
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000000)]
 => [call] <unresolved contract>.<unresolved method>(msg_data=70a08231000000000000000000000000c27fbedbf5253341c9878f51a6457bf582ced511) (addr=******************************************, value=<nil>, sender=******************************************)
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000000)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=6afc0c5f) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.getApprovedTokens() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return ([******************************************, ******************************************])]
 => [return (return_data=000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000020000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a690000000000000000000000009491f0dfb965bc45570dd449801432599f0542a0)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=13adf30b) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.bitcornMinterAsset() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (******************************************)]
 => [return (return_data=000000000000000000000000fc11d02bd1b627683935c7cd70b328df9af6275d)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=0a7292f5000000000000000000000000a647ff3c36cfab592509e13860ab8c4f28781a660000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a69) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.sharesOf(******************************************, ******************************************) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (0)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000000)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=0a7292f500000000000000000000000000000000000000000000000000000000001231230000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a69) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.sharesOf(******************************************, ******************************************) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (0)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000000)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=0a7292f5000000000000000000000000a647ff3c36cfab592509e13860ab8c4f28781a660000000000000000000000009491f0dfb965bc45570dd449801432599f0542a0) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.sharesOf(******************************************, ******************************************) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (0)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000000)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=0a7292f500000000000000000000000000000000000000000000000000000000001231230000000000000000000000009491f0dfb965bc45570dd449801432599f0542a0) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.sharesOf(******************************************, ******************************************) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (0)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000000)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=9a1ce881) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.isBridgeEnabled() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (true)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000001)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=6afc0c5f) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.getApprovedTokens() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return ([******************************************, ******************************************])]
 => [return (return_data=000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000020000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a690000000000000000000000009491f0dfb965bc45570dd449801432599f0542a0)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=13adf30b) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.bitcornMinterAsset() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (******************************************)]
 => [return (return_data=000000000000000000000000fc11d02bd1b627683935c7cd70b328df9af6275d)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=6afc0c5f) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.getApprovedTokens() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return ([******************************************, ******************************************])]
 => [return (return_data=000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000020000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a690000000000000000000000009491f0dfb965bc45570dd449801432599f0542a0)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=13adf30b) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.bitcornMinterAsset() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (******************************************)]
 => [return (return_data=000000000000000000000000fc11d02bd1b627683935c7cd70b328df9af6275d)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=6afc0c5f) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.getApprovedTokens() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return ([******************************************, ******************************************])]
 => [return (return_data=000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000020000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a690000000000000000000000009491f0dfb965bc45570dd449801432599f0542a0)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=13adf30b) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.bitcornMinterAsset() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (******************************************)]
 => [return (return_data=000000000000000000000000fc11d02bd1b627683935c7cd70b328df9af6275d)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=0a7292f5000000000000000000000000a647ff3c36cfab592509e13860ab8c4f28781a660000000000000000000000009491f0dfb965bc45570dd449801432599f0542a0) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.sharesOf(******************************************, ******************************************) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (0)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000000)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=0a7292f500000000000000000000000000000000000000000000000000000000001231230000000000000000000000009491f0dfb965bc45570dd449801432599f0542a0) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.sharesOf(******************************************, ******************************************) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (0)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000000)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=bf6b874e0000000000000000000000009491f0dfb965bc45570dd449801432599f0542a0) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.totalShares(******************************************) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (0)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000000)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=6afc0c5f) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.getApprovedTokens() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return ([******************************************, ******************************************])]
 => [return (return_data=000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000020000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a690000000000000000000000009491f0dfb965bc45570dd449801432599f0542a0)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=13adf30b) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.bitcornMinterAsset() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (******************************************)]
 => [return (return_data=000000000000000000000000fc11d02bd1b627683935c7cd70b328df9af6275d)]
 => [call] <unresolved contract>.<unresolved method>(msg_data=70a08231000000000000000000000000c27fbedbf5253341c9878f51a6457bf582ced511) (addr=******************************************, value=<nil>, sender=******************************************)
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000000)]
 => [call] <unresolved contract>.<unresolved method>(msg_data=70a08231000000000000000000000000c27fbedbf5253341c9878f51a6457bf582ced511) (addr=******************************************, value=<nil>, sender=******************************************)
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000000)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=6afc0c5f) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.getApprovedTokens() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return ([******************************************, ******************************************])]
 => [return (return_data=000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000020000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a690000000000000000000000009491f0dfb965bc45570dd449801432599f0542a0)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=13adf30b) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.bitcornMinterAsset() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (******************************************)]
 => [return (return_data=000000000000000000000000fc11d02bd1b627683935c7cd70b328df9af6275d)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=0a7292f5000000000000000000000000a647ff3c36cfab592509e13860ab8c4f28781a660000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a69) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.sharesOf(******************************************, ******************************************) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (0)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000000)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=0a7292f500000000000000000000000000000000000000000000000000000000001231230000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a69) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.sharesOf(******************************************, ******************************************) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (0)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000000)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=0a7292f5000000000000000000000000a647ff3c36cfab592509e13860ab8c4f28781a660000000000000000000000009491f0dfb965bc45570dd449801432599f0542a0) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.sharesOf(******************************************, ******************************************) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (0)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000000)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=0a7292f500000000000000000000000000000000000000000000000000000000001231230000000000000000000000009491f0dfb965bc45570dd449801432599f0542a0) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.sharesOf(******************************************, ******************************************) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (0)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000000)]
 => [return ()]

[Property Test Execution Trace]
[Execution Trace]
 => [call] CryticTester.crytic_solvent_shares_bitcorn() (addr=******************************************, value=0, sender=******************************************)
 => [call] ERC1967Proxy.<unresolved method>(msg_data=18160ddd) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> Bitcorn.totalSupply() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (2000000000000000000)]
 => [return (return_data=0000000000000000000000000000000000000000000000001bc16d674ec80000)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=bf6b874e000000000000000000000000aa80b404c0c9c17a62129b00da58e257db87e1b2) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.totalShares(******************************************) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (0)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000000)]
 => [return (false)]

 Fuzzer stopped, test results follow below ...
 [PASSED] Assertion Test: CryticTester.AUTHORIZE_UPGRADE_SELECTOR_INTERNAL()
 [PASSED] Assertion Test: CryticTester.BURN_CAPABILITY_SELECTOR()
 [PASSED] Assertion Test: CryticTester.BURN_FROM_SELECTOR()
 [PASSED] Assertion Test: CryticTester.BURN_SELECTOR()
 [PASSED] Assertion Test: CryticTester.EMPTY_INITIALIZE_DATA()
 [PASSED] Assertion Test: CryticTester.MAXIMUM_REASONABLE_TOKEN_SUPPLY()
 [PASSED] Assertion Test: CryticTester.MINT_SELECTOR()
 [PASSED] Assertion Test: CryticTester.MINT_TO_SELECTOR()
 [PASSED] Assertion Test: CryticTester.PAUSE_SELECTOR()
 [PASSED] Assertion Test: CryticTester.SET_PUBLIC_CAPABILITY_SELECTOR()
 [PASSED] Assertion Test: CryticTester.SET_ROLE_CAPABILITY_SELECTOR()
 [PASSED] Assertion Test: CryticTester.SET_USER_ROLE_SELECTOR()
 [PASSED] Assertion Test: CryticTester.SETWITHDRAWALFEE_SELECTOR()
 [PASSED] Assertion Test: CryticTester.UNPAUSE_SELECTOR()
 [PASSED] Assertion Test: CryticTester.anyInvalidTokenReverts(address,uint256)
 [PASSED] Assertion Test: CryticTester.canAlwaysWithdraw()
 [PASSED] Assertion Test: CryticTester.cannotDepositAfterBridgeEnabled(uint256,uint256)
 [PASSED] Assertion Test: CryticTester.cannotDepositForAfterBridgeEnabled(uint256,uint256)
 [PASSED] Assertion Test: CryticTester.cornSilo_bridgeAllTokens(address,uint256,uint256,uint256,bytes)
 [PASSED] Assertion Test: CryticTester.cornSilo_bridgeBitcorn(address,uint256,uint256,uint256,bytes)
 [PASSED] Assertion Test: CryticTester.cornSilo_bridgeToken(uint256,address,uint256,uint256,bytes)
 [PASSED] Assertion Test: CryticTester.cornSilo_deposit(uint256,uint256)
 [PASSED] Assertion Test: CryticTester.cornSilo_depositFor(uint256,uint256)
 [PASSED] Assertion Test: CryticTester.cornSilo_enableBridge()
 [PASSED] Assertion Test: CryticTester.cornSilo_mintAndDepositBitcorn(uint256)
 [PASSED] Assertion Test: CryticTester.cornSilo_mintAndDepositBitcornFor(uint256)
 [PASSED] Assertion Test: CryticTester.cornSilo_pause()
 [PASSED] Assertion Test: CryticTester.cornSilo_redeemAll()
 [PASSED] Assertion Test: CryticTester.cornSilo_redeemBitcorn(uint256)
 [PASSED] Assertion Test: CryticTester.cornSilo_redeemToken(uint256,uint256)
 [PASSED] Assertion Test: CryticTester.cornSilo_setWithdrawalFee(uint256)
 [PASSED] Assertion Test: CryticTester.cornSilo_unpause()
 [PASSED] Assertion Test: CryticTester.critic_solvent()
 [PASSED] Property Test: CryticTester.crytic_can_never_disable_bridge()
 [PASSED] Property Test: CryticTester.crytic_deposits_increase_shares()
 [PASSED] Property Test: CryticTester.crytic_ensure_modulo_divisible()
 [PASSED] Property Test: CryticTester.crytic_share_decrease_on_bridge()
 [PASSED] Property Test: CryticTester.crytic_share_value_ratio_is_constant_on_all_actions()
 [PASSED] Property Test: CryticTester.crytic_solvent_balances_bitcorn()
 [PASSED] Property Test: CryticTester.crytic_solvent_balances_bitcorn_reverse()
 [PASSED] Property Test: CryticTester.crytic_sum_of_flows()
 [PASSED] Property Test: CryticTester.crytic_sum_of_shares()
 [PASSED] Property Test: CryticTester.crytic_withdrawals_decrease_shares()
 [FAILED] Property Test: CryticTester.crytic_solvent_shares_bitcorn()
Test for method "CryticTester.crytic_solvent_shares_bitcorn()" failed after the following call sequence:
[Call Sequence]
1) CryticTester.cornSilo_deposit(91687770586880857492174702047381654035746670744311968002121780829686237431475, 10000) (block=11, time=25, gas=12500000, gasprice=1, value=0, sender=******************************************)
2) CryticTester.cornSilo_enableBridge() (block=11, time=25, gas=12500000, gasprice=1, value=0, sender=******************************************)
3) CryticTester.cornSilo_mintAndDepositBitcorn(492451759) (block=20, time=78, gas=12500000, gasprice=1, value=0, sender=******************************************)
4) CryticTester.cornSilo_bridgeAllTokens(******************************************, 60, 2, 1000000000000000000, 553fc125eb03fdd5cd52a215) (block=30, time=104, gas=12500000, gasprice=1, value=0, sender=******************************************)
[Execution Trace]
 => [call] CryticTester.cornSilo_bridgeAllTokens(******************************************, 60, 2, 1000000000000000000, 553fc125eb03fdd5cd52a215) (addr=******************************************, value=0, sender=******************************************)
 => [call] ERC1967Proxy.<unresolved method>(msg_data=6afc0c5f) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.getApprovedTokens() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return ([******************************************, ******************************************])]
 => [return (return_data=000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000020000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a690000000000000000000000009491f0dfb965bc45570dd449801432599f0542a0)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=13adf30b) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.bitcornMinterAsset() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (******************************************)]
 => [return (return_data=000000000000000000000000fc11d02bd1b627683935c7cd70b328df9af6275d)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=9a1ce881) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.isBridgeEnabled() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (true)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000001)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=6afc0c5f) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.getApprovedTokens() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return ([******************************************, ******************************************])]
 => [return (return_data=000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000020000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a690000000000000000000000009491f0dfb965bc45570dd449801432599f0542a0)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=13adf30b) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.bitcornMinterAsset() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (******************************************)]
 => [return (return_data=000000000000000000000000fc11d02bd1b627683935c7cd70b328df9af6275d)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=6afc0c5f) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.getApprovedTokens() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return ([******************************************, ******************************************])]
 => [return (return_data=000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000020000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a690000000000000000000000009491f0dfb965bc45570dd449801432599f0542a0)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=13adf30b) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.bitcornMinterAsset() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (******************************************)]
 => [return (return_data=000000000000000000000000fc11d02bd1b627683935c7cd70b328df9af6275d)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=6afc0c5f) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.getApprovedTokens() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return ([******************************************, ******************************************])]
 => [return (return_data=000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000020000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a690000000000000000000000009491f0dfb965bc45570dd449801432599f0542a0)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=13adf30b) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.bitcornMinterAsset() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (******************************************)]
 => [return (return_data=000000000000000000000000fc11d02bd1b627683935c7cd70b328df9af6275d)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=0a7292f5000000000000000000000000a647ff3c36cfab592509e13860ab8c4f28781a660000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a69) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.sharesOf(******************************************, ******************************************) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (10000)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000002710)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=0a7292f500000000000000000000000000000000000000000000000000000000001231230000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a69) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.sharesOf(******************************************, ******************************************) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (0)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000000)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=bf6b874e0000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a69) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.totalShares(******************************************) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (10000)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000002710)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=6afc0c5f) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.getApprovedTokens() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return ([******************************************, ******************************************])]
 => [return (return_data=000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000020000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a690000000000000000000000009491f0dfb965bc45570dd449801432599f0542a0)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=13adf30b) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.bitcornMinterAsset() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (******************************************)]
 => [return (return_data=000000000000000000000000fc11d02bd1b627683935c7cd70b328df9af6275d)]
 => [call] <unresolved contract>.<unresolved method>(msg_data=70a08231000000000000000000000000c27fbedbf5253341c9878f51a6457bf582ced511) (addr=******************************************, value=<nil>, sender=******************************************)
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000002710)]
 => [call] <unresolved contract>.<unresolved method>(msg_data=70a08231000000000000000000000000c27fbedbf5253341c9878f51a6457bf582ced511) (addr=******************************************, value=<nil>, sender=******************************************)
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000000)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=6afc0c5f) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.getApprovedTokens() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return ([******************************************, ******************************************])]
 => [return (return_data=000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000020000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a690000000000000000000000009491f0dfb965bc45570dd449801432599f0542a0)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=13adf30b) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.bitcornMinterAsset() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (******************************************)]
 => [return (return_data=000000000000000000000000fc11d02bd1b627683935c7cd70b328df9af6275d)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=0a7292f5000000000000000000000000a647ff3c36cfab592509e13860ab8c4f28781a660000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a69) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.sharesOf(******************************************, ******************************************) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (10000)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000002710)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=0a7292f500000000000000000000000000000000000000000000000000000000001231230000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a69) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.sharesOf(******************************************, ******************************************) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (0)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000000)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=0a7292f5000000000000000000000000a647ff3c36cfab592509e13860ab8c4f28781a660000000000000000000000009491f0dfb965bc45570dd449801432599f0542a0) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.sharesOf(******************************************, ******************************************) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (0)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000000)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=0a7292f500000000000000000000000000000000000000000000000000000000001231230000000000000000000000009491f0dfb965bc45570dd449801432599f0542a0) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.sharesOf(******************************************, ******************************************) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (0)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000000)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=9a1ce881) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.isBridgeEnabled() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (true)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000001)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=6afc0c5f) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.getApprovedTokens() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return ([******************************************, ******************************************])]
 => [return (return_data=000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000020000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a690000000000000000000000009491f0dfb965bc45570dd449801432599f0542a0)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=13adf30b) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.bitcornMinterAsset() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (******************************************)]
 => [return (return_data=000000000000000000000000fc11d02bd1b627683935c7cd70b328df9af6275d)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=6afc0c5f) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.getApprovedTokens() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return ([******************************************, ******************************************])]
 => [return (return_data=000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000020000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a690000000000000000000000009491f0dfb965bc45570dd449801432599f0542a0)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=13adf30b) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.bitcornMinterAsset() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (******************************************)]
 => [return (return_data=000000000000000000000000fc11d02bd1b627683935c7cd70b328df9af6275d)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=6afc0c5f) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.getApprovedTokens() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return ([******************************************, ******************************************])]
 => [return (return_data=000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000020000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a690000000000000000000000009491f0dfb965bc45570dd449801432599f0542a0)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=13adf30b) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.bitcornMinterAsset() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (******************************************)]
 => [return (return_data=000000000000000000000000fc11d02bd1b627683935c7cd70b328df9af6275d)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=0a7292f5000000000000000000000000a647ff3c36cfab592509e13860ab8c4f28781a660000000000000000000000009491f0dfb965bc45570dd449801432599f0542a0) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.sharesOf(******************************************, ******************************************) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (0)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000000)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=0a7292f500000000000000000000000000000000000000000000000000000000001231230000000000000000000000009491f0dfb965bc45570dd449801432599f0542a0) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.sharesOf(******************************************, ******************************************) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (0)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000000)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=bf6b874e0000000000000000000000009491f0dfb965bc45570dd449801432599f0542a0) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.totalShares(******************************************) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (0)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000000)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=6afc0c5f) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.getApprovedTokens() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return ([******************************************, ******************************************])]
 => [return (return_data=000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000020000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a690000000000000000000000009491f0dfb965bc45570dd449801432599f0542a0)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=13adf30b) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.bitcornMinterAsset() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (******************************************)]
 => [return (return_data=000000000000000000000000fc11d02bd1b627683935c7cd70b328df9af6275d)]
 => [call] <unresolved contract>.<unresolved method>(msg_data=70a08231000000000000000000000000c27fbedbf5253341c9878f51a6457bf582ced511) (addr=******************************************, value=<nil>, sender=******************************************)
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000002710)]
 => [call] <unresolved contract>.<unresolved method>(msg_data=70a08231000000000000000000000000c27fbedbf5253341c9878f51a6457bf582ced511) (addr=******************************************, value=<nil>, sender=******************************************)
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000000)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=6afc0c5f) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.getApprovedTokens() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return ([******************************************, ******************************************])]
 => [return (return_data=000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000020000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a690000000000000000000000009491f0dfb965bc45570dd449801432599f0542a0)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=13adf30b) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.bitcornMinterAsset() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (******************************************)]
 => [return (return_data=000000000000000000000000fc11d02bd1b627683935c7cd70b328df9af6275d)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=0a7292f5000000000000000000000000a647ff3c36cfab592509e13860ab8c4f28781a660000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a69) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.sharesOf(******************************************, ******************************************) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (10000)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000002710)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=0a7292f500000000000000000000000000000000000000000000000000000000001231230000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a69) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.sharesOf(******************************************, ******************************************) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (0)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000000)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=0a7292f5000000000000000000000000a647ff3c36cfab592509e13860ab8c4f28781a660000000000000000000000009491f0dfb965bc45570dd449801432599f0542a0) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.sharesOf(******************************************, ******************************************) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (0)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000000)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=0a7292f500000000000000000000000000000000000000000000000000000000001231230000000000000000000000009491f0dfb965bc45570dd449801432599f0542a0) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.sharesOf(******************************************, ******************************************) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (0)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000000)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=6afc0c5f) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.getApprovedTokens() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return ([******************************************, ******************************************])]
 => [return (return_data=000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000020000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a690000000000000000000000009491f0dfb965bc45570dd449801432599f0542a0)]
 => [call] <unresolved contract>.<unresolved method>(msg_data=70a08231000000000000000000000000c27fbedbf5253341c9878f51a6457bf582ced511) (addr=******************************************, value=<nil>, sender=******************************************)
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000002710)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=0a7292f5000000000000000000000000a647ff3c36cfab592509e13860ab8c4f28781a660000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a69) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.sharesOf(******************************************, ******************************************) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (10000)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000002710)]
 => [call] <unresolved contract>.<unresolved method>(msg_data=70a08231000000000000000000000000c27fbedbf5253341c9878f51a6457bf582ced511) (addr=******************************************, value=<nil>, sender=******************************************)
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000000)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=0a7292f5000000000000000000000000a647ff3c36cfab592509e13860ab8c4f28781a660000000000000000000000009491f0dfb965bc45570dd449801432599f0542a0) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.sharesOf(******************************************, ******************************************) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (0)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000000)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=0a7292f5000000000000000000000000a647ff3c36cfab592509e13860ab8c4f28781a66000000000000000000000000aa80b404c0c9c17a62129b00da58e257db87e1b2) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.sharesOf(******************************************, ******************************************) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (4924517590000000000)]
 => [return (return_data=000000000000000000000000000000000000000000000000445766a82a1cdc00)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=3d1698120000000000000000000000000000000000000000000000000000000000000032000000000000000000000000000000000000000000000000000000000000003c00000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000de0b6b3a764000000000000000000000000000000000000000000000000000000000000000000a0000000000000000000000000000000000000000000000000000000000000000c553fc125eb03fdd5cd52a2150000000000000000000000000000000000000000) (addr=******************************************, value=0, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.bridgeAllTokens(******************************************, 60, 2, 1000000000000000000, 553fc125eb03fdd5cd52a215) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [call] <unresolved contract>.<unresolved method>(msg_data=bda009fe0000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a69) (addr=******************************************, value=<nil>, sender=******************************************)
 => [return (return_data=000000000000000000000000e0a57d49baeb9ff12d3d3028cf09d1d2e1af0460)]
 => [call] <unresolved contract>.<unresolved method>(msg_data=dd62ed3e000000000000000000000000c27fbedbf5253341c9878f51a6457bf582ced511000000000000000000000000e0a57d49baeb9ff12d3d3028cf09d1d2e1af0460) (addr=******************************************, value=<nil>, sender=******************************************)
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000000)]
 => [call] <unresolved contract>.<unresolved method>(msg_data=095ea7b3000000000000000000000000e0a57d49baeb9ff12d3d3028cf09d1d2e1af04600000000000000000000000000000000000000000000000000000000000002710) (addr=******************************************, value=0, sender=******************************************)
 => [event] Approval(******************************************, ******************************************, 10000)
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000001)]
 => [call] <unresolved contract>.<unresolved method>(msg_data=bda009fe000000000000000000000000aa80b404c0c9c17a62129b00da58e257db87e1b2) (addr=******************************************, value=<nil>, sender=******************************************)
 => [return (return_data=000000000000000000000000e0a57d49baeb9ff12d3d3028cf09d1d2e1af0460)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=dd62ed3e000000000000000000000000c27fbedbf5253341c9878f51a6457bf582ced511000000000000000000000000e0a57d49baeb9ff12d3d3028cf09d1d2e1af0460) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> Bitcorn.allowance(******************************************, ******************************************) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (0)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000000)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=095ea7b3000000000000000000000000e0a57d49baeb9ff12d3d3028cf09d1d2e1af04600000000000000000000000000000000000000000000000001bc16d674ec80000) (addr=******************************************, value=0, sender=******************************************)
 => [proxy call] ERC1967Proxy -> Bitcorn.approve(******************************************, 2000000000000000000) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [event] Approval(******************************************, ******************************************, 2000000000000000000)
 => [return (true)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000001)]
 => [call] <unresolved contract>.<unresolved method>(msg_data=4fb1a07b0000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a6900000000000000000000000000000000000000000000000000000000000000320000000000000000000000000000000000000000000000000000000000000032000000000000000000000000000000000000000000000000000000000000271000000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000de0b6b3a764000000000000000000000000000000000000000000000000000000000000000000e0000000000000000000000000000000000000000000000000000000000000000c553fc125eb03fdd5cd52a2150000000000000000000000000000000000000000) (addr=******************************************, value=0, sender=******************************************)
 => [call] <unresolved contract>.<unresolved method>(msg_data=23b872dd000000000000000000000000c27fbedbf5253341c9878f51a6457bf582ced511000000000000000000000000e0a57d49baeb9ff12d3d3028cf09d1d2e1af04600000000000000000000000000000000000000000000000000000000000002710) (addr=******************************************, value=0, sender=******************************************)
 => [event] Approval(******************************************, ******************************************, 0)
 => [event] Transfer(******************************************, ******************************************, 10000)
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000001)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=23b872dd000000000000000000000000c27fbedbf5253341c9878f51a6457bf582ced511000000000000000000000000e0a57d49baeb9ff12d3d3028cf09d1d2e1af04600000000000000000000000000000000000000000000000001bc16d674ec80000) (addr=******************************************, value=0, sender=******************************************)
 => [proxy call] ERC1967Proxy -> Bitcorn.transferFrom(******************************************, ******************************************, 2000000000000000000) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [event] Approval(******************************************, ******************************************, 0)
 => [event] Transfer(******************************************, ******************************************, 2000000000000000000)
 => [return (true)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000001)]
 => [return (return_data=00000000000000000000000000000000000000000000000000000000000000200000000000000000000000000000000000000000000000000000000000000000)]
 => [event] TokenBridged(******************************************, ******************************************, ******************************************, 10000, 2, 1000000000000000000, 553fc125eb03fdd5cd52a215)
 => [call] <unresolved contract>.<unresolved method>(msg_data=a9059cbb00000000000000000000000049be013ba450ca2893b167aeac4044c873eae700000000000000000000000000000000000000000000000000000000000bebc200) (addr=******************************************, value=0, sender=******************************************)
 => [event] Transfer(******************************************, ******************************************, 200000000)
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000001)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=dd62ed3e000000000000000000000000c27fbedbf5253341c9878f51a6457bf582ced5110000000000000000000000002b28fb2641bd0c432d53d909e84d3a41aba92fff) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> Bitcorn.allowance(******************************************, ******************************************) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (0)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000000)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=095ea7b30000000000000000000000002b28fb2641bd0c432d53d909e84d3a41aba92fff0000000000000000000000000000000000000000000000002895f940db54dc00) (addr=******************************************, value=0, sender=******************************************)
 => [proxy call] ERC1967Proxy -> Bitcorn.approve(******************************************, 2924517590000000000) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [event] Approval(******************************************, ******************************************, 2924517590000000000)
 => [return (true)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000001)]
 => [call] <unresolved contract>.<unresolved method>(msg_data=549e8426000000000000000000000000a647ff3c36cfab592509e13860ab8c4f28781a660000000000000000000000000000000000000000000000000cd48bd98c8cdbc4000000000000000000000000000000000000000000000000000000000000003c000000000000000000000000a647ff3c36cfab592509e13860ab8c4f28781a66000000000000000000000000a647ff3c36cfab592509e13860ab8c4f28781a6600000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000de0b6b3a76400000000000000000000000000000000000000000000000000002895f940db54dc0000000000000000000000000000000000000000000000000000000000000001200000000000000000000000000000000000000000000000000000000000000000) (addr=******************************************, value=0, sender=******************************************)
 => [call] ERC1967Proxy.<unresolved method>(msg_data=23b872dd000000000000000000000000c27fbedbf5253341c9878f51a6457bf582ced5110000000000000000000000002b28fb2641bd0c432d53d909e84d3a41aba92fff0000000000000000000000000000000000000000000000002895f940db54dc00) (addr=******************************************, value=0, sender=******************************************)
 => [proxy call] ERC1967Proxy -> Bitcorn.transferFrom(******************************************, ******************************************, 2924517590000000000) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [event] Approval(******************************************, ******************************************, 0)
 => [event] Transfer(******************************************, ******************************************, 2924517590000000000)
 => [return (true)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000001)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=42966c680000000000000000000000000000000000000000000000002895f940db54dc00) (addr=******************************************, value=0, sender=******************************************)
 => [proxy call] ERC1967Proxy -> Bitcorn.burn(2924517590000000000) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [call] <unresolved contract>.<unresolved method>(msg_data=b70096130000000000000000000000002b28fb2641bd0c432d53d909e84d3a41aba92fff000000000000000000000000aa80b404c0c9c17a62129b00da58e257db87e1b242966c6800000000000000000000000000000000000000000000000000000000) (addr=******************************************, value=<nil>, sender=******************************************)
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000001)]
 => [event] Transfer(******************************************, ******************************************, 2924517590000000000)
 => [return ()]
 => [return]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000000)]
 => [event] TokenBridged(******************************************, ******************************************, ******************************************, 2924517590000000000, 2, 1000000000000000000, 553fc125eb03fdd5cd52a215)
 => [call] <unresolved contract>.<unresolved method>(msg_data=a9059cbb00000000000000000000000049be013ba450ca2893b167aeac4044c873eae70000000000000000000000000000000000000000000000000000000000116e75af) (addr=******************************************, value=0, sender=******************************************)
 => [event] Transfer(******************************************, ******************************************, 292451759)
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000001)]
 => [return ()]
 => [return]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=9a1ce881) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.isBridgeEnabled() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (true)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000001)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=6afc0c5f) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.getApprovedTokens() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return ([******************************************, ******************************************])]
 => [return (return_data=000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000020000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a690000000000000000000000009491f0dfb965bc45570dd449801432599f0542a0)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=13adf30b) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.bitcornMinterAsset() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (******************************************)]
 => [return (return_data=000000000000000000000000fc11d02bd1b627683935c7cd70b328df9af6275d)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=6afc0c5f) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.getApprovedTokens() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return ([******************************************, ******************************************])]
 => [return (return_data=000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000020000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a690000000000000000000000009491f0dfb965bc45570dd449801432599f0542a0)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=13adf30b) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.bitcornMinterAsset() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (******************************************)]
 => [return (return_data=000000000000000000000000fc11d02bd1b627683935c7cd70b328df9af6275d)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=6afc0c5f) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.getApprovedTokens() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return ([******************************************, ******************************************])]
 => [return (return_data=000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000020000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a690000000000000000000000009491f0dfb965bc45570dd449801432599f0542a0)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=13adf30b) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.bitcornMinterAsset() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (******************************************)]
 => [return (return_data=000000000000000000000000fc11d02bd1b627683935c7cd70b328df9af6275d)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=0a7292f5000000000000000000000000a647ff3c36cfab592509e13860ab8c4f28781a660000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a69) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.sharesOf(******************************************, ******************************************) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (0)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000000)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=0a7292f500000000000000000000000000000000000000000000000000000000001231230000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a69) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.sharesOf(******************************************, ******************************************) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (0)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000000)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=bf6b874e0000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a69) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.totalShares(******************************************) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (0)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000000)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=6afc0c5f) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.getApprovedTokens() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return ([******************************************, ******************************************])]
 => [return (return_data=000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000020000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a690000000000000000000000009491f0dfb965bc45570dd449801432599f0542a0)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=13adf30b) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.bitcornMinterAsset() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (******************************************)]
 => [return (return_data=000000000000000000000000fc11d02bd1b627683935c7cd70b328df9af6275d)]
 => [call] <unresolved contract>.<unresolved method>(msg_data=70a08231000000000000000000000000c27fbedbf5253341c9878f51a6457bf582ced511) (addr=******************************************, value=<nil>, sender=******************************************)
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000000)]
 => [call] <unresolved contract>.<unresolved method>(msg_data=70a08231000000000000000000000000c27fbedbf5253341c9878f51a6457bf582ced511) (addr=******************************************, value=<nil>, sender=******************************************)
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000000)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=6afc0c5f) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.getApprovedTokens() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return ([******************************************, ******************************************])]
 => [return (return_data=000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000020000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a690000000000000000000000009491f0dfb965bc45570dd449801432599f0542a0)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=13adf30b) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.bitcornMinterAsset() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (******************************************)]
 => [return (return_data=000000000000000000000000fc11d02bd1b627683935c7cd70b328df9af6275d)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=0a7292f5000000000000000000000000a647ff3c36cfab592509e13860ab8c4f28781a660000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a69) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.sharesOf(******************************************, ******************************************) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (0)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000000)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=0a7292f500000000000000000000000000000000000000000000000000000000001231230000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a69) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.sharesOf(******************************************, ******************************************) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (0)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000000)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=0a7292f5000000000000000000000000a647ff3c36cfab592509e13860ab8c4f28781a660000000000000000000000009491f0dfb965bc45570dd449801432599f0542a0) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.sharesOf(******************************************, ******************************************) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (0)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000000)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=0a7292f500000000000000000000000000000000000000000000000000000000001231230000000000000000000000009491f0dfb965bc45570dd449801432599f0542a0) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.sharesOf(******************************************, ******************************************) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (0)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000000)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=9a1ce881) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.isBridgeEnabled() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (true)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000001)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=6afc0c5f) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.getApprovedTokens() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return ([******************************************, ******************************************])]
 => [return (return_data=000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000020000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a690000000000000000000000009491f0dfb965bc45570dd449801432599f0542a0)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=13adf30b) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.bitcornMinterAsset() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (******************************************)]
 => [return (return_data=000000000000000000000000fc11d02bd1b627683935c7cd70b328df9af6275d)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=6afc0c5f) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.getApprovedTokens() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return ([******************************************, ******************************************])]
 => [return (return_data=000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000020000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a690000000000000000000000009491f0dfb965bc45570dd449801432599f0542a0)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=13adf30b) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.bitcornMinterAsset() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (******************************************)]
 => [return (return_data=000000000000000000000000fc11d02bd1b627683935c7cd70b328df9af6275d)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=6afc0c5f) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.getApprovedTokens() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return ([******************************************, ******************************************])]
 => [return (return_data=000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000020000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a690000000000000000000000009491f0dfb965bc45570dd449801432599f0542a0)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=13adf30b) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.bitcornMinterAsset() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (******************************************)]
 => [return (return_data=000000000000000000000000fc11d02bd1b627683935c7cd70b328df9af6275d)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=0a7292f5000000000000000000000000a647ff3c36cfab592509e13860ab8c4f28781a660000000000000000000000009491f0dfb965bc45570dd449801432599f0542a0) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.sharesOf(******************************************, ******************************************) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (0)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000000)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=0a7292f500000000000000000000000000000000000000000000000000000000001231230000000000000000000000009491f0dfb965bc45570dd449801432599f0542a0) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.sharesOf(******************************************, ******************************************) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (0)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000000)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=bf6b874e0000000000000000000000009491f0dfb965bc45570dd449801432599f0542a0) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.totalShares(******************************************) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (0)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000000)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=6afc0c5f) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.getApprovedTokens() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return ([******************************************, ******************************************])]
 => [return (return_data=000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000020000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a690000000000000000000000009491f0dfb965bc45570dd449801432599f0542a0)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=13adf30b) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.bitcornMinterAsset() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (******************************************)]
 => [return (return_data=000000000000000000000000fc11d02bd1b627683935c7cd70b328df9af6275d)]
 => [call] <unresolved contract>.<unresolved method>(msg_data=70a08231000000000000000000000000c27fbedbf5253341c9878f51a6457bf582ced511) (addr=******************************************, value=<nil>, sender=******************************************)
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000000)]
 => [call] <unresolved contract>.<unresolved method>(msg_data=70a08231000000000000000000000000c27fbedbf5253341c9878f51a6457bf582ced511) (addr=******************************************, value=<nil>, sender=******************************************)
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000000)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=6afc0c5f) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.getApprovedTokens() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return ([******************************************, ******************************************])]
 => [return (return_data=000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000020000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a690000000000000000000000009491f0dfb965bc45570dd449801432599f0542a0)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=13adf30b) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.bitcornMinterAsset() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (******************************************)]
 => [return (return_data=000000000000000000000000fc11d02bd1b627683935c7cd70b328df9af6275d)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=0a7292f5000000000000000000000000a647ff3c36cfab592509e13860ab8c4f28781a660000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a69) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.sharesOf(******************************************, ******************************************) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (0)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000000)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=0a7292f500000000000000000000000000000000000000000000000000000000001231230000000000000000000000001a4652fe54709a39bf1ddda84ef325f7abda5a69) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.sharesOf(******************************************, ******************************************) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (0)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000000)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=0a7292f5000000000000000000000000a647ff3c36cfab592509e13860ab8c4f28781a660000000000000000000000009491f0dfb965bc45570dd449801432599f0542a0) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.sharesOf(******************************************, ******************************************) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (0)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000000)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=0a7292f500000000000000000000000000000000000000000000000000000000001231230000000000000000000000009491f0dfb965bc45570dd449801432599f0542a0) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.sharesOf(******************************************, ******************************************) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (0)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000000)]
 => [return ()]

[Property Test Execution Trace]
[Execution Trace]
 => [call] CryticTester.crytic_solvent_shares_bitcorn() (addr=******************************************, value=0, sender=******************************************)
 => [call] ERC1967Proxy.<unresolved method>(msg_data=18160ddd) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> Bitcorn.totalSupply() (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (2000000000000000000)]
 => [return (return_data=0000000000000000000000000000000000000000000000001bc16d674ec80000)]
 => [call] ERC1967Proxy.<unresolved method>(msg_data=bf6b874e000000000000000000000000aa80b404c0c9c17a62129b00da58e257db87e1b2) (addr=******************************************, value=<nil>, sender=******************************************)
 => [proxy call] ERC1967Proxy -> CornSilo.totalShares(******************************************) (addr=******************************************, code=******************************************, value=0, sender=******************************************)
 => [return (0)]
 => [return (return_data=0000000000000000000000000000000000000000000000000000000000000000)]
 => [return (false)]

 Test summary: 43 test(s) passed, 1 test(s) failed
 Coverage report saved to file: medusa/coverage_report.html
