[2025-06-23 12:59:30.20] [Worker 9] Killed (thread killed). Stopping.
[2025-06-23 12:59:30.20] [Worker 10] Killed (thread killed). Stopping.
[2025-06-23 12:59:30.20] [Worker 11] Killed (thread killed). Stopping.
[2025-06-23 12:59:30.20] [Worker 12] Killed (thread killed). Stopping.
[2025-06-23 12:59:30.20] [Worker 13] Killed (thread killed). Stopping.
[2025-06-23 12:59:30.20] [Worker 14] Killed (thread killed). Stopping.
[2025-06-23 12:59:30.20] [Worker 15] Killed (thread killed). Stopping.
[2025-06-23 12:59:30.20] [status] tests: 0/8, fuzzing: 42554805/500000000, values: [9690541540084876,31536000000000000000000000000000000,9796145786409392,1000000004985469027,0,1469077363556905636,12216183031718674209556,1], cov: 113891, corpus: 282
optimize_max_health_increase: max value: 9690541540084876

  Call sequence:
    CryticTester.stakedCap_approve(0xf07,115792089237316195423570985008687907853269984665640564039457584007913109639937) from: ****************************************** Time delay: 574632 seconds Block delay: 49025
    CryticTester.switch_asset(212044767462858914926783678947438165064938418098592215469523184700305166611) from: ****************************************** Time delay: 136394 seconds Block delay: 39519
    CryticTester.feeAuction_buy(92652242509499653241450768969330428099610015019579684000640442862674338254586,[0xf07, 0x1fffffffe, 0xffffffff, 0x1fffffffe, 0x94771550282853f6e0124c302f7de1cf50aa45ca, 0xd6bbde9174b1cdaa358d2cf4d57d1a9f7178fbff, 0x1af7f588a501ea2b5bb3feefa744892aa2cf00e6, 0x1fffffffe, 0xffffffff, 0x2fffffffd, 0x92a6649fdcc044da968d94202465578a9371c7b1, 0xffffffff, 0x1fffffffe, 0x2fffffffd, 0x89ca9f4f77b267778eb2ea0ba1beadee8523af36, 0x5615deb798bb3e4dfa0139dfa1b3d433cc23b72f],[**********, 255, 115792089237316195423570985008687907853269984665640564039457584007786771967682],0xffffffff,93879539468895363378602490700177108785076057462777304803290818807386828763502) from: ****************************************** Time delay: 29577 seconds Block delay: 57807
    CryticTester.delegation_setLtvBuffer(49827746443653132539802951233502146851327483214359573697327852414130813135244) from: ****************************************** Time delay: 90029 seconds Block delay: 28494
    CryticTester.oracle_setStaleness(0xffffffff,6888066478505441749850992418581994297142195019068002000074442631546382030335) from: ****************************************** Time delay: 592773 seconds Block delay: 50592
    CryticTester.lender_repay(79134579944527902223956207440668662801201128554431323912964015423061838657580) from: ****************************************** Time delay: 322357 seconds Block delay: 10
    CryticTester.property_repaid_debt_equals_zero_debt() from: ****************************************** Time delay: 156190 seconds Block delay: 14278
    CryticTester.lender_repay(115792089237316195423570985008687907853269984665639664039457584007913129639937) from: ****************************************** Time delay: 395200 seconds Block delay: 48689
    CryticTester.capToken_redeem_clamped(95741646795015930638342469798301058198531925459299825715208509091224771411149) from: ****************************************** Time delay: 288282 seconds Block delay: 32651
    CryticTester.lender_liquidate(96527322811128641503200922976669652610386069949039409468408214856818078802911) from: ****************************************** Time delay: 166184 seconds Block delay: 4924
    CryticTester.asset_mint(0x2a07706473244bc757e10f2a9e86fb532828afe3,1313373041) from: ****************************************** Time delay: 406880 seconds Block delay: 16480
    CryticTester.feeAuction_setMinStartPrice(50567676296237530215709939272598174094397627629202802397599245577743710227668) from: ****************************************** Time delay: 419861 seconds Block delay: 5005
    CryticTester.lender_borrow_clamped(46579599527300147752911888174834388979015831070268901955322967468044978416515) from: ****************************************** Time delay: 3867 seconds Block delay: 25461
    CryticTester.property_borrowed_asset_value() from: ****************************************** Time delay: 222812 seconds Block delay: 50592
    CryticTester.lender_initiateLiquidation() from: ****************************************** Time delay: 322345 seconds Block delay: 41859
    CryticTester.property_total_borrowed_less_than_total_supply() from: ****************************************** Time delay: 322338 seconds Block delay: 4991
    CryticTester.capToken_setFeeData(0x1804c8ab1f12e6bbf3894d4083f33e07309d1f38,(49869528211447337507581, 105595305270768091135739420498098964224445880925025084223878852022216500825372, 40223805042954923313978570538536601647058217615594817495771589330482330191206, 99999999999999999999999999, 115792089237316195423570985008687907853269984665640414039457584007913129639935, 33056249739822063734181)) from: ****************************************** Time delay: 59694 seconds Block delay: 46152
    CryticTester.mockNetworkMiddleware_setMockSlashableCollateralByVault(53502540585222975478199632749217283793864113140416536062295354888833147494184) from: ****************************************** Time delay: 311699 seconds Block delay: 36723
    CryticTester.mockNetworkMiddleware_setMockSlashableCollateralByVault(53502540585222975478199632749217283793864113140416536062295354888833147494184) from: ****************************************** Time delay: 311699 seconds Block delay: 36723
    CryticTester.capToken_mint_clamped(12000000000000000000001) from: ****************************************** Time delay: 352545 seconds Block delay: 43649
    CryticTester.capToken_mint_clamped(12000000000000000000001) from: ****************************************** Time delay: 352545 seconds Block delay: 43649
    CryticTester.stakedCap_deposit(51895644719467792066571894091710869393543355905728171984000194425741445815407,0xf02) from: ****************************************** Time delay: 305996 seconds Block delay: 52772
    CryticTester.lender_realizeRestakerInterest() from: ****************************************** Time delay: 150273 seconds Block delay: 32651
    CryticTester.lender_cancelLiquidation_clamped() from: ****************************************** Time delay: 387502 seconds Block delay: 38369
    CryticTester.oracle_setPriceOracleData(0x1fffffffe,(0xffffffff, "\163\237\199\177")) from: ****************************************** Time delay: 283490 seconds Block delay: 17085
    CryticTester.accessControl_grantRole("\143\160\230\212\233@\171M\142\213\170=\ETB\153\217*\228X\fI\154Vl\243\&5\157/\140\&9\EM\fc",0xffffffff) from: ****************************************** Time delay: 114565 seconds Block delay: 33175
    CryticTester.property_borrowed_asset_value() from: ****************************************** Time delay: 153418 seconds Block delay: 28126
    CryticTester.capToken_investAll() from: ****************************************** Time delay: 455740 seconds Block delay: 51642
    CryticTester.lender_borrow_clamped(115792089237316195423570985008687907853269984665640564039457584007913129639935) from: ****************************************** Time delay: 275394 seconds Block delay: 45626
    CryticTester.accessControl_grantRole("\148\EM0\DC4\201\182\177\216\&07wo\r\209\\\175\254hzE\DLE%\151\b\248T\t\160\246\129\&7\146",0x7fa9385be102ac3eac297483dd6233d62b3e1496) from: ****************************************** Time delay: 322310 seconds Block delay: 9
    CryticTester.mockERC4626Tester_decreaseYield(**********) from: ****************************************** Time delay: 3866 seconds Block delay: 32652
    CryticTester.stakedCap_permit(0x2fffffffd,0x1fffffffe,115792089237316195423570985008687907853269984665640564039457584007910970861364,39999,71,"ChainlinkAdapter\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL","\236\244\182\NUL'[\140\231\143\SOHg}\251@\176\243\232\229h\200\131y\fdy\221\160\212\207\&0\185\137") from: ****************************************** Time delay: 105767 seconds Block delay: 23640
    CryticTester.property_ltv() from: ****************************************** Time delay: 322347 seconds Block delay: 59502
    CryticTester.stakedCap_withdraw(115792089237316195423570985008687907853269984665640564039457584007913029639935,0x886d6d1eb8d415b00052828cd6d5b321f072073d,0x1fffffffe) from: ****************************************** Time delay: 390587 seconds Block delay: 5053
    CryticTester.property_repaid_debt_equals_zero_debt() from: ****************************************** Time delay: 38885 seconds Block delay: 16063
    CryticTester.lender_liquidate(115792089237316195423570985008687907853269984665640564039457584007910970861362) from: ****************************************** Time delay: 23094 seconds Block delay: 101
    CryticTester.capToken_approve(0xf02,50186849216440882834365773503793987581223009780705702046838983657059356594816) from: ****************************************** Time delay: 222375 seconds Block delay: 23167
    CryticTester.feeAuction_setMinStartPrice(3210730645604) from: ****************************************** Time delay: 361136 seconds Block delay: 81
    CryticTester.doomsday_liquidate(77784623682452896297276757612921833371589091981796380949434520261093249435818) from: ****************************************** Time delay: 208886 seconds Block delay: 12232
    CryticTester.capToken_realizeInterest(0xffffffff) from: ****************************************** Time delay: 270654 seconds Block delay: 50312
    CryticTester.switchDebtToken(82299135160964070076846021324374906854627379199758588254364202784130756958275) from: ****************************************** Time delay: 31594 seconds Block delay: 13782
    CryticTester.lender_borrow_clamped(115792089237316195423570985008687907853269984665640564039457584007913129639935) from: ****************************************** Time delay: 216610 seconds Block delay: 26048
    CryticTester.capToken_transferFrom(0x2fffffffd,0xc7183455a4c133ae270771860664b6b7ec320bb1,109753202256784357702607674318137699895751128515144968268951822493682106180631) from: ****************************************** Time delay: 322357 seconds Block delay: 5020
    CryticTester.property_sum_of_unrealized_interest() from: ****************************************** Time delay: 322373 seconds Block delay: 32551
    CryticTester.mockERC4626Tester_setDecimalsOffset(5) from: ****************************************** Time delay: 331505 seconds Block delay: 9687
    CryticTester.accessControl_revokeRole("Test Token\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL",0x2fffffffd) from: ****************************************** Time delay: 350070 seconds Block delay: 53562
    CryticTester.capToken_unpauseProtocol() from: ****************************************** Time delay: 283488 seconds Block delay: 4953
    CryticTester.mockERC4626Tester_transfer(0x2fffffffd,290) from: ****************************************** Time delay: 73041 seconds Block delay: 14020
    CryticTester.accessControl_renounceRole("\EM\SOH\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL",0x3cff5e7ebecb676c3cb602d0ef2d46710b88854e) from: ****************************************** Time delay: 140902 seconds Block delay: 10001
    CryticTester.switchAaveOracle(75582292690828949683231465870923526644620298948961349679535477177936890517430) from: ****************************************** Time delay: 318509 seconds Block delay: 38370
    CryticTester.capToken_realizeInterest(0x1fffffffe) from: ****************************************** Time delay: 387502 seconds Block delay: 28031
    CryticTester.capToken_transfer(0x2fffffffd,257) from: ****************************************** Time delay: 135364 seconds Block delay: 49820
    CryticTester.property_ltv() from: ****************************************** Time delay: 31594 seconds Block delay: 253
    CryticTester.capToken_transferFrom(0xf0c,0x2fffffffd,62031852822804768606857136463745591661113956147321267829981804459965119870318) from: ****************************************** Time delay: 181471 seconds Block delay: 27136
    CryticTester.delegation_registerNetwork(0xffffffff) from: ****************************************** Time delay: 547689 seconds Block delay: 4989
    CryticTester.capToken_burn_clamped(2366588) from: ****************************************** Time delay: 199287 seconds Block delay: 35429
    CryticTester.capToken_setReserve(10211146) from: ****************************************** Time delay: 347391 seconds Block delay: 19329
    CryticTester.stakedCap_transfer(0x1fffffffe,22819595148480029428855420776828552955534733005086817128369931298986036538352) from: ****************************************** Time delay: 4 seconds Block delay: 780
    CryticTester.lender_borrow_clamped(9999) from: ****************************************** Time delay: 322339 seconds Block delay: 60055
    CryticTester.capToken_realizeInterest(0x2fffffffd) from: ****************************************** Time delay: 434894 seconds Block delay: 8781
    CryticTester.capToken_investAll() from: ****************************************** Time delay: 172101 seconds Block delay: 29505
    CryticTester.lender_repay(27) from: ****************************************** Time delay: 65534 seconds Block delay: 11036
    CryticTester.oracle_setMarketOracleData(0x1fffffffe,(0x2fffffffd, "\219\221KQ\162\DC3H\DC2")) from: ****************************************** Time delay: 33271 seconds Block delay: 33
    CryticTester.capToken_unpauseProtocol() from: ****************************************** Time delay: 195400 seconds Block delay: 51485
    CryticTester.feeAuction_setDuration(21445290057162079105033759873766799800133526494718495041771069284180491617224) from: ****************************************** Time delay: 135485 seconds Block delay: 58182
    CryticTester.accessControl_revokeRole("cUSD\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\215",0xffffffff) from: ****************************************** Time delay: 249336 seconds Block delay: 50592
    CryticTester.lender_initiateLiquidation_clamped() from: ****************************************** Time delay: 59696 seconds Block delay: 23169
    CryticTester.switchDebtToken(11) from: ****************************************** Time delay: 210608 seconds Block delay: 13782
    CryticTester.property_vault_solvency_borrows() from: ****************************************** Time delay: 322275 seconds Block delay: 16480
    CryticTester.capToken_divestAll() from: ******************************************
    CryticTester.stakedCap_approve(0x5991a2df15a8f6a256d3ec51e99254cd3fb576a9,115792089237316195423570985008687907853269984665640564039457584007912725541410) from: ****************************************** Time delay: 38059 seconds Block delay: 51009
    CryticTester.capToken_pauseAsset(0x1d1499e622d69689cdf9004d05ec547d650ff211) from: ****************************************** Time delay: 160323 seconds Block delay: 16816
    CryticTester.accessControl_revokeRole("\144P\149\202N#+\241fF\225\196\250\182\240\EMR\160\235\224\158\GS\157\148\231Y{\141N\158\SO.",0x3381cd18e2fb4db236bf0525938ab6e43db0440f) from: ****************************************** Time delay: 47900 seconds Block delay: 23978
    CryticTester.capToken_realizeInterest(0x2fffffffd) from: ****************************************** Time delay: 322365 seconds Block delay: 46605
    CryticTester.stakedCap_transfer(0x1fffffffe,115792089237316195423570985008687907853269984665640564039457584007913129639872) from: ****************************************** Time delay: 318510 seconds Block delay: 32330
    CryticTester.mockERC4626Tester_mintUnbackedShares(115792089237316195423570985008687907853269984665640564039457584007913129639931,0x15cf58144ef33af1e14b5208015d11f9143e27b9) from: ****************************************** Time delay: 3601 seconds Block delay: 42338
    CryticTester.oracle_setRestakerRate(0x7fa9385be102ac3eac297483dd6233d62b3e1496,52359581941030304095795513635831085096219002025) from: ****************************************** Time delay: 259406 seconds Block delay: 3600
    CryticTester.capToken_redeem(66313201102099127286249950685487492618510521500503146,[32169690217161784883955862442891440183354993550477856192686699548216942073856, 63333457621250470506069238845460555266390631925739996133861700882954726080328, 3276429096102354768361815641123313676262645592217858841454786477159, 115792089237316195423570985008687907853269984665640564039457584007913129639933, 1300202221949594854561439788196867520249436694787561709720278151212369272, 115792089237316195423570985008687907853269984665640564039456584007913129639935, 115792089237316195423570985008687907853269984665640564039456584007913129639935, 115792089237316195423570985008687907853269984665640564039457584007913129639932],0x1fffffffe,94252487115086896848587406963246346202571478773099354264329256250722187542866) from: ****************************************** Time delay: 121287 seconds Block delay: 5006
    CryticTester.lender_realizeRestakerInterest() from: ****************************************** Time delay: 276463 seconds Block delay: 44568

Traces:
call [1mERC1967Proxy::[unknown method](0x08cf6fec0000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m(/recon/test/recon/mocks/MockMiddleware.sol:71)
 delegatecall ******************************************::[1mreservesData(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (0, [1mERC1967Proxy, [1mERC1967Proxy, [1mERC1967Proxy, 6, false, 100000000)
  (0, [1mERC1967Proxy, [1mERC1967Proxy, [1mERC1967Proxy, 6, false, 100000000)
call [1mERC1967Proxy::[unknown method](0x70a082310000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m(/recon/test/recon/mocks/MockMiddleware.sol:73)
 delegatecall ******************************************::[1mbalanceOf([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (128672012395)
  (128672012395)
call [1mERC1967Proxy::[unknown method](0x71a97305) [1m(/recon/test/recon/mocks/MockMiddleware.sol:77)
 delegatecall ******************************************::[1massets() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    ([******************************************, ******************************************, ******************************************])
  ([******************************************, ******************************************, ******************************************])
call [1mERC1967Proxy::[unknown method](0xd8cb4aa30000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m(/recon/test/recon/mocks/MockMiddleware.sol:89)
 delegatecall ******************************************::[1mbonus([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
   call [1mERC1967Proxy::[unknown method](0x330244300000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
     delegatecall ******************************************::[1mcoverage([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
       call [1mMockMiddleware::slashableCollateral(address,uint48)([1mCryticTester, 1535673600) [1m<no source map>
         call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
           delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
             call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
               call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                  (8)
               call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                  (0, ************, 0, **********, 0)
                (************, **********)
              (************, **********)
            (************, **********)
         call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
            (18)
          (************00)
       call [1mMockMiddleware::coverage(address)([1mCryticTester) [1m<no source map>
         call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
           delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
             call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
               call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                  (8)
               call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                  (0, ************, 0, **********, 0)
                (************, **********)
              (************, **********)
            (************, **********)
         call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
            (18)
          (************00)
        (************00)
      (************00)
   call [1mERC1967Proxy::[unknown method](0xf01391e30000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
     delegatecall ******************************************::[1mslashableCollateral([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
       call [1mMockMiddleware::slashableCollateral(address,uint48)([1mCryticTester, 1535673600) [1m<no source map>
         call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
           delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
             call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
               call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                  (8)
               call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                  (0, ************, 0, **********, 0)
                (************, **********)
              (************, **********)
            (************, **********)
         call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
            (18)
          (************00)
        (************00)
      (************00)
   call [1mERC1967Proxy::[unknown method](0xb1732b************000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
     delegatecall ******************************************::[1mliquidationThreshold([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
        (700000000000000000000000000)
      (700000000000000000000000000)
   call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m<no source map>
     delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
       call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
         call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
            (8)
         call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
            (0, 100000000, 0, **********, 0)
          (100000000, **********)
        (100000000, **********)
      (100000000, **********)
   call [1mERC1967Proxy::[unknown method](0x70a082310000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
     delegatecall ******************************************::[1mbalanceOf([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
        (128672012395)
      (128672012395)
   call [1mERC1967Proxy::[unknown method](0x98db1b080000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
     delegatecall ******************************************::[1mrestakerRate([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
        (795513583471503155188697930)
      (795513583471503155188697930)
   call [1mERC1967Proxy::[unknown method](0x70a082310000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
     delegatecall ******************************************::[1mbalanceOf([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
        (128672012395)
      (128672012395)
    (100000000000000000000000000)
  (100000000000000000000000000)
call [1mERC1967Proxy::[unknown method](0xfeee17560000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m(/recon/test/recon/mocks/MockMiddleware.sol:47)
 delegatecall ******************************************::[1mcurrentUtilizationIndex(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
   delegatecall ******************************************::0x3c054127e912a1b0cc7579bc5827e495c2ce52587bc3871751e3281fc5599b38c3bfc4000000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7 [1m<no source map>
      ******************************************000006b6a5818f7cbccd8557
    (31703432289687169762647)
  (31703432289687169762647)
call [1mERC1967Proxy::[unknown method](0xbf20d9dc0000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m(/recon/test/recon/mocks/MockMiddleware.sol:49)
 delegatecall ******************************************::[1mutilization(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
   delegatecall ******************************************::0x0f82ffd0e912a1b0cc7579bc5827e495c2ce52587bc3871751e3281fc5599b38c3bfc4000000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7 [1m<no source map>
      ******************************************00000000001280f39a235acd
    (5208333332208333)
  (5208333332208333)
call [1mERC1967Proxy::[unknown method](0x8d7301240000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m(/recon/test/recon/mocks/MockMiddleware.sol:52)
 delegatecall ******************************************::[1mtotalBorrows(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (124999999973)
  (124999999973)
call [1mERC1967Proxy::[unknown method](0xc0af0d3b0000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m(/recon/test/recon/mocks/MockMiddleware.sol:53)
 delegatecall ******************************************::[1mgetVaultDebt(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (124999999973)
  (124999999973)
call [1mERC1967Proxy::[unknown method](0xb7902303) [1m(/recon/test/recon/mocks/MockMiddleware.sol:55)
 delegatecall ******************************************::[1minsuranceFund() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (******************************************)
  (******************************************)
call ******************************************::[1mbalanceOf(******************************************) [1m(/recon/test/recon/mocks/MockMiddleware.sol:54)
  (0)
call [1mERC1967Proxy::[unknown method](0x18160ddd) [1m(/recon/test/recon/mocks/MockMiddleware.sol:55)
 delegatecall ******************************************::[1mtotalSupply() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (24000000000000000000002000000000000)
  (24000000000000000000002000000000000)
call [1mERC1967Proxy::[unknown method](0xe75179a40000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m(/recon/test/recon/mocks/MockMiddleware.sol:58)
 delegatecall ******************************************::[1mreserve(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (10211146)
  (10211146)
call [1mERC1967Proxy::[unknown method](0x01e1d114) [1m(/recon/test/recon/mocks/MockMiddleware.sol:89)
 delegatecall ******************************************::[1mtotalAssets() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (0)
  (0)
call [1mERC1967Proxy::[unknown method](0x18160ddd) [1m(/recon/test/recon/mocks/MockMiddleware.sol:89)
 delegatecall ******************************************::[1mtotalSupply() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (0)
  (0)
call [1mERC1967Proxy::[unknown method](0x70a082310000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m(/recon/test/recon/mocks/MockMiddleware.sol:89)
 delegatecall ******************************************::[1mbalanceOf([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (23880000000000000000001989999999743)
  (23880000000000000000001989999999743)
call [1mERC1967Proxy::[unknown method](0x2c3ee88c000000000000000000000000000000000004995ff983c42c4b5104f1553e3aff) [1m(/recon/test/recon/mocks/MockMiddleware.sol:89)
 delegatecall ******************************************::[1mgetRedeemAmount(23880000000000000000001989999999743) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
   delegatecall ******************************************::0x4bfcc81c3b40995b576f8dd0a8521bba471c5346e53f6a25529b0903b82331eb1a2afe00000000000000000000000000000000000004995ff983c42c4b5104f1553e3aff [1m<no source map>
     call [1mERC1967Proxy::[unknown method](0x18160ddd) [1m<no source map>
       delegatecall ******************************************::[1mtotalSupply() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (24000000000000000000002000000000000)
        (24000000000000000000002000000000000)
     call [1mERC1967Proxy::[unknown method](0x71a97305) [1m<no source map>
       delegatecall ******************************************::[1massets() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          ([******************************************, ******************************************, ******************************************])
        ([******************************************, ******************************************, ******************************************])
     call [1mERC1967Proxy::[unknown method](0x9782e8210000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m<no source map>
       delegatecall ******************************************::[1mtotalSupplies(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (24000000000000000000002)
        (24000000000000000000002)
     call [1mERC1967Proxy::[unknown method](0x9782e821000000000000000000000000d16d567549a2a2a2005aeacf7fb193851603dd70) [1m<no source map>
       delegatecall ******************************************::[1mtotalSupplies(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (0)
        (0)
     call [1mERC1967Proxy::[unknown method](0x9782e82100000000000000000000000096d3f6c20eed2697647f543fe6c08bc2fbf39758) [1m<no source map>
       delegatecall ******************************************::[1mtotalSupplies(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (0)
        (0)
      ******************************************00000000000000000000004000000000000000000000000000000000000000000000000000000000000000c0000000000000000000000000000000000000000000000000000000000000000300000000000000000000000000000000000000000000050e8992a65668200001000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000003000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
    ([23880000000000000000001, 0, 0], [0, 0, 0])
  ([23880000000000000000001, 0, 0], [0, 0, 0])
call [1mERC1967Proxy::[unknown method](0x92e423b50000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m(/recon/test/recon/mocks/MockMiddleware.sol:89)
 delegatecall ******************************************::[1magent([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
   delegatecall ******************************************::0x2a43ad13d6af1ec8a1789f5ada2b972bd1569f7c83af2e268be17cd65efe8474ebf088000000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496 [1m<no source map>
     call [1mERC1967Proxy::[unknown method](0x330244300000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mcoverage([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
         call [1mMockMiddleware::slashableCollateral(address,uint48)([1mCryticTester, 1535673600) [1m<no source map>
           call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
             delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
               call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
                 call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                    (8)
                 call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                    (0, ************, 0, **********, 0)
                  (************, **********)
                (************, **********)
              (************, **********)
           call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
              (18)
            (************00)
         call [1mMockMiddleware::coverage(address)([1mCryticTester) [1m<no source map>
           call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
             delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
               call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
                 call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                    (8)
                 call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                    (0, ************, 0, **********, 0)
                  (************, **********)
                (************, **********)
              (************, **********)
           call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
              (18)
            (************00)
          (************00)
        (************00)
     call [1mERC1967Proxy::[unknown method](0xf01391e30000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mslashableCollateral([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
         call [1mMockMiddleware::slashableCollateral(address,uint48)([1mCryticTester, 1535673600) [1m<no source map>
           call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
             delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
               call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
                 call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                    (8)
                 call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                    (0, ************, 0, **********, 0)
                  (************, **********)
                (************, **********)
              (************, **********)
           call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
              (18)
            (************00)
          (************00)
        (************00)
     call [1mERC1967Proxy::[unknown method](0xb1732b************000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mliquidationThreshold([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (700000000000000000000000000)
        (700000000000000000000000000)
     call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m<no source map>
       delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
         call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
           call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
              (8)
           call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
              (0, 100000000, 0, **********, 0)
            (100000000, **********)
          (100000000, **********)
        (100000000, **********)
     call [1mERC1967Proxy::[unknown method](0x70a082310000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mbalanceOf([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (128672012395)
        (128672012395)
     call [1mERC1967Proxy::[unknown method](0x98db1b080000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mrestakerRate([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (795513583471503155188697930)
        (795513583471503155188697930)
     call [1mERC1967Proxy::[unknown method](0x70a082310000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mbalanceOf([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (128672012395)
        (128672012395)
      ******************************************00000000000016bcc41e9000000000000000000000000000000000000000000000000000000016bcc41e900000000000000000000000000000000000000000000000000000000c38da54a4d8000000000000000000000000000000000000000001bca300f4d662807cdc00000000000000000000000000000000000000000000024306c4097859c43c000000000000000000000000000000000000000000000004353153b78d69481d39ca1d
    (************00, ************00, 13438320682200, 537532827288000000000000000, 700000000000000000000000000, 1302246047988717790772709917)
  (************00, ************00, 13438320682200, 537532827288000000000000000, 700000000000000000000000000, 1302246047988717790772709917)
call [1mHEVM::[1mprank([1mCryticTester) [1m(/recon/test/recon/targets/DebtTokenTargets.sol:30)
  0x
call [1mERC1967Proxy::[unknown method](0xc0af0d3b0000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m(/recon/test/recon-fee-auction/CryticToFoundry.sol:22)
 delegatecall ******************************************::[1mgetVaultDebt(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (124999999973)
  (124999999973)
call ******************************************::[1mbalanceOf([1mERC1967Proxy) [1m(/recon/test/recon-fee-auction/CryticToFoundry.sol:22)
  (23999999999875000000029)
call [1mERC1967Proxy::[unknown method](0x92e423b50000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m(/recon/test/recon/mocks/MockMiddleware.sol:89)
 delegatecall ******************************************::[1magent([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
   delegatecall ******************************************::0x2a43ad13d6af1ec8a1789f5ada2b972bd1569f7c83af2e268be17cd65efe8474ebf088000000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496 [1m<no source map>
     call [1mERC1967Proxy::[unknown method](0x330244300000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mcoverage([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
         call [1mMockMiddleware::slashableCollateral(address,uint48)([1mCryticTester, 1535673600) [1m<no source map>
           call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
             delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
               call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
                 call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                    (8)
                 call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                    (0, ************, 0, **********, 0)
                  (************, **********)
                (************, **********)
              (************, **********)
           call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
              (18)
            (************00)
         call [1mMockMiddleware::coverage(address)([1mCryticTester) [1m<no source map>
           call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
             delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
               call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
                 call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                    (8)
                 call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                    (0, ************, 0, **********, 0)
                  (************, **********)
                (************, **********)
              (************, **********)
           call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
              (18)
            (************00)
          (************00)
        (************00)
     call [1mERC1967Proxy::[unknown method](0xf01391e30000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mslashableCollateral([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
         call [1mMockMiddleware::slashableCollateral(address,uint48)([1mCryticTester, 1535673600) [1m<no source map>
           call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
             delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
               call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
                 call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                    (8)
                 call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                    (0, ************, 0, **********, 0)
                  (************, **********)
                (************, **********)
              (************, **********)
           call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
              (18)
            (************00)
          (************00)
        (************00)
     call [1mERC1967Proxy::[unknown method](0xb1732b************000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mliquidationThreshold([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (700000000000000000000000000)
        (700000000000000000000000000)
     call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m<no source map>
       delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
         call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
           call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
              (8)
           call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
              (0, 100000000, 0, **********, 0)
            (100000000, **********)
          (100000000, **********)
        (100000000, **********)
     call [1mERC1967Proxy::[unknown method](0x70a082310000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mbalanceOf([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (128672012395)
        (128672012395)
     call [1mERC1967Proxy::[unknown method](0x98db1b080000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mrestakerRate([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (795513583471503155188697930)
        (795513583471503155188697930)
     call [1mERC1967Proxy::[unknown method](0x70a082310000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mbalanceOf([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (128672012395)
        (128672012395)
      ******************************************00000000000016bcc41e9000000000000000000000000000000000000000000000000000000016bcc41e900000000000000000000000000000000000000000000000000000000c38da54a4d8000000000000000000000000000000000000000001bca300f4d662807cdc00000000000000000000000000000000000000000000024306c4097859c43c000000000000000000000000000000000000000000000004353153b78d69481d39ca1d
    (************00, ************00, 13438320682200, 537532827288000000000000000, 700000000000000000000000000, 1302246047988717790772709917)
  (************00, ************00, 13438320682200, 537532827288000000000000000, 700000000000000000000000000, 1302246047988717790772709917)
call [1mERC1967Proxy::[unknown method](0x8d7301240000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m(/recon/test/recon-fee-auction/CryticToFoundry.sol:22)
 delegatecall ******************************************::[1mtotalBorrows(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (124999999973)
  (124999999973)
call [1mERC1967Proxy::[unknown method](0x9782e8210000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m(/recon/test/recon-fee-auction/CryticToFoundry.sol:22)
 delegatecall ******************************************::[1mtotalSupplies(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (24000000000000000000002)
  (24000000000000000000002)
call [1mERC1967Proxy::[unknown method](0x0a8d01da0000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e14960000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m(/recon/test/recon-fee-auction/CryticToFoundry.sol:22)
 delegatecall ******************************************::[1mrealizeRestakerInterest([1mCryticTester, ******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
   delegatecall ******************************************::0x6d4323a8d6af1ec8a1789f5ada2b972bd1569f7c83af2e268be17cd65efe8474ebf088000000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e14960000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7 [1m<no source map>
     delegatecall ******************************************::0x5afe0823d6af1ec8a1789f5ada2b972bd1569f7c83af2e268be17cd65efe8474ebf088000000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e14960000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7 [1m<no source map>
       call [1mERC1967Proxy::[unknown method](0x70a082310000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
         delegatecall ******************************************::[1mbalanceOf([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
            (128672012395)
          (128672012395)
       call [1mERC1967Proxy::[unknown method](0x98db1b080000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
         delegatecall ******************************************::[1mrestakerRate([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
            (795513583471503155188697930)
          (795513583471503155188697930)
        ******************************************00000000000000015469e93b
     call [1mERC1967Proxy::[unknown method](0xa0821be30000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m<no source map>
       delegatecall ******************************************::[1mavailableBalance(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
         delegatecall ******************************************::0x50ba5827e912a1b0cc7579bc5827e495c2ce52587bc3871751e3281fc5599b38c3bfc4000000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7 [1m<no source map>
            ******************************************000005150ae84a6fc46b5e1d
          (23999999999875000000029)
        (23999999999875000000029)
     call [1mERC1967Proxy::[unknown method](0x40c10f190000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496000000000000000000000000000000000000000000000000000000015469e93b) [1m<no source map>
       delegatecall ******************************************::[1mmint([1mCryticTester, 5711194427) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
         call [1mERC1967Proxy::[unknown method](0x2daff25c0000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m<no source map>
           delegatecall ******************************************::[1mmarketRate(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
             call ******************************************::[1mrate([1mMockAaveDataProvider, ******************************************) [1m<no source map>
               call [1mMockAaveDataProvider::getReserveData(address)(******************************************) [1m<no source map>
                  (0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, **********)
                (0)
              (0)
            (0)
         call [1mERC1967Proxy::[unknown method](0x58d7849a0000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m<no source map>
           delegatecall ******************************************::[1mbenchmarkRate(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
              (150000000000000000000000000)
            (150000000000000000000000000)
         call [1mERC1967Proxy::[unknown method](0x207b27df0000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m<no source map>
           delegatecall ******************************************::[1mutilizationRate(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
             call ******************************************::[1mrate([1mMockAaveDataProvider, ******************************************) [1m<no source map>
               call [1mMockAaveDataProvider::getReserveData(address)(******************************************) [1m<no source map>
                  (0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, **********)
                (0)
              (0)
            (0)
         call [1mERC1967Proxy::[unknown method](0xd95d920c40c10f190000000000000000000000000000000000000000000000000000000000000000000000000000000089ca9f4f77b267778eb2ea0ba1beadee8523af3600000000000000000000000015cf58144ef33af1e14b5208015d11f9143e27b9) [1m<no source map>
           delegatecall ******************************************::[1mcheckAccess(0x40c10f19, [1mERC1967Proxy, [1mERC1967Proxy) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
              (true)
            (true)
         emit Transfer(from=******************************************, to=[1mCryticTester, value=**********) [1m<no source map>
          0x
        0x
     call [1mERC1967Proxy::[unknown method](0x6c665a550000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7000000000000000000000000000000000000000000000000000000015469e93b0000000000000000000000002a07706473244bc757e10f2a9e86fb532828afe3) [1m<no source map>
       delegatecall ******************************************::[1mborrow(******************************************, 5711194427, [1mERC1967Proxy) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
         call [1mERC1967Proxy::[unknown method](0xd95d920c6c665a550000000000000000000000000000000000000000000000000000000000000000000000000000000092a6649fdcc044da968d94202465578a9371c7b100000000000000000000000015cf58144ef33af1e14b5208015d11f9143e27b9) [1m<no source map>
           delegatecall ******************************************::[1mcheckAccess(lfZU, [1mERC1967Proxy, [1mERC1967Proxy) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
              (true)
            (true)
         delegatecall ******************************************::0x53c655a85c48f30a22a9811126b69b5adcaabfc5ae0a83b6493e1b31e09dc579923ad1000000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7000000000000000000000000000000000000000000000000000000015469e93b [1m<no source map>
            0x
         delegatecall ******************************************::0xebd0396de912a1b0cc7579bc5827e495c2ce52587bc3871751e3281fc5599b38c3bfc4000000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7000000000000000000000000000000000000000000000000000000015469e93b0000000000000000000000002a07706473244bc757e10f2a9e86fb532828afe3 [1m<no source map>
           call ******************************************::[1mtransfer([1mERC1967Proxy, 5711194427) [1m<no source map>
             emit Transfer(from=[1mERC1967Proxy, to=[1mERC1967Proxy, value=5711194427) [1m<no source map>
              (true)
           emit Borrow(asset=[1mERC1967Proxy, agent=******************************************, amount=5711194427) [1m<no source map>
            0x
          0x
        0x
     call [1mERC1967Proxy::[unknown method](0x3d11c00d0000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e14960000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m<no source map>
       delegatecall ******************************************::[1mdistributeRewards([1mCryticTester, ******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
         call ******************************************::[1mbalanceOf([1mERC1967Proxy) [1m<no source map>
            (7024567468)
         call [1mMockMiddleware::slashableCollateral(address,uint48)([1mCryticTester, 1535673600) [1m<no source map>
           call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
             delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
               call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
                 call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                    (8)
                 call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                    (0, ************, 0, **********, 0)
                  (************, **********)
                (************, **********)
              (************, **********)
           call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
              (18)
            (************00)
         call [1mMockMiddleware::coverage(address)([1mCryticTester) [1m<no source map>
           call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
             delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
               call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
                 call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                    (8)
                 call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                    (0, ************, 0, **********, 0)
                  (************, **********)
                (************, **********)
              (************, **********)
           call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
              (18)
            (************00)
         call ******************************************::[1mtransfer([1mMockMiddleware, 7024567468) [1m<no source map>
           emit Transfer(from=[1mERC1967Proxy, to=[1mMockMiddleware, value=7024567468) [1m<no source map>
            (true)
         call [1mMockMiddleware::distributeRewards(address,address)([1mCryticTester, ******************************************) [1m<no source map>
            0x
         emit DistributeReward(agent=[1mCryticTester, asset=******************************************, amount=7024567468) [1m<no source map>
          0x
        0x
     emit RealizeInterest(asset=******************************************, realizedInterest=5711194427, interestReceiver=[1mERC1967Proxy) [1m<no source map>
      ******************************************00000000000000015469e93b
    (5711194427)
  (5711194427)
call [1mERC1967Proxy::[unknown method](0xc0af0d3b0000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m(/recon/test/recon-fee-auction/CryticToFoundry.sol:22)
 delegatecall ******************************************::[1mgetVaultDebt(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (130711194400)
  (130711194400)
call ******************************************::[1mbalanceOf([1mERC1967Proxy) [1m(/recon/test/recon-fee-auction/CryticToFoundry.sol:22)
  (23999999999869288805602)
call [1mERC1967Proxy::[unknown method](0x92e423b50000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m(/recon/test/recon/mocks/MockMiddleware.sol:89)
 delegatecall ******************************************::[1magent([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
   delegatecall ******************************************::0x2a43ad13d6af1ec8a1789f5ada2b972bd1569f7c83af2e268be17cd65efe8474ebf088000000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496 [1m<no source map>
     call [1mERC1967Proxy::[unknown method](0x330244300000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mcoverage([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
         call [1mMockMiddleware::slashableCollateral(address,uint48)([1mCryticTester, 1535673600) [1m<no source map>
           call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
             delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
               call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
                 call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                    (8)
                 call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                    (0, ************, 0, **********, 0)
                  (************, **********)
                (************, **********)
              (************, **********)
           call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
              (18)
            (************00)
         call [1mMockMiddleware::coverage(address)([1mCryticTester) [1m<no source map>
           call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
             delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
               call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
                 call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                    (8)
                 call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                    (0, ************, 0, **********, 0)
                  (************, **********)
                (************, **********)
              (************, **********)
           call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
              (18)
            (************00)
          (************00)
        (************00)
     call [1mERC1967Proxy::[unknown method](0xf01391e30000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mslashableCollateral([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
         call [1mMockMiddleware::slashableCollateral(address,uint48)([1mCryticTester, 1535673600) [1m<no source map>
           call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
             delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
               call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
                 call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                    (8)
                 call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                    (0, ************, 0, **********, 0)
                  (************, **********)
                (************, **********)
              (************, **********)
           call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
              (18)
            (************00)
          (************00)
        (************00)
     call [1mERC1967Proxy::[unknown method](0xb1732b************000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mliquidationThreshold([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (700000000000000000000000000)
        (700000000000000000000000000)
     call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m<no source map>
       delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
         call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
           call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
              (8)
           call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
              (0, 100000000, 0, **********, 0)
            (100000000, **********)
          (100000000, **********)
        (100000000, **********)
     call [1mERC1967Proxy::[unknown method](0x70a082310000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mbalanceOf([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (134383206821)
        (134383206821)
     call [1mERC1967Proxy::[unknown method](0x98db1b080000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mrestakerRate([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (795513583471503155188697930)
        (795513583471503155188697930)
     call [1mERC1967Proxy::[unknown method](0x70a082310000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mbalanceOf([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (134383206821)
        (134383206821)
      ******************************************00000000000016bcc41e9000000000000000000000000000000000000000000000000000000016bcc41e900000000000000000000000000000000000000000000000000000000c38da54a474000000000000000000000000000000000000000001bca300f4c82c85e9c200000000000000000000000000000000000000000000024306c4097859c43c000000000000000000000000000000000000000000000004353153b7afd6c7236756a9
    (************00, ************00, 13438320682100, 537532827284000000000000000, 700000000000000000000000000, 1302246047998408332312794793)
  (************00, ************00, 13438320682100, 537532827284000000000000000, 700000000000000000000000000, 1302246047998408332312794793)
call [1mERC1967Proxy::[unknown method](0x8d7301240000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m(/recon/test/recon-fee-auction/CryticToFoundry.sol:22)
 delegatecall ******************************************::[1mtotalBorrows(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (130711194400)
  (130711194400)
call [1mERC1967Proxy::[unknown method](0x08cf6fec0000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m(/recon/test/recon/mocks/MockMiddleware.sol:71)
 delegatecall ******************************************::[1mreservesData(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (0, [1mERC1967Proxy, [1mERC1967Proxy, [1mERC1967Proxy, 6, false, 100000000)
  (0, [1mERC1967Proxy, [1mERC1967Proxy, [1mERC1967Proxy, 6, false, 100000000)
call [1mERC1967Proxy::[unknown method](0x70a082310000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m(/recon/test/recon/mocks/MockMiddleware.sol:73)
 delegatecall ******************************************::[1mbalanceOf([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (134383206821)
  (134383206821)
call [1mERC1967Proxy::[unknown method](0x71a97305) [1m(/recon/test/recon/mocks/MockMiddleware.sol:77)
 delegatecall ******************************************::[1massets() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    ([******************************************, ******************************************, ******************************************])
  ([******************************************, ******************************************, ******************************************])
call [1mERC1967Proxy::[unknown method](0xd8cb4aa30000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m(/recon/test/recon/mocks/MockMiddleware.sol:89)
 delegatecall ******************************************::[1mbonus([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
   call [1mERC1967Proxy::[unknown method](0x330244300000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
     delegatecall ******************************************::[1mcoverage([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
       call [1mMockMiddleware::slashableCollateral(address,uint48)([1mCryticTester, 1535673600) [1m<no source map>
         call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
           delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
             call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
               call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                  (8)
               call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                  (0, ************, 0, **********, 0)
                (************, **********)
              (************, **********)
            (************, **********)
         call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
            (18)
          (************00)
       call [1mMockMiddleware::coverage(address)([1mCryticTester) [1m<no source map>
         call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
           delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
             call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
               call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                  (8)
               call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                  (0, ************, 0, **********, 0)
                (************, **********)
              (************, **********)
            (************, **********)
         call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
            (18)
          (************00)
        (************00)
      (************00)
   call [1mERC1967Proxy::[unknown method](0xf01391e30000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
     delegatecall ******************************************::[1mslashableCollateral([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
       call [1mMockMiddleware::slashableCollateral(address,uint48)([1mCryticTester, 1535673600) [1m<no source map>
         call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
           delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
             call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
               call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                  (8)
               call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                  (0, ************, 0, **********, 0)
                (************, **********)
              (************, **********)
            (************, **********)
         call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
            (18)
          (************00)
        (************00)
      (************00)
   call [1mERC1967Proxy::[unknown method](0xb1732b************000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
     delegatecall ******************************************::[1mliquidationThreshold([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
        (700000000000000000000000000)
      (700000000000000000000000000)
   call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m<no source map>
     delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
       call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
         call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
            (8)
         call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
            (0, 100000000, 0, **********, 0)
          (100000000, **********)
        (100000000, **********)
      (100000000, **********)
   call [1mERC1967Proxy::[unknown method](0x70a082310000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
     delegatecall ******************************************::[1mbalanceOf([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
        (134383206821)
      (134383206821)
   call [1mERC1967Proxy::[unknown method](0x98db1b080000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
     delegatecall ******************************************::[1mrestakerRate([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
        (795513583471503155188697930)
      (795513583471503155188697930)
   call [1mERC1967Proxy::[unknown method](0x70a082310000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
     delegatecall ******************************************::[1mbalanceOf([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
        (134383206821)
      (134383206821)
    (100000000000000000000000000)
  (100000000000000000000000000)
call [1mERC1967Proxy::[unknown method](0xfeee17560000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m(/recon/test/recon/mocks/MockMiddleware.sol:47)
 delegatecall ******************************************::[1mcurrentUtilizationIndex(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
   delegatecall ******************************************::0x3c054127e912a1b0cc7579bc5827e495c2ce52587bc3871751e3281fc5599b38c3bfc4000000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7 [1m<no source map>
      ******************************************000006b6a5818f7cbccd8557
    (31703432289687169762647)
  (31703432289687169762647)
call [1mERC1967Proxy::[unknown method](0xbf20d9dc0000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m(/recon/test/recon/mocks/MockMiddleware.sol:49)
 delegatecall ******************************************::[1mutilization(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
   delegatecall ******************************************::0x0f82ffd0e912a1b0cc7579bc5827e495c2ce52587bc3871751e3281fc5599b38c3bfc4000000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7 [1m<no source map>
      ******************************************00000000001359617aeeb5aa
    (5446299766666666)
  (5446299766666666)
call [1mERC1967Proxy::[unknown method](0x8d7301240000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m(/recon/test/recon/mocks/MockMiddleware.sol:52)
 delegatecall ******************************************::[1mtotalBorrows(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (130711194400)
  (130711194400)
call [1mERC1967Proxy::[unknown method](0xc0af0d3b0000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m(/recon/test/recon/mocks/MockMiddleware.sol:53)
 delegatecall ******************************************::[1mgetVaultDebt(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (130711194400)
  (130711194400)
call [1mERC1967Proxy::[unknown method](0xb7902303) [1m(/recon/test/recon/mocks/MockMiddleware.sol:55)
 delegatecall ******************************************::[1minsuranceFund() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (******************************************)
  (******************************************)
call ******************************************::[1mbalanceOf(******************************************) [1m(/recon/test/recon/mocks/MockMiddleware.sol:54)
  (0)
call [1mERC1967Proxy::[unknown method](0x18160ddd) [1m(/recon/test/recon/mocks/MockMiddleware.sol:55)
 delegatecall ******************************************::[1mtotalSupply() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (24000000000000000000002000000000000)
  (24000000000000000000002000000000000)
call [1mERC1967Proxy::[unknown method](0xe75179a40000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m(/recon/test/recon/mocks/MockMiddleware.sol:58)
 delegatecall ******************************************::[1mreserve(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (10211146)
  (10211146)
call [1mERC1967Proxy::[unknown method](0x01e1d114) [1m(/recon/test/recon/mocks/MockMiddleware.sol:89)
 delegatecall ******************************************::[1mtotalAssets() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (0)
  (0)
call [1mERC1967Proxy::[unknown method](0x18160ddd) [1m(/recon/test/recon/mocks/MockMiddleware.sol:89)
 delegatecall ******************************************::[1mtotalSupply() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (0)
  (0)
call [1mERC1967Proxy::[unknown method](0x70a082310000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m(/recon/test/recon/mocks/MockMiddleware.sol:89)
 delegatecall ******************************************::[1mbalanceOf([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (23880000000000000000001989999999743)
  (23880000000000000000001989999999743)
call [1mERC1967Proxy::[unknown method](0x2c3ee88c000000000000000000000000000000000004995ff983c42c4b5104f1553e3aff) [1m(/recon/test/recon/mocks/MockMiddleware.sol:89)
 delegatecall ******************************************::[1mgetRedeemAmount(23880000000000000000001989999999743) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
   delegatecall ******************************************::0x4bfcc81c3b40995b576f8dd0a8521bba471c5346e53f6a25529b0903b82331eb1a2afe00000000000000000000000000000000000004995ff983c42c4b5104f1553e3aff [1m<no source map>
     call [1mERC1967Proxy::[unknown method](0x18160ddd) [1m<no source map>
       delegatecall ******************************************::[1mtotalSupply() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (24000000000000000000002000000000000)
        (24000000000000000000002000000000000)
     call [1mERC1967Proxy::[unknown method](0x71a97305) [1m<no source map>
       delegatecall ******************************************::[1massets() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          ([******************************************, ******************************************, ******************************************])
        ([******************************************, ******************************************, ******************************************])
     call [1mERC1967Proxy::[unknown method](0x9782e8210000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m<no source map>
       delegatecall ******************************************::[1mtotalSupplies(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (24000000000000000000002)
        (24000000000000000000002)
     call [1mERC1967Proxy::[unknown method](0x9782e821000000000000000000000000d16d567549a2a2a2005aeacf7fb193851603dd70) [1m<no source map>
       delegatecall ******************************************::[1mtotalSupplies(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (0)
        (0)
     call [1mERC1967Proxy::[unknown method](0x9782e82100000000000000000000000096d3f6c20eed2697647f543fe6c08bc2fbf39758) [1m<no source map>
       delegatecall ******************************************::[1mtotalSupplies(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (0)
        (0)
      ******************************************00000000000000000000004000000000000000000000000000000000000000000000000000000000000000c0000000000000000000000000000000000000000000000000000000000000000300000000000000000000000000000000000000000000050e8992a65668200001000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000003000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
    ([23880000000000000000001, 0, 0], [0, 0, 0])
  ([23880000000000000000001, 0, 0], [0, 0, 0])
call [1mERC1967Proxy::[unknown method](0x92e423b50000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m(/recon/test/recon/mocks/MockMiddleware.sol:89)
 delegatecall ******************************************::[1magent([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
   delegatecall ******************************************::0x2a43ad13d6af1ec8a1789f5ada2b972bd1569f7c83af2e268be17cd65efe8474ebf088000000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496 [1m<no source map>
     call [1mERC1967Proxy::[unknown method](0x330244300000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mcoverage([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
         call [1mMockMiddleware::slashableCollateral(address,uint48)([1mCryticTester, 1535673600) [1m<no source map>
           call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
             delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
               call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
                 call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                    (8)
                 call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                    (0, ************, 0, **********, 0)
                  (************, **********)
                (************, **********)
              (************, **********)
           call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
              (18)
            (************00)
         call [1mMockMiddleware::coverage(address)([1mCryticTester) [1m<no source map>
           call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
             delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
               call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
                 call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                    (8)
                 call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                    (0, ************, 0, **********, 0)
                  (************, **********)
                (************, **********)
              (************, **********)
           call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
              (18)
            (************00)
          (************00)
        (************00)
     call [1mERC1967Proxy::[unknown method](0xf01391e30000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mslashableCollateral([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
         call [1mMockMiddleware::slashableCollateral(address,uint48)([1mCryticTester, 1535673600) [1m<no source map>
           call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
             delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
               call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
                 call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                    (8)
                 call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                    (0, ************, 0, **********, 0)
                  (************, **********)
                (************, **********)
              (************, **********)
           call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
              (18)
            (************00)
          (************00)
        (************00)
     call [1mERC1967Proxy::[unknown method](0xb1732b************000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mliquidationThreshold([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (700000000000000000000000000)
        (700000000000000000000000000)
     call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m<no source map>
       delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
         call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
           call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
              (8)
           call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
              (0, 100000000, 0, **********, 0)
            (100000000, **********)
          (100000000, **********)
        (100000000, **********)
     call [1mERC1967Proxy::[unknown method](0x70a082310000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mbalanceOf([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (134383206821)
        (134383206821)
     call [1mERC1967Proxy::[unknown method](0x98db1b080000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mrestakerRate([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (795513583471503155188697930)
        (795513583471503155188697930)
     call [1mERC1967Proxy::[unknown method](0x70a082310000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mbalanceOf([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (134383206821)
        (134383206821)
      ******************************************00000000000016bcc41e9000000000000000000000000000000000000000000000000000000016bcc41e900000000000000000000000000000000000000000000000000000000c38da54a474000000000000000000000000000000000000000001bca300f4c82c85e9c200000000000000000000000000000000000000000000024306c4097859c43c000000000000000000000000000000000000000000000004353153b7afd6c7236756a9
    (************00, ************00, 13438320682100, 537532827284000000000000000, 700000000000000000000000000, 1302246047998408332312794793)
  (************00, ************00, 13438320682100, 537532827284000000000000000, 700000000000000000000000000, 1302246047998408332312794793)

optimize_burnable_amount_no_fee: max value: 31536000000000000000000000000000000

  Call sequence:
    CryticTester.stakedCap_approve(0xf07,115792089237316195423570985008687907853269984665640564039457584007913109639937) from: ****************************************** Time delay: 574632 seconds Block delay: 49025
    CryticTester.switch_asset(212044767462858914926783678947438165064938418098592215469523184700305166611) from: ****************************************** Time delay: 136394 seconds Block delay: 39519
    CryticTester.feeAuction_buy(92652242509499653241450768969330428099610015019579684000640442862674338254586,[0xf07, 0x1fffffffe, 0xffffffff, 0x1fffffffe, 0x94771550282853f6e0124c302f7de1cf50aa45ca, 0xd6bbde9174b1cdaa358d2cf4d57d1a9f7178fbff, 0x1af7f588a501ea2b5bb3feefa744892aa2cf00e6, 0x1fffffffe, 0xffffffff, 0x2fffffffd, 0x92a6649fdcc044da968d94202465578a9371c7b1, 0xffffffff, 0x1fffffffe, 0x2fffffffd, 0x89ca9f4f77b267778eb2ea0ba1beadee8523af36, 0x5615deb798bb3e4dfa0139dfa1b3d433cc23b72f],[**********, 255, 115792089237316195423570985008687907853269984665640564039457584007786771967682],0xffffffff,93879539468895363378602490700177108785076057462777304803290818807386828763502) from: ****************************************** Time delay: 29577 seconds Block delay: 57807
    CryticTester.delegation_setLtvBuffer(49827746443653132539802951233502146851327483214359573697327852414130813135244) from: ****************************************** Time delay: 90029 seconds Block delay: 28494
    CryticTester.oracle_setStaleness(0xffffffff,6888066478505441749850992418581994297142195019068002000074442631546382030335) from: ****************************************** Time delay: 592773 seconds Block delay: 50592
    CryticTester.lender_repay(79134579944527902223956207440668662801201128554431323912964015423061838657580) from: ****************************************** Time delay: 322357 seconds Block delay: 10
    CryticTester.property_repaid_debt_equals_zero_debt() from: ****************************************** Time delay: 156190 seconds Block delay: 14278
    CryticTester.lender_repay(115792089237316195423570985008687907853269984665639664039457584007913129639937) from: ****************************************** Time delay: 395200 seconds Block delay: 48689
    CryticTester.capToken_redeem_clamped(95741646795015930638342469798301058198531925459299825715208509091224771411149) from: ****************************************** Time delay: 288282 seconds Block delay: 32651
    CryticTester.lender_liquidate(96527322811128641503200922976669652610386069949039409468408214856818078802911) from: ****************************************** Time delay: 166184 seconds Block delay: 4924
    CryticTester.asset_mint(0x2a07706473244bc757e10f2a9e86fb532828afe3,1313373041) from: ****************************************** Time delay: 406880 seconds Block delay: 16480
    CryticTester.feeAuction_setMinStartPrice(50567676296237530215709939272598174094397627629202802397599245577743710227668) from: ****************************************** Time delay: 419861 seconds Block delay: 5005
    CryticTester.lender_borrow_clamped(46579599527300147752911888174834388979015831070268901955322967468044978416515) from: ****************************************** Time delay: 3867 seconds Block delay: 25461
    CryticTester.property_borrowed_asset_value() from: ****************************************** Time delay: 222812 seconds Block delay: 50592
    CryticTester.lender_initiateLiquidation() from: ****************************************** Time delay: 322345 seconds Block delay: 41859
    CryticTester.property_total_borrowed_less_than_total_supply() from: ****************************************** Time delay: 322338 seconds Block delay: 4991
    CryticTester.capToken_setFeeData(0x1804c8ab1f12e6bbf3894d4083f33e07309d1f38,(49869528211447337507581, 105595305270768091135739420498098964224445880925025084223878852022216500825372, 40223805042954923313978570538536601647058217615594817495771589330482330191206, 99999999999999999999999999, 115792089237316195423570985008687907853269984665640414039457584007913129639935, 33056249739822063734181)) from: ****************************************** Time delay: 59694 seconds Block delay: 46152
    CryticTester.mockNetworkMiddleware_setMockSlashableCollateralByVault(53502540585222975478199632749217283793864113140416536062295354888833147494184) from: ****************************************** Time delay: 311699 seconds Block delay: 36723
    CryticTester.mockNetworkMiddleware_setMockSlashableCollateralByVault(53502540585222975478199632749217283793864113140416536062295354888833147494184) from: ****************************************** Time delay: 311699 seconds Block delay: 36723
    CryticTester.capToken_mint_clamped(12000000000000000000001) from: ****************************************** Time delay: 352545 seconds Block delay: 43649
    CryticTester.capToken_mint_clamped(12000000000000000000001) from: ****************************************** Time delay: 352545 seconds Block delay: 43649
    CryticTester.stakedCap_deposit(51895644719467792066571894091710869393543355905728171984000194425741445815407,0xf02) from: ****************************************** Time delay: 305996 seconds Block delay: 52772
    CryticTester.mockERC4626Tester_decreaseYield(87491451613419423481861767412035902270526217848459061812387440353582418791169) from: ****************************************** Time delay: 542488 seconds Block delay: 51640
    CryticTester.property_repaid_debt_equals_zero_debt() from: ****************************************** Time delay: 477281 seconds Block delay: 52157
    CryticTester.capToken_rescueERC20(0x1fffffffe,0xf0b) from: ****************************************** Time delay: 235976 seconds Block delay: 38370
    CryticTester.capToken_mint_clamped(100000000000000000000000) from: ****************************************** Time delay: 419861 seconds Block delay: 51722
    CryticTester.stakedCap_withdraw(15676537107484606108685441334103026608970695120048888931086499268106074929408,0xffffffff,0xffffffff) from: ****************************************** Time delay: 424724 seconds Block delay: 29
    CryticTester.property_sum_of_withdrawals() from: ****************************************** Time delay: 531977 seconds Block delay: 4943
    CryticTester.capToken_rescueERC20(0xffffffff,0xffffffff) from: ****************************************** Time delay: 46515 seconds Block delay: 54400
    CryticTester.delegation_addAgent_clamped(19531223562357129865134724754745466567110609999947022834626841932678294104885,72487521914368377953988876135746317038880994823307043348320678734180453963688) from: ****************************************** Time delay: 85183 seconds Block delay: 30240
    CryticTester.delegation_modifyAgent_clamped(26952286189329098591336106216817974982575214762653844347701796976931558349516,26800594708312799192745907939700047175999914997954045271137693482473807871488) from: ****************************************** Time delay: 441100 seconds Block delay: 14426
    CryticTester.accessControl_revokeRole("debt\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL",0xffffffff) from: ****************************************** Time delay: 559699 seconds Block delay: 19373
    CryticTester.capToken_redeem_clamped(972) from: ****************************************** Time delay: 96 seconds Block delay: 52566
    CryticTester.property_borrowed_asset_value() from: ****************************************** Time delay: 117047 seconds Block delay: 2652
    CryticTester.delegation_setLtvBuffer(109562668649329611418622810869346749521815631562538768477693817388077988169217) from: ****************************************** Time delay: 1 seconds Block delay: 58182
    CryticTester.lender_cancelLiquidation() from: ****************************************** Time delay: 427125 seconds Block delay: 35974
    CryticTester.stakedCap_withdraw(115792089237316195423570985008687907853269984665640564039457584007913129638935,0x2fffffffd,0x1fffffffe) from: ****************************************** Time delay: 74232 seconds Block delay: 26213
    CryticTester.lender_borrow_clamped(115792089237316195423570985008687907853269984665640564039457584007913129539935) from: ****************************************** Time delay: 255 seconds Block delay: 24236
    CryticTester.feeReceiver_distribute() from: ****************************************** Time delay: 280807 seconds Block delay: 13781
    CryticTester.property_sum_of_withdrawals() from: ****************************************** Time delay: 378553 seconds Block delay: 46202
    CryticTester.capToken_setFractionalReserveVault() from: ****************************************** Time delay: 324839 seconds Block delay: 24908
    CryticTester.asset_mint(0xffffffff,126357672254) from: ****************************************** Time delay: 232345 seconds Block delay: 41
    CryticTester.property_agent_cannot_have_less_than_minBorrow_balance_of_debt_token() from: ****************************************** Time delay: 553490 seconds Block delay: 54155
    CryticTester.feeAuction_buy(115792089237316195423570985008687907853269984665640564039457584007913129639931,[0xf04, 0x13aa49bac059d709dd0a18d6bb63290076a702d7, 0x2fffffffd, 0xffffffff, 0xf03, 0x1fffffffe, 0x1fffffffe, 0x1fffffffe, 0xf01, 0x2fffffffd, 0xffffffff],[18288757733045380551503274528125006854836485293821381037851949420789314613531, 27593983388239062173684167097781732468358246060390907396799600740230209691904, 114407876677640157244460992782994412749592936608506359720342060011178126094316, 806, 115792089237316195423570985008687907853269984665639664039457584007913129639937, 112460761921767176663790737009612481659650724618787106453571493765519229241706, 97104360294539861685405085511446747187079341451529004986688083398527911233535, 28626148368098058556224300028183112535249935289012135035102653942533485220944, 99505910399632209966995905115613293827486230739871862580187145672592491923954, 98086272215821989079428328276031762678184978372882329803266057022444646876759, 115792089237316195423570985008687907853269984665640564039457584007911816266896],0x1fffffffe,111082430914387101348512142399068398536180595074737189522987659418204691030402) from: ****************************************** Time delay: 16379 seconds Block delay: 80
    CryticTester.lender_removeAsset(0x2fffffffd) from: ****************************************** Time delay: 569114 seconds Block delay: 59224
    CryticTester.capToken_pauseAsset(0xffffffff) from: ****************************************** Time delay: 222375 seconds Block delay: 26155
    CryticTester.property_liquidation_does_not_increase_bonus() from: ****************************************** Time delay: 440097 seconds Block delay: 55819
    CryticTester.property_utilization_ratio_never_greater_than_1e27() from: ****************************************** Time delay: 187012 seconds Block delay: 9685
    CryticTester.capToken_setWhitelist(0x886d6d1eb8d415b00052828cd6d5b321f072073d,false) from: ****************************************** Time delay: 455741 seconds Block delay: 32854
    CryticTester.feeAuction_setStartPrice(7377677814908888162002474126200827938967415571586839545227110197266877943779) from: ****************************************** Time delay: 135363 seconds Block delay: 64
    CryticTester.feeAuction_buy_clamped(12162600305462670775391727367113962142614630742603541447094442274112133518431,0) from: ****************************************** Time delay: 140903 seconds Block delay: 8782
    CryticTester.delegation_setLtvBuffer(23938022857093894203326269702339161604873691904342839652509404595822782986470) from: ****************************************** Time delay: 256840 seconds Block delay: 19879
    CryticTester.mockAaveDataProvider_setVariableBorrowRate(982) from: ****************************************** Time delay: 63439 seconds Block delay: 1707
    CryticTester.accessControl_grantRole("\212\217\175\&3\205\131+'|C\162\134\GS^\DLE\214\237\152\215\&7\166\224\207\CAN\205\&1FG\139\197\CANw",0x1fffffffe) from: ****************************************** Time delay: 155512 seconds Block delay: 40107
    CryticTester.stakedCap_notify() from: ****************************************** Time delay: 136393 seconds Block delay: 24908
    CryticTester.oracle_setRestakerRate(0x212224d2f2d262cd093ee13240ca4873fccbba3c,115792089237316195423570985008687907853269984665640564039457584007913129639908) from: ****************************************** Time delay: 414579 seconds Block delay: 24224
    CryticTester.property_utilization_ratio_never_greater_than_1e27() from: ****************************************** Time delay: 321220 seconds Block delay: 25911
    CryticTester.capToken_burn(31536000000000000000000000000000000,65536,32077810481401622068091747949417155326238316850259719424869565855956394466754) from: ****************************************** Time delay: 50 seconds Block delay: 1000

Traces:
call [1mERC1967Proxy::[unknown method](0x08cf6fec0000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m(/recon/test/recon/mocks/MockMiddleware.sol:71)
 delegatecall ******************************************::[1mreservesData(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (0, [1mERC1967Proxy, [1mERC1967Proxy, [1mERC1967Proxy, 6, false, 100000000)
  (0, [1mERC1967Proxy, [1mERC1967Proxy, [1mERC1967Proxy, 6, false, 100000000)
call [1mERC1967Proxy::[unknown method](0x70a082310000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m(/recon/test/recon/mocks/MockMiddleware.sol:73)
 delegatecall ******************************************::[1mbalanceOf([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (0)
  (0)
call [1mERC1967Proxy::[unknown method](0x71a97305) [1m(/recon/test/recon/mocks/MockMiddleware.sol:77)
 delegatecall ******************************************::[1massets() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    ([******************************************, ******************************************, ******************************************])
  ([******************************************, ******************************************, ******************************************])
call ******************************************::[1mbalanceOf([1mERC1967Proxy) [1m(/recon/test/recon/mocks/MockMiddleware.sol:86)
  (124000000000000000000002)
call ******************************************::[1mbalanceOf([1mERC1967Proxy) [1m(/recon/test/recon/mocks/MockMiddleware.sol:86)
  (0)
call ******************************************::[1mbalanceOf([1mERC1967Proxy) [1m(/recon/test/recon/mocks/MockMiddleware.sol:86)
  (0)
call [1mERC1967Proxy::[unknown method](0xd8cb4aa30000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m(/recon/test/recon/mocks/MockMiddleware.sol:89)
 delegatecall ******************************************::[1mbonus([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
   call [1mERC1967Proxy::[unknown method](0x330244300000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
     delegatecall ******************************************::[1mcoverage([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
       call [1mMockMiddleware::slashableCollateral(address,uint48)([1mCryticTester, 1534982400) [1m<no source map>
         call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
           delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
             call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
               call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                  (8)
               call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                  (0, ************, 0, **********, 0)
                (************, **********)
              (************, **********)
            (************, **********)
         call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
            (18)
          (************00)
       call [1mMockMiddleware::coverage(address)([1mCryticTester) [1m<no source map>
         call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
           delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
             call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
               call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                  (8)
               call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                  (0, ************, 0, **********, 0)
                (************, **********)
              (************, **********)
            (************, **********)
         call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
            (18)
          (************00)
        (************00)
      (************00)
   call [1mERC1967Proxy::[unknown method](0xf01391e30000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
     delegatecall ******************************************::[1mslashableCollateral([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
       call [1mMockMiddleware::slashableCollateral(address,uint48)([1mCryticTester, 1534982400) [1m<no source map>
         call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
           delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
             call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
               call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                  (8)
               call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                  (0, ************, 0, **********, 0)
                (************, **********)
              (************, **********)
            (************, **********)
         call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
            (18)
          (************00)
        (************00)
      (************00)
   call [1mERC1967Proxy::[unknown method](0xb1732b************000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
     delegatecall ******************************************::[1mliquidationThreshold([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
        (700000000000000000000000000)
      (700000000000000000000000000)
   [91merror Revert Panic(0x4e487b710000000000000000000000000000000000000000000000000000000000000012) [1m<no source map>
 [91merror Revert Panic(0x4e487b710000000000000000000000000000000000000000000000000000000000000012) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:41)
call [1mERC1967Proxy::[unknown method](0xfeee17560000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m(/recon/test/recon/mocks/MockMiddleware.sol:47)
 delegatecall ******************************************::[1mcurrentUtilizationIndex(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
   delegatecall ******************************************::0x3c054127e912a1b0cc7579bc5827e495c2ce52587bc3871751e3281fc5599b38c3bfc4000000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7 [1m<no source map>
      ******************************************000000000000000000000000
    (0)
  (0)
call [1mERC1967Proxy::[unknown method](0xbf20d9dc0000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m(/recon/test/recon/mocks/MockMiddleware.sol:49)
 delegatecall ******************************************::[1mutilization(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
   delegatecall ******************************************::0x0f82ffd0e912a1b0cc7579bc5827e495c2ce52587bc3871751e3281fc5599b38c3bfc4000000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7 [1m<no source map>
      ******************************************000000000000000000000000
    (0)
  (0)
call [1mERC1967Proxy::[unknown method](0x8d7301240000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m(/recon/test/recon/mocks/MockMiddleware.sol:52)
 delegatecall ******************************************::[1mtotalBorrows(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (0)
  (0)
call [1mERC1967Proxy::[unknown method](0xc0af0d3b0000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m(/recon/test/recon/mocks/MockMiddleware.sol:53)
 delegatecall ******************************************::[1mgetVaultDebt(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (0)
  (0)
call [1mERC1967Proxy::[unknown method](0xb7902303) [1m(/recon/test/recon/mocks/MockMiddleware.sol:55)
 delegatecall ******************************************::[1minsuranceFund() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (******************************************)
  (******************************************)
call ******************************************::[1mbalanceOf(******************************************) [1m(/recon/test/recon/mocks/MockMiddleware.sol:54)
  (0)
call [1mERC1967Proxy::[unknown method](0x18160ddd) [1m(/recon/test/recon/mocks/MockMiddleware.sol:55)
 delegatecall ******************************************::[1mtotalSupply() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (124000000000000000000001999999999028)
  (124000000000000000000001999999999028)
call [1mERC1967Proxy::[unknown method](0xe75179a40000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m(/recon/test/recon/mocks/MockMiddleware.sol:58)
 delegatecall ******************************************::[1mreserve(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (0)
  (0)
call [1mERC1967Proxy::[unknown method](0x01e1d114) [1m(/recon/test/recon/mocks/MockMiddleware.sol:89)
 delegatecall ******************************************::[1mtotalAssets() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (100000000000000000)
  (100000000000000000)
call [1mERC1967Proxy::[unknown method](0x18160ddd) [1m(/recon/test/recon/mocks/MockMiddleware.sol:89)
 delegatecall ******************************************::[1mtotalSupply() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (0)
  (0)
call [1mERC1967Proxy::[unknown method](0x70a082310000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m(/recon/test/recon/mocks/MockMiddleware.sol:89)
 delegatecall ******************************************::[1mbalanceOf([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (123379999999999999900001989999999028)
  (123379999999999999900001989999999028)
call [1mERC1967Proxy::[unknown method](0x2c3ee88c000000000000000000000000000000000017c31a8928cae4d914a1dbf7b43834) [1m(/recon/test/recon/mocks/MockMiddleware.sol:89)
 delegatecall ******************************************::[1mgetRedeemAmount(123379999999999999900001989999999028) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
   delegatecall ******************************************::0x4bfcc81c3b40995b576f8dd0a8521bba471c5346e53f6a25529b0903b82331eb1a2afe00000000000000000000000000000000000017c31a8928cae4d914a1dbf7b43834 [1m<no source map>
     call [1mERC1967Proxy::[unknown method](0x18160ddd) [1m<no source map>
       delegatecall ******************************************::[1mtotalSupply() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (124000000000000000000001999999999028)
        (124000000000000000000001999999999028)
     call [1mERC1967Proxy::[unknown method](0x71a97305) [1m<no source map>
       delegatecall ******************************************::[1massets() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          ([******************************************, ******************************************, ******************************************])
        ([******************************************, ******************************************, ******************************************])
     call [1mERC1967Proxy::[unknown method](0x9782e8210000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m<no source map>
       delegatecall ******************************************::[1mtotalSupplies(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (124000000000000000000002)
        (124000000000000000000002)
     call [1mERC1967Proxy::[unknown method](0x9782e821000000000000000000000000d16d567549a2a2a2005aeacf7fb193851603dd70) [1m<no source map>
       delegatecall ******************************************::[1mtotalSupplies(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (0)
        (0)
     call [1mERC1967Proxy::[unknown method](0x9782e82100000000000000000000000096d3f6c20eed2697647f543fe6c08bc2fbf39758) [1m<no source map>
       delegatecall ******************************************::[1mtotalSupplies(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (0)
        (0)
      ******************************************00000000000000000000004000000000000000000000000000000000000000000000000000000000000000c00000000000000000000000000000000000000000000000000000000000000003000000000000000000000000000000000000000000001a207175b0be6f4e7961000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000003000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
    ([123379999999999999900001, 0, 0], [0, 0, 0])
  ([123379999999999999900001, 0, 0], [0, 0, 0])
call [1mERC1967Proxy::[unknown method](0x92e423b50000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m(/recon/test/recon/mocks/MockMiddleware.sol:89)
 delegatecall ******************************************::[1magent([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
   delegatecall ******************************************::0x2a43ad13d6af1ec8a1789f5ada2b972bd1569f7c83af2e268be17cd65efe8474ebf088000000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496 [1m<no source map>
     call [1mERC1967Proxy::[unknown method](0x330244300000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mcoverage([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
         call [1mMockMiddleware::slashableCollateral(address,uint48)([1mCryticTester, 1534982400) [1m<no source map>
           call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
             delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
               call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
                 call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                    (8)
                 call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                    (0, ************, 0, **********, 0)
                  (************, **********)
                (************, **********)
              (************, **********)
           call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
              (18)
            (************00)
         call [1mMockMiddleware::coverage(address)([1mCryticTester) [1m<no source map>
           call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
             delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
               call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
                 call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                    (8)
                 call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                    (0, ************, 0, **********, 0)
                  (************, **********)
                (************, **********)
              (************, **********)
           call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
              (18)
            (************00)
          (************00)
        (************00)
     call [1mERC1967Proxy::[unknown method](0xf01391e30000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mslashableCollateral([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
         call [1mMockMiddleware::slashableCollateral(address,uint48)([1mCryticTester, 1534982400) [1m<no source map>
           call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
             delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
               call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
                 call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                    (8)
                 call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                    (0, ************, 0, **********, 0)
                  (************, **********)
                (************, **********)
              (************, **********)
           call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
              (18)
            (************00)
          (************00)
        (************00)
     call [1mERC1967Proxy::[unknown method](0xb1732b************000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mliquidationThreshold([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (700000000000000000000000000)
        (700000000000000000000000000)
      ******************************************00000000000016bcc41e9000000000000000000000000000000000000000000000000000000016bcc41e9000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000024306c4097859c43c000000ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
    (************00, ************00, 0, 0, 700000000000000000000000000, 115792089237316195423570985008687907853269984665640564039457584007913129639935)
  (************00, ************00, 0, 0, 700000000000000000000000000, 115792089237316195423570985008687907853269984665640564039457584007913129639935)
call [1mERC1967Proxy::[unknown method](0x70a082310000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m(/recon/test/recon/targets/MockAaveDataProviderTargets.sol:26)
 delegatecall ******************************************::[1mbalanceOf([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (123379999999999999900001989999999028)
  (123379999999999999900001989999999028)
call [1mERC1967Proxy::[unknown method](0xb7902303) [1m(/recon/test/recon/targets/MockAaveDataProviderTargets.sol:26)
 delegatecall ******************************************::[1minsuranceFund() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (******************************************)
  (******************************************)
call ******************************************::[1mbalanceOf(******************************************) [1m(/recon/test/recon/targets/MockAaveDataProviderTargets.sol:26)
  (0)
call [1mERC1967Proxy::[unknown method](0x18160ddd) [1m(/recon/test/recon/targets/MockAaveDataProviderTargets.sol:26)
 delegatecall ******************************************::[1mtotalSupply() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (124000000000000000000001999999999028)
  (124000000000000000000001999999999028)
call ******************************************::[1mbalanceOf([1mCryticTester) [1m(/recon/test/recon/targets/MockAaveDataProviderTargets.sol:26)
  (309361009821345068724781053)
call [1mERC1967Proxy::[unknown method](0xb7c4a6bf0000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d700000000000000000000000000000000000612d847b578e7643c28ac00000000) [1m(/recon/test/recon/targets/MockAaveDataProviderTargets.sol:26)
 delegatecall ******************************************::[1mgetBurnAmount(******************************************, 31536000000000000000000000000000000) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
   delegatecall ******************************************::0x2d761af33b40995b576f8dd0a8521bba471c5346e53f6a25529b0903b82331eb1a2afe0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d700000000000000000000000000000000000612d847b578e7643c28ac00000000 [1m<no source map>
     call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m<no source map>
       delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
         call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
           call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
              (8)
           call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
              (0, 100000000, 0, **********, 0)
            (100000000, **********)
          (100000000, **********)
        (100000000, **********)
     call [1mERC1967Proxy::[unknown method](0x41976e0900000000000000000000000092a6649fdcc044da968d94202465578a9371c7b1) [1m<no source map>
       delegatecall ******************************************::[1mgetPrice([1mERC1967Proxy) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
         call ******************************************::[1mprice([1mERC1967Proxy) [1m<no source map>
           call [1mERC1967Proxy::[unknown method](0x18160ddd) [1m<no source map>
             delegatecall ******************************************::[1mtotalSupply() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
                (124000000000000000000001999999999028)
              (124000000000000000000001999999999028)
           call [1mERC1967Proxy::[unknown method](0x71a97305) [1m<no source map>
             delegatecall ******************************************::[1massets() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
                ([******************************************, ******************************************, ******************************************])
              ([******************************************, ******************************************, ******************************************])
           call [1mERC1967Proxy::[unknown method](0x9782e8210000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m<no source map>
             delegatecall ******************************************::[1mtotalSupplies(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
                (124000000000000000000002)
              (124000000000000000000002)
           call ******************************************::[1mdecimals() [1m<no source map>
              (6)
           call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m<no source map>
             delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
               call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
                 call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                    (8)
                 call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                    (0, 100000000, 0, **********, 0)
                  (100000000, **********)
                (100000000, **********)
              (100000000, **********)
           call [1mERC1967Proxy::[unknown method](0x9782e821000000000000000000000000d16d567549a2a2a2005aeacf7fb193851603dd70) [1m<no source map>
             delegatecall ******************************************::[1mtotalSupplies(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
                (0)
              (0)
           call ******************************************::[1mdecimals() [1m<no source map>
              (8)
           call [1mERC1967Proxy::[unknown method](0x41976e09000000000000000000000000d16d567549a2a2a2005aeacf7fb193851603dd70) [1m<no source map>
             delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
               call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
                 call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                    (8)
                 call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                    (0, 100000000, 0, **********, 0)
                  (100000000, **********)
                (100000000, **********)
              (100000000, **********)
           call [1mERC1967Proxy::[unknown method](0x9782e82100000000000000000000000096d3f6c20eed2697647f543fe6c08bc2fbf39758) [1m<no source map>
             delegatecall ******************************************::[1mtotalSupplies(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
                (0)
              (0)
           call ******************************************::[1mdecimals() [1m<no source map>
              (18)
           call [1mERC1967Proxy::[unknown method](0x41976e0900000000000000000000000096d3f6c20eed2697647f543fe6c08bc2fbf39758) [1m<no source map>
             delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
               call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
                 call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                    (8)
                 call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                    (0, 100000000, 0, **********, 0)
                  (100000000, **********)
                (100000000, **********)
              (100000000, **********)
           call [1mERC1967Proxy::[unknown method](0x313ce567) [1m<no source map>
             delegatecall ******************************************::[1mdecimals() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
                (18)
              (18)
            (100000000, **********)
          (100000000, **********)
        (100000000, **********)
     call ******************************************::[1mdecimals() [1m<no source map>
        (6)
     call [1mERC1967Proxy::[unknown method](0x313ce567) [1m<no source map>
       delegatecall ******************************************::[1mdecimals() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (18)
        (18)
     call [1mERC1967Proxy::[unknown method](0x18160ddd) [1m<no source map>
       delegatecall ******************************************::[1mtotalSupply() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (124000000000000000000001999999999028)
        (124000000000000000000001999999999028)
     call [1mERC1967Proxy::[unknown method](0x9782e8210000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m<no source map>
       delegatecall ******************************************::[1mtotalSupplies(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (124000000000000000000002)
        (124000000000000000000002)
      ******************************************000006ad91ea931c6ec000000000000000000000000000000000000000000000000000000000000000000000
    (31536000000000000000000, 0)
  (31536000000000000000000, 0)
call [1mHEVM::[1mprank([1mCryticTester) [1m(/recon/test/recon/targets/MockAaveDataProviderTargets.sol:26)
  0x
call [1mERC1967Proxy::[unknown method](0x561279870000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d700000000000000000000000000000000000612d847b578e7643c28ac0000000000000000000000000000000000000000000000000000000000000000000100000000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e149646eb65b761caf7e5ee8e48a7068abf2dab77dbbc050968a932b6a794e1625dc2) [1m(/recon/test/recon/targets/MockAaveDataProviderTargets.sol:26)
 delegatecall ******************************************::[1mburn(******************************************, 31536000000000000000000000000000000, 65536, [1mCryticTester, 32077810481401622068091747949417155326238316850259719424869565855956394466754) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
   delegatecall ******************************************::0x2d761af33b40995b576f8dd0a8521bba471c5346e53f6a25529b0903b82331eb1a2afe0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d700000000000000000000000000000000000612d847b578e7643c28ac00000000 [1m<no source map>
     call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m<no source map>
       delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
         call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
           call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
              (8)
           call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
              (0, 100000000, 0, **********, 0)
            (100000000, **********)
          (100000000, **********)
        (100000000, **********)
     call [1mERC1967Proxy::[unknown method](0x41976e0900000000000000000000000092a6649fdcc044da968d94202465578a9371c7b1) [1m<no source map>
       delegatecall ******************************************::[1mgetPrice([1mERC1967Proxy) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
         call ******************************************::[1mprice([1mERC1967Proxy) [1m<no source map>
           call [1mERC1967Proxy::[unknown method](0x18160ddd) [1m<no source map>
             delegatecall ******************************************::[1mtotalSupply() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
                (124000000000000000000001999999999028)
              (124000000000000000000001999999999028)
           call [1mERC1967Proxy::[unknown method](0x71a97305) [1m<no source map>
             delegatecall ******************************************::[1massets() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
                ([******************************************, ******************************************, ******************************************])
              ([******************************************, ******************************************, ******************************************])
           call [1mERC1967Proxy::[unknown method](0x9782e8210000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m<no source map>
             delegatecall ******************************************::[1mtotalSupplies(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
                (124000000000000000000002)
              (124000000000000000000002)
           call ******************************************::[1mdecimals() [1m<no source map>
              (6)
           call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m<no source map>
             delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
               call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
                 call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                    (8)
                 call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                    (0, 100000000, 0, **********, 0)
                  (100000000, **********)
                (100000000, **********)
              (100000000, **********)
           call [1mERC1967Proxy::[unknown method](0x9782e821000000000000000000000000d16d567549a2a2a2005aeacf7fb193851603dd70) [1m<no source map>
             delegatecall ******************************************::[1mtotalSupplies(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
                (0)
              (0)
           call ******************************************::[1mdecimals() [1m<no source map>
              (8)
           call [1mERC1967Proxy::[unknown method](0x41976e09000000000000000000000000d16d567549a2a2a2005aeacf7fb193851603dd70) [1m<no source map>
             delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
               call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
                 call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                    (8)
                 call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                    (0, 100000000, 0, **********, 0)
                  (100000000, **********)
                (100000000, **********)
              (100000000, **********)
           call [1mERC1967Proxy::[unknown method](0x9782e82100000000000000000000000096d3f6c20eed2697647f543fe6c08bc2fbf39758) [1m<no source map>
             delegatecall ******************************************::[1mtotalSupplies(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
                (0)
              (0)
           call ******************************************::[1mdecimals() [1m<no source map>
              (18)
           call [1mERC1967Proxy::[unknown method](0x41976e0900000000000000000000000096d3f6c20eed2697647f543fe6c08bc2fbf39758) [1m<no source map>
             delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
               call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
                 call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                    (8)
                 call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                    (0, 100000000, 0, **********, 0)
                  (100000000, **********)
                (100000000, **********)
              (100000000, **********)
           call [1mERC1967Proxy::[unknown method](0x313ce567) [1m<no source map>
             delegatecall ******************************************::[1mdecimals() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
                (18)
              (18)
            (100000000, **********)
          (100000000, **********)
        (100000000, **********)
     call ******************************************::[1mdecimals() [1m<no source map>
        (6)
     call [1mERC1967Proxy::[unknown method](0x313ce567) [1m<no source map>
       delegatecall ******************************************::[1mdecimals() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (18)
        (18)
     call [1mERC1967Proxy::[unknown method](0x18160ddd) [1m<no source map>
       delegatecall ******************************************::[1mtotalSupply() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (124000000000000000000001999999999028)
        (124000000000000000000001999999999028)
     call [1mERC1967Proxy::[unknown method](0x9782e8210000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m<no source map>
       delegatecall ******************************************::[1mtotalSupplies(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (124000000000000000000002)
        (124000000000000000000002)
      ******************************************000006ad91ea931c6ec000000000000000000000000000000000000000000000000000000000000000000000
   delegatecall ******************************************::0x53c655a85c48f30a22a9811126b69b5adcaabfc5ae0a83b6493e1b31e09dc579923ad1000000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d70000000000000000000000000000000000000000000006ad91ea931c6ec00000 [1m<no source map>
      0x
   delegatecall ******************************************::0x7be70baee912a1b0cc7579bc5827e495c2ce52587bc3871751e3281fc5599b38c3bfc4000000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d700000000000000000000000000000000000612d847b578e7643c28ac000000000000000000000000000000000000000000000000000006ad91ea931c6ec0000000000000000000000000000000000000000000000000000000000000000100000000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e149646eb65b761caf7e5ee8e48a7068abf2dab77dbbc050968a932b6a794e1625dc20000000000000000000000000000000000000000000000000000000000000000 [1m<no source map>
     call ******************************************::[1mtransfer([1mCryticTester, 31536000000000000000000) [1m<no source map>
       emit Transfer(from=[1mERC1967Proxy, to=[1mCryticTester, value=31536000000000000000000) [1m<no source map>
        (true)
     emit Burn(burner=[1mCryticTester, receiver=[1mCryticTester, asset=******************************************, amountIn=31536000000000000000000000000000000, amountOut=31536000000000000000000, fee=0) [1m<no source map>
      0x
   emit Transfer(from=[1mCryticTester, to=******************************************, value=31536000000000000000000000000000000) [1m<no source map>
    (31536000000000000000000)
  (31536000000000000000000)
call [1mERC1967Proxy::[unknown method](0x70a082310000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m(/recon/test/recon/targets/MockAaveDataProviderTargets.sol:26)
 delegatecall ******************************************::[1mbalanceOf([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (91843999999999999900001989999999028)
  (91843999999999999900001989999999028)
call [1mERC1967Proxy::[unknown method](0xb7902303) [1m(/recon/test/recon/targets/MockAaveDataProviderTargets.sol:26)
 delegatecall ******************************************::[1minsuranceFund() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (******************************************)
  (******************************************)
call ******************************************::[1mbalanceOf(******************************************) [1m(/recon/test/recon/targets/MockAaveDataProviderTargets.sol:26)
  (0)
call [1mERC1967Proxy::[unknown method](0x18160ddd) [1m(/recon/test/recon/targets/MockAaveDataProviderTargets.sol:26)
 delegatecall ******************************************::[1mtotalSupply() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (92464000000000000000001999999999028)
  (92464000000000000000001999999999028)
call ******************************************::[1mbalanceOf([1mCryticTester) [1m(/recon/test/recon/targets/MockAaveDataProviderTargets.sol:26)
  (309392545821345068724781053)
call [1mERC1967Proxy::[unknown method](0xd936547e0000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m(/recon/test/recon/targets/MockAaveDataProviderTargets.sol:26)
 delegatecall ******************************************::[1mwhitelisted([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (false)
  (false)
call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m(/recon/test/recon/targets/MockAaveDataProviderTargets.sol:26)
 delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
   call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
     call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
        (8)
     call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
        (0, 100000000, 0, **********, 0)
      (100000000, **********)
    (100000000, **********)
  (100000000, **********)
call [1mERC1967Proxy::[unknown method](0x41976e0900000000000000000000000092a6649fdcc044da968d94202465578a9371c7b1) [1m(/recon/test/recon/targets/MockAaveDataProviderTargets.sol:26)
 delegatecall ******************************************::[1mgetPrice([1mERC1967Proxy) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
   call ******************************************::[1mprice([1mERC1967Proxy) [1m<no source map>
     call [1mERC1967Proxy::[unknown method](0x18160ddd) [1m<no source map>
       delegatecall ******************************************::[1mtotalSupply() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (92464000000000000000001999999999028)
        (92464000000000000000001999999999028)
     call [1mERC1967Proxy::[unknown method](0x71a97305) [1m<no source map>
       delegatecall ******************************************::[1massets() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          ([******************************************, ******************************************, ******************************************])
        ([******************************************, ******************************************, ******************************************])
     call [1mERC1967Proxy::[unknown method](0x9782e8210000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m<no source map>
       delegatecall ******************************************::[1mtotalSupplies(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (92464000000000000000002)
        (92464000000000000000002)
     call ******************************************::[1mdecimals() [1m<no source map>
        (6)
     call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m<no source map>
       delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
         call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
           call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
              (8)
           call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
              (0, 100000000, 0, **********, 0)
            (100000000, **********)
          (100000000, **********)
        (100000000, **********)
     call [1mERC1967Proxy::[unknown method](0x9782e821000000000000000000000000d16d567549a2a2a2005aeacf7fb193851603dd70) [1m<no source map>
       delegatecall ******************************************::[1mtotalSupplies(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (0)
        (0)
     call ******************************************::[1mdecimals() [1m<no source map>
        (8)
     call [1mERC1967Proxy::[unknown method](0x41976e09000000000000000000000000d16d567549a2a2a2005aeacf7fb193851603dd70) [1m<no source map>
       delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
         call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
           call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
              (8)
           call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
              (0, 100000000, 0, **********, 0)
            (100000000, **********)
          (100000000, **********)
        (100000000, **********)
     call [1mERC1967Proxy::[unknown method](0x9782e82100000000000000000000000096d3f6c20eed2697647f543fe6c08bc2fbf39758) [1m<no source map>
       delegatecall ******************************************::[1mtotalSupplies(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (0)
        (0)
     call ******************************************::[1mdecimals() [1m<no source map>
        (18)
     call [1mERC1967Proxy::[unknown method](0x41976e0900000000000000000000000096d3f6c20eed2697647f543fe6c08bc2fbf39758) [1m<no source map>
       delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
         call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
           call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
              (8)
           call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
              (0, 100000000, 0, **********, 0)
            (100000000, **********)
          (100000000, **********)
        (100000000, **********)
     call [1mERC1967Proxy::[unknown method](0x313ce567) [1m<no source map>
       delegatecall ******************************************::[1mdecimals() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (18)
        (18)
      (100000000, **********)
    (100000000, **********)
  (100000000, **********)
call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/MockAaveDataProviderTargets.sol:26)
  (6)
call [1mERC1967Proxy::[unknown method](0x08cf6fec0000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m(/recon/test/recon/mocks/MockMiddleware.sol:71)
 delegatecall ******************************************::[1mreservesData(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (0, [1mERC1967Proxy, [1mERC1967Proxy, [1mERC1967Proxy, 6, false, 100000000)
  (0, [1mERC1967Proxy, [1mERC1967Proxy, [1mERC1967Proxy, 6, false, 100000000)
call [1mERC1967Proxy::[unknown method](0x70a082310000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m(/recon/test/recon/mocks/MockMiddleware.sol:73)
 delegatecall ******************************************::[1mbalanceOf([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (0)
  (0)
call [1mERC1967Proxy::[unknown method](0x71a97305) [1m(/recon/test/recon/mocks/MockMiddleware.sol:77)
 delegatecall ******************************************::[1massets() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    ([******************************************, ******************************************, ******************************************])
  ([******************************************, ******************************************, ******************************************])
call ******************************************::[1mbalanceOf([1mERC1967Proxy) [1m(/recon/test/recon/mocks/MockMiddleware.sol:86)
  (92464000000000000000002)
call ******************************************::[1mbalanceOf([1mERC1967Proxy) [1m(/recon/test/recon/mocks/MockMiddleware.sol:86)
  (0)
call ******************************************::[1mbalanceOf([1mERC1967Proxy) [1m(/recon/test/recon/mocks/MockMiddleware.sol:86)
  (0)
call [1mERC1967Proxy::[unknown method](0xd8cb4aa30000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m(/recon/test/recon/mocks/MockMiddleware.sol:89)
 delegatecall ******************************************::[1mbonus([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
   call [1mERC1967Proxy::[unknown method](0x330244300000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
     delegatecall ******************************************::[1mcoverage([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
       call [1mMockMiddleware::slashableCollateral(address,uint48)([1mCryticTester, 1534982400) [1m<no source map>
         call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
           delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
             call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
               call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                  (8)
               call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                  (0, ************, 0, **********, 0)
                (************, **********)
              (************, **********)
            (************, **********)
         call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
            (18)
          (************00)
       call [1mMockMiddleware::coverage(address)([1mCryticTester) [1m<no source map>
         call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
           delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
             call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
               call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                  (8)
               call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                  (0, ************, 0, **********, 0)
                (************, **********)
              (************, **********)
            (************, **********)
         call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
            (18)
          (************00)
        (************00)
      (************00)
   call [1mERC1967Proxy::[unknown method](0xf01391e30000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
     delegatecall ******************************************::[1mslashableCollateral([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
       call [1mMockMiddleware::slashableCollateral(address,uint48)([1mCryticTester, 1534982400) [1m<no source map>
         call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
           delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
             call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
               call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                  (8)
               call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                  (0, ************, 0, **********, 0)
                (************, **********)
              (************, **********)
            (************, **********)
         call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
            (18)
          (************00)
        (************00)
      (************00)
   call [1mERC1967Proxy::[unknown method](0xb1732b************000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
     delegatecall ******************************************::[1mliquidationThreshold([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
        (700000000000000000000000000)
      (700000000000000000000000000)
   [91merror Revert Panic(0x4e487b710000000000000000000000000000000000000000000000000000000000000012) [1m<no source map>
 [91merror Revert Panic(0x4e487b710000000000000000000000000000000000000000000000000000000000000012) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:41)
call [1mERC1967Proxy::[unknown method](0xfeee17560000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m(/recon/test/recon/mocks/MockMiddleware.sol:47)
 delegatecall ******************************************::[1mcurrentUtilizationIndex(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
   delegatecall ******************************************::0x3c054127e912a1b0cc7579bc5827e495c2ce52587bc3871751e3281fc5599b38c3bfc4000000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7 [1m<no source map>
      ******************************************000000000000000000000000
    (0)
  (0)
call [1mERC1967Proxy::[unknown method](0xbf20d9dc0000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m(/recon/test/recon/mocks/MockMiddleware.sol:49)
 delegatecall ******************************************::[1mutilization(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
   delegatecall ******************************************::0x0f82ffd0e912a1b0cc7579bc5827e495c2ce52587bc3871751e3281fc5599b38c3bfc4000000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7 [1m<no source map>
      ******************************************000000000000000000000000
    (0)
  (0)
call [1mERC1967Proxy::[unknown method](0x8d7301240000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m(/recon/test/recon/mocks/MockMiddleware.sol:52)
 delegatecall ******************************************::[1mtotalBorrows(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (0)
  (0)
call [1mERC1967Proxy::[unknown method](0xc0af0d3b0000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m(/recon/test/recon/mocks/MockMiddleware.sol:53)
 delegatecall ******************************************::[1mgetVaultDebt(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (0)
  (0)
call [1mERC1967Proxy::[unknown method](0xb7902303) [1m(/recon/test/recon/mocks/MockMiddleware.sol:55)
 delegatecall ******************************************::[1minsuranceFund() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (******************************************)
  (******************************************)
call ******************************************::[1mbalanceOf(******************************************) [1m(/recon/test/recon/mocks/MockMiddleware.sol:54)
  (0)
call [1mERC1967Proxy::[unknown method](0x18160ddd) [1m(/recon/test/recon/mocks/MockMiddleware.sol:55)
 delegatecall ******************************************::[1mtotalSupply() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (92464000000000000000001999999999028)
  (92464000000000000000001999999999028)
call [1mERC1967Proxy::[unknown method](0xe75179a40000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m(/recon/test/recon/mocks/MockMiddleware.sol:58)
 delegatecall ******************************************::[1mreserve(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (0)
  (0)
call [1mERC1967Proxy::[unknown method](0x01e1d114) [1m(/recon/test/recon/mocks/MockMiddleware.sol:89)
 delegatecall ******************************************::[1mtotalAssets() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (100000000000000000)
  (100000000000000000)
call [1mERC1967Proxy::[unknown method](0x18160ddd) [1m(/recon/test/recon/mocks/MockMiddleware.sol:89)
 delegatecall ******************************************::[1mtotalSupply() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (0)
  (0)
call [1mERC1967Proxy::[unknown method](0x70a082310000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m(/recon/test/recon/mocks/MockMiddleware.sol:89)
 delegatecall ******************************************::[1mbalanceOf([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (91843999999999999900001989999999028)
  (91843999999999999900001989999999028)
call [1mERC1967Proxy::[unknown method](0x2c3ee88c000000000000000000000000000000000011b042417351fd74d8792ff7b43834) [1m(/recon/test/recon/mocks/MockMiddleware.sol:89)
 delegatecall ******************************************::[1mgetRedeemAmount(91843999999999999900001989999999028) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
   delegatecall ******************************************::0x4bfcc81c3b40995b576f8dd0a8521bba471c5346e53f6a25529b0903b82331eb1a2afe00000000000000000000000000000000000011b042417351fd74d8792ff7b43834 [1m<no source map>
     call [1mERC1967Proxy::[unknown method](0x18160ddd) [1m<no source map>
       delegatecall ******************************************::[1mtotalSupply() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (92464000000000000000001999999999028)
        (92464000000000000000001999999999028)
     call [1mERC1967Proxy::[unknown method](0x71a97305) [1m<no source map>
       delegatecall ******************************************::[1massets() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          ([******************************************, ******************************************, ******************************************])
        ([******************************************, ******************************************, ******************************************])
     call [1mERC1967Proxy::[unknown method](0x9782e8210000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m<no source map>
       delegatecall ******************************************::[1mtotalSupplies(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (92464000000000000000002)
        (92464000000000000000002)
     call [1mERC1967Proxy::[unknown method](0x9782e821000000000000000000000000d16d567549a2a2a2005aeacf7fb193851603dd70) [1m<no source map>
       delegatecall ******************************************::[1mtotalSupplies(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (0)
        (0)
     call [1mERC1967Proxy::[unknown method](0x9782e82100000000000000000000000096d3f6c20eed2697647f543fe6c08bc2fbf39758) [1m<no source map>
       delegatecall ******************************************::[1mtotalSupplies(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (0)
        (0)
      ******************************************00000000000000000000004000000000000000000000000000000000000000000000000000000000000000c00000000000000000000000000000000000000000000000000000000000000003000000000000000000000000000000000000000000001372df8b1da2008e7961000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000003000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
    ([91843999999999999900001, 0, 0], [0, 0, 0])
  ([91843999999999999900001, 0, 0], [0, 0, 0])
call [1mERC1967Proxy::[unknown method](0x92e423b50000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m(/recon/test/recon/mocks/MockMiddleware.sol:89)
 delegatecall ******************************************::[1magent([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
   delegatecall ******************************************::0x2a43ad13d6af1ec8a1789f5ada2b972bd1569f7c83af2e268be17cd65efe8474ebf088000000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496 [1m<no source map>
     call [1mERC1967Proxy::[unknown method](0x330244300000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mcoverage([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
         call [1mMockMiddleware::slashableCollateral(address,uint48)([1mCryticTester, 1534982400) [1m<no source map>
           call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
             delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
               call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
                 call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                    (8)
                 call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                    (0, ************, 0, **********, 0)
                  (************, **********)
                (************, **********)
              (************, **********)
           call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
              (18)
            (************00)
         call [1mMockMiddleware::coverage(address)([1mCryticTester) [1m<no source map>
           call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
             delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
               call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
                 call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                    (8)
                 call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                    (0, ************, 0, **********, 0)
                  (************, **********)
                (************, **********)
              (************, **********)
           call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
              (18)
            (************00)
          (************00)
        (************00)
     call [1mERC1967Proxy::[unknown method](0xf01391e30000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mslashableCollateral([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
         call [1mMockMiddleware::slashableCollateral(address,uint48)([1mCryticTester, 1534982400) [1m<no source map>
           call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
             delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
               call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
                 call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                    (8)
                 call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                    (0, ************, 0, **********, 0)
                  (************, **********)
                (************, **********)
              (************, **********)
           call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
              (18)
            (************00)
          (************00)
        (************00)
     call [1mERC1967Proxy::[unknown method](0xb1732b************000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mliquidationThreshold([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (700000000000000000000000000)
        (700000000000000000000000000)
      ******************************************00000000000016bcc41e9000000000000000000000000000000000000000000000000000000016bcc41e9000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000024306c4097859c43c000000ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
    (************00, ************00, 0, 0, 700000000000000000000000000, 115792089237316195423570985008687907853269984665640564039457584007913129639935)
  (************00, ************00, 0, 0, 700000000000000000000000000, 115792089237316195423570985008687907853269984665640564039457584007913129639935)

optimize_max_health_decrease: max value: 9796145786409392

  Call sequence:
    CryticTester.stakedCap_approve(0xf07,115792089237316195423570985008687907853269984665640564039457584007913109639937) from: ****************************************** Time delay: 574632 seconds Block delay: 49025
    CryticTester.switch_asset(212044767462858914926783678947438165064938418098592215469523184700305166611) from: ****************************************** Time delay: 136394 seconds Block delay: 39519
    CryticTester.feeAuction_buy(92652242509499653241450768969330428099610015019579684000640442862674338254586,[0xf07, 0x1fffffffe, 0xffffffff, 0x1fffffffe, 0x94771550282853f6e0124c302f7de1cf50aa45ca, 0xd6bbde9174b1cdaa358d2cf4d57d1a9f7178fbff, 0x1af7f588a501ea2b5bb3feefa744892aa2cf00e6, 0x1fffffffe, 0xffffffff, 0x2fffffffd, 0x92a6649fdcc044da968d94202465578a9371c7b1, 0xffffffff, 0x1fffffffe, 0x2fffffffd, 0x89ca9f4f77b267778eb2ea0ba1beadee8523af36, 0x5615deb798bb3e4dfa0139dfa1b3d433cc23b72f],[**********, 255, 115792089237316195423570985008687907853269984665640564039457584007786771967682],0xffffffff,93879539468895363378602490700177108785076057462777304803290818807386828763502) from: ****************************************** Time delay: 29577 seconds Block delay: 57807
    CryticTester.delegation_setLtvBuffer(49827746443653132539802951233502146851327483214359573697327852414130813135244) from: ****************************************** Time delay: 90029 seconds Block delay: 28494
    CryticTester.oracle_setStaleness(0xffffffff,6888066478505441749850992418581994297142195019068002000074442631546382030335) from: ****************************************** Time delay: 592773 seconds Block delay: 50592
    CryticTester.lender_repay(79134579944527902223956207440668662801201128554431323912964015423061838657580) from: ****************************************** Time delay: 322357 seconds Block delay: 10
    CryticTester.property_repaid_debt_equals_zero_debt() from: ****************************************** Time delay: 156190 seconds Block delay: 14278
    CryticTester.lender_repay(115792089237316195423570985008687907853269984665639664039457584007913129639937) from: ****************************************** Time delay: 395200 seconds Block delay: 48689
    CryticTester.capToken_redeem_clamped(95741646795015930638342469798301058198531925459299825715208509091224771411149) from: ****************************************** Time delay: 288282 seconds Block delay: 32651
    CryticTester.lender_liquidate(96527322811128641503200922976669652610386069949039409468408214856818078802911) from: ****************************************** Time delay: 166184 seconds Block delay: 4924
    CryticTester.asset_mint(0x2a07706473244bc757e10f2a9e86fb532828afe3,1313373041) from: ****************************************** Time delay: 406880 seconds Block delay: 16480
    CryticTester.feeAuction_setMinStartPrice(50567676296237530215709939272598174094397627629202802397599245577743710227668) from: ****************************************** Time delay: 419861 seconds Block delay: 5005
    CryticTester.lender_borrow_clamped(46579599527300147752911888174834388979015831070268901955322967468044978416515) from: ****************************************** Time delay: 3867 seconds Block delay: 25461
    CryticTester.property_borrowed_asset_value() from: ****************************************** Time delay: 222812 seconds Block delay: 50592
    CryticTester.lender_initiateLiquidation() from: ****************************************** Time delay: 322345 seconds Block delay: 41859
    CryticTester.property_total_borrowed_less_than_total_supply() from: ****************************************** Time delay: 322338 seconds Block delay: 4991
    CryticTester.capToken_setFeeData(0x1804c8ab1f12e6bbf3894d4083f33e07309d1f38,(49869528211447337507581, 105595305270768091135739420498098964224445880925025084223878852022216500825372, 40223805042954923313978570538536601647058217615594817495771589330482330191206, 99999999999999999999999999, 115792089237316195423570985008687907853269984665640414039457584007913129639935, 33056249739822063734181)) from: ****************************************** Time delay: 59694 seconds Block delay: 46152
    CryticTester.mockNetworkMiddleware_setMockSlashableCollateralByVault(53502540585222975478199632749217283793864113140416536062295354888833147494184) from: ****************************************** Time delay: 311699 seconds Block delay: 36723
    CryticTester.mockNetworkMiddleware_setMockSlashableCollateralByVault(53502540585222975478199632749217283793864113140416536062295354888833147494184) from: ****************************************** Time delay: 311699 seconds Block delay: 36723
    CryticTester.capToken_mint_clamped(12000000000000000000001) from: ****************************************** Time delay: 352545 seconds Block delay: 43649
    CryticTester.capToken_mint_clamped(12000000000000000000001) from: ****************************************** Time delay: 352545 seconds Block delay: 43649
    CryticTester.stakedCap_deposit(51895644719467792066571894091710869393543355905728171984000194425741445815407,0xf02) from: ****************************************** Time delay: 305996 seconds Block delay: 52772
    CryticTester.lender_realizeRestakerInterest() from: ****************************************** Time delay: 150273 seconds Block delay: 32651
    CryticTester.lender_cancelLiquidation_clamped() from: ****************************************** Time delay: 387502 seconds Block delay: 38369
    CryticTester.oracle_setPriceOracleData(0x1fffffffe,(0xffffffff, "\163\237\199\177")) from: ****************************************** Time delay: 283490 seconds Block delay: 17085
    CryticTester.accessControl_grantRole("\143\160\230\212\233@\171M\142\213\170=\ETB\153\217*\228X\fI\154Vl\243\&5\157/\140\&9\EM\fc",0xffffffff) from: ****************************************** Time delay: 114565 seconds Block delay: 33175
    CryticTester.property_borrowed_asset_value() from: ****************************************** Time delay: 153418 seconds Block delay: 28126
    CryticTester.capToken_investAll() from: ****************************************** Time delay: 455740 seconds Block delay: 51642
    CryticTester.lender_borrow_clamped(115792089237316195423570985008687907853269984665640564039457584007913129639935) from: ****************************************** Time delay: 275394 seconds Block delay: 45626
    CryticTester.accessControl_grantRole("\148\EM0\DC4\201\182\177\216\&07wo\r\209\\\175\254hzE\DLE%\151\b\248T\t\160\246\129\&7\146",0x7fa9385be102ac3eac297483dd6233d62b3e1496) from: ****************************************** Time delay: 322310 seconds Block delay: 9
    CryticTester.mockERC4626Tester_decreaseYield(**********) from: ****************************************** Time delay: 3866 seconds Block delay: 32652
    CryticTester.stakedCap_permit(0x2fffffffd,0x1fffffffe,115792089237316195423570985008687907853269984665640564039457584007910970861364,39999,71,"ChainlinkAdapter\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL","\236\244\182\NUL'[\140\231\143\SOHg}\251@\176\243\232\229h\200\131y\fdy\221\160\212\207\&0\185\137") from: ****************************************** Time delay: 105767 seconds Block delay: 23640
    CryticTester.property_ltv() from: ****************************************** Time delay: 322347 seconds Block delay: 59502
    CryticTester.stakedCap_withdraw(115792089237316195423570985008687907853269984665640564039457584007913029639935,0x886d6d1eb8d415b00052828cd6d5b321f072073d,0x1fffffffe) from: ****************************************** Time delay: 390587 seconds Block delay: 5053
    CryticTester.property_repaid_debt_equals_zero_debt() from: ****************************************** Time delay: 38885 seconds Block delay: 16063
    CryticTester.lender_liquidate(115792089237316195423570985008687907853269984665640564039457584007910970861362) from: ****************************************** Time delay: 23094 seconds Block delay: 101
    CryticTester.capToken_approve(0xf02,50186849216440882834365773503793987581223009780705702046838983657059356594816) from: ****************************************** Time delay: 222375 seconds Block delay: 23167
    CryticTester.feeAuction_setMinStartPrice(3210730645604) from: ****************************************** Time delay: 361136 seconds Block delay: 81
    CryticTester.doomsday_liquidate(77784623682452896297276757612921833371589091981796380949434520261093249435818) from: ****************************************** Time delay: 208886 seconds Block delay: 12232
    CryticTester.capToken_realizeInterest(0xffffffff) from: ****************************************** Time delay: 270654 seconds Block delay: 50312
    CryticTester.switchDebtToken(82299135160964070076846021324374906854627379199758588254364202784130756958275) from: ****************************************** Time delay: 31594 seconds Block delay: 13782
    CryticTester.lender_borrow_clamped(115792089237316195423570985008687907853269984665640564039457584007913129639935) from: ****************************************** Time delay: 216610 seconds Block delay: 26048
    CryticTester.capToken_transferFrom(0x2fffffffd,0xc7183455a4c133ae270771860664b6b7ec320bb1,109753202256784357702607674318137699895751128515144968268951822493682106180631) from: ****************************************** Time delay: 322357 seconds Block delay: 5020
    CryticTester.property_sum_of_unrealized_interest() from: ****************************************** Time delay: 322373 seconds Block delay: 32551
    CryticTester.mockERC4626Tester_setDecimalsOffset(5) from: ****************************************** Time delay: 331505 seconds Block delay: 9687
    CryticTester.accessControl_revokeRole("Test Token\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL",0x2fffffffd) from: ****************************************** Time delay: 350070 seconds Block delay: 53562
    CryticTester.capToken_unpauseProtocol() from: ****************************************** Time delay: 283488 seconds Block delay: 4953
    CryticTester.mockERC4626Tester_transfer(0x2fffffffd,290) from: ****************************************** Time delay: 73041 seconds Block delay: 14020
    CryticTester.accessControl_renounceRole("\EM\SOH\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL",0x3cff5e7ebecb676c3cb602d0ef2d46710b88854e) from: ****************************************** Time delay: 140902 seconds Block delay: 10001
    CryticTester.switchAaveOracle(75582292690828949683231465870923526644620298948961349679535477177936890517430) from: ****************************************** Time delay: 318509 seconds Block delay: 38370
    CryticTester.capToken_realizeInterest(0x1fffffffe) from: ****************************************** Time delay: 387502 seconds Block delay: 28031
    CryticTester.capToken_transfer(0x2fffffffd,257) from: ****************************************** Time delay: 135364 seconds Block delay: 49820
    CryticTester.property_ltv() from: ****************************************** Time delay: 31594 seconds Block delay: 253
    CryticTester.capToken_transferFrom(0xf0c,0x2fffffffd,62031852822804768606857136463745591661113956147321267829981804459965119870318) from: ****************************************** Time delay: 181471 seconds Block delay: 27136
    CryticTester.delegation_registerNetwork(0xffffffff) from: ****************************************** Time delay: 547689 seconds Block delay: 4989
    CryticTester.capToken_burn_clamped(2366588) from: ****************************************** Time delay: 199287 seconds Block delay: 35429
    CryticTester.capToken_setReserve(10211146) from: ****************************************** Time delay: 347391 seconds Block delay: 19329
    CryticTester.stakedCap_transfer(0x1fffffffe,22819595148480029428855420776828552955534733005086817128369931298986036538352) from: ****************************************** Time delay: 4 seconds Block delay: 780
    CryticTester.lender_borrow_clamped(9999) from: ****************************************** Time delay: 322339 seconds Block delay: 60055
    CryticTester.capToken_realizeInterest(0x2fffffffd) from: ****************************************** Time delay: 434894 seconds Block delay: 8781
    CryticTester.capToken_investAll() from: ****************************************** Time delay: 172101 seconds Block delay: 29505
    CryticTester.lender_repay(27) from: ****************************************** Time delay: 65534 seconds Block delay: 11036
    CryticTester.oracle_setMarketOracleData(0x1fffffffe,(0x2fffffffd, "\219\221KQ\162\DC3H\DC2")) from: ****************************************** Time delay: 33271 seconds Block delay: 33
    CryticTester.capToken_unpauseProtocol() from: ****************************************** Time delay: 195400 seconds Block delay: 51485
    CryticTester.feeAuction_setDuration(21445290057162079105033759873766799800133526494718495041771069284180491617224) from: ****************************************** Time delay: 135485 seconds Block delay: 58182
    CryticTester.accessControl_revokeRole("cUSD\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\215",0xffffffff) from: ****************************************** Time delay: 249336 seconds Block delay: 50592
    CryticTester.lender_initiateLiquidation_clamped() from: ****************************************** Time delay: 59696 seconds Block delay: 23169
    CryticTester.switchDebtToken(11) from: ****************************************** Time delay: 210608 seconds Block delay: 13782
    CryticTester.property_vault_solvency_borrows() from: ****************************************** Time delay: 322275 seconds Block delay: 16480
    CryticTester.capToken_divestAll() from: ******************************************
    CryticTester.stakedCap_approve(0x5991a2df15a8f6a256d3ec51e99254cd3fb576a9,115792089237316195423570985008687907853269984665640564039457584007912725541410) from: ****************************************** Time delay: 38059 seconds Block delay: 51009
    CryticTester.capToken_pauseAsset(0x1d1499e622d69689cdf9004d05ec547d650ff211) from: ****************************************** Time delay: 160323 seconds Block delay: 16816
    CryticTester.accessControl_revokeRole("\144P\149\202N#+\241fF\225\196\250\182\240\EMR\160\235\224\158\GS\157\148\231Y{\141N\158\SO.",0x3381cd18e2fb4db236bf0525938ab6e43db0440f) from: ****************************************** Time delay: 47900 seconds Block delay: 23978
    CryticTester.capToken_realizeInterest(0x2fffffffd) from: ****************************************** Time delay: 322365 seconds Block delay: 46605
    CryticTester.stakedCap_transfer(0x1fffffffe,115792089237316195423570985008687907853269984665640564039457584007913129639872) from: ****************************************** Time delay: 318510 seconds Block delay: 32330
    CryticTester.mockERC4626Tester_mintUnbackedShares(115792089237316195423570985008687907853269984665640564039457584007913129639931,0x15cf58144ef33af1e14b5208015d11f9143e27b9) from: ****************************************** Time delay: 3601 seconds Block delay: 42338
    CryticTester.oracle_setRestakerRate(0x7fa9385be102ac3eac297483dd6233d62b3e1496,52359581941030304095795513635831085096219002025) from: ****************************************** Time delay: 259406 seconds Block delay: 3600
    CryticTester.property_health_should_not_change_when_realizeRestakerInterest_is_called() from: ****************************************** Time delay: 85184 seconds Block delay: 8783
    CryticTester.lender_removeAsset(0xffffffff) from: ****************************************** Time delay: 111765 seconds Block delay: 16444
    CryticTester.lender_addAsset(******************************************,0x2fffffffd,0xf03,0xffffffff,115792089237316195423570985008687907853269984665640564039457584007913129622070,33230864123914941547021397277882961735642603000778060860774395275857743132686) from: ****************************************** Time delay: 150273 seconds Block delay: 47640
    CryticTester.lender_realizeRestakerInterest() from: ****************************************** Time delay: 4177 seconds Block delay: 25461

Traces:
call [1mERC1967Proxy::[unknown method](0x08cf6fec0000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m(/recon/test/recon/mocks/MockMiddleware.sol:71)
 delegatecall ******************************************::[1mreservesData(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (0, [1mERC1967Proxy, [1mERC1967Proxy, [1mERC1967Proxy, 6, false, 100000000)
  (0, [1mERC1967Proxy, [1mERC1967Proxy, [1mERC1967Proxy, 6, false, 100000000)
call [1mERC1967Proxy::[unknown method](0x70a082310000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m(/recon/test/recon/mocks/MockMiddleware.sol:73)
 delegatecall ******************************************::[1mbalanceOf([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (128557552372)
  (128557552372)
call [1mERC1967Proxy::[unknown method](0x71a97305) [1m(/recon/test/recon/mocks/MockMiddleware.sol:77)
 delegatecall ******************************************::[1massets() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    ([******************************************, ******************************************, ******************************************])
  ([******************************************, ******************************************, ******************************************])
call [1mERC1967Proxy::[unknown method](0xd8cb4aa30000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m(/recon/test/recon/mocks/MockMiddleware.sol:89)
 delegatecall ******************************************::[1mbonus([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
   call [1mERC1967Proxy::[unknown method](0x330244300000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
     delegatecall ******************************************::[1mcoverage([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
       call [1mMockMiddleware::slashableCollateral(address,uint48)([1mCryticTester, 1535414400) [1m<no source map>
         call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
           delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
             call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
               call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                  (8)
               call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                  (0, ************, 0, **********, 0)
                (************, **********)
              (************, **********)
            (************, **********)
         call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
            (18)
          (************00)
       call [1mMockMiddleware::coverage(address)([1mCryticTester) [1m<no source map>
         call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
           delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
             call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
               call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                  (8)
               call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                  (0, ************, 0, **********, 0)
                (************, **********)
              (************, **********)
            (************, **********)
         call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
            (18)
          (************00)
        (************00)
      (************00)
   call [1mERC1967Proxy::[unknown method](0xf01391e30000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
     delegatecall ******************************************::[1mslashableCollateral([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
       call [1mMockMiddleware::slashableCollateral(address,uint48)([1mCryticTester, 1535414400) [1m<no source map>
         call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
           delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
             call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
               call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                  (8)
               call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                  (0, ************, 0, **********, 0)
                (************, **********)
              (************, **********)
            (************, **********)
         call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
            (18)
          (************00)
        (************00)
      (************00)
   call [1mERC1967Proxy::[unknown method](0xb1732b************000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
     delegatecall ******************************************::[1mliquidationThreshold([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
        (700000000000000000000000000)
      (700000000000000000000000000)
   call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m<no source map>
     delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
       call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
         call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
            (8)
         call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
            (0, 100000000, 0, **********, 0)
          (100000000, **********)
        (100000000, **********)
      (100000000, **********)
   call [1mERC1967Proxy::[unknown method](0x70a082310000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
     delegatecall ******************************************::[1mbalanceOf([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
        (128557552372)
      (128557552372)
   call [1mERC1967Proxy::[unknown method](0x98db1b080000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
     delegatecall ******************************************::[1mrestakerRate([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
        (795513583471503155188697930)
      (795513583471503155188697930)
   call [1mERC1967Proxy::[unknown method](0x70a082310000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
     delegatecall ******************************************::[1mbalanceOf([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
        (128557552372)
      (128557552372)
    (100000000000000000000000000)
  (100000000000000000000000000)
call [1mERC1967Proxy::[unknown method](0xfeee17560000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m(/recon/test/recon/mocks/MockMiddleware.sol:47)
 delegatecall ******************************************::[1mcurrentUtilizationIndex(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
   delegatecall ******************************************::0x3c054127e912a1b0cc7579bc5827e495c2ce52587bc3871751e3281fc5599b38c3bfc4000000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7 [1m<no source map>
      ******************************************00000681d1bf16b441383591
    (30728942706564326241681)
  (30728942706564326241681)
call [1mERC1967Proxy::[unknown method](0xbf20d9dc0000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m(/recon/test/recon/mocks/MockMiddleware.sol:49)
 delegatecall ******************************************::[1mutilization(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
   delegatecall ******************************************::0x0f82ffd0e912a1b0cc7579bc5827e495c2ce52587bc3871751e3281fc5599b38c3bfc4000000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7 [1m<no source map>
      ******************************************00000000001280f39a235acd
    (5208333332208333)
  (5208333332208333)
call [1mERC1967Proxy::[unknown method](0x8d7301240000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m(/recon/test/recon/mocks/MockMiddleware.sol:52)
 delegatecall ******************************************::[1mtotalBorrows(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (124999999973)
  (124999999973)
call [1mERC1967Proxy::[unknown method](0xc0af0d3b0000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m(/recon/test/recon/mocks/MockMiddleware.sol:53)
 delegatecall ******************************************::[1mgetVaultDebt(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (124999999973)
  (124999999973)
call [1mERC1967Proxy::[unknown method](0xb7902303) [1m(/recon/test/recon/mocks/MockMiddleware.sol:55)
 delegatecall ******************************************::[1minsuranceFund() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (******************************************)
  (******************************************)
call ******************************************::[1mbalanceOf(******************************************) [1m(/recon/test/recon/mocks/MockMiddleware.sol:54)
  (0)
call [1mERC1967Proxy::[unknown method](0x18160ddd) [1m(/recon/test/recon/mocks/MockMiddleware.sol:55)
 delegatecall ******************************************::[1mtotalSupply() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (24000000000000000000002000000000000)
  (24000000000000000000002000000000000)
call [1mERC1967Proxy::[unknown method](0xe75179a40000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m(/recon/test/recon/mocks/MockMiddleware.sol:58)
 delegatecall ******************************************::[1mreserve(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (10211146)
  (10211146)
call [1mERC1967Proxy::[unknown method](0x01e1d114) [1m(/recon/test/recon/mocks/MockMiddleware.sol:89)
 delegatecall ******************************************::[1mtotalAssets() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (0)
  (0)
call [1mERC1967Proxy::[unknown method](0x18160ddd) [1m(/recon/test/recon/mocks/MockMiddleware.sol:89)
 delegatecall ******************************************::[1mtotalSupply() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (0)
  (0)
call [1mERC1967Proxy::[unknown method](0x70a082310000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m(/recon/test/recon/mocks/MockMiddleware.sol:89)
 delegatecall ******************************************::[1mbalanceOf([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (23880000000000000000001989999999743)
  (23880000000000000000001989999999743)
call [1mERC1967Proxy::[unknown method](0x2c3ee88c000000000000000000000000000000000004995ff983c42c4b5104f1553e3aff) [1m(/recon/test/recon/mocks/MockMiddleware.sol:89)
 delegatecall ******************************************::[1mgetRedeemAmount(23880000000000000000001989999999743) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
   delegatecall ******************************************::0x4bfcc81c3b40995b576f8dd0a8521bba471c5346e53f6a25529b0903b82331eb1a2afe00000000000000000000000000000000000004995ff983c42c4b5104f1553e3aff [1m<no source map>
     call [1mERC1967Proxy::[unknown method](0x18160ddd) [1m<no source map>
       delegatecall ******************************************::[1mtotalSupply() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (24000000000000000000002000000000000)
        (24000000000000000000002000000000000)
     call [1mERC1967Proxy::[unknown method](0x71a97305) [1m<no source map>
       delegatecall ******************************************::[1massets() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          ([******************************************, ******************************************, ******************************************])
        ([******************************************, ******************************************, ******************************************])
     call [1mERC1967Proxy::[unknown method](0x9782e8210000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m<no source map>
       delegatecall ******************************************::[1mtotalSupplies(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (24000000000000000000002)
        (24000000000000000000002)
     call [1mERC1967Proxy::[unknown method](0x9782e821000000000000000000000000d16d567549a2a2a2005aeacf7fb193851603dd70) [1m<no source map>
       delegatecall ******************************************::[1mtotalSupplies(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (0)
        (0)
     call [1mERC1967Proxy::[unknown method](0x9782e82100000000000000000000000096d3f6c20eed2697647f543fe6c08bc2fbf39758) [1m<no source map>
       delegatecall ******************************************::[1mtotalSupplies(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (0)
        (0)
      ******************************************00000000000000000000004000000000000000000000000000000000000000000000000000000000000000c0000000000000000000000000000000000000000000000000000000000000000300000000000000000000000000000000000000000000050e8992a65668200001000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000003000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
    ([23880000000000000000001, 0, 0], [0, 0, 0])
  ([23880000000000000000001, 0, 0], [0, 0, 0])
call [1mERC1967Proxy::[unknown method](0x92e423b50000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m(/recon/test/recon/mocks/MockMiddleware.sol:89)
 delegatecall ******************************************::[1magent([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
   delegatecall ******************************************::0x2a43ad13d6af1ec8a1789f5ada2b972bd1569f7c83af2e268be17cd65efe8474ebf088000000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496 [1m<no source map>
     call [1mERC1967Proxy::[unknown method](0x330244300000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mcoverage([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
         call [1mMockMiddleware::slashableCollateral(address,uint48)([1mCryticTester, 1535414400) [1m<no source map>
           call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
             delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
               call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
                 call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                    (8)
                 call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                    (0, ************, 0, **********, 0)
                  (************, **********)
                (************, **********)
              (************, **********)
           call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
              (18)
            (************00)
         call [1mMockMiddleware::coverage(address)([1mCryticTester) [1m<no source map>
           call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
             delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
               call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
                 call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                    (8)
                 call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                    (0, ************, 0, **********, 0)
                  (************, **********)
                (************, **********)
              (************, **********)
           call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
              (18)
            (************00)
          (************00)
        (************00)
     call [1mERC1967Proxy::[unknown method](0xf01391e30000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mslashableCollateral([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
         call [1mMockMiddleware::slashableCollateral(address,uint48)([1mCryticTester, 1535414400) [1m<no source map>
           call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
             delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
               call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
                 call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                    (8)
                 call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                    (0, ************, 0, **********, 0)
                  (************, **********)
                (************, **********)
              (************, **********)
           call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
              (18)
            (************00)
          (************00)
        (************00)
     call [1mERC1967Proxy::[unknown method](0xb1732b************000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mliquidationThreshold([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (700000000000000000000000000)
        (700000000000000000000000000)
     call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m<no source map>
       delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
         call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
           call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
              (8)
           call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
              (0, 100000000, 0, **********, 0)
            (100000000, **********)
          (100000000, **********)
        (100000000, **********)
     call [1mERC1967Proxy::[unknown method](0x70a082310000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mbalanceOf([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (128557552372)
        (128557552372)
     call [1mERC1967Proxy::[unknown method](0x98db1b080000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mrestakerRate([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (795513583471503155188697930)
        (795513583471503155188697930)
     call [1mERC1967Proxy::[unknown method](0x70a082310000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mbalanceOf([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (128557552372)
        (128557552372)
      ******************************************00000000000016bcc41e9000000000000000000000000000000000000000000000000000000016bcc41e900000000000000000000000000000000000000000000000000000000c27f13e02f4000000000000000000000000000000000000000001ba3bcda33890a9aa0200000000000000000000000000000000000000000000024306c4097859c43c0000000000000000000000000000000000000000000000043b0bd5182f980b8f0cc9d4
    (************00, ************00, 13365690630900, 534627625236000000000000000, 700000000000000000000000000, 1309322539573221418666346964)
  (************00, ************00, 13365690630900, 534627625236000000000000000, 700000000000000000000000000, 1309322539573221418666346964)
call [1mHEVM::[1mprank([1mCryticTester) [1m(/recon/test/recon/targets/DebtTokenTargets.sol:30)
  0x
call [1mERC1967Proxy::[unknown method](0xc0af0d3b0000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m(/recon/test/recon-fee-auction/CryticToFoundry.sol:22)
 delegatecall ******************************************::[1mgetVaultDebt(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (124999999973)
  (124999999973)
call ******************************************::[1mbalanceOf([1mERC1967Proxy) [1m(/recon/test/recon-fee-auction/CryticToFoundry.sol:22)
  (23999999999875000000029)
call [1mERC1967Proxy::[unknown method](0x92e423b50000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m(/recon/test/recon/mocks/MockMiddleware.sol:89)
 delegatecall ******************************************::[1magent([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
   delegatecall ******************************************::0x2a43ad13d6af1ec8a1789f5ada2b972bd1569f7c83af2e268be17cd65efe8474ebf088000000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496 [1m<no source map>
     call [1mERC1967Proxy::[unknown method](0x330244300000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mcoverage([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
         call [1mMockMiddleware::slashableCollateral(address,uint48)([1mCryticTester, 1535414400) [1m<no source map>
           call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
             delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
               call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
                 call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                    (8)
                 call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                    (0, ************, 0, **********, 0)
                  (************, **********)
                (************, **********)
              (************, **********)
           call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
              (18)
            (************00)
         call [1mMockMiddleware::coverage(address)([1mCryticTester) [1m<no source map>
           call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
             delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
               call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
                 call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                    (8)
                 call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                    (0, ************, 0, **********, 0)
                  (************, **********)
                (************, **********)
              (************, **********)
           call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
              (18)
            (************00)
          (************00)
        (************00)
     call [1mERC1967Proxy::[unknown method](0xf01391e30000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mslashableCollateral([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
         call [1mMockMiddleware::slashableCollateral(address,uint48)([1mCryticTester, 1535414400) [1m<no source map>
           call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
             delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
               call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
                 call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                    (8)
                 call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                    (0, ************, 0, **********, 0)
                  (************, **********)
                (************, **********)
              (************, **********)
           call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
              (18)
            (************00)
          (************00)
        (************00)
     call [1mERC1967Proxy::[unknown method](0xb1732b************000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mliquidationThreshold([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (700000000000000000000000000)
        (700000000000000000000000000)
     call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m<no source map>
       delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
         call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
           call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
              (8)
           call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
              (0, 100000000, 0, **********, 0)
            (100000000, **********)
          (100000000, **********)
        (100000000, **********)
     call [1mERC1967Proxy::[unknown method](0x70a082310000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mbalanceOf([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (128557552372)
        (128557552372)
     call [1mERC1967Proxy::[unknown method](0x98db1b080000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mrestakerRate([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (795513583471503155188697930)
        (795513583471503155188697930)
     call [1mERC1967Proxy::[unknown method](0x70a082310000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mbalanceOf([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (128557552372)
        (128557552372)
      ******************************************00000000000016bcc41e9000000000000000000000000000000000000000000000000000000016bcc41e900000000000000000000000000000000000000000000000000000000c27f13e02f4000000000000000000000000000000000000000001ba3bcda33890a9aa0200000000000000000000000000000000000000000000024306c4097859c43c0000000000000000000000000000000000000000000000043b0bd5182f980b8f0cc9d4
    (************00, ************00, 13365690630900, 534627625236000000000000000, 700000000000000000000000000, 1309322539573221418666346964)
  (************00, ************00, 13365690630900, 534627625236000000000000000, 700000000000000000000000000, 1309322539573221418666346964)
call [1mERC1967Proxy::[unknown method](0x8d7301240000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m(/recon/test/recon-fee-auction/CryticToFoundry.sol:22)
 delegatecall ******************************************::[1mtotalBorrows(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (124999999973)
  (124999999973)
call [1mERC1967Proxy::[unknown method](0x9782e8210000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m(/recon/test/recon-fee-auction/CryticToFoundry.sol:22)
 delegatecall ******************************************::[1mtotalSupplies(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (24000000000000000000002)
  (24000000000000000000002)
call [1mERC1967Proxy::[unknown method](0x0a8d01da0000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e14960000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m(/recon/test/recon-fee-auction/CryticToFoundry.sol:22)
 delegatecall ******************************************::[1mrealizeRestakerInterest([1mCryticTester, ******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
   delegatecall ******************************************::0x6d4323a8d6af1ec8a1789f5ada2b972bd1569f7c83af2e268be17cd65efe8474ebf088000000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e14960000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7 [1m<no source map>
     delegatecall ******************************************::0x5afe0823d6af1ec8a1789f5ada2b972bd1569f7c83af2e268be17cd65efe8474ebf088000000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e14960000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7 [1m<no source map>
       call [1mERC1967Proxy::[unknown method](0x70a082310000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
         delegatecall ******************************************::[1mbalanceOf([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
            (128557552372)
          (128557552372)
       call [1mERC1967Proxy::[unknown method](0x98db1b080000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
         delegatecall ******************************************::[1mrestakerRate([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
            (795513583471503155188697930)
          (795513583471503155188697930)
        ******************************************00000000000000012ff1f751
     call [1mERC1967Proxy::[unknown method](0xa0821be30000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m<no source map>
       delegatecall ******************************************::[1mavailableBalance(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
         delegatecall ******************************************::0x50ba5827e912a1b0cc7579bc5827e495c2ce52587bc3871751e3281fc5599b38c3bfc4000000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7 [1m<no source map>
            ******************************************000005150ae84a6fc46b5e1d
          (23999999999875000000029)
        (23999999999875000000029)
     call [1mERC1967Proxy::[unknown method](0x40c10f190000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496000000000000000000000000000000000000000000000000000000012ff1f751) [1m<no source map>
       delegatecall ******************************************::[1mmint([1mCryticTester, 5099353937) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
         call [1mERC1967Proxy::[unknown method](0x2daff25c0000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m<no source map>
           delegatecall ******************************************::[1mmarketRate(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
             call ******************************************::[1mrate([1mMockAaveDataProvider, ******************************************) [1m<no source map>
               call [1mMockAaveDataProvider::getReserveData(address)(******************************************) [1m<no source map>
                  (0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, **********)
                (0)
              (0)
            (0)
         call [1mERC1967Proxy::[unknown method](0x58d7849a0000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m<no source map>
           delegatecall ******************************************::[1mbenchmarkRate(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
              (150000000000000000000000000)
            (150000000000000000000000000)
         call [1mERC1967Proxy::[unknown method](0x207b27df0000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m<no source map>
           delegatecall ******************************************::[1mutilizationRate(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
             call ******************************************::[1mrate([1mMockAaveDataProvider, ******************************************) [1m<no source map>
               call [1mMockAaveDataProvider::getReserveData(address)(******************************************) [1m<no source map>
                  (0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, **********)
                (0)
              (0)
            (0)
         call [1mERC1967Proxy::[unknown method](0xd95d920c40c10f190000000000000000000000000000000000000000000000000000000000000000000000000000000089ca9f4f77b267778eb2ea0ba1beadee8523af3600000000000000000000000015cf58144ef33af1e14b5208015d11f9143e27b9) [1m<no source map>
           delegatecall ******************************************::[1mcheckAccess(0x40c10f19, [1mERC1967Proxy, [1mERC1967Proxy) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
              (true)
            (true)
         emit Transfer(from=******************************************, to=[1mCryticTester, value=**********) [1m<no source map>
          0x
        0x
     call [1mERC1967Proxy::[unknown method](0x6c665a550000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7000000000000000000000000000000000000000000000000000000012ff1f7510000000000000000000000002a07706473244bc757e10f2a9e86fb532828afe3) [1m<no source map>
       delegatecall ******************************************::[1mborrow(******************************************, 5099353937, [1mERC1967Proxy) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
         call [1mERC1967Proxy::[unknown method](0xd95d920c6c665a550000000000000000000000000000000000000000000000000000000000000000000000000000000092a6649fdcc044da968d94202465578a9371c7b100000000000000000000000015cf58144ef33af1e14b5208015d11f9143e27b9) [1m<no source map>
           delegatecall ******************************************::[1mcheckAccess(lfZU, [1mERC1967Proxy, [1mERC1967Proxy) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
              (true)
            (true)
         delegatecall ******************************************::0x53c655a85c48f30a22a9811126b69b5adcaabfc5ae0a83b6493e1b31e09dc579923ad1000000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7000000000000000000000000000000000000000000000000000000012ff1f751 [1m<no source map>
            0x
         delegatecall ******************************************::0xebd0396de912a1b0cc7579bc5827e495c2ce52587bc3871751e3281fc5599b38c3bfc4000000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7000000000000000000000000000000000000000000000000000000012ff1f7510000000000000000000000002a07706473244bc757e10f2a9e86fb532828afe3 [1m<no source map>
           call ******************************************::[1mtransfer([1mERC1967Proxy, 5099353937) [1m<no source map>
             emit Transfer(from=[1mERC1967Proxy, to=[1mERC1967Proxy, value=5099353937) [1m<no source map>
              (true)
           emit Borrow(asset=[1mERC1967Proxy, agent=******************************************, amount=5099353937) [1m<no source map>
            0x
          0x
        0x
     call [1mERC1967Proxy::[unknown method](0x3d11c00d0000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e14960000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m<no source map>
       delegatecall ******************************************::[1mdistributeRewards([1mCryticTester, ******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
         call ******************************************::[1mbalanceOf([1mERC1967Proxy) [1m<no source map>
            (6412726978)
         call [1mMockMiddleware::slashableCollateral(address,uint48)([1mCryticTester, 1535414400) [1m<no source map>
           call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
             delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
               call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
                 call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                    (8)
                 call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                    (0, ************, 0, **********, 0)
                  (************, **********)
                (************, **********)
              (************, **********)
           call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
              (18)
            (************00)
         call [1mMockMiddleware::coverage(address)([1mCryticTester) [1m<no source map>
           call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
             delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
               call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
                 call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                    (8)
                 call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                    (0, ************, 0, **********, 0)
                  (************, **********)
                (************, **********)
              (************, **********)
           call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
              (18)
            (************00)
         call ******************************************::[1mtransfer([1mMockMiddleware, 6412726978) [1m<no source map>
           emit Transfer(from=[1mERC1967Proxy, to=[1mMockMiddleware, value=6412726978) [1m<no source map>
            (true)
         call [1mMockMiddleware::distributeRewards(address,address)([1mCryticTester, ******************************************) [1m<no source map>
            0x
         emit DistributeReward(agent=[1mCryticTester, asset=******************************************, amount=6412726978) [1m<no source map>
          0x
        0x
     emit RealizeInterest(asset=******************************************, realizedInterest=5099353937, interestReceiver=[1mERC1967Proxy) [1m<no source map>
      ******************************************00000000000000012ff1f751
    (5099353937)
  (5099353937)
call [1mERC1967Proxy::[unknown method](0xc0af0d3b0000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m(/recon/test/recon-fee-auction/CryticToFoundry.sol:22)
 delegatecall ******************************************::[1mgetVaultDebt(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (130099353910)
  (130099353910)
call ******************************************::[1mbalanceOf([1mERC1967Proxy) [1m(/recon/test/recon-fee-auction/CryticToFoundry.sol:22)
  (23999999999869900646092)
call [1mERC1967Proxy::[unknown method](0x92e423b50000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m(/recon/test/recon/mocks/MockMiddleware.sol:89)
 delegatecall ******************************************::[1magent([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
   delegatecall ******************************************::0x2a43ad13d6af1ec8a1789f5ada2b972bd1569f7c83af2e268be17cd65efe8474ebf088000000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496 [1m<no source map>
     call [1mERC1967Proxy::[unknown method](0x330244300000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mcoverage([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
         call [1mMockMiddleware::slashableCollateral(address,uint48)([1mCryticTester, 1535414400) [1m<no source map>
           call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
             delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
               call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
                 call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                    (8)
                 call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                    (0, ************, 0, **********, 0)
                  (************, **********)
                (************, **********)
              (************, **********)
           call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
              (18)
            (************00)
         call [1mMockMiddleware::coverage(address)([1mCryticTester) [1m<no source map>
           call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
             delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
               call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
                 call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                    (8)
                 call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                    (0, ************, 0, **********, 0)
                  (************, **********)
                (************, **********)
              (************, **********)
           call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
              (18)
            (************00)
          (************00)
        (************00)
     call [1mERC1967Proxy::[unknown method](0xf01391e30000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mslashableCollateral([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
         call [1mMockMiddleware::slashableCollateral(address,uint48)([1mCryticTester, 1535414400) [1m<no source map>
           call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
             delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
               call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
                 call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                    (8)
                 call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                    (0, ************, 0, **********, 0)
                  (************, **********)
                (************, **********)
              (************, **********)
           call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
              (18)
            (************00)
          (************00)
        (************00)
     call [1mERC1967Proxy::[unknown method](0xb1732b************000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mliquidationThreshold([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (700000000000000000000000000)
        (700000000000000000000000000)
     call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m<no source map>
       delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
         call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
           call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
              (8)
           call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
              (0, 100000000, 0, **********, 0)
            (100000000, **********)
          (100000000, **********)
        (100000000, **********)
     call [1mERC1967Proxy::[unknown method](0x70a082310000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mbalanceOf([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (133656906310)
        (133656906310)
     call [1mERC1967Proxy::[unknown method](0x98db1b080000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mrestakerRate([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (795513583471503155188697930)
        (795513583471503155188697930)
     call [1mERC1967Proxy::[unknown method](0x70a082310000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mbalanceOf([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (133656906310)
        (133656906310)
      ******************************************00000000000016bcc41e9000000000000000000000000000000000000000000000000000000016bcc41e900000000000000000000000000000000000000000000000000000000c27f13e0358000000000000000000000000000000000000000001ba3bcda346c6a43d1c00000000000000000000000000000000000000000000024306c4097859c43c0000000000000000000000000000000000000000000000043b0bd5180cca80a1487824
    (************00, ************00, 13365690631000, 534627625240000000000000000, 700000000000000000000000000, 1309322539563425272879937572)
  (************00, ************00, 13365690631000, 534627625240000000000000000, 700000000000000000000000000, 1309322539563425272879937572)
call [1mERC1967Proxy::[unknown method](0x8d7301240000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m(/recon/test/recon-fee-auction/CryticToFoundry.sol:22)
 delegatecall ******************************************::[1mtotalBorrows(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (130099353910)
  (130099353910)
call [1mERC1967Proxy::[unknown method](0x08cf6fec0000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m(/recon/test/recon/mocks/MockMiddleware.sol:71)
 delegatecall ******************************************::[1mreservesData(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (0, [1mERC1967Proxy, [1mERC1967Proxy, [1mERC1967Proxy, 6, false, 100000000)
  (0, [1mERC1967Proxy, [1mERC1967Proxy, [1mERC1967Proxy, 6, false, 100000000)
call [1mERC1967Proxy::[unknown method](0x70a082310000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m(/recon/test/recon/mocks/MockMiddleware.sol:73)
 delegatecall ******************************************::[1mbalanceOf([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (133656906310)
  (133656906310)
call [1mERC1967Proxy::[unknown method](0x71a97305) [1m(/recon/test/recon/mocks/MockMiddleware.sol:77)
 delegatecall ******************************************::[1massets() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    ([******************************************, ******************************************, ******************************************])
  ([******************************************, ******************************************, ******************************************])
call [1mERC1967Proxy::[unknown method](0xd8cb4aa30000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m(/recon/test/recon/mocks/MockMiddleware.sol:89)
 delegatecall ******************************************::[1mbonus([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
   call [1mERC1967Proxy::[unknown method](0x330244300000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
     delegatecall ******************************************::[1mcoverage([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
       call [1mMockMiddleware::slashableCollateral(address,uint48)([1mCryticTester, 1535414400) [1m<no source map>
         call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
           delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
             call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
               call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                  (8)
               call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                  (0, ************, 0, **********, 0)
                (************, **********)
              (************, **********)
            (************, **********)
         call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
            (18)
          (************00)
       call [1mMockMiddleware::coverage(address)([1mCryticTester) [1m<no source map>
         call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
           delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
             call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
               call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                  (8)
               call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                  (0, ************, 0, **********, 0)
                (************, **********)
              (************, **********)
            (************, **********)
         call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
            (18)
          (************00)
        (************00)
      (************00)
   call [1mERC1967Proxy::[unknown method](0xf01391e30000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
     delegatecall ******************************************::[1mslashableCollateral([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
       call [1mMockMiddleware::slashableCollateral(address,uint48)([1mCryticTester, 1535414400) [1m<no source map>
         call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
           delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
             call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
               call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                  (8)
               call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                  (0, ************, 0, **********, 0)
                (************, **********)
              (************, **********)
            (************, **********)
         call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
            (18)
          (************00)
        (************00)
      (************00)
   call [1mERC1967Proxy::[unknown method](0xb1732b************000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
     delegatecall ******************************************::[1mliquidationThreshold([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
        (700000000000000000000000000)
      (700000000000000000000000000)
   call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m<no source map>
     delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
       call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
         call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
            (8)
         call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
            (0, 100000000, 0, **********, 0)
          (100000000, **********)
        (100000000, **********)
      (100000000, **********)
   call [1mERC1967Proxy::[unknown method](0x70a082310000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
     delegatecall ******************************************::[1mbalanceOf([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
        (133656906310)
      (133656906310)
   call [1mERC1967Proxy::[unknown method](0x98db1b080000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
     delegatecall ******************************************::[1mrestakerRate([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
        (795513583471503155188697930)
      (795513583471503155188697930)
   call [1mERC1967Proxy::[unknown method](0x70a082310000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
     delegatecall ******************************************::[1mbalanceOf([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
        (133656906310)
      (133656906310)
    (100000000000000000000000000)
  (100000000000000000000000000)
call [1mERC1967Proxy::[unknown method](0xfeee17560000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m(/recon/test/recon/mocks/MockMiddleware.sol:47)
 delegatecall ******************************************::[1mcurrentUtilizationIndex(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
   delegatecall ******************************************::0x3c054127e912a1b0cc7579bc5827e495c2ce52587bc3871751e3281fc5599b38c3bfc4000000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7 [1m<no source map>
      ******************************************00000681d1bf16b441383591
    (30728942706564326241681)
  (30728942706564326241681)
call [1mERC1967Proxy::[unknown method](0xbf20d9dc0000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m(/recon/test/recon/mocks/MockMiddleware.sol:49)
 delegatecall ******************************************::[1mutilization(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
   delegatecall ******************************************::0x0f82ffd0e912a1b0cc7579bc5827e495c2ce52587bc3871751e3281fc5599b38c3bfc4000000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7 [1m<no source map>
      ******************************************0000000000134231d8a357ba
    (5420806412916666)
  (5420806412916666)
call [1mERC1967Proxy::[unknown method](0x8d7301240000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m(/recon/test/recon/mocks/MockMiddleware.sol:52)
 delegatecall ******************************************::[1mtotalBorrows(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (130099353910)
  (130099353910)
call [1mERC1967Proxy::[unknown method](0xc0af0d3b0000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m(/recon/test/recon/mocks/MockMiddleware.sol:53)
 delegatecall ******************************************::[1mgetVaultDebt(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (130099353910)
  (130099353910)
call [1mERC1967Proxy::[unknown method](0xb7902303) [1m(/recon/test/recon/mocks/MockMiddleware.sol:55)
 delegatecall ******************************************::[1minsuranceFund() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (******************************************)
  (******************************************)
call ******************************************::[1mbalanceOf(******************************************) [1m(/recon/test/recon/mocks/MockMiddleware.sol:54)
  (0)
call [1mERC1967Proxy::[unknown method](0x18160ddd) [1m(/recon/test/recon/mocks/MockMiddleware.sol:55)
 delegatecall ******************************************::[1mtotalSupply() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (24000000000000000000002000000000000)
  (24000000000000000000002000000000000)
call [1mERC1967Proxy::[unknown method](0xe75179a40000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m(/recon/test/recon/mocks/MockMiddleware.sol:58)
 delegatecall ******************************************::[1mreserve(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (10211146)
  (10211146)
call [1mERC1967Proxy::[unknown method](0x01e1d114) [1m(/recon/test/recon/mocks/MockMiddleware.sol:89)
 delegatecall ******************************************::[1mtotalAssets() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (0)
  (0)
call [1mERC1967Proxy::[unknown method](0x18160ddd) [1m(/recon/test/recon/mocks/MockMiddleware.sol:89)
 delegatecall ******************************************::[1mtotalSupply() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (0)
  (0)
call [1mERC1967Proxy::[unknown method](0x70a082310000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m(/recon/test/recon/mocks/MockMiddleware.sol:89)
 delegatecall ******************************************::[1mbalanceOf([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (23880000000000000000001989999999743)
  (23880000000000000000001989999999743)
call [1mERC1967Proxy::[unknown method](0x2c3ee88c000000000000000000000000000000000004995ff983c42c4b5104f1553e3aff) [1m(/recon/test/recon/mocks/MockMiddleware.sol:89)
 delegatecall ******************************************::[1mgetRedeemAmount(23880000000000000000001989999999743) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
   delegatecall ******************************************::0x4bfcc81c3b40995b576f8dd0a8521bba471c5346e53f6a25529b0903b82331eb1a2afe00000000000000000000000000000000000004995ff983c42c4b5104f1553e3aff [1m<no source map>
     call [1mERC1967Proxy::[unknown method](0x18160ddd) [1m<no source map>
       delegatecall ******************************************::[1mtotalSupply() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (24000000000000000000002000000000000)
        (24000000000000000000002000000000000)
     call [1mERC1967Proxy::[unknown method](0x71a97305) [1m<no source map>
       delegatecall ******************************************::[1massets() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          ([******************************************, ******************************************, ******************************************])
        ([******************************************, ******************************************, ******************************************])
     call [1mERC1967Proxy::[unknown method](0x9782e8210000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m<no source map>
       delegatecall ******************************************::[1mtotalSupplies(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (24000000000000000000002)
        (24000000000000000000002)
     call [1mERC1967Proxy::[unknown method](0x9782e821000000000000000000000000d16d567549a2a2a2005aeacf7fb193851603dd70) [1m<no source map>
       delegatecall ******************************************::[1mtotalSupplies(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (0)
        (0)
     call [1mERC1967Proxy::[unknown method](0x9782e82100000000000000000000000096d3f6c20eed2697647f543fe6c08bc2fbf39758) [1m<no source map>
       delegatecall ******************************************::[1mtotalSupplies(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (0)
        (0)
      ******************************************00000000000000000000004000000000000000000000000000000000000000000000000000000000000000c0000000000000000000000000000000000000000000000000000000000000000300000000000000000000000000000000000000000000050e8992a65668200001000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000003000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
    ([23880000000000000000001, 0, 0], [0, 0, 0])
  ([23880000000000000000001, 0, 0], [0, 0, 0])
call [1mERC1967Proxy::[unknown method](0x92e423b50000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m(/recon/test/recon/mocks/MockMiddleware.sol:89)
 delegatecall ******************************************::[1magent([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
   delegatecall ******************************************::0x2a43ad13d6af1ec8a1789f5ada2b972bd1569f7c83af2e268be17cd65efe8474ebf088000000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496 [1m<no source map>
     call [1mERC1967Proxy::[unknown method](0x330244300000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mcoverage([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
         call [1mMockMiddleware::slashableCollateral(address,uint48)([1mCryticTester, 1535414400) [1m<no source map>
           call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
             delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
               call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
                 call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                    (8)
                 call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                    (0, ************, 0, **********, 0)
                  (************, **********)
                (************, **********)
              (************, **********)
           call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
              (18)
            (************00)
         call [1mMockMiddleware::coverage(address)([1mCryticTester) [1m<no source map>
           call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
             delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
               call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
                 call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                    (8)
                 call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                    (0, ************, 0, **********, 0)
                  (************, **********)
                (************, **********)
              (************, **********)
           call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
              (18)
            (************00)
          (************00)
        (************00)
     call [1mERC1967Proxy::[unknown method](0xf01391e30000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mslashableCollateral([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
         call [1mMockMiddleware::slashableCollateral(address,uint48)([1mCryticTester, 1535414400) [1m<no source map>
           call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
             delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
               call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
                 call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                    (8)
                 call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                    (0, ************, 0, **********, 0)
                  (************, **********)
                (************, **********)
              (************, **********)
           call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
              (18)
            (************00)
          (************00)
        (************00)
     call [1mERC1967Proxy::[unknown method](0xb1732b************000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mliquidationThreshold([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (700000000000000000000000000)
        (700000000000000000000000000)
     call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m<no source map>
       delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
         call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
           call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
              (8)
           call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
              (0, 100000000, 0, **********, 0)
            (100000000, **********)
          (100000000, **********)
        (100000000, **********)
     call [1mERC1967Proxy::[unknown method](0x70a082310000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mbalanceOf([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (133656906310)
        (133656906310)
     call [1mERC1967Proxy::[unknown method](0x98db1b080000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mrestakerRate([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (795513583471503155188697930)
        (795513583471503155188697930)
     call [1mERC1967Proxy::[unknown method](0x70a082310000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mbalanceOf([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (133656906310)
        (133656906310)
      ******************************************00000000000016bcc41e9000000000000000000000000000000000000000000000000000000016bcc41e900000000000000000000000000000000000000000000000000000000c27f13e0358000000000000000000000000000000000000000001ba3bcda346c6a43d1c00000000000000000000000000000000000000000000024306c4097859c43c0000000000000000000000000000000000000000000000043b0bd5180cca80a1487824
    (************00, ************00, 13365690631000, 534627625240000000000000000, 700000000000000000000000000, 1309322539563425272879937572)
  (************00, ************00, 13365690631000, 534627625240000000000000000, 700000000000000000000000000, 1309322539563425272879937572)

optimize_total_supply_to_total_vault_debt_ratio: max value: 1000000004985469027

  Call sequence:
    CryticTester.mockNetworkMiddleware_setMockSlashableCollateralByVault(53502540585222975478199632749217283793864113140416536062295354888833147494184) from: ****************************************** Time delay: 311699 seconds Block delay: 36723
    CryticTester.capToken_mint_clamped(12000000000000000000001) from: ****************************************** Time delay: 352545 seconds Block delay: 43649
    CryticTester.mockNetworkMiddleware_registerVault(0xffffffff) from: ****************************************** Time delay: 311575 seconds Block delay: 39455
    CryticTester.mockNetworkMiddleware_setMockSlashableCollateralByVault(103341770688910690652700114498011722191660581929354966258250607465273380350457) from: ****************************************** Time delay: 322335 seconds Block delay: 11826
    CryticTester.capToken_removeAsset(0x2fffffffd) from: ****************************************** Time delay: 64 seconds Block delay: 31460
    CryticTester.capToken_burn(0x1d1499e622d69689cdf9004d05ec547d650ff211,33026036380388846468995296099857229870287056192530636535709815224896729802240,77474550557632893495551472452977347152358378822205064876601557015268799553025,0xffffffff,69876383228922939022944651115856946628174398641990156836326730177986738856418) from: ****************************************** Time delay: 510426 seconds Block delay: 45267
    CryticTester.asset_approve(0xe8dc788818033232ef9772cb2e6622f1ec8bc840,93888916009839368263721418442165802286) from: ****************************************** Time delay: 1156 seconds Block delay: 39455
    CryticTester.mockERC4626Tester_setDecimalsOffset(3) from: ****************************************** Time delay: 321220 seconds Block delay: 2
    CryticTester.property_sum_of_withdrawals() from: ****************************************** Time delay: 86347 seconds Block delay: 255
    CryticTester.feeAuction_setMinStartPrice(115792089237316195423570985008687907853269984665639664039457584007913129639935) from: ****************************************** Time delay: 74230 seconds Block delay: 49062
    CryticTester.mockERC4626Tester_transferFrom(0x1fffffffe,0x3d7ebc40af7092e3f1c81f2e996cba5cae2090d7,55486778323198774283109890152636832016297137555854510969585311409942335317332) from: ****************************************** Time delay: 322293 seconds Block delay: 28699
    CryticTester.property_staked_cap_value_non_decreasing() from: ****************************************** Time delay: 7 seconds Block delay: 38081
    CryticTester.asset_mint(0xdead,306352413121947439385720704169792436304) from: ****************************************** Time delay: 17 seconds Block delay: 16478
    CryticTester.switchChainlinkOracle(95268842821874189123591965977547467005438531829027889129450937170047408690750) from: ****************************************** Time delay: 4573 seconds Block delay: 27174
    CryticTester.stakedCap_notify() from: ****************************************** Time delay: 482712 seconds Block delay: 24965
    CryticTester.property_sum_of_withdrawals() from: ****************************************** Time delay: 116188 seconds Block delay: 47640
    CryticTester.stakedCap_withdraw(35217631253033781749631934490387713807952790775725121623659404032578384695871,0x1,0xffffffff) from: ****************************************** Time delay: 33271 seconds Block delay: 27404
    CryticTester.capToken_setFractionalReserveVault() from: ****************************************** Time delay: 86349 seconds Block delay: 127
    CryticTester.capToken_setReserve(100000000000000000000000001) from: ****************************************** Time delay: 105767 seconds Block delay: 13110
    CryticTester.lender_initiateLiquidation_clamped() from: ****************************************** Time delay: 63442 seconds Block delay: 11942
    CryticTester.lender_initiateLiquidation() from: ****************************************** Time delay: 94246 seconds Block delay: 53274
    CryticTester.delegation_modifyAgent_clamped(17391375523763239189511393302753618965174506667155018537852061066121620091294,114478009246680025180879295555784456585143961019315544106736908853806034780761) from: ****************************************** Time delay: 187012 seconds Block delay: 33175
    CryticTester.lender_initiateLiquidation_clamped() from: ****************************************** Time delay: 209930 seconds Block delay: 20937
    CryticTester.feeAuction_buy(115792089237316195423570985008687907853269984665640564039457584007913129639932,[0xf06, 0x1fffffffe, 0xffffffff, 0x2fffffffd, 0xffffffff, 0x13aa49bac059d709dd0a18d6bb63290076a702d7, 0xf05, 0x1fffffffe, 0xc7183455a4c133ae270771860664b6b7ec320bb1, 0xf0c, 0x2fffffffd, 0x1, 0x2fffffffd, 0x2fffffffd, 0x1fffffffe, 0xf62849f9a0b5bf2913b396098f7c7019b51a820a, 0x1fffffffe, 0x1fffffffe],[95793814639993972538821689612325297065465594663390136592485785061519712502461, 1299633, 99, 115546649153271254022992749491123068506962056301975226109019193847010234212045, 115792089237316195423570985008687907853269984665640564039457584007913129639887, 19501336369257708946939779389804098070606822925710035835875274658093540759041, 1287956517180963674156257851300921764802417529610338852098294092195279287, 91017312493346857623130728750076483137469384941982117977624691926288183797951, 53644767598621578002071332026059539452880200048325130187416623715983775748745, 115792089237316195423570985008687907853269984665640564039457584007913129639931, 37515046952636475852407950281924120137196896349147891888516740853035006342449, 39481940234449593371804293714542569013955595844749221626493416257160966189697, 90385520311021144990097604999196765004159367141365591112877874048365403250188, 115792089237316195423570985008687907853269984665640564039457584007912725541411, 58316600614662170387030161318098776295292846042010730695325107253298205665106, 100000000000000000000000000000000000000000000000000, 38997062404859409172640230631007077805187212963837609531130533493622814830931, 129722545106290856774404440328014, 26183363301017231788129032529630043876631257603527773176634421493561398810007, 100000000000000000001, 100115552129831589314885543674584881244299289545591675108371084739807054710527, 92219459787980572854361853440790244646003901440968426334458525477159827542902, 995, 75791471954062728115606380026964145154782207769776464881181507315547677986152, 115792089237316195423570985008687907853269984665640564039457584007913129639835, 69384269729348635299822566197562641919627353281152244290816347835475870387461, 73709535362447606736273804987039066788201978006482149610996525928537187532402, 239],0xffffffff,19151801178918424305722266665876421099641432693842541268148869424928606131817) from: ****************************************** Time delay: 577107 seconds Block delay: 39352
    CryticTester.capToken_redeem_clamped(34) from: ****************************************** Time delay: 153417 seconds Block delay: 39352
    CryticTester.capToken_mint(105000001,49361526454000902024328066824959051163886000586217558502514558695011380813746,379) from: ****************************************** Time delay: 317803 seconds Block delay: 5140
    CryticTester.capToken_setFeeData(0x1fffffffe,(11844706565091681398, 4674636451979033599651893182689631730741036777735610805209328523850249509649, 1000000000000000000000000000, 97082044222241584980230011537402957586218230367994809031662555297507089206344, 42623083564973132904909133479741973070529010339227054074088554046945855304309, 34563125598220983437288756414919209010915282724742233141891261661873659926272)) from: ****************************************** Time delay: 17865 seconds Block delay: 16480
    CryticTester.property_ltv() from: ****************************************** Time delay: 225906 seconds Block delay: 33200
    CryticTester.doomsday_liquidate(1) from: ****************************************** Time delay: 28 seconds Block delay: 34046
    CryticTester.lender_realizeRestakerInterest() from: ****************************************** Time delay: 352545 seconds Block delay: 14912
    CryticTester.delegation_modifyAgent_clamped(30905745076851324130144166620516720077825926167802236943536856098877451575040,108607783773114327266907907776627985601985294418040860824770370774981503487131) from: ****************************************** Time delay: 322368 seconds Block delay: 35508
    CryticTester.lender_borrow_clamped(95185054349249297046991078356701240284039064889740784140610539094492925545639) from: ****************************************** Time delay: 322358 seconds Block delay: 39
    CryticTester.property_repaid_debt_equals_zero_debt() from: ****************************************** Time delay: 574634 seconds Block delay: 46422
    CryticTester.capToken_rescueERC20(0xf01,0xffffffff) from: ****************************************** Time delay: 577105 seconds Block delay: 14914
    CryticTester.mockERC4626Tester_setDecimalsOffset(68) from: ****************************************** Time delay: 117047 seconds Block delay: 33201
    CryticTester.capToken_approve(0xffffffff,100621007291447721180434960852340378177702450129561057302023289189225562239084) from: ****************************************** Time delay: 1001 seconds Block delay: 59135
    CryticTester.stakedCap_permit(0xf03,0x2fffffffd,6888066478505441749850992418581994297142195019068002000074442631546382030336,557,9,"\162|\SYN\n\210\230\184\aB\DC2z\190\&1=\DC2n\187\251\241\t-\152\131\227\134K\ACKk\139i?2","\214\185V\143&L\ACKv+\165\DC48{\232\133v\\%\238,\159\149\219\173uTu\193%F\187M") from: ****************************************** Time delay: 322365 seconds Block delay: 27175
    CryticTester.mockNetworkMiddleware_setMockCollateralByVault(0xdead,56594641188588821197158331686264624028786381258636344674644939566614266704785) from: ****************************************** Time delay: 275861 seconds Block delay: 21855
    CryticTester.stakedCap_mint(115719249778752139918448961059749812029638576529014125342545545677235830778432,******************************************) from: ****************************************** Time delay: 276463 seconds Block delay: 56836
    CryticTester.capToken_setReserve(370) from: ****************************************** Time delay: 510905 seconds Block delay: 12155
    CryticTester.mockAaveDataProvider_setVariableBorrowRate(3295493704845230918676268186660034770988220244417374095193402096706034821992) from: ****************************************** Time delay: 540358 seconds Block delay: 254
    CryticTester.delegation_addAgent(0x7fa9385be102ac3eac297483dd6233d62b3e1496,0x27cc01a4676c73fe8b6d0933ac991bff1d77c4da,66566758980580692006064708711997028686144029252637137462516830403300202306266,84987457058295610114227274606547148904833517438231786628844799174151683338989) from: ****************************************** Time delay: 100095 seconds Block delay: 896
    CryticTester.mockERC4626Tester_approve(0x1fffffffe,93028748682325518004809996304569245887784994654100434936701279444684354357251) from: ****************************************** Time delay: 35059 seconds Block delay: 26154
    CryticTester.asset_approve(0x3cff5e7ebecb676c3cb602d0ef2d46710b88854e,19999999) from: ****************************************** Time delay: 496794 seconds Block delay: 10801
    CryticTester.property_debt_token_balance_gte_total_vault_debt() from: ****************************************** Time delay: 510907 seconds Block delay: 51642
    CryticTester.capToken_transferFrom(0xda5a5adc64c8013d334a0da9e711b364af7a4c2d,0x1fffffffe,54395381009925244318985803930048076807025207207304456358577942148389051558413) from: ****************************************** Time delay: 386819 seconds Block delay: 31200
    CryticTester.property_cap_token_backed_1_to_1() from: ****************************************** Time delay: 370631 seconds Block delay: 14278
    CryticTester.stakedCap_withdraw(115792089237316195423570985008687907853269984665640564039457584007913029639935,0xf09,0xf04) from: ****************************************** Time delay: 259405 seconds Block delay: 25985
    CryticTester.mockNetworkMiddleware_setMockCollateralByVault(0xf09,100115552129831589314885543674584881244299289545591675108371084739807054710529) from: ****************************************** Time delay: 322311 seconds Block delay: 29827
    CryticTester.switchActor(81) from: ****************************************** Time delay: 440560 seconds Block delay: 2374
    CryticTester.switchDebtToken(115646429363832098537373089264952926292865375328040177159188071830437333487930) from: ****************************************** Time delay: 317803 seconds Block delay: 39352
    CryticTester.delegation_addAgent(0x1fffffffe,0x89ca9f4f77b267778eb2ea0ba1beadee8523af36,115792089237316195423570985008687907853269984665640564039457584007913129629937,87745317156631132313614441652867040025212480607034151382306998687754632128952) from: ****************************************** Time delay: 249353 seconds Block delay: 44570
    CryticTester.lender_borrow_clamped(73476395) from: ****************************************** Time delay: 586967 seconds Block delay: 52861
    CryticTester.capToken_burn_clamped(0) from: ****************************************** Time delay: 226100 seconds Block delay: 4993
    CryticTester.capToken_realizeInterest(0x2fffffffd) from: ****************************************** Time delay: 251445 seconds Block delay: 12645
    CryticTester.property_borrower_cannot_borrow_more_than_ltv() from: ****************************************** Time delay: 540359 seconds Block delay: 10800
    CryticTester.stakedCap_mint(115792089237316195423570985008687907853269984665639314039457584007913129639937,0x3a6a84cd762d9707a21605b548aaab891562aab) from: ****************************************** Time delay: 126793 seconds Block delay: 45627
    CryticTester.mockAaveDataProvider_setVariableBorrowRate(36581447843391259732751762620568373862564550332328855862777449817024807651245) from: ****************************************** Time delay: 259406 seconds Block delay: 49442
    CryticTester.lender_borrow_clamped(100052986) from: ****************************************** Time delay: 111767 seconds Block delay: 41311
    CryticTester.switchActor(860997276350484435442403721360910327564212592) from: ****************************************** Time delay: 322372 seconds Block delay: 32332
    CryticTester.property_fractional_reserve_vault_has_reserve_amount_of_underlying_asset() from: ****************************************** Time delay: 207154 seconds Block delay: 39350
    CryticTester.property_cap_token_backed_1_to_1() from: ****************************************** Time delay: 407356 seconds Block delay: 4924
    CryticTester.lender_borrow_clamped(100052986) from: ****************************************** Time delay: 62969 seconds Block delay: 29825

Traces:
call [1mERC1967Proxy::[unknown method](0x08cf6fec00000000000000000000000096d3f6c20eed2697647f543fe6c08bc2fbf39758) [1m(/recon/test/recon/mocks/MockMiddleware.sol:71)
 delegatecall ******************************************::[1mreservesData(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (2, [1mERC1967Proxy, [1mERC1967Proxy, [1mERC1967Proxy, 18, false, 100000000)
  (2, [1mERC1967Proxy, [1mERC1967Proxy, [1mERC1967Proxy, 18, false, 100000000)
call [1mERC1967Proxy::[unknown method](0x70a082310000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m(/recon/test/recon/mocks/MockMiddleware.sol:73)
 delegatecall ******************************************::[1mbalanceOf([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (0)
  (0)
call [1mERC1967Proxy::[unknown method](0x71a97305) [1m(/recon/test/recon/mocks/MockMiddleware.sol:77)
 delegatecall ******************************************::[1massets() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    ([******************************************, ******************************************, ******************************************])
  ([******************************************, ******************************************, ******************************************])
call ******************************************::[1mbalanceOf([1mERC1967Proxy) [1m(/recon/test/recon/mocks/MockMiddleware.sol:86)
  (0)
call ******************************************::[1mbalanceOf([1mERC1967Proxy) [1m(/recon/test/recon/mocks/MockMiddleware.sol:86)
  (0)
call ******************************************::[1mbalanceOf([1mERC1967Proxy) [1m(/recon/test/recon/mocks/MockMiddleware.sol:86)
  (11999999999999899946982)
call [1mERC1967Proxy::[unknown method](0xd8cb4aa30000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m(/recon/test/recon/mocks/MockMiddleware.sol:89)
 delegatecall ******************************************::[1mbonus([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
   call [1mERC1967Proxy::[unknown method](0x330244300000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
     delegatecall ******************************************::[1mcoverage([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
       call [1mMockMiddleware::slashableCollateral(address,uint48)([1mCryticTester, 1535328000) [1m<no source map>
         call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
           delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
             call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
               call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                  (8)
               call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                  (0, ************, 0, **********, 0)
                (************, **********)
              (************, **********)
            (************, **********)
         call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
            (18)
          (************00)
       call [1mMockMiddleware::coverage(address)([1mCryticTester) [1m<no source map>
         call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
           delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
             call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
               call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                  (8)
               call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                  (0, ************, 0, **********, 0)
                (************, **********)
              (************, **********)
            (************, **********)
         call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
            (18)
          (************00)
        (************00)
      (************00)
   call [1mERC1967Proxy::[unknown method](0xf01391e30000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
     delegatecall ******************************************::[1mslashableCollateral([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
       call [1mMockMiddleware::slashableCollateral(address,uint48)([1mCryticTester, 1535328000) [1m<no source map>
         call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
           delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
             call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
               call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                  (8)
               call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                  (0, ************, 0, **********, 0)
                (************, **********)
              (************, **********)
            (************, **********)
         call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
            (18)
          (************00)
        (************00)
      (************00)
   call [1mERC1967Proxy::[unknown method](0xb1732b************000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
     delegatecall ******************************************::[1mliquidationThreshold([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
        (700000000000000000000000000)
      (700000000000000000000000000)
   [91merror Revert Panic(0x4e487b710000000000000000000000000000000000000000000000000000000000000012) [1m<no source map>
 [91merror Revert Panic(0x4e487b710000000000000000000000000000000000000000000000000000000000000012) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:41)
call [1mERC1967Proxy::[unknown method](0xfeee175600000000000000000000000096d3f6c20eed2697647f543fe6c08bc2fbf39758) [1m(/recon/test/recon/mocks/MockMiddleware.sol:47)
 delegatecall ******************************************::[1mcurrentUtilizationIndex(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
   delegatecall ******************************************::0x3c054127e912a1b0cc7579bc5827e495c2ce52587bc3871751e3281fc5599b38c3bfc40000000000000000000000000096d3f6c20eed2697647f543fe6c08bc2fbf39758 [1m<no source map>
      ******************************************0000000073b13880422ed467
    (8336506508756833383)
  (8336506508756833383)
call [1mERC1967Proxy::[unknown method](0xbf20d9dc00000000000000000000000096d3f6c20eed2697647f543fe6c08bc2fbf39758) [1m(/recon/test/recon/mocks/MockMiddleware.sol:49)
 delegatecall ******************************************::[1mutilization(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
   delegatecall ******************************************::0x0f82ffd0e912a1b0cc7579bc5827e495c2ce52587bc3871751e3281fc5599b38c3bfc40000000000000000000000000096d3f6c20eed2697647f543fe6c08bc2fbf39758 [1m<no source map>
      ******************************************0000000000000795488eb435
    (8337748833333)
  (8337748833333)
call [1mERC1967Proxy::[unknown method](0x8d73012400000000000000000000000096d3f6c20eed2697647f543fe6c08bc2fbf39758) [1m(/recon/test/recon/mocks/MockMiddleware.sol:52)
 delegatecall ******************************************::[1mtotalBorrows(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (100052986)
  (100052986)
call [1mERC1967Proxy::[unknown method](0xc0af0d3b00000000000000000000000096d3f6c20eed2697647f543fe6c08bc2fbf39758) [1m(/recon/test/recon/mocks/MockMiddleware.sol:53)
 delegatecall ******************************************::[1mgetVaultDebt(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (100052986)
  (100052986)
call [1mERC1967Proxy::[unknown method](0xb7902303) [1m(/recon/test/recon/mocks/MockMiddleware.sol:55)
 delegatecall ******************************************::[1minsuranceFund() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (******************************************)
  (******************************************)
call ******************************************::[1mbalanceOf(******************************************) [1m(/recon/test/recon/mocks/MockMiddleware.sol:54)
  (0)
call [1mERC1967Proxy::[unknown method](0x18160ddd) [1m(/recon/test/recon/mocks/MockMiddleware.sol:55)
 delegatecall ******************************************::[1mtotalSupply() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (11999999999999999999966)
  (11999999999999999999966)
call [1mERC1967Proxy::[unknown method](0xe75179a400000000000000000000000096d3f6c20eed2697647f543fe6c08bc2fbf39758) [1m(/recon/test/recon/mocks/MockMiddleware.sol:58)
 delegatecall ******************************************::[1mreserve(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (370)
  (370)
call [1mERC1967Proxy::[unknown method](0x01e1d114) [1m(/recon/test/recon/mocks/MockMiddleware.sol:89)
 delegatecall ******************************************::[1mtotalAssets() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (0)
  (0)
call [1mERC1967Proxy::[unknown method](0x18160ddd) [1m(/recon/test/recon/mocks/MockMiddleware.sol:89)
 delegatecall ******************************************::[1mtotalSupply() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (0)
  (0)
call [1mERC1967Proxy::[unknown method](0x70a082310000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m(/recon/test/recon/mocks/MockMiddleware.sol:89)
 delegatecall ******************************************::[1mbalanceOf([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (11939999999999999999966)
  (11939999999999999999966)
call [1mERC1967Proxy::[unknown method](0x2c3ee88c00000000000000000000000000000000000000000000028744c9532b340fffde) [1m(/recon/test/recon/mocks/MockMiddleware.sol:89)
 delegatecall ******************************************::[1mgetRedeemAmount(11939999999999999999966) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
   delegatecall ******************************************::0x4bfcc81c3b40995b576f8dd0a8521bba471c5346e53f6a25529b0903b82331eb1a2afe0000000000000000000000000000000000000000000000028744c9532b340fffde [1m<no source map>
     call [1mERC1967Proxy::[unknown method](0x18160ddd) [1m<no source map>
       delegatecall ******************************************::[1mtotalSupply() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (11999999999999999999966)
        (11999999999999999999966)
     call [1mERC1967Proxy::[unknown method](0x71a97305) [1m<no source map>
       delegatecall ******************************************::[1massets() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          ([******************************************, ******************************************, ******************************************])
        ([******************************************, ******************************************, ******************************************])
     call [1mERC1967Proxy::[unknown method](0x9782e8210000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m<no source map>
       delegatecall ******************************************::[1mtotalSupplies(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (0)
        (0)
     call [1mERC1967Proxy::[unknown method](0x9782e821000000000000000000000000d16d567549a2a2a2005aeacf7fb193851603dd70) [1m<no source map>
       delegatecall ******************************************::[1mtotalSupplies(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (0)
        (0)
     call [1mERC1967Proxy::[unknown method](0x9782e82100000000000000000000000096d3f6c20eed2697647f543fe6c08bc2fbf39758) [1m<no source map>
       delegatecall ******************************************::[1mtotalSupplies(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (11999999999999999999968)
        (11999999999999999999968)
      ******************************************00000000000000000000004000000000000000000000000000000000000000000000000000000000000000c000000000000000000000000000000000000000000000000000000000000000030000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000028744c9532b340fffdf0000000000000000000000000000000000000000000000000000000000000003000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
    ([0, 0, 11939999999999999999967], [0, 0, 0])
  ([0, 0, 11939999999999999999967], [0, 0, 0])
call [1mERC1967Proxy::[unknown method](0x92e423b50000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m(/recon/test/recon/mocks/MockMiddleware.sol:89)
 delegatecall ******************************************::[1magent([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
   delegatecall ******************************************::0x2a43ad13d6af1ec8a1789f5ada2b972bd1569f7c83af2e268be17cd65efe8474ebf088000000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496 [1m<no source map>
     call [1mERC1967Proxy::[unknown method](0x330244300000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mcoverage([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
         call [1mMockMiddleware::slashableCollateral(address,uint48)([1mCryticTester, 1535328000) [1m<no source map>
           call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
             delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
               call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
                 call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                    (8)
                 call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                    (0, ************, 0, **********, 0)
                  (************, **********)
                (************, **********)
              (************, **********)
           call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
              (18)
            (************00)
         call [1mMockMiddleware::coverage(address)([1mCryticTester) [1m<no source map>
           call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
             delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
               call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
                 call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                    (8)
                 call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                    (0, ************, 0, **********, 0)
                  (************, **********)
                (************, **********)
              (************, **********)
           call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
              (18)
            (************00)
          (************00)
        (************00)
     call [1mERC1967Proxy::[unknown method](0xf01391e30000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mslashableCollateral([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
         call [1mMockMiddleware::slashableCollateral(address,uint48)([1mCryticTester, 1535328000) [1m<no source map>
           call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
             delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
               call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
                 call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                    (8)
                 call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                    (0, ************, 0, **********, 0)
                  (************, **********)
                (************, **********)
              (************, **********)
           call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
              (18)
            (************00)
          (************00)
        (************00)
     call [1mERC1967Proxy::[unknown method](0xb1732b************000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mliquidationThreshold([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (700000000000000000000000000)
        (700000000000000000000000000)
      ******************************************00000000000016bcc41e9000000000000000000000000000000000000000000000000000000016bcc41e9000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000024306c4097859c43c000000ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
    (************00, ************00, 0, 0, 700000000000000000000000000, 115792089237316195423570985008687907853269984665640564039457584007913129639935)
  (************00, ************00, 0, 0, 700000000000000000000000000, 115792089237316195423570985008687907853269984665640564039457584007913129639935)
call [1mHEVM::[1mprank([1mCryticTester) [1m(/recon/test/recon/targets/DebtTokenTargets.sol:30)
  0x
call ******************************************::[1mbalanceOf([1mCryticTester) [1m(/recon/test/recon-fee-auction/CryticToFoundry.sol:22)
  (309473009821345068724781087)
call [1mERC1967Proxy::[unknown method](0x08cf6fec00000000000000000000000096d3f6c20eed2697647f543fe6c08bc2fbf39758) [1m(/recon/test/recon-fee-auction/CryticToFoundry.sol:22)
 delegatecall ******************************************::[1mreservesData(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (2, [1mERC1967Proxy, [1mERC1967Proxy, [1mERC1967Proxy, 18, false, 100000000)
  (2, [1mERC1967Proxy, [1mERC1967Proxy, [1mERC1967Proxy, 18, false, 100000000)
call [1mERC1967Proxy::[unknown method](0x70a082310000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m(/recon/test/recon-fee-auction/CryticToFoundry.sol:22)
 delegatecall ******************************************::[1mbalanceOf([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (0)
  (0)
call [1mERC1967Proxy::[unknown method](0xf0a053950000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e149600000000000000000000000096d3f6c20eed2697647f543fe6c08bc2fbf39758) [1m(/recon/test/recon-fee-auction/CryticToFoundry.sol:22)
 delegatecall ******************************************::[1mmaxBorrowable([1mCryticTester, ******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
   delegatecall ******************************************::0x59f432fed6af1ec8a1789f5ada2b972bd1569f7c83af2e268be17cd65efe8474ebf088000000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e149600000000000000000000000096d3f6c20eed2697647f543fe6c08bc2fbf39758 [1m<no source map>
     call [1mERC1967Proxy::[unknown method](0x330244300000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mcoverage([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
         call [1mMockMiddleware::slashableCollateral(address,uint48)([1mCryticTester, 1535328000) [1m<no source map>
           call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
             delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
               call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
                 call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                    (8)
                 call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                    (0, ************, 0, **********, 0)
                  (************, **********)
                (************, **********)
              (************, **********)
           call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
              (18)
            (************00)
         call [1mMockMiddleware::coverage(address)([1mCryticTester) [1m<no source map>
           call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
             delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
               call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
                 call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                    (8)
                 call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                    (0, ************, 0, **********, 0)
                  (************, **********)
                (************, **********)
              (************, **********)
           call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
              (18)
            (************00)
          (************00)
        (************00)
     call [1mERC1967Proxy::[unknown method](0xf01391e30000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mslashableCollateral([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
         call [1mMockMiddleware::slashableCollateral(address,uint48)([1mCryticTester, 1535328000) [1m<no source map>
           call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
             delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
               call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
                 call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                    (8)
                 call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                    (0, ************, 0, **********, 0)
                  (************, **********)
                (************, **********)
              (************, **********)
           call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
              (18)
            (************00)
          (************00)
        (************00)
     call [1mERC1967Proxy::[unknown method](0xb1732b************000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mliquidationThreshold([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (700000000000000000000000000)
        (700000000000000000000000000)
     call [1mERC1967Proxy::[unknown method](0x70a082310000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mbalanceOf([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (0)
        (0)
     call [1mERC1967Proxy::[unknown method](0x98db1b080000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mrestakerRate([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (0)
        (0)
     call [1mERC1967Proxy::[unknown method](0x7473eea60000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mltv([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (500000000000000000000000000)
        (500000000000000000000000000)
     call [1mERC1967Proxy::[unknown method](0x41976e0900000000000000000000000096d3f6c20eed2697647f543fe6c08bc2fbf39758) [1m<no source map>
       delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
         call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
           call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
              (8)
           call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
              (0, 100000000, 0, **********, 0)
            (100000000, **********)
          (100000000, **********)
        (100000000, **********)
     call [1mERC1967Proxy::[unknown method](0xa0821be300000000000000000000000096d3f6c20eed2697647f543fe6c08bc2fbf39758) [1m<no source map>
       delegatecall ******************************************::[1mavailableBalance(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
         delegatecall ******************************************::0x50ba5827e912a1b0cc7579bc5827e495c2ce52587bc3871751e3281fc5599b38c3bfc40000000000000000000000000096d3f6c20eed2697647f543fe6c08bc2fbf39758 [1m<no source map>
            ******************************************0000028a8574254669894fe6
          (11999999999999899946982)
        (11999999999999899946982)
      ******************************************0000028a8574254669894fe6
    (11999999999999899946982)
  (11999999999999899946982)
call [1mERC1967Proxy::[unknown method](0x5c975abb) [1m(/recon/test/recon-fee-auction/CryticToFoundry.sol:22)
 delegatecall ******************************************::[1mpaused() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (false)
  (false)
call [1mERC1967Proxy::[unknown method](0x2e48152c00000000000000000000000096d3f6c20eed2697647f543fe6c08bc2fbf39758) [1m(/recon/test/recon-fee-auction/CryticToFoundry.sol:22)
 delegatecall ******************************************::[1mpaused(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (false)
  (false)
call [1mHEVM::[1mprank([1mCryticTester) [1m(/recon/test/recon-fee-auction/CryticToFoundry.sol:22)
  0x
call [1mERC1967Proxy::[unknown method](0x6c665a5500000000000000000000000096d3f6c20eed2697647f543fe6c08bc2fbf397580000000000000000000000000000000000000000000000000000000005f6affa0000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m(/recon/test/recon-fee-auction/CryticToFoundry.sol:22)
 delegatecall ******************************************::[1mborrow(******************************************, 100052986, [1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
   delegatecall ******************************************::0xc8293757d6af1ec8a1789f5ada2b972bd1569f7c83af2e268be17cd65efe8474ebf088000000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e149600000000000000000000000096d3f6c20eed2697647f543fe6c08bc2fbf397580000000000000000000000000000000000000000000000000000000005f6affa0000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e14960000000000000000000000000000000000000000000000000000000000000000 [1m<no source map>
     delegatecall ******************************************::0x5afe0823d6af1ec8a1789f5ada2b972bd1569f7c83af2e268be17cd65efe8474ebf088000000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e149600000000000000000000000096d3f6c20eed2697647f543fe6c08bc2fbf39758 [1m<no source map>
       call [1mERC1967Proxy::[unknown method](0x70a082310000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
         delegatecall ******************************************::[1mbalanceOf([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
            (0)
          (0)
       call [1mERC1967Proxy::[unknown method](0x98db1b080000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
         delegatecall ******************************************::[1mrestakerRate([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
            (0)
          (0)
        ******************************************000000000000000000000000
     call [1mERC1967Proxy::[unknown method](0xa0821be300000000000000000000000096d3f6c20eed2697647f543fe6c08bc2fbf39758) [1m<no source map>
       delegatecall ******************************************::[1mavailableBalance(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
         delegatecall ******************************************::0x50ba5827e912a1b0cc7579bc5827e495c2ce52587bc3871751e3281fc5599b38c3bfc40000000000000000000000000096d3f6c20eed2697647f543fe6c08bc2fbf39758 [1m<no source map>
            ******************************************0000028a8574254669894fe6
          (11999999999999899946982)
        (11999999999999899946982)
     delegatecall ******************************************::0x2857f05ad6af1ec8a1789f5ada2b972bd1569f7c83af2e268be17cd65efe8474ebf088000000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e149600000000000000000000000096d3f6c20eed2697647f543fe6c08bc2fbf397580000000000000000000000000000000000000000000000000000000005f6affa0000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e14960000000000000000000000000000000000000000000000000000000000000000 [1m<no source map>
       delegatecall ******************************************::0x59f432fed6af1ec8a1789f5ada2b972bd1569f7c83af2e268be17cd65efe8474ebf088000000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e149600000000000000000000000096d3f6c20eed2697647f543fe6c08bc2fbf39758 [1m<no source map>
         call [1mERC1967Proxy::[unknown method](0x330244300000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
           delegatecall ******************************************::[1mcoverage([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
             call [1mMockMiddleware::slashableCollateral(address,uint48)([1mCryticTester, 1535328000) [1m<no source map>
               call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
                 delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
                   call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
                     call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                        (8)
                     call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                        (0, ************, 0, **********, 0)
                      (************, **********)
                    (************, **********)
                  (************, **********)
               call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
                  (18)
                (************00)
             call [1mMockMiddleware::coverage(address)([1mCryticTester) [1m<no source map>
               call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
                 delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
                   call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
                     call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                        (8)
                     call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                        (0, ************, 0, **********, 0)
                      (************, **********)
                    (************, **********)
                  (************, **********)
               call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
                  (18)
                (************00)
              (************00)
            (************00)
         call [1mERC1967Proxy::[unknown method](0xf01391e30000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
           delegatecall ******************************************::[1mslashableCollateral([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
             call [1mMockMiddleware::slashableCollateral(address,uint48)([1mCryticTester, 1535328000) [1m<no source map>
               call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
                 delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
                   call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
                     call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                        (8)
                     call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                        (0, ************, 0, **********, 0)
                      (************, **********)
                    (************, **********)
                  (************, **********)
               call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
                  (18)
                (************00)
              (************00)
            (************00)
         call [1mERC1967Proxy::[unknown method](0xb1732b************000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
           delegatecall ******************************************::[1mliquidationThreshold([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
              (700000000000000000000000000)
            (700000000000000000000000000)
         call [1mERC1967Proxy::[unknown method](0x70a082310000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
           delegatecall ******************************************::[1mbalanceOf([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
              (0)
            (0)
         call [1mERC1967Proxy::[unknown method](0x98db1b080000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
           delegatecall ******************************************::[1mrestakerRate([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
              (0)
            (0)
         call [1mERC1967Proxy::[unknown method](0x7473eea60000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
           delegatecall ******************************************::[1mltv([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
              (500000000000000000000000000)
            (500000000000000000000000000)
         call [1mERC1967Proxy::[unknown method](0x41976e0900000000000000000000000096d3f6c20eed2697647f543fe6c08bc2fbf39758) [1m<no source map>
           delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
             call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
               call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                  (8)
               call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                  (0, 100000000, 0, **********, 0)
                (100000000, **********)
              (100000000, **********)
            (100000000, **********)
         call [1mERC1967Proxy::[unknown method](0xa0821be300000000000000000000000096d3f6c20eed2697647f543fe6c08bc2fbf39758) [1m<no source map>
           delegatecall ******************************************::[1mavailableBalance(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
             delegatecall ******************************************::0x50ba5827e912a1b0cc7579bc5827e495c2ce52587bc3871751e3281fc5599b38c3bfc40000000000000000000000000096d3f6c20eed2697647f543fe6c08bc2fbf39758 [1m<no source map>
                ******************************************0000028a8574254669894fe6
              (11999999999999899946982)
            (11999999999999899946982)
          ******************************************0000028a8574254669894fe6
        0x
     call [1mERC1967Proxy::[unknown method](0x01fdc8710000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1msetLastBorrow([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
         call [1mERC1967Proxy::[unknown method](0xd95d920c01fdc871000000000000000000000000000000000000000000000000000000000000000000000000000000002a07706473244bc757e10f2a9e86fb532828afe300000000000000000000000015cf58144ef33af1e14b5208015d11f9143e27b9) [1m<no source map>
           delegatecall ******************************************::[1mcheckAccess(0x01fdc871, [1mERC1967Proxy, [1mERC1967Proxy) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
              (true)
            (true)
          0x
        0x
     call [1mERC1967Proxy::[unknown method](0x6c665a5500000000000000000000000096d3f6c20eed2697647f543fe6c08bc2fbf397580000000000000000000000000000000000000000000000000000000005f6affa0000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mborrow(******************************************, 100052986, [1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
         call [1mERC1967Proxy::[unknown method](0xd95d920c6c665a550000000000000000000000000000000000000000000000000000000000000000000000000000000092a6649fdcc044da968d94202465578a9371c7b100000000000000000000000015cf58144ef33af1e14b5208015d11f9143e27b9) [1m<no source map>
           delegatecall ******************************************::[1mcheckAccess(lfZU, [1mERC1967Proxy, [1mERC1967Proxy) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
              (true)
            (true)
         delegatecall ******************************************::0x53c655a85c48f30a22a9811126b69b5adcaabfc5ae0a83b6493e1b31e09dc579923ad10000000000000000000000000096d3f6c20eed2697647f543fe6c08bc2fbf397580000000000000000000000000000000000000000000000000000000005f6affa [1m<no source map>
            0x
         delegatecall ******************************************::0xebd0396de912a1b0cc7579bc5827e495c2ce52587bc3871751e3281fc5599b38c3bfc40000000000000000000000000096d3f6c20eed2697647f543fe6c08bc2fbf397580000000000000000000000000000000000000000000000000000000005f6affa0000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496 [1m<no source map>
           call ******************************************::[1mtransfer([1mCryticTester, 100052986) [1m<no source map>
             emit Transfer(from=[1mERC1967Proxy, to=[1mCryticTester, value=100052986) [1m<no source map>
              (true)
           emit Borrow(asset=[1mERC1967Proxy, agent=******************************************, amount=100052986) [1m<no source map>
            0x
          0x
        0x
     call [1mERC1967Proxy::[unknown method](0x40c10f190000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e14960000000000000000000000000000000000000000000000000000000005f6affa) [1m<no source map>
       delegatecall ******************************************::[1mmint([1mCryticTester, 100052986) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
         call [1mERC1967Proxy::[unknown method](0x2daff25c00000000000000000000000096d3f6c20eed2697647f543fe6c08bc2fbf39758) [1m<no source map>
           delegatecall ******************************************::[1mmarketRate(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
             call ******************************************::[1mrate([1mMockAaveDataProvider, ******************************************) [1m<no source map>
               call [1mMockAaveDataProvider::getReserveData(address)(******************************************) [1m<no source map>
                  (0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, **********)
                (0)
              (0)
            (0)
         call [1mERC1967Proxy::[unknown method](0x58d7849a00000000000000000000000096d3f6c20eed2697647f543fe6c08bc2fbf39758) [1m<no source map>
           delegatecall ******************************************::[1mbenchmarkRate(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
              (150000000000000000000000000)
            (150000000000000000000000000)
         call [1mERC1967Proxy::[unknown method](0x207b27df00000000000000000000000096d3f6c20eed2697647f543fe6c08bc2fbf39758) [1m<no source map>
           delegatecall ******************************************::[1mutilizationRate(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
             call ******************************************::[1mrate([1mMockAaveDataProvider, ******************************************) [1m<no source map>
               call [1mMockAaveDataProvider::getReserveData(address)(******************************************) [1m<no source map>
                  (0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, **********)
                (0)
              (0)
            (0)
         call [1mERC1967Proxy::[unknown method](0xd95d920c40c10f190000000000000000000000000000000000000000000000000000000000000000000000000000000094771550282853f6e0124c302f7de1cf50aa45ca00000000000000000000000015cf58144ef33af1e14b5208015d11f9143e27b9) [1m<no source map>
           delegatecall ******************************************::[1mcheckAccess(0x40c10f19, [1mERC1967Proxy, [1mERC1967Proxy) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
              (true)
            (true)
         emit Transfer(from=******************************************, to=[1mCryticTester, value=100052986) [1m<no source map>
          0x
        0x
     emit Borrow(asset=******************************************, agent=[1mCryticTester, amount=100052986) [1m<no source map>
      0x
    0x
  0x
call [1mERC1967Proxy::[unknown method](0x70a082310000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m(/recon/test/recon-fee-auction/CryticToFoundry.sol:22)
 delegatecall ******************************************::[1mbalanceOf([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (100052986)
  (100052986)
call [1mERC1967Proxy::[unknown method](0x92e423b50000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m(/recon/test/recon-fee-auction/CryticToFoundry.sol:22)
 delegatecall ******************************************::[1magent([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
   delegatecall ******************************************::0x2a43ad13d6af1ec8a1789f5ada2b972bd1569f7c83af2e268be17cd65efe8474ebf088000000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496 [1m<no source map>
     call [1mERC1967Proxy::[unknown method](0x330244300000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mcoverage([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
         call [1mMockMiddleware::slashableCollateral(address,uint48)([1mCryticTester, 1535431175) [1m<no source map>
           call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
             delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
               call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
                 call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                    (8)
                 call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                    (0, ************, 0, **********, 0)
                  (************, **********)
                (************, **********)
              (************, **********)
           call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
              (18)
            (************00)
         call [1mMockMiddleware::coverage(address)([1mCryticTester) [1m<no source map>
           call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
             delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
               call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
                 call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                    (8)
                 call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                    (0, ************, 0, **********, 0)
                  (************, **********)
                (************, **********)
              (************, **********)
           call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
              (18)
            (************00)
          (************00)
        (************00)
     call [1mERC1967Proxy::[unknown method](0xf01391e30000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mslashableCollateral([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
         call [1mMockMiddleware::slashableCollateral(address,uint48)([1mCryticTester, 1535431175) [1m<no source map>
           call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
             delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
               call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
                 call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                    (8)
                 call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                    (0, ************, 0, **********, 0)
                  (************, **********)
                (************, **********)
              (************, **********)
           call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
              (18)
            (************00)
          (************00)
        (************00)
     call [1mERC1967Proxy::[unknown method](0xb1732b************000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mliquidationThreshold([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (700000000000000000000000000)
        (700000000000000000000000000)
     call [1mERC1967Proxy::[unknown method](0x41976e0900000000000000000000000096d3f6c20eed2697647f543fe6c08bc2fbf39758) [1m<no source map>
       delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
         call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
           call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
              (8)
           call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
              (0, 100000000, 0, **********, 0)
            (100000000, **********)
          (100000000, **********)
        (100000000, **********)
     call [1mERC1967Proxy::[unknown method](0x70a082310000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mbalanceOf([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (100052986)
        (100052986)
     call [1mERC1967Proxy::[unknown method](0x98db1b080000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mrestakerRate([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (0)
        (0)
     call [1mERC1967Proxy::[unknown method](0x70a082310000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mbalanceOf([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (100052986)
        (100052986)
      ******************************************00000000000016bcc41e9000000000000000000000000000000000000000000000000000000016bcc41e9000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000024306c4097859c43c000000ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
    (************00, ************00, 0, 0, 700000000000000000000000000, 115792089237316195423570985008687907853269984665640564039457584007913129639935)
  (************00, ************00, 0, 0, 700000000000000000000000000, 115792089237316195423570985008687907853269984665640564039457584007913129639935)
call [1mERC1967Proxy::[unknown method](0x70a082310000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m(/recon/test/recon-fee-auction/CryticToFoundry.sol:22)
 delegatecall ******************************************::[1mbalanceOf([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (100052986)
  (100052986)
call ******************************************::[1mbalanceOf([1mCryticTester) [1m(/recon/test/recon-fee-auction/CryticToFoundry.sol:22)
  (309473009821345068824834073)
call [1mERC1967Proxy::[unknown method](0x41976e0900000000000000000000000096d3f6c20eed2697647f543fe6c08bc2fbf39758) [1m(/recon/test/recon-fee-auction/CryticToFoundry.sol:22)
 delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
   call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
     call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
        (8)
     call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
        (0, 100000000, 0, **********, 0)
      (100000000, **********)
    (100000000, **********)
  (100000000, **********)
call [1mMockMiddleware::coverageByVault(address,address,address,address,uint48)(******************************************, [1mCryticTester, ******************************************, ******************************************, 0) [1m(/recon/test/recon-fee-auction/CryticToFoundry.sol:22)
 call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
   delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
     call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
       call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
          (8)
       call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
          (0, ************, 0, **********, 0)
        (************, **********)
      (************, **********)
    (************, **********)
 call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
    (18)
  (************00, 100000000000000000000)
call [1mERC1967Proxy::[unknown method](0x7473eea60000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m(/recon/test/recon-fee-auction/CryticToFoundry.sol:22)
 delegatecall ******************************************::[1mltv([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (500000000000000000000000000)
  (500000000000000000000000000)
call [1mERC1967Proxy::[unknown method](0x08cf6fec00000000000000000000000096d3f6c20eed2697647f543fe6c08bc2fbf39758) [1m(/recon/test/recon/mocks/MockMiddleware.sol:71)
 delegatecall ******************************************::[1mreservesData(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (2, [1mERC1967Proxy, [1mERC1967Proxy, [1mERC1967Proxy, 18, false, 100000000)
  (2, [1mERC1967Proxy, [1mERC1967Proxy, [1mERC1967Proxy, 18, false, 100000000)
call [1mERC1967Proxy::[unknown method](0x70a082310000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m(/recon/test/recon/mocks/MockMiddleware.sol:73)
 delegatecall ******************************************::[1mbalanceOf([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (100052986)
  (100052986)
call [1mERC1967Proxy::[unknown method](0x71a97305) [1m(/recon/test/recon/mocks/MockMiddleware.sol:77)
 delegatecall ******************************************::[1massets() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    ([******************************************, ******************************************, ******************************************])
  ([******************************************, ******************************************, ******************************************])
call ******************************************::[1mbalanceOf([1mERC1967Proxy) [1m(/recon/test/recon/mocks/MockMiddleware.sol:86)
  (0)
call ******************************************::[1mbalanceOf([1mERC1967Proxy) [1m(/recon/test/recon/mocks/MockMiddleware.sol:86)
  (0)
call ******************************************::[1mbalanceOf([1mERC1967Proxy) [1m(/recon/test/recon/mocks/MockMiddleware.sol:86)
  (11999999999999799893996)
call [1mERC1967Proxy::[unknown method](0xd8cb4aa30000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m(/recon/test/recon/mocks/MockMiddleware.sol:89)
 delegatecall ******************************************::[1mbonus([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
   call [1mERC1967Proxy::[unknown method](0x330244300000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
     delegatecall ******************************************::[1mcoverage([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
       call [1mMockMiddleware::slashableCollateral(address,uint48)([1mCryticTester, 1535431175) [1m<no source map>
         call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
           delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
             call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
               call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                  (8)
               call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                  (0, ************, 0, **********, 0)
                (************, **********)
              (************, **********)
            (************, **********)
         call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
            (18)
          (************00)
       call [1mMockMiddleware::coverage(address)([1mCryticTester) [1m<no source map>
         call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
           delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
             call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
               call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                  (8)
               call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                  (0, ************, 0, **********, 0)
                (************, **********)
              (************, **********)
            (************, **********)
         call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
            (18)
          (************00)
        (************00)
      (************00)
   call [1mERC1967Proxy::[unknown method](0xf01391e30000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
     delegatecall ******************************************::[1mslashableCollateral([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
       call [1mMockMiddleware::slashableCollateral(address,uint48)([1mCryticTester, 1535431175) [1m<no source map>
         call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
           delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
             call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
               call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                  (8)
               call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                  (0, ************, 0, **********, 0)
                (************, **********)
              (************, **********)
            (************, **********)
         call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
            (18)
          (************00)
        (************00)
      (************00)
   call [1mERC1967Proxy::[unknown method](0xb1732b************000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
     delegatecall ******************************************::[1mliquidationThreshold([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
        (700000000000000000000000000)
      (700000000000000000000000000)
   call [1mERC1967Proxy::[unknown method](0x41976e0900000000000000000000000096d3f6c20eed2697647f543fe6c08bc2fbf39758) [1m<no source map>
     delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
       call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
         call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
            (8)
         call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
            (0, 100000000, 0, **********, 0)
          (100000000, **********)
        (100000000, **********)
      (100000000, **********)
   call [1mERC1967Proxy::[unknown method](0x70a082310000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
     delegatecall ******************************************::[1mbalanceOf([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
        (100052986)
      (100052986)
   call [1mERC1967Proxy::[unknown method](0x98db1b080000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
     delegatecall ******************************************::[1mrestakerRate([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
        (0)
      (0)
   call [1mERC1967Proxy::[unknown method](0x70a082310000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
     delegatecall ******************************************::[1mbalanceOf([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
        (100052986)
      (100052986)
   [91merror Revert Panic(0x4e487b710000000000000000000000000000000000000000000000000000000000000012) [1m<no source map>
 [91merror Revert Panic(0x4e487b710000000000000000000000000000000000000000000000000000000000000012) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:41)
call [1mERC1967Proxy::[unknown method](0xfeee175600000000000000000000000096d3f6c20eed2697647f543fe6c08bc2fbf39758) [1m(/recon/test/recon/mocks/MockMiddleware.sol:47)
 delegatecall ******************************************::[1mcurrentUtilizationIndex(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
   delegatecall ******************************************::0x3c054127e912a1b0cc7579bc5827e495c2ce52587bc3871751e3281fc5599b38c3bfc40000000000000000000000000096d3f6c20eed2697647f543fe6c08bc2fbf39758 [1m<no source map>
      ******************************************0000000073b13880422ed467
    (8336506508756833383)
  (8336506508756833383)
call [1mERC1967Proxy::[unknown method](0xbf20d9dc00000000000000000000000096d3f6c20eed2697647f543fe6c08bc2fbf39758) [1m(/recon/test/recon/mocks/MockMiddleware.sol:49)
 delegatecall ******************************************::[1mutilization(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
   delegatecall ******************************************::0x0f82ffd0e912a1b0cc7579bc5827e495c2ce52587bc3871751e3281fc5599b38c3bfc40000000000000000000000000096d3f6c20eed2697647f543fe6c08bc2fbf39758 [1m<no source map>
      ******************************************0000000000000f2a911d686a
    (16675497666666)
  (16675497666666)
call [1mERC1967Proxy::[unknown method](0x8d73012400000000000000000000000096d3f6c20eed2697647f543fe6c08bc2fbf39758) [1m(/recon/test/recon/mocks/MockMiddleware.sol:52)
 delegatecall ******************************************::[1mtotalBorrows(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (200105972)
  (200105972)
call [1mERC1967Proxy::[unknown method](0xc0af0d3b00000000000000000000000096d3f6c20eed2697647f543fe6c08bc2fbf39758) [1m(/recon/test/recon/mocks/MockMiddleware.sol:53)
 delegatecall ******************************************::[1mgetVaultDebt(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (200105972)
  (200105972)
call [1mERC1967Proxy::[unknown method](0xb7902303) [1m(/recon/test/recon/mocks/MockMiddleware.sol:55)
 delegatecall ******************************************::[1minsuranceFund() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (******************************************)
  (******************************************)
call ******************************************::[1mbalanceOf(******************************************) [1m(/recon/test/recon/mocks/MockMiddleware.sol:54)
  (0)
call [1mERC1967Proxy::[unknown method](0x18160ddd) [1m(/recon/test/recon/mocks/MockMiddleware.sol:55)
 delegatecall ******************************************::[1mtotalSupply() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (11999999999999999999966)
  (11999999999999999999966)
call [1mERC1967Proxy::[unknown method](0xe75179a400000000000000000000000096d3f6c20eed2697647f543fe6c08bc2fbf39758) [1m(/recon/test/recon/mocks/MockMiddleware.sol:58)
 delegatecall ******************************************::[1mreserve(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (370)
  (370)
call [1mERC1967Proxy::[unknown method](0x01e1d114) [1m(/recon/test/recon/mocks/MockMiddleware.sol:89)
 delegatecall ******************************************::[1mtotalAssets() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (0)
  (0)
call [1mERC1967Proxy::[unknown method](0x18160ddd) [1m(/recon/test/recon/mocks/MockMiddleware.sol:89)
 delegatecall ******************************************::[1mtotalSupply() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (0)
  (0)
call [1mERC1967Proxy::[unknown method](0x70a082310000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m(/recon/test/recon/mocks/MockMiddleware.sol:89)
 delegatecall ******************************************::[1mbalanceOf([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (11939999999999999999966)
  (11939999999999999999966)
call [1mERC1967Proxy::[unknown method](0x2c3ee88c00000000000000000000000000000000000000000000028744c9532b340fffde) [1m(/recon/test/recon/mocks/MockMiddleware.sol:89)
 delegatecall ******************************************::[1mgetRedeemAmount(11939999999999999999966) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
   delegatecall ******************************************::0x4bfcc81c3b40995b576f8dd0a8521bba471c5346e53f6a25529b0903b82331eb1a2afe0000000000000000000000000000000000000000000000028744c9532b340fffde [1m<no source map>
     call [1mERC1967Proxy::[unknown method](0x18160ddd) [1m<no source map>
       delegatecall ******************************************::[1mtotalSupply() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (11999999999999999999966)
        (11999999999999999999966)
     call [1mERC1967Proxy::[unknown method](0x71a97305) [1m<no source map>
       delegatecall ******************************************::[1massets() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          ([******************************************, ******************************************, ******************************************])
        ([******************************************, ******************************************, ******************************************])
     call [1mERC1967Proxy::[unknown method](0x9782e8210000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m<no source map>
       delegatecall ******************************************::[1mtotalSupplies(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (0)
        (0)
     call [1mERC1967Proxy::[unknown method](0x9782e821000000000000000000000000d16d567549a2a2a2005aeacf7fb193851603dd70) [1m<no source map>
       delegatecall ******************************************::[1mtotalSupplies(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (0)
        (0)
     call [1mERC1967Proxy::[unknown method](0x9782e82100000000000000000000000096d3f6c20eed2697647f543fe6c08bc2fbf39758) [1m<no source map>
       delegatecall ******************************************::[1mtotalSupplies(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (11999999999999999999968)
        (11999999999999999999968)
      ******************************************00000000000000000000004000000000000000000000000000000000000000000000000000000000000000c000000000000000000000000000000000000000000000000000000000000000030000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000028744c9532b340fffdf0000000000000000000000000000000000000000000000000000000000000003000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
    ([0, 0, 11939999999999999999967], [0, 0, 0])
  ([0, 0, 11939999999999999999967], [0, 0, 0])
call [1mERC1967Proxy::[unknown method](0x92e423b50000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m(/recon/test/recon/mocks/MockMiddleware.sol:89)
 delegatecall ******************************************::[1magent([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
   delegatecall ******************************************::0x2a43ad13d6af1ec8a1789f5ada2b972bd1569f7c83af2e268be17cd65efe8474ebf088000000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496 [1m<no source map>
     call [1mERC1967Proxy::[unknown method](0x330244300000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mcoverage([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
         call [1mMockMiddleware::slashableCollateral(address,uint48)([1mCryticTester, 1535431175) [1m<no source map>
           call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
             delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
               call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
                 call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                    (8)
                 call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                    (0, ************, 0, **********, 0)
                  (************, **********)
                (************, **********)
              (************, **********)
           call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
              (18)
            (************00)
         call [1mMockMiddleware::coverage(address)([1mCryticTester) [1m<no source map>
           call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
             delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
               call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
                 call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                    (8)
                 call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                    (0, ************, 0, **********, 0)
                  (************, **********)
                (************, **********)
              (************, **********)
           call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
              (18)
            (************00)
          (************00)
        (************00)
     call [1mERC1967Proxy::[unknown method](0xf01391e30000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mslashableCollateral([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
         call [1mMockMiddleware::slashableCollateral(address,uint48)([1mCryticTester, 1535431175) [1m<no source map>
           call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
             delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
               call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
                 call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                    (8)
                 call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                    (0, ************, 0, **********, 0)
                  (************, **********)
                (************, **********)
              (************, **********)
           call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
              (18)
            (************00)
          (************00)
        (************00)
     call [1mERC1967Proxy::[unknown method](0xb1732b************000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mliquidationThreshold([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (700000000000000000000000000)
        (700000000000000000000000000)
     call [1mERC1967Proxy::[unknown method](0x41976e0900000000000000000000000096d3f6c20eed2697647f543fe6c08bc2fbf39758) [1m<no source map>
       delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
         call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
           call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
              (8)
           call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
              (0, 100000000, 0, **********, 0)
            (100000000, **********)
          (100000000, **********)
        (100000000, **********)
     call [1mERC1967Proxy::[unknown method](0x70a082310000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mbalanceOf([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (100052986)
        (100052986)
     call [1mERC1967Proxy::[unknown method](0x98db1b080000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mrestakerRate([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (0)
        (0)
     call [1mERC1967Proxy::[unknown method](0x70a082310000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mbalanceOf([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (100052986)
        (100052986)
      ******************************************00000000000016bcc41e9000000000000000000000000000000000000000000000000000000016bcc41e9000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000024306c4097859c43c000000ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
    (************00, ************00, 0, 0, 700000000000000000000000000, 115792089237316195423570985008687907853269984665640564039457584007913129639935)
  (************00, ************00, 0, 0, 700000000000000000000000000, 115792089237316195423570985008687907853269984665640564039457584007913129639935)

optimize_max_ltv_delta: max value: 0
Call sequence:
(no transactions)
optimize_total_vault_debt_to_total_supply_ratio: max value: 1469077363556905636

  Call sequence:
    CryticTester.capToken_mint_clamped(2940256241080723) from: ******************************************
    CryticTester.lender_borrow_clamped(115792089237316195423570985008687907853269984665640564039457584007913129639935) from: ******************************************
    *wait* Time delay: 2 seconds Block delay: 1
    CryticTester.oracle_setRestakerRate(0x7fa9385be102ac3eac297483dd6233d62b3e1496,1659740792625656803891943158350725516683096366) from: ******************************************
    CryticTester.capToken_approve(0x1fffffffe,16777112156491222573490197687613459281703916249552063793942059748928208623551) from: ****************************************** Time delay: 414736 seconds Block delay: 21677
    CryticTester.mockChainlinkPriceFeed_setLatestAnswer_clamped(1299632) from: ****************************************** Time delay: 358061 seconds Block delay: 51
    CryticTester.capToken_mint(6255653517518526384108502892701697061827949785359202246477646247900327426394,104320389095934669290375601861354154738738444137374160887807589788231234029247,83760349027100609670931846945953019969109401042295885775848543993541642483279) from: ****************************************** Time delay: 287316 seconds Block delay: 35677
    CryticTester.property_vault_solvency_assets() from: ****************************************** Time delay: 280808 seconds Block delay: 5054
    CryticTester.capToken_setReserve(2358855236791142102419530823920898622676070430632549833897479160284194400584) from: ****************************************** Time delay: 315003 seconds Block delay: 48688
    CryticTester.property_borrower_cannot_borrow_more_than_ltv() from: ****************************************** Time delay: 557748 seconds Block delay: 28126
    CryticTester.property_utilization_index_only_increases() from: ****************************************** Time delay: 255 seconds Block delay: 51290
    CryticTester.property_no_operation_makes_user_liquidatable() from: ****************************************** Time delay: 322342 seconds Block delay: 32551
    CryticTester.feeAuction_buy_clamped(45157855367311532494918399931910273082665199915486779819551795753692785563792,0) from: ****************************************** Time delay: 160321 seconds Block delay: 39351
    CryticTester.capToken_mint_clamped(100000) from: ****************************************** Time delay: 276834 seconds Block delay: 25501
    CryticTester.mockERC4626Tester_transfer(0x10000,115792089237316195423570985008687907853269984665640564039457584007913129639931) from: ****************************************** Time delay: 311700 seconds Block delay: 32331
    CryticTester.property_agent_cannot_have_less_than_minBorrow_balance_of_debt_token() from: ****************************************** Time delay: 288284 seconds Block delay: 45819
    CryticTester.stakedCap_notify() from: ****************************************** Time delay: 385873 seconds Block delay: 59983
    CryticTester.asset_mint(0xf01,33540518) from: ****************************************** Time delay: 420078 seconds Block delay: 51722
    CryticTester.feeAuction_buy(6888066478505441749850992418581994297142195019068002000074442631546382030337,[0xdead, 0xffffffff, 0x1fffffffe, 0x2fffffffd, 0x1fffffffe, 0x1fffffffe, 0x2fffffffd, 0xffffffff, 0xf04, 0x2fffffffd, 0xffffffff, 0xffffffff],[9728295160334050626760683695944138799732954178906269794697302240415750034756, 115792089237316195423570985008687907853269984665640234039457584007913129639936, 268643227578015101421467709934205222578122132934391736336039382216492722281, 77474550557632893495551472452977347152358378822205064876601557015268799553023, 115792089237316195423570985008687907853269984665640564039457584007910970861362, 88548890465471904486023476185079022661763572811919433958705235177219338217784, 32169690217161784883955862442891440183354993550477856192686699548216942073855, 115792089237316195423570985008687907853269984665640564039457584007913129539937, 115792089237316195423570985008687907853269984665639714039457584007913129639935, 81955473079516046949633743016697847541294818689821282749996681496272635257092, 115792089237316195423570985008687907853269984665640564039457584007913129539936],0x1fffffffe,23097481917460044914155768056969534769514088604947386826394237876657613274271) from: ****************************************** Time delay: 262680 seconds Block delay: 58182
    CryticTester.delegation_registerNetwork(0xffffffff) from: ****************************************** Time delay: 282376 seconds Block delay: 58463
    CryticTester.mockNetworkMiddleware_setMockCollateralByVault(0xffffffff,398) from: ****************************************** Time delay: 347839 seconds Block delay: 58783
    CryticTester.stakedCap_withdraw(33026036380388846468995296099857229870287056192530636535709815224896729802240,0xf03,0xdb25a7b768311de128bbda7b8426c3f9c74f3240) from: ****************************************** Time delay: 385872 seconds Block delay: 27136
    CryticTester.feeReceiver_setProtocolFeePercentage(21643255574832448172145843886769055921448724047249458168541561933837088473900) from: ****************************************** Time delay: 557749 seconds Block delay: 5013
    CryticTester.capToken_rescueERC20(0xf04,0xffffffff) from: ****************************************** Time delay: 324839 seconds Block delay: 41270
    CryticTester.mockERC4626Tester_approve(0x1fffffffe,115792089237316195423570985008687907853269984665640564039457584007913129638935) from: ****************************************** Time delay: 85184 seconds Block delay: 42689
    CryticTester.lender_initiateLiquidation_clamped() from: ****************************************** Time delay: 208886 seconds Block delay: 11103
    CryticTester.capToken_divestAll() from: ****************************************** Time delay: 140904 seconds Block delay: 8667
    CryticTester.property_no_operation_makes_user_liquidatable() from: ****************************************** Time delay: 100640 seconds Block delay: 56096
    CryticTester.doomsday_repay(171645511379316382865197029873716534452863851318281232086353469133608950567) from: ****************************************** Time delay: 259407 seconds Block delay: 49
    CryticTester.lender_borrow_clamped(95270632104269184892124067154208463294097412143723888093092076833348243955751) from: ****************************************** Time delay: 522178 seconds Block delay: 41619
    CryticTester.capToken_mint(268,71752003484100711960621348102513260904695913429572967591788865049259678344849,46053722202771484915541151) from: ****************************************** Time delay: 10001 seconds Block delay: 28
    CryticTester.property_utilization_index_only_increases() from: ****************************************** Time delay: 51 seconds Block delay: 35973
    CryticTester.lender_addAsset(0xffffffff,0x1fffffffe,0xffffffff,0x1fffffffe,115792089237316195423570985008687907853269953129640564039457584007913129639935,58360574260107184800744631306689004670377455312925845766213602543508459378316) from: ****************************************** Time delay: 512439 seconds Block delay: 11036
    CryticTester.stakedCap_transferFrom(0x3c4293f66941eca00f4950c10d4255d5c271baef,0x1fffffffe,115792089237316195423570985008687907853269984665640564039457584007913129639932) from: ****************************************** Time delay: 208349 seconds Block delay: 22815
    CryticTester.property_sum_of_deposits() from: ****************************************** Time delay: 340209 seconds Block delay: 42691
    CryticTester.oracle_setBenchmarkRate(0x212224d2f2d262cd093ee13240ca4873fccbba3c,28300637623896771941709217596652005582743766817181502227070143654330710848768) from: ****************************************** Time delay: 10799 seconds Block delay: 4769
    CryticTester.asset_mint(0xda5a5adc64c8013d334a0da9e711b364af7a4c2d,31535999999999999999999999999999999) from: ****************************************** Time delay: 474369 seconds Block delay: 5054
    CryticTester.capToken_setWhitelist(0x1fffffffe,false) from: ****************************************** Time delay: 415353 seconds Block delay: 4976
    CryticTester.property_liquidation_does_not_increase_bonus() from: ****************************************** Time delay: 111766 seconds Block delay: 55506
    CryticTester.capToken_burn_clamped(52738546399762956626807170797471732431077206301345827531978664208513480762150) from: ****************************************** Time delay: 510907 seconds Block delay: 24965
    CryticTester.feeAuction_buy(115792089237316195423570985008687907853269984665640564039457584007786771967682,[0x1, 0xffffffff, 0x8227724c33c1748a42d1c1cd06e21ab8deb6eb0a, 0x1fffffffe, 0xf62849f9a0b5bf2913b396098f7c7019b51a820a, 0xf09, 0xf04, 0x3d7ebc40af7092e3f1c81f2e996cba5cae2090d7, 0xd6bbde9174b1cdaa358d2cf4d57d1a9f7178fbff, 0xffffffff, 0xffffffff, 0x2fffffffd, 0x1fffffffe, 0x2fffffffd, 0x1fffffffe, 0x1fffffffe, 0x7fa9385be102ac3eac297483dd6233d62b3e1496, 0x3a6a84cd762d9707a21605b548aaab891562aab, 0x96d3f6c20eed2697647f543fe6c08bc2fbf39758, 0x2fffffffd, 0xffffffff, 0x1fffffffe, 0x8227724c33c1748a42d1c1cd06e21ab8deb6eb0a, 0xf01, 0x1fffffffe, 0xffffffff, 0x92a6649fdcc044da968d94202465578a9371c7b1],[244, 11016829767418271482556186814003731805227642623290926554356266769347495270406, 0, 63551708895793737708728838858617298371874817902563367925012898637171567851743, 13368142101690143735105409428366271864280455549563107948228605699650558449467, 37918459150976193064592928317706390578459730965133018611890265802158201590632, 49999999999999999999999999, 64379739247229900650667875329471612376850680589871039636736551876411080124728, 115792089237316195423570985008687907853269984665640564039457584007913129639933, 470, 115792089237316195423570985008687907853269984665640564039457584007913129639934, 6229420587986584004948174139341158331454353103101795561763766619835141470719, 109088742831196451494467509654170432829037111197482062339251291247703034071379, 106467062970613950766547446246779725787856360999619006308053486222138342878577, 73807430138237836565582952255921885115620507360191341928869027985785984934951, 365, 96673254906201526033381976786053581130660348393909763765880524031872866293143, 54359003226631933111189683143790020701183572381525067114624940188691539964328, 59973346815672831926163497568378141712083154210823772905920279673669617585995, 32, 94601515879624740885183856432713473682155575860020554138564038220642087002188, 10370276322604635184924207815421325951279938986011697163117774226915915217920, 115792089237316195423570985008687907853269984665640564039457584007913129639921, 65536, 115719249778752139918448961059749812029638576529014125342545545677235830778430],0xf09,115792089237316195423570985008687907853269984665640564039457584007913129639934) from: ****************************************** Time delay: 312375 seconds Block delay: 27136
    CryticTester.capToken_redeem(115792089237316195423570985008687907853269984665640564039457584007913129639856,[27650148577657713781497646146066884270158473361113376293581721278666310263895, 115792089237316195423570985008687907853269984665640564039457584007913129639934, 57988056135651736980300544776080677153497296890808890070696702479224948582576, 115792089237316195423570985008687907853269984665639664039457584007913129639937, 92570141075415530481426812376647089091564127758287840920420791562607484790639, 1000000000000000001, 52530685416568763918012740112037917631294342306582607866152598954097212389232, 71806160856289740614180419567366628467647448859939066439565565178371742671283, 7089122403552233525076381680682590278201326435647891920056818678495767290413, 74765590316438721873018290148271937701985588262012052597345626980822317483263, 115792089237316195423570985008687907853269984665640564039457584007913129629936, 115792089237316195423570985008687907853269984665640564006401334268091065905755],0xe54a55121a47451c5727adbaf9b9fc1643477e25,14637933800688351804178568965129234790508584450334756328880858205417545468489) from: ****************************************** Time delay: 100835 seconds Block delay: 40045
    CryticTester.switchChainlinkOracle(19722689166088262091317933009719571580241023687125421578397752524560160327078) from: ****************************************** Time delay: 86348 seconds Block delay: 40046
    CryticTester.capToken_transferFrom(0xf0c,0xe916cadb12c49389e487eb1e8194b1459b29b0ec,114705477446752060699366354645849301190648477079312152081477419052255729923859) from: ****************************************** Time delay: 249334 seconds Block delay: 16479
    CryticTester.property_fractional_reserve_vault_has_reserve_amount_of_underlying_asset() from: ****************************************** Time delay: 35059 seconds Block delay: 35975
    CryticTester.capToken_rescueERC20(0xf07,0x1fffffffe) from: ****************************************** Time delay: 105765 seconds Block delay: 4984
    CryticTester.capToken_divestAll() from: ****************************************** Time delay: 322342 seconds Block delay: 52860
    CryticTester.lender_cancelLiquidation() from: ****************************************** Time delay: 27 seconds Block delay: 25934
    CryticTester.capToken_unpauseProtocol() from: ****************************************** Time delay: 94557 seconds Block delay: 41311
    CryticTester.feeAuction_buy(1295953201772911215391058989745868821651057887752387839782086074958115661824,[0xffffffff, 0x1fffffffe, 0xffffffff, 0xffffffff, 0xffffffff, 0xdeadbeef, 0x7fa9385be102ac3eac297483dd6233d62b3e1496, 0xffffffff, 0x1d1499e622d69689cdf9004d05ec547d650ff211, 0xf05, 0xbeef, 0x796f2974e3c1af763252512dd6d521e9e984726c, 0x2fffffffd, 0x2fffffffd, 0x2fffffffd, 0xdb25a7b768311de128bbda7b8426c3f9c74f3240, 0xf0a],[38317538679683301928019512555710560700911605843435499162856026992644330086911],0xbeef,49999999999999999999999999) from: ****************************************** Time delay: 228128 seconds Block delay: 53451
    CryticTester.capToken_mint_clamped(100000) from: ****************************************** Time delay: 361136 seconds Block delay: 5053
    CryticTester.stakedCap_approve(0xffffffff,42781945847000261017560425177569179459669254910944366752090067922001662062337) from: ****************************************** Time delay: 305996 seconds Block delay: 44568
    CryticTester.oracle_setBenchmarkRate(0x2fffffffd,16401528547756156502805844937203779715125832591776771322878842751218723527565) from: ****************************************** Time delay: 471435 seconds Block delay: 37010
    CryticTester.property_ltv() from: ****************************************** Time delay: 439556 seconds Block delay: 15369
    CryticTester.property_no_operation_makes_user_liquidatable() from: ****************************************** Time delay: 477281 seconds Block delay: 47639
    CryticTester.lender_initiateLiquidation_clamped() from: ****************************************** Time delay: 574631 seconds Block delay: 11038
    CryticTester.lender_cancelLiquidation() from: ****************************************** Time delay: 384588 seconds Block delay: 7398
    CryticTester.property_total_system_collateralization() from: ****************************************** Time delay: 387502 seconds Block delay: 52394
    CryticTester.property_repaid_debt_equals_zero_debt() from: ****************************************** Time delay: 999 seconds Block delay: 2512
    CryticTester.property_delegated_value_greater_than_borrowed_value() from: ****************************************** Time delay: 45912 seconds Block delay: 34304
    CryticTester.property_utilization_ratio_never_greater_than_1e27() from: ****************************************** Time delay: 33329 seconds Block delay: 48688
    CryticTester.switch_asset(0) from: ****************************************** Time delay: 84342 seconds Block delay: 41
    CryticTester.doomsday_repay(20120357981985427129107873259332595344267898675904925553472763736546248807831) from: ****************************************** Time delay: 490448 seconds Block delay: 17770
    CryticTester.accessControl_revokeRole("log(string,uint256)\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL",0x2fffffffd) from: ****************************************** Time delay: 10674 seconds Block delay: 60267
    CryticTester.feeReceiver_setProtocolFeeReceiver(0xffffffff) from: ****************************************** Time delay: 282375 seconds Block delay: 39518
    CryticTester.capToken_setWhitelist(0x2fffffffd,true) from: ****************************************** Time delay: 199729 seconds Block delay: 33175
    CryticTester.mockChainlinkPriceFeed_setLatestAnswer_clamped(324) from: ****************************************** Time delay: 34092 seconds Block delay: 25460
    CryticTester.property_utilization_ratio_never_greater_than_1e27() from: ****************************************** Time delay: 326329 seconds Block delay: 21677
    CryticTester.mockERC4626Tester_decreaseYield(60867252621184541549873157081741803635608146214527623939265991508106089064618) from: ****************************************** Time delay: 321374 seconds Block delay: 28206
    CryticTester.capToken_realizeInterest(0xffffffff) from: ****************************************** Time delay: 599316 seconds Block delay: 51486
    CryticTester.capToken_unpauseAsset(0x1fffffffe) from: ****************************************** Time delay: 600847 seconds Block delay: 40106
    CryticTester.capToken_unpauseProtocol() from: ****************************************** Time delay: 94247 seconds Block delay: 10523
    CryticTester.delegation_registerNetwork(0xc7183455a4c133ae270771860664b6b7ec320bb1) from: ****************************************** Time delay: 288283 seconds Block delay: 4462
    CryticTester.mockAaveDataProvider_setVariableBorrowRate(61380931199663461603488443308885187377509162551452075777419550267189947820649) from: ****************************************** Time delay: 237193 seconds Block delay: 6721
    CryticTester.mockAaveDataProvider_setVariableBorrowRate(22900426696761416736584470058323642222356459238800218407334671570241883983103) from: ****************************************** Time delay: 48 seconds Block delay: 32956
    CryticTester.capToken_transferFrom(0x20000,0xf62849f9a0b5bf2913b396098f7c7019b51a820a,0) from: ****************************************** Time delay: 367476 seconds Block delay: 1123
    CryticTester.mockERC4626Tester_transferFrom(0x1fffffffe,0x2fffffffd,58475162279656145996186385736222923982266579340447986304962461090637245129355) from: ****************************************** Time delay: 414579 seconds Block delay: 4973
    CryticTester.switchActor(114342427445605199480459797558436441654921884827007358498084712437140454968072) from: ****************************************** Time delay: 17865 seconds Block delay: 1118
    CryticTester.property_sum_of_deposits() from: ****************************************** Time delay: 379552 seconds Block delay: 40597
    CryticTester.property_sum_of_withdrawals() from: ****************************************** Time delay: 221737 seconds Block delay: 45625
    CryticTester.feeReceiver_setProtocolFeePercentage(7) from: ****************************************** Time delay: 27 seconds Block delay: 48689
    CryticTester.feeReceiver_setProtocolFeeReceiver(0x3c4293f66941eca00f4950c10d4255d5c271baef) from: ****************************************** Time delay: 96 seconds Block delay: 15367
    CryticTester.property_agent_cannot_have_less_than_minBorrow_balance_of_debt_token() from: ****************************************** Time delay: 385872 seconds Block delay: 39351
    CryticTester.capToken_transferFrom(0xffffffff,0x2fffffffd,31536000) from: ****************************************** Time delay: 114028 seconds Block delay: 32
    CryticTester.stakedCap_approve(0x2fffffffd,18446744073709551616) from: ****************************************** Time delay: 187012 seconds Block delay: 35264
    CryticTester.accessControl_revokeRole("\t\220\252sT0`B\148\192\biE\207\150%\215$B\251\EOTEZ_\205Gdz~c\148'",0x8227724c33c1748a42d1c1cd06e21ab8deb6eb0a) from: ****************************************** Time delay: 66543 seconds Block delay: 8783
    CryticTester.property_borrowed_asset_value() from: ****************************************** Time delay: 136777 seconds Block delay: 7398
    CryticTester.switchAaveOracle(28166748920309444930076388507129977298847027362412380788178805293856650890703) from: ****************************************** Time delay: 322347 seconds Block delay: 55504
    CryticTester.feeReceiver_distribute() from: ****************************************** Time delay: 62877 seconds Block delay: 38371
    CryticTester.accessControl_revokeRole("\ETB= iB\241b\ETB\195\&6\162\212\215\199\183\197\DC1'\b`,\203\&1E\200\158\DEL$]G\162\206",0x2fffffffd) from: ****************************************** Time delay: 117048 seconds Block delay: 9687
    CryticTester.lender_cancelLiquidation() from: ****************************************** Time delay: 78570 seconds Block delay: 5140
    CryticTester.lender_realizeRestakerInterest() from: ****************************************** Time delay: 311576 seconds Block delay: 39587
    CryticTester.lender_removeAsset(0xffffffff) from: ****************************************** Time delay: 511822 seconds Block delay: 59552
    CryticTester.property_agent_cannot_have_less_than_minBorrow_balance_of_debt_token() from: ****************************************** Time delay: 126793 seconds Block delay: 4944
    CryticTester.capToken_unpauseAsset(0xe54a55121a47451c5727adbaf9b9fc1643477e25) from: ****************************************** Time delay: 280808 seconds Block delay: 20937
    CryticTester.capToken_redeem(115792089237316195423570985008687907853269984665640564039457584007913029639937,[115792089237316195423570985008687907853269984665640564006401334268091065905756, 36476089178085317725773695751354688874132735635622280066396310911619021993285],0xf62849f9a0b5bf2913b396098f7c7019b51a820a,692) from: ****************************************** Time delay: 282375 seconds Block delay: 17233
    CryticTester.doomsday_liquidate(26005990769491162148790225244598197160853393209480272681610186931625424422827) from: ****************************************** Time delay: 321220 seconds Block delay: 52861
    CryticTester.property_no_operation_makes_user_liquidatable() from: ****************************************** Time delay: 559698 seconds Block delay: 48271
    CryticTester.asset_mint(0x2fffffffd,17) from: ****************************************** Time delay: 386818 seconds Block delay: 19

Traces:
call [1mERC1967Proxy::[unknown method](0x08cf6fec0000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m(/recon/test/recon/mocks/MockMiddleware.sol:71)
 delegatecall ******************************************::[1mreservesData(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (0, [1mERC1967Proxy, [1mERC1967Proxy, [1mERC1967Proxy, 6, false, 100000000)
  (0, [1mERC1967Proxy, [1mERC1967Proxy, [1mERC1967Proxy, 6, false, 100000000)
call [1mERC1967Proxy::[unknown method](0x70a082310000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m(/recon/test/recon/mocks/MockMiddleware.sol:73)
 delegatecall ******************************************::[1mbalanceOf([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (0)
  (0)
call [1mERC1967Proxy::[unknown method](0x71a97305) [1m(/recon/test/recon/mocks/MockMiddleware.sol:77)
 delegatecall ******************************************::[1massets() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    ([******************************************, ******************************************, ******************************************])
  ([******************************************, ******************************************, ******************************************])
call [1mERC1967Proxy::[unknown method](0xd8cb4aa30000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m(/recon/test/recon/mocks/MockMiddleware.sol:89)
 delegatecall ******************************************::[1mbonus([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
   call [1mERC1967Proxy::[unknown method](0x330244300000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
     delegatecall ******************************************::[1mcoverage([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
       call [1mMockMiddleware::slashableCollateral(address,uint48)([1mCryticTester, 1541289600) [1m<no source map>
         call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
           delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
             call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
               call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                  (8)
               call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                  (0, ************, 0, 1541457332, 0)
                (************, 1541457332)
              (************, 1541457332)
            (************, 1541457332)
         call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
            (18)
          (************00)
       call [1mMockMiddleware::coverage(address)([1mCryticTester) [1m<no source map>
         call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
           delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
             call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
               call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                  (8)
               call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                  (0, ************, 0, 1541457332, 0)
                (************, 1541457332)
              (************, 1541457332)
            (************, 1541457332)
         call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
            (18)
          (************00)
        (************00)
      (************00)
   call [1mERC1967Proxy::[unknown method](0xf01391e30000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
     delegatecall ******************************************::[1mslashableCollateral([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
       call [1mMockMiddleware::slashableCollateral(address,uint48)([1mCryticTester, 1541289600) [1m<no source map>
         call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
           delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
             call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
               call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                  (8)
               call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                  (0, ************, 0, 1541457332, 0)
                (************, 1541457332)
              (************, 1541457332)
            (************, 1541457332)
         call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
            (18)
          (************00)
        (************00)
      (************00)
   call [1mERC1967Proxy::[unknown method](0xb1732b************000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
     delegatecall ******************************************::[1mliquidationThreshold([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
        (700000000000000000000000000)
      (700000000000000000000000000)
   call [1mERC1967Proxy::[unknown method](0x41976e0900000000000000000000000096d3f6c20eed2697647f543fe6c08bc2fbf39758) [1m<no source map>
     delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
       call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
         call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
            (8)
         call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
            (0, 85000324, 0, 1541457332, 0)
          (85000324, 1541457332)
        (85000324, 1541457332)
      (85000324, 1541457332)
   call [1mERC1967Proxy::[unknown method](0x70a082310000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
     delegatecall ******************************************::[1mbalanceOf([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
        (3181588307854953)
      (3181588307854953)
   call [1mERC1967Proxy::[unknown method](0x98db1b080000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
     delegatecall ******************************************::[1mrestakerRate([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
        (891943156690984724057439563)
      (891943156690984724057439563)
   call [1mERC1967Proxy::[unknown method](0x70a082310000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
     delegatecall ******************************************::[1mbalanceOf([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
        (3181588307854953)
      (3181588307854953)
    (100000000000000000000000000)
  (100000000000000000000000000)
call [1mERC1967Proxy::[unknown method](0xfeee17560000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m(/recon/test/recon/mocks/MockMiddleware.sol:47)
 delegatecall ******************************************::[1mcurrentUtilizationIndex(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
   delegatecall ******************************************::0x3c054127e912a1b0cc7579bc5827e495c2ce52587bc3871751e3281fc5599b38c3bfc4000000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7 [1m<no source map>
      ******************************************000000000000000000000000
    (0)
  (0)
call [1mERC1967Proxy::[unknown method](0xbf20d9dc0000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m(/recon/test/recon/mocks/MockMiddleware.sol:49)
 delegatecall ******************************************::[1mutilization(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
   delegatecall ******************************************::0x0f82ffd0e912a1b0cc7579bc5827e495c2ce52587bc3871751e3281fc5599b38c3bfc4000000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7 [1m<no source map>
      ******************************************000000000000000000000000
    (0)
  (0)
call [1mERC1967Proxy::[unknown method](0x8d7301240000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m(/recon/test/recon/mocks/MockMiddleware.sol:52)
 delegatecall ******************************************::[1mtotalBorrows(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (0)
  (0)
call [1mERC1967Proxy::[unknown method](0xc0af0d3b0000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m(/recon/test/recon/mocks/MockMiddleware.sol:53)
 delegatecall ******************************************::[1mgetVaultDebt(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (0)
  (0)
call [1mERC1967Proxy::[unknown method](0xb7902303) [1m(/recon/test/recon/mocks/MockMiddleware.sol:55)
 delegatecall ******************************************::[1minsuranceFund() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (******************************************)
  (******************************************)
call ******************************************::[1mbalanceOf(******************************************) [1m(/recon/test/recon/mocks/MockMiddleware.sol:54)
  (0)
call [1mERC1967Proxy::[unknown method](0x18160ddd) [1m(/recon/test/recon/mocks/MockMiddleware.sol:55)
 delegatecall ******************************************::[1mtotalSupply() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (2940************)
  (2940************)
call [1mERC1967Proxy::[unknown method](0xe75179a40000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m(/recon/test/recon/mocks/MockMiddleware.sol:58)
 delegatecall ******************************************::[1mreserve(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (0)
  (0)
call [1mERC1967Proxy::[unknown method](0x01e1d114) [1m(/recon/test/recon/mocks/MockMiddleware.sol:89)
 delegatecall ******************************************::[1mtotalAssets() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (0)
  (0)
call [1mERC1967Proxy::[unknown method](0x18160ddd) [1m(/recon/test/recon/mocks/MockMiddleware.sol:89)
 delegatecall ******************************************::[1mtotalSupply() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (0)
  (0)
call [1mERC1967Proxy::[unknown method](0x70a082310000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m(/recon/test/recon/mocks/MockMiddleware.sol:89)
 delegatecall ******************************************::[1mbalanceOf([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (2925548750000000)
  (2925548750000000)
call [1mERC1967Proxy::[unknown method](0x2c3ee88c000000000000000000000000000000000000000000000000000a64c566044f80) [1m(/recon/test/recon/mocks/MockMiddleware.sol:89)
 delegatecall ******************************************::[1mgetRedeemAmount(2925548750000000) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
   delegatecall ******************************************::0x4bfcc81c3b40995b576f8dd0a8521bba471c5346e53f6a25529b0903b82331eb1a2afe00000000000000000000000000000000000000000000000000000a64c566044f80 [1m<no source map>
     call [1mERC1967Proxy::[unknown method](0x18160ddd) [1m<no source map>
       delegatecall ******************************************::[1mtotalSupply() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (2940************)
        (2940************)
     call [1mERC1967Proxy::[unknown method](0x71a97305) [1m<no source map>
       delegatecall ******************************************::[1massets() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          ([******************************************, ******************************************, ******************************************])
        ([******************************************, ******************************************, ******************************************])
     call [1mERC1967Proxy::[unknown method](0x9782e8210000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m<no source map>
       delegatecall ******************************************::[1mtotalSupplies(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (0)
        (0)
     call [1mERC1967Proxy::[unknown method](0x9782e821000000000000000000000000d16d567549a2a2a2005aeacf7fb193851603dd70) [1m<no source map>
       delegatecall ******************************************::[1mtotalSupplies(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (0)
        (0)
     call [1mERC1967Proxy::[unknown method](0x9782e82100000000000000000000000096d3f6c20eed2697647f543fe6c08bc2fbf39758) [1m<no source map>
       delegatecall ******************************************::[1mtotalSupplies(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (2940256241080723)
        (2940256241080723)
      ******************************************00000000000000000000004000000000000000000000000000000000000000000000000000000000000000c0000000000000000000000000000000000000000000000000000000000000000300000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000a64c6d8277cf70000000000000000000000000000000000000000000000000000000000000003000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
    ([0, 0, 2925554959875319], [0, 0, 0])
  ([0, 0, 2925554959875319], [0, 0, 0])
call [1mERC1967Proxy::[unknown method](0x92e423b50000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m(/recon/test/recon/mocks/MockMiddleware.sol:89)
 delegatecall ******************************************::[1magent([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
   delegatecall ******************************************::0x2a43ad13d6af1ec8a1789f5ada2b972bd1569f7c83af2e268be17cd65efe8474ebf088000000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496 [1m<no source map>
     call [1mERC1967Proxy::[unknown method](0x330244300000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mcoverage([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
         call [1mMockMiddleware::slashableCollateral(address,uint48)([1mCryticTester, 1541289600) [1m<no source map>
           call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
             delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
               call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
                 call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                    (8)
                 call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                    (0, ************, 0, 1541457332, 0)
                  (************, 1541457332)
                (************, 1541457332)
              (************, 1541457332)
           call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
              (18)
            (************00)
         call [1mMockMiddleware::coverage(address)([1mCryticTester) [1m<no source map>
           call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
             delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
               call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
                 call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                    (8)
                 call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                    (0, ************, 0, 1541457332, 0)
                  (************, 1541457332)
                (************, 1541457332)
              (************, 1541457332)
           call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
              (18)
            (************00)
          (************00)
        (************00)
     call [1mERC1967Proxy::[unknown method](0xf01391e30000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mslashableCollateral([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
         call [1mMockMiddleware::slashableCollateral(address,uint48)([1mCryticTester, 1541289600) [1m<no source map>
           call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
             delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
               call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
                 call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                    (8)
                 call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                    (0, ************, 0, 1541457332, 0)
                  (************, 1541457332)
                (************, 1541457332)
              (************, 1541457332)
           call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
              (18)
            (************00)
          (************00)
        (************00)
     call [1mERC1967Proxy::[unknown method](0xb1732b************000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mliquidationThreshold([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (700000000000000000000000000)
        (700000000000000000000000000)
     call [1mERC1967Proxy::[unknown method](0x41976e0900000000000000000000000096d3f6c20eed2697647f543fe6c08bc2fbf39758) [1m<no source map>
       delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
         call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
           call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
              (8)
           call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
              (0, 85000324, 0, 1541457332, 0)
            (85000324, 1541457332)
          (85000324, 1541457332)
        (85000324, 1541457332)
     call [1mERC1967Proxy::[unknown method](0x70a082310000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mbalanceOf([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (3181588307854953)
        (3181588307854953)
     call [1mERC1967Proxy::[unknown method](0x98db1b080000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mrestakerRate([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (891943156690984724057439563)
        (891943156690984724057439563)
     call [1mERC1967Proxy::[unknown method](0x70a082310000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mbalanceOf([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (3181588307854953)
        (3181588307854953)
      ******************************************00000000000016bcc41e9000000000000000000000000000000000000000000000000000000016bcc41e90000000000000000000000000000000000000000000000000000000000000060feb000000000000000000000000000000000000000000000000dc8a7261ea6380000000000000000000000000000000000000000000024306c4097859c43c0000000000000000000000000000000000000000087bbfed2af142fc5b9544ba20358c
    (************00, ************00, 397291, 15891640000000000000, 700000000000000000000000000, 44048317228429539053238054725629324)
  (************00, ************00, 397291, 15891640000000000000, 700000000000000000000000000, 44048317228429539053238054725629324)
call [1mHEVM::[1mprank([1mCryticTester) [1m(/recon/test/recon/targets/DebtTokenTargets.sol:30)
  0x
call ******************************************::[1mmint(******************************************, 17) [1m(/recon/test/recon-fee-auction/FeeAuctionCryticTester.sol:15)
 emit Transfer(from=******************************************, to=******************************************, value=17) [1m<no source map>
  0x
call [1mERC1967Proxy::[unknown method](0x08cf6fec0000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m(/recon/test/recon/mocks/MockMiddleware.sol:71)
 delegatecall ******************************************::[1mreservesData(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (0, [1mERC1967Proxy, [1mERC1967Proxy, [1mERC1967Proxy, 6, false, 100000000)
  (0, [1mERC1967Proxy, [1mERC1967Proxy, [1mERC1967Proxy, 6, false, 100000000)
call [1mERC1967Proxy::[unknown method](0x70a082310000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m(/recon/test/recon/mocks/MockMiddleware.sol:73)
 delegatecall ******************************************::[1mbalanceOf([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (0)
  (0)
call [1mERC1967Proxy::[unknown method](0x71a97305) [1m(/recon/test/recon/mocks/MockMiddleware.sol:77)
 delegatecall ******************************************::[1massets() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    ([******************************************, ******************************************, ******************************************])
  ([******************************************, ******************************************, ******************************************])
call [1mERC1967Proxy::[unknown method](0xd8cb4aa30000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m(/recon/test/recon/mocks/MockMiddleware.sol:89)
 delegatecall ******************************************::[1mbonus([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
   call [1mERC1967Proxy::[unknown method](0x330244300000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
     delegatecall ******************************************::[1mcoverage([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
       call [1mMockMiddleware::slashableCollateral(address,uint48)([1mCryticTester, 1541289600) [1m<no source map>
         call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
           delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
             call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
               call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                  (8)
               call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                  (0, ************, 0, 1541457332, 0)
                (************, 1541457332)
              (************, 1541457332)
            (************, 1541457332)
         call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
            (18)
          (************00)
       call [1mMockMiddleware::coverage(address)([1mCryticTester) [1m<no source map>
         call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
           delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
             call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
               call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                  (8)
               call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                  (0, ************, 0, 1541457332, 0)
                (************, 1541457332)
              (************, 1541457332)
            (************, 1541457332)
         call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
            (18)
          (************00)
        (************00)
      (************00)
   call [1mERC1967Proxy::[unknown method](0xf01391e30000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
     delegatecall ******************************************::[1mslashableCollateral([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
       call [1mMockMiddleware::slashableCollateral(address,uint48)([1mCryticTester, 1541289600) [1m<no source map>
         call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
           delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
             call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
               call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                  (8)
               call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                  (0, ************, 0, 1541457332, 0)
                (************, 1541457332)
              (************, 1541457332)
            (************, 1541457332)
         call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
            (18)
          (************00)
        (************00)
      (************00)
   call [1mERC1967Proxy::[unknown method](0xb1732b************000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
     delegatecall ******************************************::[1mliquidationThreshold([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
        (700000000000000000000000000)
      (700000000000000000000000000)
   call [1mERC1967Proxy::[unknown method](0x41976e0900000000000000000000000096d3f6c20eed2697647f543fe6c08bc2fbf39758) [1m<no source map>
     delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
       call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
         call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
            (8)
         call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
            (0, 85000324, 0, 1541457332, 0)
          (85000324, 1541457332)
        (85000324, 1541457332)
      (85000324, 1541457332)
   call [1mERC1967Proxy::[unknown method](0x70a082310000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
     delegatecall ******************************************::[1mbalanceOf([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
        (3181588307854953)
      (3181588307854953)
   call [1mERC1967Proxy::[unknown method](0x98db1b080000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
     delegatecall ******************************************::[1mrestakerRate([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
        (891943156690984724057439563)
      (891943156690984724057439563)
   call [1mERC1967Proxy::[unknown method](0x70a082310000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
     delegatecall ******************************************::[1mbalanceOf([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
        (3181588307854953)
      (3181588307854953)
    (100000000000000000000000000)
  (100000000000000000000000000)
call [1mERC1967Proxy::[unknown method](0xfeee17560000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m(/recon/test/recon/mocks/MockMiddleware.sol:47)
 delegatecall ******************************************::[1mcurrentUtilizationIndex(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
   delegatecall ******************************************::0x3c054127e912a1b0cc7579bc5827e495c2ce52587bc3871751e3281fc5599b38c3bfc4000000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7 [1m<no source map>
      ******************************************000000000000000000000000
    (0)
  (0)
call [1mERC1967Proxy::[unknown method](0xbf20d9dc0000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m(/recon/test/recon/mocks/MockMiddleware.sol:49)
 delegatecall ******************************************::[1mutilization(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
   delegatecall ******************************************::0x0f82ffd0e912a1b0cc7579bc5827e495c2ce52587bc3871751e3281fc5599b38c3bfc4000000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7 [1m<no source map>
      ******************************************000000000000000000000000
    (0)
  (0)
call [1mERC1967Proxy::[unknown method](0x8d7301240000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m(/recon/test/recon/mocks/MockMiddleware.sol:52)
 delegatecall ******************************************::[1mtotalBorrows(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (0)
  (0)
call [1mERC1967Proxy::[unknown method](0xc0af0d3b0000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m(/recon/test/recon/mocks/MockMiddleware.sol:53)
 delegatecall ******************************************::[1mgetVaultDebt(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (0)
  (0)
call [1mERC1967Proxy::[unknown method](0xb7902303) [1m(/recon/test/recon/mocks/MockMiddleware.sol:55)
 delegatecall ******************************************::[1minsuranceFund() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (******************************************)
  (******************************************)
call ******************************************::[1mbalanceOf(******************************************) [1m(/recon/test/recon/mocks/MockMiddleware.sol:54)
  (0)
call [1mERC1967Proxy::[unknown method](0x18160ddd) [1m(/recon/test/recon/mocks/MockMiddleware.sol:55)
 delegatecall ******************************************::[1mtotalSupply() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (2940************)
  (2940************)
call [1mERC1967Proxy::[unknown method](0xe75179a40000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m(/recon/test/recon/mocks/MockMiddleware.sol:58)
 delegatecall ******************************************::[1mreserve(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (0)
  (0)
call [1mERC1967Proxy::[unknown method](0x01e1d114) [1m(/recon/test/recon/mocks/MockMiddleware.sol:89)
 delegatecall ******************************************::[1mtotalAssets() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (0)
  (0)
call [1mERC1967Proxy::[unknown method](0x18160ddd) [1m(/recon/test/recon/mocks/MockMiddleware.sol:89)
 delegatecall ******************************************::[1mtotalSupply() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (0)
  (0)
call [1mERC1967Proxy::[unknown method](0x70a082310000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m(/recon/test/recon/mocks/MockMiddleware.sol:89)
 delegatecall ******************************************::[1mbalanceOf([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    (2925548750000000)
  (2925548750000000)
call [1mERC1967Proxy::[unknown method](0x2c3ee88c000000000000000000000000000000000000000000000000000a64c566044f80) [1m(/recon/test/recon/mocks/MockMiddleware.sol:89)
 delegatecall ******************************************::[1mgetRedeemAmount(2925548750000000) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
   delegatecall ******************************************::0x4bfcc81c3b40995b576f8dd0a8521bba471c5346e53f6a25529b0903b82331eb1a2afe00000000000000000000000000000000000000000000000000000a64c566044f80 [1m<no source map>
     call [1mERC1967Proxy::[unknown method](0x18160ddd) [1m<no source map>
       delegatecall ******************************************::[1mtotalSupply() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (2940************)
        (2940************)
     call [1mERC1967Proxy::[unknown method](0x71a97305) [1m<no source map>
       delegatecall ******************************************::[1massets() [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          ([******************************************, ******************************************, ******************************************])
        ([******************************************, ******************************************, ******************************************])
     call [1mERC1967Proxy::[unknown method](0x9782e8210000000000000000000000003d7ebc40af7092e3f1c81f2e996cba5cae2090d7) [1m<no source map>
       delegatecall ******************************************::[1mtotalSupplies(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (0)
        (0)
     call [1mERC1967Proxy::[unknown method](0x9782e821000000000000000000000000d16d567549a2a2a2005aeacf7fb193851603dd70) [1m<no source map>
       delegatecall ******************************************::[1mtotalSupplies(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (0)
        (0)
     call [1mERC1967Proxy::[unknown method](0x9782e82100000000000000000000000096d3f6c20eed2697647f543fe6c08bc2fbf39758) [1m<no source map>
       delegatecall ******************************************::[1mtotalSupplies(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (2940256241080723)
        (2940256241080723)
      ******************************************00000000000000000000004000000000000000000000000000000000000000000000000000000000000000c0000000000000000000000000000000000000000000000000000000000000000300000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000a64c6d8277cf70000000000000000000000000000000000000000000000000000000000000003000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
    ([0, 0, 2925554959875319], [0, 0, 0])
  ([0, 0, 2925554959875319], [0, 0, 0])
call [1mERC1967Proxy::[unknown method](0x92e423b50000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m(/recon/test/recon/mocks/MockMiddleware.sol:89)
 delegatecall ******************************************::[1magent([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
   delegatecall ******************************************::0x2a43ad13d6af1ec8a1789f5ada2b972bd1569f7c83af2e268be17cd65efe8474ebf088000000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496 [1m<no source map>
     call [1mERC1967Proxy::[unknown method](0x330244300000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mcoverage([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
         call [1mMockMiddleware::slashableCollateral(address,uint48)([1mCryticTester, 1541289600) [1m<no source map>
           call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
             delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
               call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
                 call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                    (8)
                 call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                    (0, ************, 0, 1541457332, 0)
                  (************, 1541457332)
                (************, 1541457332)
              (************, 1541457332)
           call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
              (18)
            (************00)
         call [1mMockMiddleware::coverage(address)([1mCryticTester) [1m<no source map>
           call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
             delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
               call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
                 call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                    (8)
                 call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                    (0, ************, 0, 1541457332, 0)
                  (************, 1541457332)
                (************, 1541457332)
              (************, 1541457332)
           call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
              (18)
            (************00)
          (************00)
        (************00)
     call [1mERC1967Proxy::[unknown method](0xf01391e30000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mslashableCollateral([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
         call [1mMockMiddleware::slashableCollateral(address,uint48)([1mCryticTester, 1541289600) [1m<no source map>
           call [1mERC1967Proxy::[unknown method](0x41976e090000000000000000000000003cff5e7ebecb676c3cb602d0ef2d46710b88854e) [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
             delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
               call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
                 call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
                    (8)
                 call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
                    (0, ************, 0, 1541457332, 0)
                  (************, 1541457332)
                (************, 1541457332)
              (************, 1541457332)
           call ******************************************::[1mdecimals() [1m(/recon/test/recon/targets/IgnoredTargets.sol:46)
              (18)
            (************00)
          (************00)
        (************00)
     call [1mERC1967Proxy::[unknown method](0xb1732b************000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mliquidationThreshold([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (700000000000000000000000000)
        (700000000000000000000000000)
     call [1mERC1967Proxy::[unknown method](0x41976e0900000000000000000000000096d3f6c20eed2697647f543fe6c08bc2fbf39758) [1m<no source map>
       delegatecall ******************************************::[1mgetPrice(******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
         call ******************************************::[1mprice([1mMockChainlinkPriceFeed) [1m<no source map>
           call [1mMockChainlinkPriceFeed::decimals()() [1m<no source map>
              (8)
           call [1mMockChainlinkPriceFeed::latestRoundData()() [1m<no source map>
              (0, 85000324, 0, 1541457332, 0)
            (85000324, 1541457332)
          (85000324, 1541457332)
        (85000324, 1541457332)
     call [1mERC1967Proxy::[unknown method](0x70a082310000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mbalanceOf([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (3181588307854953)
        (3181588307854953)
     call [1mERC1967Proxy::[unknown method](0x98db1b080000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mrestakerRate([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (891943156690984724057439563)
        (891943156690984724057439563)
     call [1mERC1967Proxy::[unknown method](0x70a082310000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) [1m<no source map>
       delegatecall ******************************************::[1mbalanceOf([1mCryticTester) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
          (3181588307854953)
        (3181588307854953)
      ******************************************00000000000016bcc41e9000000000000000000000000000000000000000000000000000000016bcc41e90000000000000000000000000000000000000000000000000000000000000060feb000000000000000000000000000000000000000000000000dc8a7261ea6380000000000000000000000000000000000000000000024306c4097859c43c0000000000000000000000000000000000000000087bbfed2af142fc5b9544ba20358c
    (************00, ************00, 397291, 15891640000000000000, 700000000000000000000000000, 44048317228429539053238054725629324)
  (************00, ************00, 397291, 15891640000000000000, 700000000000000000000000000, 44048317228429539053238054725629324)

optimize_debt_token_supply_less_than_total_vault_debt: max value: 12216183031718674209556

  Call sequence:
    CryticTester.mockNetworkMiddleware_setMockSlashableCollateralByVault(53502540585222975478199632749217283793864113140416536062295354888833147494184) from: ****************************************** Time delay: 311699 seconds Block delay: 36723
    CryticTester.capToken_realizeInterest(0xd16d567549a2a2a2005aeacf7fb193851603dd70) from: ****************************************** Time delay: 162053 seconds Block delay: 14019
    CryticTester.mockNetworkMiddleware_setMockSlashableCollateralByVault(53502540585222975478199632749217283793864113140416536062295354888833147494184) from: ****************************************** Time delay: 311699 seconds Block delay: 36723
    CryticTester.capToken_mint_clamped(8850985747073128664440) from: ****************************************** Time delay: 50 seconds Block delay: 5022
    CryticTester.capToken_mint_clamped(12000000000000000000001) from: ****************************************** Time delay: 352545 seconds Block delay: 43649
    CryticTester.property_sum_of_unrealized_interest() from: ****************************************** Time delay: 2 seconds Block delay: 4974
    CryticTester.capToken_mint_clamped(12000000000000000000001) from: ****************************************** Time delay: 352545 seconds Block delay: 43649
    CryticTester.mockERC4626Tester_increaseYield(88249228096072468423581664786026562537575070733971820421751774233686869860906) from: ****************************************** Time delay: 181472 seconds Block delay: 58106
    CryticTester.stakedCap_deposit(51895644719467792066571894091710869393543355905728171984000194425741445815407,0xf02) from: ****************************************** Time delay: 305996 seconds Block delay: 52772
    CryticTester.property_sum_of_unrealized_interest() from: ****************************************** Time delay: 586966 seconds Block delay: 52859
    CryticTester.lender_realizeRestakerInterest() from: ****************************************** Time delay: 150273 seconds Block delay: 32651
    CryticTester.property_utilization_ratio_never_greater_than_1e27() from: ****************************************** Time delay: 574633 seconds Block delay: 19880
    CryticTester.lender_cancelLiquidation_clamped() from: ****************************************** Time delay: 387502 seconds Block delay: 38369
    CryticTester.capToken_mint_clamped(8850985747073128664440) from: ****************************************** Time delay: 181471 seconds Block delay: 4923
    CryticTester.oracle_setPriceOracleData(0x1fffffffe,(0xffffffff, "\163\237\199\177")) from: ****************************************** Time delay: 283490 seconds Block delay: 17085
    CryticTester.property_staked_cap_value_non_decreasing() from: ****************************************** Time delay: 385872 seconds Block delay: 3599
    CryticTester.accessControl_grantRole("\143\160\230\212\233@\171M\142\213\170=\ETB\153\217*\228X\fI\154Vl\243\&5\157/\140\&9\EM\fc",0xffffffff) from: ****************************************** Time delay: 114565 seconds Block delay: 33175
    CryticTester.switchActor(38317538679683301928019512555710560700911605843435499162856026992644330086912) from: ****************************************** Time delay: 322336 seconds Block delay: 49527
    CryticTester.property_borrowed_asset_value() from: ****************************************** Time delay: 153418 seconds Block delay: 28126
    CryticTester.property_liquidation_does_not_increase_bonus() from: ****************************************** Time delay: 85457 seconds Block delay: 58105
    CryticTester.capToken_investAll() from: ****************************************** Time delay: 455740 seconds Block delay: 51642
    CryticTester.lender_pauseAsset(false) from: ****************************************** Time delay: 410162 seconds Block delay: 1119
    CryticTester.lender_borrow_clamped(115792089237316195423570985008687907853269984665640564039457584007913129639935) from: ****************************************** Time delay: 275394 seconds Block delay: 45626
    CryticTester.capToken_unpauseAsset(0xffffffff) from: ****************************************** Time delay: 34092 seconds Block delay: 4974
    CryticTester.accessControl_grantRole("\148\EM0\DC4\201\182\177\216\&07wo\r\209\\\175\254hzE\DLE%\151\b\248T\t\160\246\129\&7\146",0x7fa9385be102ac3eac297483dd6233d62b3e1496) from: ****************************************** Time delay: 322310 seconds Block delay: 9
    CryticTester.property_health_should_not_change_when_realizeRestakerInterest_is_called() from: ****************************************** Time delay: 535525 seconds Block delay: 32
    CryticTester.mockERC4626Tester_decreaseYield(**********) from: ****************************************** Time delay: 3866 seconds Block delay: 32652
    CryticTester.mockERC4626Tester_transferFrom(0xffffffff,0xc7183455a4c133ae270771860664b6b7ec320bb1,113116376468343745172458936117545930748501093733714462470774784039397509037532) from: ****************************************** Time delay: 292347 seconds Block delay: 36859
    CryticTester.stakedCap_permit(0x2fffffffd,0x1fffffffe,115792089237316195423570985008687907853269984665640564039457584007910970861364,39999,71,"ChainlinkAdapter\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL\NUL","\236\244\182\NUL'[\140\231\143\SOHg}\251@\176\243\232\229h\200\131y\fdy\221\160\212\207\&0\185\137") from: ****************************************** Time delay: 105767 seconds Block delay: 23640
    CryticTester.stakedCap_redeem(4312697559108886881446064448948269894861176531083023474289797534431693051232,0xdb25a7b768311de128bbda7b8426c3f9c74f3240,0x2fffffffd) from: ****************************************** Time delay: 600848 seconds Block delay: 3906
    CryticTester.delegation_modifyAgent_clamped(1313373042,22419374970176140318335480282100458256277235882458427714327060199385416933100) from: ****************************************** Time delay: 159703 seconds Block delay: 42229
    CryticTester.stakedCap_approve(0x2fffffffd,115792089237316195423570985008687907853269984665640564039457584007913024639935) from: ****************************************** Time delay: 207808 seconds Block delay: 16478
    CryticTester.lender_cancelLiquidation() from: ****************************************** Time delay: 195123 seconds Block delay: 55818
    CryticTester.feeReceiver_distribute() from: ****************************************** Time delay: 312375 seconds Block delay: 16061
    CryticTester.lender_pauseAsset(true) from: ****************************************** Time delay: 311700 seconds Block delay: 16089
    CryticTester.capToken_pauseProtocol() from: ****************************************** Time delay: 318509 seconds Block delay: 12645
    CryticTester.capToken_addAsset() from: ****************************************** Time delay: 347391 seconds Block delay: 10471
    CryticTester.delegation_addAgent_clamped(33,61768611543869980110755060282590460368882008114954991291437189564263743946352) from: ****************************************** Time delay: 78572 seconds Block delay: 28205
    CryticTester.property_total_borrowed_less_than_total_supply() from: ****************************************** Time delay: 3601 seconds Block delay: 58183
    CryticTester.feeAuction_setDuration(95826862765788469442940814251057057100710765599474570499703588378320667958970) from: ****************************************** Time delay: 324841 seconds Block delay: 53011
    CryticTester.lender_cancelLiquidation_clamped() from: ****************************************** Time delay: 322365 seconds Block delay: 28495
    CryticTester.switchDebtToken(0) from: ****************************************** Time delay: 268475 seconds Block delay: 28731
    CryticTester.lender_initiateLiquidation_clamped() from: ****************************************** Time delay: 40000 seconds Block delay: 52841
    CryticTester.capToken_divestAll() from: ****************************************** Time delay: 487078 seconds Block delay: 24195
    CryticTester.mockERC4626Tester_setDecimalsOffset(60) from: ****************************************** Time delay: 311574 seconds Block delay: 51209
    CryticTester.oracle_setRestakerRate(0x7fa9385be102ac3eac297483dd6233d62b3e1496,97914013135732952113712335790635236259115548427864201875562346979296878271237) from: ****************************************** Time delay: 222376 seconds Block delay: 12053
    CryticTester.mockChainlinkPriceFeed_setMockPriceStaleness(81) from: ****************************************** Time delay: 314432 seconds Block delay: 20236
    CryticTester.mockERC4626Tester_increaseYield(29467312432407136241810950482213768708420516219072849804022175879340220480293) from: ****************************************** Time delay: 209930 seconds Block delay: 39351
    CryticTester.lender_realizeInterest() from: ****************************************** Time delay: 125395 seconds Block delay: 26050
    CryticTester.capToken_setFeeData(0xbeef,(74765590316438721873018290148271937701985588262012052597345626980822317483264, 70687549146994158286147494538198287639647025838585638479573649323444714891323, 95951759908883103747284463705422407976968104573436332324429014709807035957541, 52788620242979170359954200286573793039554081545574698041199082649316155048875, 705, 88453893171981564471173267568680726239084983217088268556984120797559203990292)) from: ****************************************** Time delay: 208885 seconds Block delay: 41280
    CryticTester.stakedCap_approve(0x1fffffffe,40553997274814329438530162879478738998005198701653608933881558403392470299109) from: ****************************************** Time delay: 62969 seconds Block delay: 17235
    CryticTester.capToken_unpauseProtocol() from: ****************************************** Time delay: 385873 seconds Block delay: 33
    CryticTester.feeAuction_buy(8895208542010828341980172269569101071761393983036656237008708880962053591086,[0x2fffffffd, 0x13aa49bac059d709dd0a18d6bb63290076a702d7, 0xf08, 0x2fffffffd],[77187223723408845181402464070016521706200371716447464929658514361134941832272, 77315607513165839970001252724382312303546318876802340530497464801888751231062],0xffffffff,316603748212039333983388) from: ****************************************** Time delay: 557749 seconds Block delay: 54982
    CryticTester.asset_approve(0xf62849f9a0b5bf2913b396098f7c7019b51a820a,65536) from: ****************************************** Time delay: 322348 seconds Block delay: 25399
    CryticTester.stakedCap_notify() from: ****************************************** Time delay: 162053 seconds Block delay: 45268
    CryticTester.switch_asset(33026036380388846468995296099857229870287056192530636535709815224896729802240) from: ****************************************** Time delay: 410162 seconds Block delay: 46200
    CryticTester.mockChainlinkPriceFeed_setLatestAnswer_clamped(33540519) from: ****************************************** Time delay: 388626 seconds Block delay: 97
    CryticTester.lender_initiateLiquidation_clamped() from: ****************************************** Time delay: 235216 seconds Block delay: 10000
    CryticTester.property_utilization_index_only_increases() from: ****************************************** Time delay: 410161 seconds Block delay: 15369
    CryticTester.oracle_setRestakerRate(0xbeef,1250126020194795319927335695006114369326472650709516935447697219542552143392) from: ****************************************** Time delay: 322312 seconds Block delay: 46200
    CryticTester.delegation_modifyAgent(0xffffffff,7699086,137383118545277663227) from: ****************************************** Time delay: 322121 seconds Block delay: 4896
    CryticTester.lender_borrow_clamped(63) from: ****************************************** Time delay: 41 seconds Block delay: 24907
    CryticTester.accessControl_renounceRole("registering network in vaults\NUL\NUL\NUL",0xffffffff) from: ****************************************** Time delay: 100097 seconds Block delay: 33174
    CryticTester.feeReceiver_distribute() from: ****************************************** Time delay: 360623 seconds Block delay: 46202
    CryticTester.lender_initiateLiquidation() from: ****************************************** Time delay: 33605 seconds Block delay: 17866
    CryticTester.capToken_approve(0xf02,115792089237316195423570985008687907853269984665640564039457584007913129639931) from: ****************************************** Time delay: 97 seconds Block delay: 16816
    CryticTester.property_agent_cannot_have_less_than_minBorrow_balance_of_debt_token() from: ****************************************** Time delay: 327873 seconds Block delay: 39519
    CryticTester.capToken_addAsset() from: ****************************************** Time delay: 115221 seconds Block delay: 39
    CryticTester.capToken_setReserve(50788385909300404455580396173899606482343860974791645243950727205585275907084) from: ****************************************** Time delay: 185597 seconds Block delay: 49249
    CryticTester.mockERC4626Tester_increaseYield(66408039214677783844945677655028605618660200502215728951416616487464480658229) from: ****************************************** Time delay: 435330 seconds Block delay: 52843
    CryticTester.capToken_burn_clamped(115792089237316195423570985008687907853269984665640564039457584007903123986609) from: ****************************************** Time delay: 600847 seconds Block delay: 36775
    CryticTester.capToken_realizeInterest(0x2fffffffd) from: ****************************************** Time delay: 2 seconds Block delay: 16478
    CryticTester.property_total_borrowed_less_than_total_supply() from: ****************************************** Time delay: 599317 seconds Block delay: 34872
    CryticTester.feeAuction_setDuration(31) from: ****************************************** Time delay: 282376 seconds Block delay: 26155
    CryticTester.property_total_system_collateralization() from: ****************************************** Time delay: 159704 seconds Block delay: 38370
    CryticTester.capToken_realizeInterest(0x89ca9f4f77b267778eb2ea0ba1beadee8523af36) from: ****************************************** Time delay: 384686 seconds Block delay: 44568
    CryticTester.switchActor(113342238171) from: ****************************************** Time delay: 100638 seconds Block delay: 31565
    CryticTester.property_borrower_cannot_borrow_more_than_ltv() from: ****************************************** Time delay: 254 seconds Block delay: 36775
    CryticTester.lender_liquidate(115792089237316195423570985008687907853269984665640564039457584007913129639906) from: ****************************************** Time delay: 1001 seconds Block delay: 8
    CryticTester.stakedCap_withdraw(61606679545965145763457913296144206886369597172102603394875641279865407782386,0x1fffffffe,0x94771550282853f6e0124c302f7de1cf50aa45ca) from: ****************************************** Time delay: 160321 seconds Block delay: 10
    CryticTester.capToken_transfer(0x89ca9f4f77b267778eb2ea0ba1beadee8523af36,6129278087000499252444599744757279765379481226944393762486952720611817013957) from: ****************************************** Time delay: 34092 seconds Block delay: 37300
    CryticTester.stakedCap_mint(33836616157800148473937241991990060311975165975819281289460902511640494382844,0xdead) from: ****************************************** Time delay: 412373 seconds Block delay: 51641
    CryticTester.feeReceiver_setProtocolFeePercentage(115792089237316195423570985008687907853269984665640564039457584007913129639933) from: ****************************************** Time delay: 312375 seconds Block delay: 47640
    CryticTester.lender_borrow(10190495373955621929695742741482195587938510275961941424369754410988170897582,0xda5a5adc64c8013d334a0da9e711b364af7a4c2d) from: ****************************************** Time delay: 361136 seconds Block delay: 33174
    CryticTester.capToken_realizeInterest(0xe916cadb12c49389e487eb1e8194b1459b29b0ec) from: ****************************************** Time delay: 192884 seconds Block delay: 39
    CryticTester.mockERC4626Tester_setDecimalsOffset(50) from: ****************************************** Time delay: 179559 seconds Block delay: 10801
    CryticTester.capToken_setFeeData(0x1fffffffe,(97818730412130498456028217244871709057806632147356837614143721108715499440612, 82657069810174430208372578199787134607887611018238740383269311998354628955848, 279375849650584, 115792089237316195423570985008687907853269984665640564039457584007913129639920, 60753329681429359700121211655624608473292583018787338383437995081075738726071, 82547580342382549090234621383407188284589140319735883084128657008216913246747)) from: ****************************************** Time delay: 278942 seconds Block delay: 33173
    CryticTester.stakedCap_approve(0x3381cd18e2fb4db236bf0525938ab6e43db0440f,499999999999999999999999999) from: ****************************************** Time delay: 7992 seconds Block delay: 41271
    CryticTester.doomsday_repay(24152580565007454900352986810512756881196148419291891996385562240646704710170) from: ****************************************** Time delay: 318776 seconds Block delay: 60248
    CryticTester.delegation_addAgent(0x756e0562323adcda4430d6cb456d9151f605290b,0xffffffff,87190009146241820878913298806446877441728324742816007198493631073739756237573,99308618773587763194121035070603348305163247127415042771256555854301992165306) from: ****************************************** Time delay: 517015 seconds Block delay: 41859
    CryticTester.capToken_burn(115792089237316195423570985008687907853269984665640564039457584007786771967683,0,115792089237316195423570985008687907853269984665640564039457584007913129636336) from: ****************************************** Time delay: 311575 seconds Block delay: 19880
    CryticTester.feeReceiver_setProtocolFeePercentage(35905653602341500901361400442936074330121370083600075234297716831397545799051) from: ****************************************** Time delay: 437838 seconds Block delay: 60451
    CryticTester.lender_repay(8814994877670565302679934914742181879983829820528246050679352628871579670713) from: ****************************************** Time delay: 95 seconds Block delay: 18429
    CryticTester.switchDebtToken(129181229575799737715131132821888667075620458965846965435092154830549421624) from: ****************************************** Time delay: 384587 seconds Block delay: 55504
    CryticTester.property_debt_token_balance_gte_total_vault_debt() from: ****************************************** Time delay: 322370 seconds Block delay: 52396
    CryticTester.capToken_unpauseProtocol() from: ****************************************** Time delay: 114027 seconds Block delay: 5216
    CryticTester.property_total_borrowed_less_than_total_supply() from: ****************************************** Time delay: 41567 seconds Block delay: 34045
    CryticTester.capToken_setWhitelist(0xffffffff,false) from: ****************************************** Time delay: 579337 seconds Block delay: 7041
    CryticTester.capToken_approve(0xffffffff,850000000000000000000000000) from: ****************************************** Time delay: 111322 seconds Block delay: 11212

Traces:
call [1mHEVM::[1mprank(******************************************) [1m(/recon/test/recon/targets/DebtTokenTargets.sol:30)
  0x
call [1mERC1967Proxy::[unknown method](0x095ea7b300000000000000000000000000000000000000000000000000000000ffffffff000000000000000000000000000000000000000002bf1a8054a46d0092000000) [1m(/recon/test/recon/targets/MockAaveDataProviderTargets.sol:26)
 delegatecall ******************************************::[1mapprove(******************************************, 850000000000000000000000000) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
   emit Approval(owner=******************************************, spender=******************************************, value=850000000000000000000000000) [1m<no source map>
    (true)
  (true)

optimize_debt_token_supply_greater_than_total_vault_debt: max value: 1

  Call sequence:
    CryticTester.mockNetworkMiddleware_setMockSlashableCollateralByVault(53502540585222975478199632749217283793864113140416536062295354888833147494184) from: ****************************************** Time delay: 311699 seconds Block delay: 36723
    CryticTester.capToken_mint_clamped(12000000000000000000001) from: ****************************************** Time delay: 352545 seconds Block delay: 43649
    CryticTester.mockNetworkMiddleware_registerVault(0xffffffff) from: ****************************************** Time delay: 311575 seconds Block delay: 39455
    CryticTester.mockNetworkMiddleware_setMockSlashableCollateralByVault(103341770688910690652700114498011722191660581929354966258250607465273380350457) from: ****************************************** Time delay: 322335 seconds Block delay: 11826
    CryticTester.capToken_removeAsset(0x2fffffffd) from: ****************************************** Time delay: 64 seconds Block delay: 31460
    CryticTester.capToken_burn(0x1d1499e622d69689cdf9004d05ec547d650ff211,33026036380388846468995296099857229870287056192530636535709815224896729802240,77474550557632893495551472452977347152358378822205064876601557015268799553025,0xffffffff,69876383228922939022944651115856946628174398641990156836326730177986738856418) from: ****************************************** Time delay: 510426 seconds Block delay: 45267
    CryticTester.property_borrowed_asset_value() from: ****************************************** Time delay: 352544 seconds Block delay: 42275
    CryticTester.mockERC4626Tester_transfer(0x1fffffffe,112227713683026349771627003536414901180348) from: ****************************************** Time delay: 111767 seconds Block delay: 31539
    CryticTester.mockERC4626Tester_mint(115792089237316195423570985008687907853269984665640564039457584007913129574402,0x1fffffffe) from: ****************************************** Time delay: 121286 seconds Block delay: 3661
    CryticTester.stakedCap_deposit(6505424303795,0xffffffff) from: ****************************************** Time delay: 559699 seconds Block delay: 48420
    CryticTester.add_new_vault() from: ****************************************** Time delay: 187182 seconds Block delay: 35509
    CryticTester.capToken_rescueERC20(0xf62849f9a0b5bf2913b396098f7c7019b51a820a,0x13aa49bac059d709dd0a18d6bb63290076a702d7) from: ****************************************** Time delay: 185596 seconds Block delay: 38331
    CryticTester.capToken_approve(0x2fffffffd,87119682380721432654019416574445532709953343451496356629497310445305800620342) from: ****************************************** Time delay: 600846 seconds Block delay: 16817
    CryticTester.lender_initiateLiquidation() from: ****************************************** Time delay: 553937 seconds Block delay: 9687
    CryticTester.delegation_modifyAgent_clamped(88161555017392371664696006170548166825313066623102466271851406705894293168096,34340726924627804145431756814751732840564568450828057886584636485960047039745) from: ****************************************** Time delay: 410161 seconds Block delay: 23653
    CryticTester.lender_cancelLiquidation_clamped() from: ****************************************** Time delay: 7992 seconds Block delay: 4960
    CryticTester.property_vault_balance_does_not_change_redeemAmountsOut() from: ****************************************** Time delay: 269177 seconds Block delay: 45261
    CryticTester.capToken_redeem(2347358584187329789335599004996709121516247478703774711636723169735025707557,[11021357299608950767547263401838070066461298999515872779121874017636808604949, 1313373041, 53179163836502978803872283191207747398453172966500799987917346598042906009579, 528, 61310070927908925861599077022926959192125330596387126121051287235379734769311, 11915002572816723537342425060161302636071918905890736287321538194101677419994, 12780918896291908725489598279, 115792089237316195423570985008687907853269984665640564039457584007913129639932, 111319661474220504706266908838698178472705531297163862412879601976905438415040, 21003301337034278187474961055981877115898374457421890916085575993649121817267, 30905745076851324130144166620516720077825926167802236943536856098877451575040, 40, 115792089237316181212473460841085105359405994800603066876984793685575961066959, 15859407313150750625435800488873464064444965204599689556908205345616246345257, 115792089237316195423570985008687907853269984665640564039457584007913129639933, 115792089237316195423570985008687907853269984665640564039457584007913129639933, 20021306486531571663861848369696293672662169073078078492386275954041659347773, 33896211294139245842982648534046452970865576393281900923462581287215990036122, 1889567280, 115792089237316195423570985008687907853269984665640564039457584007913129639932],0x3cff5e7ebecb676c3cb602d0ef2d46710b88854e,66) from: ****************************************** Time delay: 45540 seconds Block delay: 48419
    CryticTester.lender_borrow_clamped(1000000000000000001) from: ****************************************** Time delay: 116413 seconds Block delay: 35266
    CryticTester.delegation_setLtvBuffer(115792089237316195423570985008687907853269984665640564039457584007913129636335) from: ****************************************** Time delay: 322356 seconds Block delay: 12646
    CryticTester.property_vault_solvency_assets() from: ****************************************** Time delay: 259407 seconds Block delay: 54468
    CryticTester.switchActor(115792089237316195423570985008687907853269984665640564039457584007913129639885) from: ****************************************** Time delay: 2 seconds Block delay: 38329
    CryticTester.property_sum_of_unrealized_interest() from: ****************************************** Time delay: 136393 seconds Block delay: 20936
    CryticTester.feeAuction_setDuration(57896044618658097711785492504343953926418782139537452191302581570759080747169) from: ****************************************** Time delay: 276463 seconds Block delay: 45269
    CryticTester.doomsday_repay(115792089237316195423570985008687907853269984665640464039457584007913129639936) from: ****************************************** Time delay: 166861 seconds Block delay: 2256
    CryticTester.capToken_unpauseProtocol() from: ****************************************** Time delay: 222280 seconds Block delay: 39585
    CryticTester.feeAuction_setDuration(126357672252) from: ****************************************** Time delay: 369428 seconds Block delay: 51208
    CryticTester.property_sum_of_deposits() from: ****************************************** Time delay: 526618 seconds Block delay: 32147
    CryticTester.lender_borrow(10000037442,0x2fffffffd) from: ****************************************** Time delay: 39999 seconds Block delay: 34272
    CryticTester.capToken_unpauseAsset(0xffffffff) from: ****************************************** Time delay: 222374 seconds Block delay: 4924
    CryticTester.delegation_addAgent_clamped(96,63431474521911606413303923728070635055842969156844265466620743198292563289886) from: ****************************************** Time delay: 338434 seconds Block delay: 32552
    CryticTester.oracle_setRestakerRate(0x2fffffffd,21004926013655984802103850920216456623434051788825271124864416681897787741011) from: ****************************************** Time delay: 7 seconds Block delay: 5217
    CryticTester.capToken_removeAsset(0x1af7f588a501ea2b5bb3feefa744892aa2cf00e6) from: ****************************************** Time delay: 220637 seconds Block delay: 32955
    CryticTester.lender_liquidate(113965598449019994868556495253329690464703621314654490579059003950615058389760) from: ****************************************** Time delay: 82671 seconds Block delay: 5053
    CryticTester.property_ltv() from: ****************************************** Time delay: 477280 seconds Block delay: 24235
    CryticTester.stakedCap_deposit(0,0x0) from: ****************************************** Time delay: 574631 seconds Block delay: 8631
    CryticTester.capToken_mint_clamped(38412264955560054704201538765602289867346407870324629333931258434639841394739) from: ****************************************** Time delay: 384688 seconds Block delay: 24908
    CryticTester.capToken_setWhitelist(0x5991a2df15a8f6a256d3ec51e99254cd3fb576a9,true) from: ****************************************** Time delay: 59696 seconds Block delay: 49
    CryticTester.switchDebtToken(999) from: ****************************************** Time delay: 76544 seconds Block delay: 49526
    CryticTester.accessControl_revokeRole("\171\194`\219\185|\179n<\153\225\231\EOT$\236\171\RS\SUB9,\193\247\210\252\198\179\"tQ\t\tY",0xf01) from: ****************************************** Time delay: 385872 seconds Block delay: 39350

Traces:
call [1mHEVM::[1mprank([1mCryticTester) [1m(/recon/test/recon/targets/DebtTokenTargets.sol:30)
  0x
call [1mERC1967Proxy::[unknown method](0xd547741fabc260dbb97cb36e3c99e1e70424ecab1e1a392cc1f7d2fcc6b32274510909590000000000000000000000000000000000000000000000000000000000000f01) [1m(/recon/test/recon/targets/LenderTargets.sol:44)
 delegatecall ******************************************::[1mrevokeRole(0xabc260dbb97cb36e3c99e1e70424ecab1e1a392cc1f7d2fcc6b3227451090959, ******************************************) [1m(/recon/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:38)
    0x
  0x



Unique instructions: 113891
Unique codehashes: 29
Corpus size: 282
Seed: 2717406405190079184
Total calls: 42554805