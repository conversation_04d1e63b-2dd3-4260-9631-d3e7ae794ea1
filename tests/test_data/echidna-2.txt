[2024-07-24 17:35:38.82] Compiling .... Done! (103.985005652s)
Analyzing contract: /home/<USER>/recon/test/recon/CryticTester.sol:CryticTester
stderr:WARNING: Running
stderr: slither failed. Echidna will continue, however fuzzing will likely be less e
stderr:ffective.


[2024-07-24 17:37:25.73] Running slither on .... Done! (221.123287609s)
Loaded 0 transaction sequences from echidna/reproducers
Loaded 0 transaction sequences from echidna/coverage
[2024-07-24 17:41:08.91] [Worker 3] Test check_the_overflow() falsified!
  Call sequence:
CryticTester.check_redeem_1(59662101529076824938990193526833739407503722677440676524468837971619070749572) from: 0x0000000000000000000000000000000000020000 Time delay: 292304 seconds Block delay: 23978
CryticTester.check_0_assets_1() from: 0x0000000000000000000000000000000000010000 Time delay: 554465 seconds Block delay: 12338
CryticTester.check_revert_ta_2() from: 0x0000000000000000000000000000000000030000 Time delay: 588255 seconds Block delay: 42101
CryticTester.check_depositRebase_1_mid(**********) from: 0x0000000000000000000000000000000000010000 Time delay: 318197 seconds Block delay: 18429
CryticTester.oracle1_setPrice_coll(61483342328838532659029343507955476248) from: 0x0000000000000000000000000000000000020000 Time delay: 414736 seconds Block delay: 53451
CryticTester.check_the_overflow() from: 0x0000000000000000000000000000000000010000 Time delay: 490446 seconds Block delay: 32767

[2024-07-24 17:41:08.91]  Saved reproducer to echidna/reproducers-unshrunk/8185655539417418565.txt
[2024-07-24 17:41:09.02] [Worker 5] Test canary_max_debt_2() falsified!
  Call sequence:
CryticTester.sock_deposit_b(115792089237316195423570985008687907853269984665640564039457584007913129639935) from: 0x0000000000000000000000000000000000010000 Time delay: 297507 seconds Block delay: 18429
CryticTester.liquidate_attacker(502,878) from: 0x0000000000000000000000000000000000010000 Time delay: 434894 seconds Block delay: 15369
CryticTester.check_revert_ta() from: 0x0000000000000000000000000000000000030000 Time delay: 289607 seconds Block delay: 42229
CryticTester.eVault2_deposit(115792089237316195423570985008687907853269984665640564039457584007913129639935) from: 0x0000000000000000000000000000000000020000 Time delay: 207289 seconds Block delay: 20243
CryticTester.check_0_assets_1() from: 0x0000000000000000000000000000000000010000 Time delay: 111322 seconds Block delay: 23403
CryticTester.liquidate_0_sock_check_with_real_liq() from: 0x0000000000000000000000000000000000030000 Time delay: 254414 seconds Block delay: 59552
CryticTester.eVault_withdraw(826) from: 0x0000000000000000000000000000000000030000 Time delay: 116188 seconds Block delay: 59552
CryticTester.eVault2_withdraw(**********) from: 0x0000000000000000000000000000000000030000 Time delay: 19029 seconds Block delay: 2512
CryticTester.check_liquidation_solvency() from: 0x0000000000000000000000000000000000020000 Time delay: 525476 seconds Block delay: 47075
CryticTester.eVault_repay_shares(4370001) from: 0x0000000000000000000000000000000000020000 Time delay: 305572 seconds Block delay: 50499
CryticTester.canary_max_debt_2() from: 0x0000000000000000000000000000000000010000 Time delay: 390247 seconds Block delay: 561

[2024-07-24 17:41:09.02]  Saved reproducer to echidna/reproducers-unshrunk/387398919255764905.txt
[2024-07-24 17:41:10.04] [status] tests: 4/78, fuzzing: 0/1000000, values: [], cov: 40498, corpus: 0
[2024-07-24 17:41:09.06] [Worker 6] Test check_the_zero_div() falsified!
  Call sequence:
CryticTester.eVault_repay(50394664919911438484384630200319076291709167) from: 0x0000000000000000000000000000000000010000 Time delay: 4177 seconds Block delay: 38100
CryticTester.oracle1_setPrice_coll(340282366920938463463374607431768211451) from: 0x0000000000000000000000000000000000010000 Time delay: 554465 seconds Block delay: 2654
CryticTester.mockIrm_2setInterestRate(4370000) from: 0x0000000000000000000000000000000000020000 Time delay: 448552 seconds Block delay: 15367
CryticTester.check_fees_2() from: 0x0000000000000000000000000000000000020000 Time delay: 419861 seconds Block delay: 38100
CryticTester.eVault_redeem(21407801446738810548986860722330272176273551025278139166817639092186718613165) from: 0x0000000000000000000000000000000000020000 Time delay: 150273 seconds Block delay: 20243
CryticTester.check_redeem_1(**********) from: 0x0000000000000000000000000000000000030000 Time delay: 32767 seconds Block delay: 35200
CryticTester.check_liquidation_with_no_cost_2() from: 0x0000000000000000000000000000000000030000 Time delay: 49735 seconds Block delay: 23722
CryticTester.eVault_deposit(30258572236558666785769082555211476794876657652805804019904662793642091232171) from: 0x0000000000000000000000000000000000010000 Time delay: 136392 seconds Block delay: 5952
CryticTester.victim_deposit_a(4369999) from: 0x0000000000000000000000000000000000030000 Time delay: 305572 seconds Block delay: 1984
CryticTester.check_revert_ta() from: 0x0000000000000000000000000000000000030000 Time delay: 525476 seconds Block delay: 36859
CryticTester.check_depositRebase_2(85533588947605982006142414946493620787539804338651091545267319339621032018963) from: 0x0000000000000000000000000000000000020000 Time delay: 440097 seconds Block delay: 30011
CryticTester.check_touch() from: 0x0000000000000000000000000000000000030000 Time delay: 50417 seconds Block delay: 4896
CryticTester.check_the_zero_div() from: 0x0000000000000000000000000000000000010000 Time delay: 526194 seconds Block delay: 43295

[2024-07-24 17:41:09.07]  Saved reproducer to echidna/reproducers-unshrunk/6446315477214674939.txt
[2024-07-24 17:41:09.67] [Worker 9] Test check_0_assets_2() falsified!
  Call sequence:
CryticTester.check_fees() from: 0x0000000000000000000000000000000000030000 Time delay: 255 seconds Block delay: 8447
CryticTester.eVault_mint(1524785991) from: 0x0000000000000000000000000000000000030000 Time delay: 38059 seconds Block delay: 23978
CryticTester.mockIrm_2setInterestRate(56078056763137345528120083913344288638649066446058766610796692426130667665865) from: 0x0000000000000000000000000000000000010000 Time delay: 127 seconds Block delay: 45852
CryticTester.attacker_pull_debt(19683267871635916278181255656400348548159491414194204231619531585296246) from: 0x0000000000000000000000000000000000030000 Time delay: 482712 seconds Block delay: 24311
CryticTester.check_total_2_no_revert() from: 0x0000000000000000000000000000000000020000 Time delay: 511438 seconds Block delay: 5023
CryticTester.eVault_repay_shares(4370001) from: 0x0000000000000000000000000000000000020000 Time delay: 289103 seconds Block delay: 54155
CryticTester.check_redeem_2(53838008622669226826458340226883018401358628325751096640011795474340745362093) from: 0x0000000000000000000000000000000000030000 Time delay: 111322 seconds Block delay: 35200
CryticTester.mockIrm_2setInterestRate(**********) from: 0x0000000000000000000000000000000000010000 Time delay: 439556 seconds Block delay: 5023
CryticTester.oracle2_setPrice_debt(275349239273020267239483095194177781006) from: 0x0000000000000000000000000000000000020000 Time delay: 482712 seconds Block delay: 1088
CryticTester.check_fees() from: 0x0000000000000000000000000000000000030000 Time delay: 206186 seconds Block delay: 54809
CryticTester.inflation_pre_condition_1() from: 0x0000000000000000000000000000000000010000 Time delay: 404997 seconds Block delay: 59982
CryticTester.doomsday_deposit_e12() from: 0x0000000000000000000000000000000000010000 Time delay: 275394 seconds Block delay: 45819
CryticTester.sock_deposit_b(109162185432039908090367485489052272227856362788200480176645250031112595805344) from: 0x0000000000000000000000000000000000020000 Time delay: 136392 seconds Block delay: 59981
CryticTester.check_acc() from: 0x0000000000000000000000000000000000020000 Time delay: 297507 seconds Block delay: 30011
CryticTester.check_repay_coverage() from: 0x0000000000000000000000000000000000010000 Time delay: 117472 seconds Block delay: 24987
CryticTester.check_depositRebase_2(43577529584110373268496982486348829938960510974469326231628762202720317516794) from: 0x0000000000000000000000000000000000030000 Time delay: 492049 seconds Block delay: 4896
CryticTester.testTheOverflow() from: 0x0000000000000000000000000000000000030000 Time delay: 156190 seconds Block delay: 34720
CryticTester.liquidate_attacker(4370001,1524785991) from: 0x0000000000000000000000000000000000010000 Time delay: 254414 seconds Block delay: 30784
CryticTester.check_0_shares_2() from: 0x0000000000000000000000000000000000020000 Time delay: 303345 seconds Block delay: 52885
CryticTester.check_lost_yield_canary() from: 0x0000000000000000000000000000000000020000 Time delay: 303345 seconds Block delay: 22909
CryticTester.check_depositRebase_1_mid(8590372960380611629297833876341922554553446112671746707255) from: 0x0000000000000000000000000000000000030000 Time delay: 16802 seconds Block delay: 35200
CryticTester.eVault2_withdraw(4370000) from: 0x0000000000000000000000000000000000030000 Time delay: 292304 seconds Block delay: 47075
CryticTester.oracle2_setPrice_coll(657) from: 0x0000000000000000000000000000000000010000 Time delay: 404997 seconds Block delay: 11349
CryticTester.eVault_repay_shares(499) from: 0x0000000000000000000000000000000000030000 Time delay: 292304 seconds Block delay: 42101
CryticTester.liquidate_sock_clamped() from: 0x0000000000000000000000000000000000030000 Time delay: 150273 seconds Block delay: 15005
CryticTester.sock_pull_debt(115792089237316195423570985008687907853269984665640564039457584007913129639935) from: 0x0000000000000000000000000000000000030000 Time delay: 150273 seconds Block delay: 6721
CryticTester.check_depositRebase_2(337) from: 0x0000000000000000000000000000000000030000 Time delay: 275394 seconds Block delay: 20109
CryticTester.check_depositRebase_1_crit(92451182756452412003166644847359900884125623944737975871773275635168070977574) from: 0x0000000000000000000000000000000000020000 Time delay: 485769 seconds Block delay: 3661
CryticTester.doomsday_deposit_e12() from: 0x0000000000000000000000000000000000030000 Time delay: 209930 seconds Block delay: 5237
CryticTester.liquidate_0_sock_check() from: 0x0000000000000000000000000000000000020000 Time delay: 130099 seconds Block delay: 34720
CryticTester.check_liquidation_with_no_cost() from: 0x0000000000000000000000000000000000030000 Time delay: 67960 seconds Block delay: 36517
CryticTester.inflation_pre_condition_1() from: 0x0000000000000000000000000000000000010000 Time delay: 554465 seconds Block delay: 57795
CryticTester.eVault_repay_shares_to_sock(107005319350137610787823225338196724440087173301416652651508530976179873978802) from: 0x0000000000000000000000000000000000020000 Time delay: 487078 seconds Block delay: 45819
CryticTester.check_revert_ts() from: 0x0000000000000000000000000000000000020000 Time delay: 207289 seconds Block delay: 42595
CryticTester.check_depositRebase_2(97427580994000894580954359995165810972271912397833665683279550276408208316256) from: 0x0000000000000000000000000000000000020000 Time delay: 376096 seconds Block delay: 60248
CryticTester.sock_deposit_a(589) from: 0x0000000000000000000000000000000000020000 Time delay: 404997 seconds Block delay: 255
CryticTester.sock_deposit_a(1524785991) from: 0x0000000000000000000000000000000000020000 Time delay: 31594 seconds Block delay: 18429
CryticTester.liquidate_0_attacker_check_with_real_liq() from: 0x0000000000000000000000000000000000020000 Time delay: 405856 seconds Block delay: 53349
CryticTester.check_lost_debt_canary() from: 0x0000000000000000000000000000000000030000 Time delay: 289607 seconds Block delay: 12155
CryticTester.victim_deposit_a(1524785991) from: 0x0000000000000000000000000000000000030000 Time delay: 588255 seconds Block delay: 2497
CryticTester.check_revert_ts() from: 0x0000000000000000000000000000000000020000 Time delay: 233380 seconds Block delay: 15367
CryticTester.check_0_shares_2() from: 0x0000000000000000000000000000000000030000 Time delay: 447588 seconds Block delay: 24987
CryticTester.eVault_repay_shares_to_sock(366) from: 0x0000000000000000000000000000000000030000 Time delay: 82670 seconds Block delay: 32
CryticTester.check_0_assets_1() from: 0x0000000000000000000000000000000000010000 Time delay: 448552 seconds Block delay: 53425
CryticTester.check_fees_2() from: 0x0000000000000000000000000000000000020000 Time delay: 522178 seconds Block delay: 6234
CryticTester.check_lost_yield_canary() from: 0x0000000000000000000000000000000000010000 Time delay: 379552 seconds Block delay: 32147
CryticTester.sock_deposit_a(32440085270772964407780291101175792071921950863806886885435047680235921205493) from: 0x0000000000000000000000000000000000010000 Time delay: 492335 seconds Block delay: 35248
CryticTester.testTheOverflow() from: 0x0000000000000000000000000000000000010000 Time delay: 379552 seconds Block delay: 8447
CryticTester.eVault2_deposit(4369999) from: 0x0000000000000000000000000000000000020000 Time delay: 82671 seconds Block delay: 5140
CryticTester.check_revert_ta_2() from: 0x0000000000000000000000000000000000030000 Time delay: 271957 seconds Block delay: 9215
CryticTester.check_cash_2() from: 0x0000000000000000000000000000000000010000 Time delay: 275394 seconds Block delay: 60248
CryticTester.doomsday_deposit_e18() from: 0x0000000000000000000000000000000000010000 Time delay: 209930 seconds Block delay: 9966
CryticTester.check_the_zero_div() from: 0x0000000000000000000000000000000000020000 Time delay: 225906 seconds Block delay: 23722
CryticTester.check_0_assets_2() from: 0x0000000000000000000000000000000000010000 Time delay: 318197 seconds Block delay: 47075

[2024-07-24 17:41:09.70]  Saved reproducer to echidna/reproducers-unshrunk/3311337584682049167.txt
[2024-07-24 17:41:10.46] [Worker 9] New coverage: 40705 instr, 15 contracts, 1 seqs in corpus
[2024-07-24 17:41:10.56]  Saved reproducer to echidna/coverage/6896404492055341669.txt
[2024-07-24 17:41:10.59] [Worker 8] New coverage: 41517 instr, 15 contracts, 2 seqs in corpus
[2024-07-24 17:41:10.64] [Worker 2] New coverage: 41517 instr, 15 contracts, 3 seqs in corpus
[2024-07-24 17:41:10.68]  Saved reproducer to echidna/coverage/9179219124450646949.txt
[2024-07-24 17:41:10.74] [Worker 0] New coverage: 41526 instr, 15 contracts, 4 seqs in corpus
[2024-07-24 17:41:10.77]  Saved reproducer to echidna/coverage/4218633726097536617.txt
[2024-07-24 17:41:10.80] [Worker 5] New coverage: 41630 instr, 15 contracts, 5 seqs in corpus
[2024-07-24 17:41:13.04] [status] tests: 8/78, fuzzing: 1212/1000000, values: [], cov: 41960, corpus: 11
[2024-07-24 17:41:16.05] [status] tests: 8/78, fuzzing: 2351/1000000, values: [], cov: 42047, corpus: 16
[2024-07-24 17:41:10.82] [Worker 6] Test check_repay_coverage() falsified!
  Call sequence:
CryticTester.eVault_repay(50394664919911438484384630200319076291709167) from: 0x0000000000000000000000000000000000010000 Time delay: 4177 seconds Block delay: 38100
CryticTester.oracle1_setPrice_coll(340282366920938463463374607431768211451) from: 0x0000000000000000000000000000000000010000 Time delay: 554465 seconds Block delay: 2654
CryticTester.mockIrm_2setInterestRate(4370000) from: 0x0000000000000000000000000000000000020000 Time delay: 448552 seconds Block delay: 15367
CryticTester.check_fees_2() from: 0x0000000000000000000000000000000000020000 Time delay: 419861 seconds Block delay: 38100
CryticTester.eVault_redeem(21407801446738810548986860722330272176273551025278139166817639092186718613165) from: 0x0000000000000000000000000000000000020000 Time delay: 150273 seconds Block delay: 20243
CryticTester.check_redeem_1(**********) from: 0x0000000000000000000000000000000000030000 Time delay: 32767 seconds Block delay: 35200
CryticTester.check_liquidation_with_no_cost_2() from: 0x0000000000000000000000000000000000030000 Time delay: 49735 seconds Block delay: 23722
CryticTester.eVault_deposit(30258572236558666785769082555211476794876657652805804019904662793642091232171) from: 0x0000000000000000000000000000000000010000 Time delay: 136392 seconds Block delay: 5952
CryticTester.victim_deposit_a(4369999) from: 0x0000000000000000000000000000000000030000 Time delay: 305572 seconds Block delay: 1984
CryticTester.check_revert_ta() from: 0x0000000000000000000000000000000000030000 Time delay: 525476 seconds Block delay: 36859
CryticTester.check_depositRebase_2(85533588947605982006142414946493620787539804338651091545267319339621032018963) from: 0x0000000000000000000000000000000000020000 Time delay: 440097 seconds Block delay: 30011
CryticTester.check_touch() from: 0x0000000000000000000000000000000000030000 Time delay: 50417 seconds Block delay: 4896
CryticTester.check_the_zero_div() from: 0x0000000000000000000000000000000000010000 Time delay: 526194 seconds Block delay: 43295
CryticTester.check_liquidation_with_no_cost() from: 0x0000000000000000000000000000000000010000 Time delay: 4177 seconds Block delay: 561
CryticTester.check_touch_2() from: 0x0000000000000000000000000000000000010000 Time delay: 297507 seconds Block delay: 38100
CryticTester.eVault2_redeem(60493880108352244393118407992904623075660620875290849634176439740567909743239) from: 0x0000000000000000000000000000000000010000 Time delay: 419861 seconds Block delay: 23092
CryticTester.check_depositRebase_2_mid(104246478332640834046891482204454135374245623244283751879096336761743905406455) from: 0x0000000000000000000000000000000000030000 Time delay: 478623 seconds Block delay: 12338
CryticTester.eVault_borrow(1524785991) from: 0x0000000000000000000000000000000000010000 Time delay: 434894 seconds Block delay: 669
CryticTester.check_liquidation_with_no_cost_2() from: 0x0000000000000000000000000000000000030000 Time delay: 166184 seconds Block delay: 255
CryticTester.check_depositRebase_1_mid(115792089237316195423570985008687907853269984665640564039457584007913129639935) from: 0x0000000000000000000000000000000000010000 Time delay: 209930 seconds Block delay: 30256
CryticTester.liquidate_sock_clamped() from: 0x0000000000000000000000000000000000030000 Time delay: 100835 seconds Block delay: 36859
CryticTester.eVault_repay_sock(59012293805646393356619293014887325610981696395566302396641753883813819111405) from: 0x0000000000000000000000000000000000020000 Time delay: 156190 seconds Block delay: 1362
CryticTester.check_lost_debt_canary() from: 0x0000000000000000000000000000000000020000 Time delay: 136392 seconds Block delay: 5053
CryticTester.eVault_repay_shares(4370000) from: 0x0000000000000000000000000000000000020000 Time delay: 526194 seconds Block delay: 5053
CryticTester.oracle1_setPrice_debt(4370001) from: 0x0000000000000000000000000000000000020000 Time delay: 495690 seconds Block delay: 23403
CryticTester.check_redeem_2(7112480776417349510524172169113979432860554871313746556135791472474518304040) from: 0x0000000000000000000000000000000000020000 Time delay: 289103 seconds Block delay: 38100
CryticTester.liquidate_sock(5774704401807236836629785108865549933917358725801124968575920202712778656439,115792089237316195423570985008687907853269984665640564039457584007913129639935) from: 0x0000000000000000000000000000000000010000 Time delay: 271957 seconds Block delay: 53451
CryticTester.mockIrm_2setInterestRate(**********) from: 0x0000000000000000000000000000000000030000 Time delay: 412373 seconds Block delay: 20243
CryticTester.check_the_zero_div() from: 0x0000000000000000000000000000000000020000 Time delay: 390247 seconds Block delay: 46422
CryticTester.check_depositRebase_1_crit(4370000) from: 0x0000000000000000000000000000000000030000 Time delay: 512439 seconds Block delay: 36859
CryticTester.eVault_repay_shares_to_sock(3494308688951782927129227) from: 0x0000000000000000000000000000000000030000 Time delay: 65535 seconds Block delay: 4986
CryticTester.sock_borrow_b(2326154) from: 0x0000000000000000000000000000000000030000 Time delay: 547623 seconds Block delay: 58783
CryticTester.eVault2_redeem(1524785991) from: 0x0000000000000000000000000000000000010000 Time delay: 419861 seconds Block delay: 1088
CryticTester.sock_borrow_b(19041158465903075437222003327110691319451893922190067937591819594846791577973) from: 0x0000000000000000000000000000000000030000 Time delay: 300110 seconds Block delay: 9966
CryticTester.check_cash() from: 0x0000000000000000000000000000000000030000 Time delay: 112444 seconds Block delay: 11826
CryticTester.liquidate_0_sock_check_with_real_liq() from: 0x0000000000000000000000000000000000030000 Time delay: 471156 seconds Block delay: 1123
CryticTester.check_liquidation_with_no_cost_2() from: 0x0000000000000000000000000000000000030000 Time delay: 112444 seconds Block delay: 5023
CryticTester.check_touch() from: 0x0000000000000000000000000000000000020000 Time delay: 65535 seconds Block delay: 11905
CryticTester.check_0_shares_2() from: 0x0000000000000000000000000000000000020000 Time delay: 490446 seconds Block delay: 54155
CryticTester.liquidate_0_attacker_check_with_real_liq() from: 0x0000000000000000000000000000000000020000 Time delay: 522255 seconds Block delay: 2497
CryticTester.mockIrm_2setInterestRate(59247616941666394980103305628816578933365404504668089763469660930773581087802) from: 0x0000000000000000000000000000000000030000 Time delay: 404997 seconds Block delay: 19933
CryticTester.check_cash_2() from: 0x0000000000000000000000000000000000030000 Time delay: 415353 seconds Block delay: 25190
CryticTester.check_depositRebase_1(1524785993) from: 0x0000000000000000000000000000000000010000 Time delay: 117472 seconds Block delay: 49221
CryticTester.check_fees_2() from: 0x0000000000000000000000000000000000010000 Time delay: 115085 seconds Block delay: 54155
CryticTester.check_depositRebase_1_crit(23603531382934108264632473212622781210212430049779939234646411254412358619438) from: 0x0000000000000000000000000000000000020000 Time delay: 434894 seconds Block delay: 30304
CryticTester.check_acc() from: 0x0000000000000000000000000000000000030000 Time delay: 150273 seconds Block delay: 53011
CryticTester.check_acc() from: 0x0000000000000000000000000000000000020000 Time delay: 436727 seconds Block delay: 60267
CryticTester.check_revert_ta() from: 0x0000000000000000000000000000000000020000 Time delay: 429706 seconds Block delay: 53349
CryticTester.eVault_repay_sock(**********) from: 0x0000000000000000000000000000000000020000 Time delay: 209930 seconds Block delay: 23978
CryticTester.liquidate_attacker(28073212853726619682181779007584051342930941912269066103132001275090623823522,21464735611904121603643112855346601748589836942169965002862012991608442980928) from: 0x0000000000000000000000000000000000020000 Time delay: 100835 seconds Block delay: 53349
CryticTester.check_lost_yield_canary() from: 0x0000000000000000000000000000000000030000 Time delay: 415353 seconds Block delay: 60267
CryticTester.check_0_shares_2() from: 0x0000000000000000000000000000000000020000 Time delay: 440097 seconds Block delay: 46422
CryticTester.eVault_mint(67804515635023260364528535288846982843035662288546540542204344254191541732035) from: 0x0000000000000000000000000000000000030000 Time delay: 322374 seconds Block delay: 57086
CryticTester.doomsday_deposit_e6() from: 0x0000000000000000000000000000000000030000 Time delay: 390247 seconds Block delay: 30042
CryticTester.check_lost_yield_canary() from: 0x0000000000000000000000000000000000020000 Time delay: 254414 seconds Block delay: 47075
CryticTester.check_redeem_2(16615658882954774627223849281671947175662027233424417104122032826437801139269) from: 0x0000000000000000000000000000000000020000 Time delay: 195111 seconds Block delay: 42101
CryticTester.sock_borrow_b(4370001) from: 0x0000000000000000000000000000000000010000 Time delay: 407328 seconds Block delay: 22699
CryticTester.check_0_assets_1() from: 0x0000000000000000000000000000000000020000 Time delay: 419861 seconds Block delay: 60054
CryticTester.check_liquidation_solvency() from: 0x0000000000000000000000000000000000030000 Time delay: 405856 seconds Block delay: 23722
CryticTester.doomsday_deposit_e18() from: 0x0000000000000000000000000000000000010000 Time delay: 490446 seconds Block delay: 54809
CryticTester.check_revert_ta_2() from: 0x0000000000000000000000000000000000020000 Time delay: 322374 seconds Block delay: 5751
CryticTester.check_total_2_no_revert() from: 0x0000000000000000000000000000000000030000 Time delay: 198598 seconds Block delay: 38100
CryticTester.liquidate_0_sock_check() from: 0x0000000000000000000000000000000000020000 Time delay: 318197 seconds Block delay: 9966
CryticTester.mockIrm_2setInterestRate(84087439307933234834037910832555142821755968819359610354390280237536611598480) from: 0x0000000000000000000000000000000000010000 Time delay: 292304 seconds Block delay: 42229
CryticTester.liquidate_attacker_clamped() from: 0x0000000000000000000000000000000000020000 Time delay: 519847 seconds Block delay: 22699
CryticTester.eVault_borrow(**********) from: 0x0000000000000000000000000000000000010000 Time delay: 100835 seconds Block delay: 31232
CryticTester.sock_deposit_b(112239606528457014940116974137079482010982688129041635691360826520898292065789) from: 0x0000000000000000000000000000000000030000 Time delay: 322374 seconds Block delay: 5053
CryticTester.check_the_overflow() from: 0x0000000000000000000000000000000000020000 Time delay: 38059 seconds Block delay: 5054
CryticTester.sock_borrow_b(4370001) from: 0x0000000000000000000000000000000000010000 Time delay: 478623 seconds Block delay: 30784
CryticTester.victim_deposit_b(32839956490484887010092014446608273536338831118724603667326624589910825156978) from: 0x0000000000000000000000000000000000010000 Time delay: 444463 seconds Block delay: 30011
CryticTester.check_the_zero_div() from: 0x0000000000000000000000000000000000020000 Time delay: 82671 seconds Block delay: 46422
CryticTester.eVault2_redeem(60) from: 0x0000000000000000000000000000000000010000 Time delay: 38059 seconds Block delay: 20243
CryticTester.liquidate_attacker(21129447664458094257851793152611192940577614262557334901488871889858605594269,16490268914655772855870755103071309368425455791486288) from: 0x0000000000000000000000000000000000030000 Time delay: 166218 seconds Block delay: 7323
CryticTester.liquidate_sock(1637138601712189908811673979593979677212063677063858049059707311143205358763,4370000) from: 0x0000000000000000000000000000000000020000 Time delay: 31594 seconds Block delay: 15729
CryticTester.check_liquidation_with_no_cost() from: 0x0000000000000000000000000000000000020000 Time delay: 4177 seconds Block delay: 4896
CryticTester.doomsday_deposit_e12() from: 0x0000000000000000000000000000000000020000 Time delay: 33605 seconds Block delay: 22909
CryticTester.check_revert_ts() from: 0x0000000000000000000000000000000000030000 Time delay: 66543 seconds Block delay: 49415
CryticTester.testTheDivisionByZero() from: 0x0000000000000000000000000000000000010000 Time delay: 207289 seconds Block delay: 31232
CryticTester.check_debt() from: 0x0000000000000000000000000000000000020000 Time delay: 133533 seconds Block delay: 11905
CryticTester.check_acc() from: 0x0000000000000000000000000000000000020000 Time delay: 277232 seconds Block delay: 35448
CryticTester.check_depositRebase_1_mid(24095722744297382159912413568663744812081775228597111616669267437472655993852) from: 0x0000000000000000000000000000000000010000 Time delay: 434894 seconds Block delay: 47075
CryticTester.eVault_repay_shares(41095642610738590595297134868594920175045564943272593670) from: 0x0000000000000000000000000000000000010000 Time delay: 166184 seconds Block delay: 47075
CryticTester.testTheDivisionByZero() from: 0x0000000000000000000000000000000000010000 Time delay: 198598 seconds Block delay: 2512
CryticTester.sock_pull_debt(935) from: 0x0000000000000000000000000000000000020000 Time delay: 103409 seconds Block delay: 15368
CryticTester.check_fees() from: 0x0000000000000000000000000000000000030000 Time delay: 444463 seconds Block delay: 53166
CryticTester.liquidate_attacker_clamped() from: 0x0000000000000000000000000000000000030000 Time delay: 448552 seconds Block delay: 23407
CryticTester.check_revert_ts_2() from: 0x0000000000000000000000000000000000010000 Time delay: 463587 seconds Block delay: 45819
CryticTester.check_liquidation_solvency() from: 0x0000000000000000000000000000000000030000 Time delay: 33605 seconds Block delay: 4462
CryticTester.check_depositRebase_1(93482653811182435446504513881070605967598228245715387845490319898236149529207) from: 0x0000000000000000000000000000000000010000 Time delay: 400981 seconds Block delay: 45819
CryticTester.eVault2_mint(1524785991) from: 0x0000000000000000000000000000000000030000 Time delay: 16802 seconds Block delay: 2512
CryticTester.eVault_withdraw(23183100860978029081591565221777676226051256245168933565893660044627457026866) from: 0x0000000000000000000000000000000000010000 Time delay: 360624 seconds Block delay: 60054
CryticTester.oracle1_setPrice_debt(1593692384) from: 0x0000000000000000000000000000000000020000 Time delay: 50417 seconds Block delay: 53166
CryticTester.liquidate_sock(95249787485773596135910620381418282817099317231150574613890730481568943378740,62548734636805365144467723928026881525540462937523525921018382716025437927247) from: 0x0000000000000000000000000000000000020000 Time delay: 67960 seconds Block delay: 23653
CryticTester.testTheOverflow() from: 0x0000000000000000000000000000000000010000 Time delay: 588255 seconds Block delay: 60364
CryticTester.check_revert_ts_2() from: 0x0000000000000000000000000000000000010000 Time delay: 490448 seconds Block delay: 4462
CryticTester.check_repay_coverage() from: 0x0000000000000000000000000000000000010000 Time delay: 33605 seconds Block delay: 59982

[2024-07-24 17:41:10.84] [Worker 3] New coverage: 41630 instr, 15 contracts, 6 seqs in corpus
[2024-07-24 17:41:10.86]  Saved reproducer to echidna/coverage/6987548921478192332.txt
[2024-07-24 17:41:10.95]  Saved reproducer to echidna/coverage/1167218502807422080.txt
[2024-07-24 17:41:10.96] [Worker 6] New coverage: 41630 instr, 15 contracts, 7 seqs in corpus
[2024-07-24 17:41:11.04] [Worker 7] New coverage: 41630 instr, 15 contracts, 8 seqs in corpus
[2024-07-24 17:41:11.05]  Saved reproducer to echidna/reproducers-unshrunk/5270261821784168666.txt
[2024-07-24 17:41:11.14]  Saved reproducer to echidna/coverage/7445957210553654495.txt
[2024-07-24 17:41:11.24]  Saved reproducer to echidna/coverage/8548941326499075781.txt
[2024-07-24 17:41:11.32]  Saved reproducer to echidna/coverage/184235250700962877.txt
[2024-07-24 17:41:11.39] [Worker 4] New coverage: 41673 instr, 15 contracts, 9 seqs in corpus
[2024-07-24 17:41:11.46] [Worker 1] New coverage: 41673 instr, 15 contracts, 10 seqs in corpus
[2024-07-24 17:41:11.48]  Saved reproducer to echidna/coverage/2833078581479465451.txt
[2024-07-24 17:41:11.57]  Saved reproducer to echidna/coverage/5870437985485609593.txt
[2024-07-24 17:41:12.55] [Worker 1] Test inflation_pre_condition_2() falsified!
  Call sequence:
CryticTester.sock_pull_debt(64272264246531907025687649480142021475725454364882195750829610328896307605421) from: 0x0000000000000000000000000000000000020000 Time delay: 195123 seconds Block delay: 1984
CryticTester.inflation_pre_condition_1() from: 0x0000000000000000000000000000000000020000 Time delay: 207289 seconds Block delay: 19933
CryticTester.mockIrm_1setInterestRate(**********) from: 0x0000000000000000000000000000000000020000 Time delay: 116188 seconds Block delay: 47075
CryticTester.canary_max_debt() from: 0x0000000000000000000000000000000000030000 Time delay: 526194 seconds Block delay: 34841
CryticTester.canary_max_debt() from: 0x0000000000000000000000000000000000030000 Time delay: 318197 seconds Block delay: 49415
CryticTester.check_cash_2() from: 0x0000000000000000000000000000000000020000 Time delay: 297507 seconds Block delay: 40520
CryticTester.check_repay_coverage() from: 0x0000000000000000000000000000000000010000 Time delay: 361136 seconds Block delay: 34272
CryticTester.canary_max_debt() from: 0x0000000000000000000000000000000000010000 Time delay: 277232 seconds Block delay: 2497
CryticTester.check_depositRebase_1_mid(4370000) from: 0x0000000000000000000000000000000000010000 Time delay: 82670 seconds Block delay: 9966
CryticTester.check_acc() from: 0x0000000000000000000000000000000000010000 Time delay: 198598 seconds Block delay: 30842
CryticTester.attacker_pull_debt(4370000) from: 0x0000000000000000000000000000000000030000 Time delay: 554465 seconds Block delay: 53011
CryticTester.liquidate_0_attacker_check() from: 0x0000000000000000000000000000000000020000 Time delay: 361136 seconds Block delay: 800
CryticTester.check_depositRebase_2_crit(22634488943466889299252711328960504468829418200783965750424892319970386462360) from: 0x0000000000000000000000000000000000030000 Time delay: 24867 seconds Block delay: 34272
CryticTester.sock_pull_debt(64272264246531907025687649480142021475725454364882195750829610328896307605421) from: 0x0000000000000000000000000000000000010000 Time delay: 135921 seconds Block delay: 53011
CryticTester.doomsday_deposit_e6() from: 0x0000000000000000000000000000000000010000 Time delay: 490448 seconds Block delay: 53678
CryticTester.check_fees_2() from: 0x0000000000000000000000000000000000010000 Time delay: 127251 seconds Block delay: 45852
CryticTester.oracle2_setPrice_coll(**********) from: 0x0000000000000000000000000000000000030000 Time delay: 332369 seconds Block delay: 5952
CryticTester.eVault_redeem(1524785993) from: 0x0000000000000000000000000000000000030000 Time delay: 24867 seconds Block delay: 24987
CryticTester.liquidate_sock(4370000,48076062579388433476848881819620292340231758239566965453820241180602650195643) from: 0x0000000000000000000000000000000000020000 Time delay: 318197 seconds Block delay: 23722
CryticTester.sock_deposit_b(4369999) from: 0x0000000000000000000000000000000000030000 Time delay: 322247 seconds Block delay: 16089
CryticTester.eVault_mint(4370001) from: 0x0000000000000000000000000000000000030000 Time delay: 289103 seconds Block delay: 58783
CryticTester.sock_deposit_b(2773958662898725231853837332551234139906038306865900435754511245482176994765) from: 0x0000000000000000000000000000000000010000 Time delay: 90154 seconds Block delay: 12338
CryticTester.victim_deposit_a(115792089237316195423570985008687907853269984665640564039457584007913129639935) from: 0x0000000000000000000000000000000000030000 Time delay: 172101 seconds Block delay: 20840
CryticTester.eVault2_deposit(90882211300155123712369098872294867237128504323095137402077272126817656884723) from: 0x0000000000000000000000000000000000030000 Time delay: 195123 seconds Block delay: 11826
CryticTester.eVault_withdraw(115792089237316195423570985008687907853269984665640564039457584007913129639934) from: 0x0000000000000000000000000000000000020000 Time delay: 526194 seconds Block delay: 30011
CryticTester.check_cash_2() from: 0x0000000000000000000000000000000000020000 Time delay: 209930 seconds Block delay: 24987
CryticTester.eVault2_withdraw(104925695768983750340361249025271443960054455380176566915696471641610664696240) from: 0x0000000000000000000000000000000000010000 Time delay: 305572 seconds Block delay: 1123
CryticTester.liquidate_sock_clamped() from: 0x0000000000000000000000000000000000010000 Time delay: 73040 seconds Block delay: 19933
CryticTester.eVault_repay(934) from: 0x0000000000000000000000000000000000010000 Time delay: 338920 seconds Block delay: 54155
CryticTester.liquidate_0_sock_check_with_real_liq() from: 0x0000000000000000000000000000000000030000 Time delay: 82671 seconds Block delay: 12493
CryticTester.liquidate_sock(4370000,115792089237316195423570985008687907853269984665640564039457584007913129639931) from: 0x0000000000000000000000000000000000020000 Time delay: 358061 seconds Block delay: 60364
CryticTester.liquidate_0_attacker_check_with_real_liq() from: 0x0000000000000000000000000000000000010000 Time delay: 512439 seconds Block delay: 54809
CryticTester.sock_deposit_a(8497928236143637118487454809949120277053593495782025768287409455679616169313) from: 0x0000000000000000000000000000000000010000 Time delay: 412373 seconds Block delay: 38100
CryticTester.check_0_assets_1() from: 0x0000000000000000000000000000000000010000 Time delay: 67960 seconds Block delay: 32767
CryticTester.check_depositRebase_1(97219360896851757188100626029492771258912295113708459306110344610955906279906) from: 0x0000000000000000000000000000000000010000 Time delay: 448552 seconds Block delay: 12338
CryticTester.inflation_pre_condition_1() from: 0x0000000000000000000000000000000000030000 Time delay: 522178 seconds Block delay: 34720
CryticTester.liquidate_sock_clamped() from: 0x0000000000000000000000000000000000020000 Time delay: 379552 seconds Block delay: 38100
CryticTester.attacker_pull_debt(4370001) from: 0x0000000000000000000000000000000000020000 Time delay: 156190 seconds Block delay: 1123
CryticTester.eVault2_redeem(93361471945982015618036620840420330700332582826235962791529641426062347942040) from: 0x0000000000000000000000000000000000010000 Time delay: 360624 seconds Block delay: 4462
CryticTester.liquidate_sock(**********,4370000) from: 0x0000000000000000000000000000000000030000 Time delay: 156190 seconds Block delay: 4908
CryticTester.check_cash_2() from: 0x0000000000000000000000000000000000020000 Time delay: 82671 seconds Block delay: 33357
CryticTester.check_depositRebase_1(15095717422837617869303820885246513144530448367033268010511149453280953335736) from: 0x0000000000000000000000000000000000030000 Time delay: 435806 seconds Block delay: 11349
CryticTester.liquidate_sock_clamped() from: 0x0000000000000000000000000000000000030000 Time delay: 390247 seconds Block delay: 9966
CryticTester.eVault_redeem(52806318083070548715767063439315892350712624190775329021309163108880073926503) from: 0x0000000000000000000000000000000000020000 Time delay: 4177 seconds Block delay: 60364
CryticTester.check_depositRebase_1_crit(4370001) from: 0x0000000000000000000000000000000000010000 Time delay: 525476 seconds Block delay: 2497
CryticTester.check_acc() from: 0x0000000000000000000000000000000000020000 Time delay: 415353 seconds Block delay: 53166
CryticTester.sock_deposit_b(0) from: 0x0000000000000000000000000000000000010000 Time delay: 448552 seconds Block delay: 9920
CryticTester.mockIrm_2setInterestRate(66279549302271863263181239770383431033893841722854594486095932457529725702842) from: 0x0000000000000000000000000000000000030000 Time delay: 361136 seconds Block delay: 22909
CryticTester.sock_borrow_b(3137985303684348884730241372204500199359629260067751212444858229261146708812) from: 0x0000000000000000000000000000000000030000 Time delay: 419861 seconds Block delay: 30011
CryticTester.doomsday_deposit_e18() from: 0x0000000000000000000000000000000000020000 Time delay: 482712 seconds Block delay: 11905
CryticTester.check_depositRebase_2(4370001) from: 0x0000000000000000000000000000000000030000 Time delay: 305572 seconds Block delay: 23653
CryticTester.check_cash_2() from: 0x0000000000000000000000000000000000020000 Time delay: 463587 seconds Block delay: 5952
CryticTester.inflation_pre_condition_1() from: 0x0000000000000000000000000000000000020000 Time delay: 415353 seconds Block delay: 30042
CryticTester.victim_deposit_a(103) from: 0x0000000000000000000000000000000000030000 Time delay: 45142 seconds Block delay: 4462
CryticTester.check_touch_2() from: 0x0000000000000000000000000000000000030000 Time delay: 338920 seconds Block delay: 53011
CryticTester.check_liquidation_with_no_cost_2() from: 0x0000000000000000000000000000000000020000 Time delay: 361136 seconds Block delay: 53562
CryticTester.liquidate_0_sock_check() from: 0x0000000000000000000000000000000000030000 Time delay: 490446 seconds Block delay: 23722
CryticTester.eVault2_deposit(37) from: 0x0000000000000000000000000000000000030000 Time delay: 136392 seconds Block delay: 15367
CryticTester.inflation_pre_condition_2() from: 0x0000000000000000000000000000000000010000 Time delay: 376096 seconds Block delay: 561

[2024-07-24 17:41:12.59]  Saved reproducer to echidna/reproducers-unshrunk/2727601022179316682.txt
[2024-07-24 17:41:19.05] [status] tests: 8/78, fuzzing: 3209/1000000, values: [], cov: 42077, corpus: 19
[2024-07-24 17:41:12.79] [Worker 1] Test check_liquidation_solvency() falsified!
  Call sequence:
CryticTester.sock_pull_debt(64272264246531907025687649480142021475725454364882195750829610328896307605421) from: 0x0000000000000000000000000000000000020000 Time delay: 195123 seconds Block delay: 1984
CryticTester.inflation_pre_condition_1() from: 0x0000000000000000000000000000000000020000 Time delay: 207289 seconds Block delay: 19933
CryticTester.mockIrm_1setInterestRate(**********) from: 0x0000000000000000000000000000000000020000 Time delay: 116188 seconds Block delay: 47075
CryticTester.canary_max_debt() from: 0x0000000000000000000000000000000000030000 Time delay: 526194 seconds Block delay: 34841
CryticTester.canary_max_debt() from: 0x0000000000000000000000000000000000030000 Time delay: 318197 seconds Block delay: 49415
CryticTester.check_cash_2() from: 0x0000000000000000000000000000000000020000 Time delay: 297507 seconds Block delay: 40520
CryticTester.check_repay_coverage() from: 0x0000000000000000000000000000000000010000 Time delay: 361136 seconds Block delay: 34272
CryticTester.canary_max_debt() from: 0x0000000000000000000000000000000000010000 Time delay: 277232 seconds Block delay: 2497
CryticTester.check_depositRebase_1_mid(4370000) from: 0x0000000000000000000000000000000000010000 Time delay: 82670 seconds Block delay: 9966
CryticTester.check_acc() from: 0x0000000000000000000000000000000000010000 Time delay: 198598 seconds Block delay: 30842
CryticTester.attacker_pull_debt(4370000) from: 0x0000000000000000000000000000000000030000 Time delay: 554465 seconds Block delay: 53011
CryticTester.liquidate_0_attacker_check() from: 0x0000000000000000000000000000000000020000 Time delay: 361136 seconds Block delay: 800
CryticTester.check_depositRebase_2_crit(22634488943466889299252711328960504468829418200783965750424892319970386462360) from: 0x0000000000000000000000000000000000030000 Time delay: 24867 seconds Block delay: 34272
CryticTester.sock_pull_debt(64272264246531907025687649480142021475725454364882195750829610328896307605421) from: 0x0000000000000000000000000000000000010000 Time delay: 135921 seconds Block delay: 53011
CryticTester.doomsday_deposit_e6() from: 0x0000000000000000000000000000000000010000 Time delay: 490448 seconds Block delay: 53678
CryticTester.check_fees_2() from: 0x0000000000000000000000000000000000010000 Time delay: 127251 seconds Block delay: 45852
CryticTester.oracle2_setPrice_coll(**********) from: 0x0000000000000000000000000000000000030000 Time delay: 332369 seconds Block delay: 5952
CryticTester.eVault_redeem(1524785993) from: 0x0000000000000000000000000000000000030000 Time delay: 24867 seconds Block delay: 24987
CryticTester.liquidate_sock(4370000,48076062579388433476848881819620292340231758239566965453820241180602650195643) from: 0x0000000000000000000000000000000000020000 Time delay: 318197 seconds Block delay: 23722
CryticTester.sock_deposit_b(4369999) from: 0x0000000000000000000000000000000000030000 Time delay: 322247 seconds Block delay: 16089
CryticTester.eVault_mint(4370001) from: 0x0000000000000000000000000000000000030000 Time delay: 289103 seconds Block delay: 58783
CryticTester.sock_deposit_b(2773958662898725231853837332551234139906038306865900435754511245482176994765) from: 0x0000000000000000000000000000000000010000 Time delay: 90154 seconds Block delay: 12338
CryticTester.victim_deposit_a(115792089237316195423570985008687907853269984665640564039457584007913129639935) from: 0x0000000000000000000000000000000000030000 Time delay: 172101 seconds Block delay: 20840
CryticTester.eVault2_deposit(90882211300155123712369098872294867237128504323095137402077272126817656884723) from: 0x0000000000000000000000000000000000030000 Time delay: 195123 seconds Block delay: 11826
CryticTester.eVault_withdraw(115792089237316195423570985008687907853269984665640564039457584007913129639934) from: 0x0000000000000000000000000000000000020000 Time delay: 526194 seconds Block delay: 30011
CryticTester.check_cash_2() from: 0x0000000000000000000000000000000000020000 Time delay: 209930 seconds Block delay: 24987
CryticTester.eVault2_withdraw(104925695768983750340361249025271443960054455380176566915696471641610664696240) from: 0x0000000000000000000000000000000000010000 Time delay: 305572 seconds Block delay: 1123
CryticTester.liquidate_sock_clamped() from: 0x0000000000000000000000000000000000010000 Time delay: 73040 seconds Block delay: 19933
CryticTester.eVault_repay(934) from: 0x0000000000000000000000000000000000010000 Time delay: 338920 seconds Block delay: 54155
CryticTester.liquidate_0_sock_check_with_real_liq() from: 0x0000000000000000000000000000000000030000 Time delay: 82671 seconds Block delay: 12493
CryticTester.liquidate_sock(4370000,115792089237316195423570985008687907853269984665640564039457584007913129639931) from: 0x0000000000000000000000000000000000020000 Time delay: 358061 seconds Block delay: 60364
CryticTester.liquidate_0_attacker_check_with_real_liq() from: 0x0000000000000000000000000000000000010000 Time delay: 512439 seconds Block delay: 54809
CryticTester.sock_deposit_a(8497928236143637118487454809949120277053593495782025768287409455679616169313) from: 0x0000000000000000000000000000000000010000 Time delay: 412373 seconds Block delay: 38100
CryticTester.check_0_assets_1() from: 0x0000000000000000000000000000000000010000 Time delay: 67960 seconds Block delay: 32767
CryticTester.check_depositRebase_1(97219360896851757188100626029492771258912295113708459306110344610955906279906) from: 0x0000000000000000000000000000000000010000 Time delay: 448552 seconds Block delay: 12338
CryticTester.inflation_pre_condition_1() from: 0x0000000000000000000000000000000000030000 Time delay: 522178 seconds Block delay: 34720
CryticTester.liquidate_sock_clamped() from: 0x0000000000000000000000000000000000020000 Time delay: 379552 seconds Block delay: 38100
CryticTester.attacker_pull_debt(4370001) from: 0x0000000000000000000000000000000000020000 Time delay: 156190 seconds Block delay: 1123
CryticTester.eVault2_redeem(93361471945982015618036620840420330700332582826235962791529641426062347942040) from: 0x0000000000000000000000000000000000010000 Time delay: 360624 seconds Block delay: 4462
CryticTester.liquidate_sock(**********,4370000) from: 0x0000000000000000000000000000000000030000 Time delay: 156190 seconds Block delay: 4908
CryticTester.check_cash_2() from: 0x0000000000000000000000000000000000020000 Time delay: 82671 seconds Block delay: 33357
CryticTester.check_depositRebase_1(15095717422837617869303820885246513144530448367033268010511149453280953335736) from: 0x0000000000000000000000000000000000030000 Time delay: 435806 seconds Block delay: 11349
CryticTester.liquidate_sock_clamped() from: 0x0000000000000000000000000000000000030000 Time delay: 390247 seconds Block delay: 9966
CryticTester.eVault_redeem(52806318083070548715767063439315892350712624190775329021309163108880073926503) from: 0x0000000000000000000000000000000000020000 Time delay: 4177 seconds Block delay: 60364
CryticTester.check_depositRebase_1_crit(4370001) from: 0x0000000000000000000000000000000000010000 Time delay: 525476 seconds Block delay: 2497
CryticTester.check_acc() from: 0x0000000000000000000000000000000000020000 Time delay: 415353 seconds Block delay: 53166
CryticTester.sock_deposit_b(0) from: 0x0000000000000000000000000000000000010000 Time delay: 448552 seconds Block delay: 9920
CryticTester.mockIrm_2setInterestRate(66279549302271863263181239770383431033893841722854594486095932457529725702842) from: 0x0000000000000000000000000000000000030000 Time delay: 361136 seconds Block delay: 22909
CryticTester.sock_borrow_b(3137985303684348884730241372204500199359629260067751212444858229261146708812) from: 0x0000000000000000000000000000000000030000 Time delay: 419861 seconds Block delay: 30011
CryticTester.doomsday_deposit_e18() from: 0x0000000000000000000000000000000000020000 Time delay: 482712 seconds Block delay: 11905
CryticTester.check_depositRebase_2(4370001) from: 0x0000000000000000000000000000000000030000 Time delay: 305572 seconds Block delay: 23653
CryticTester.check_cash_2() from: 0x0000000000000000000000000000000000020000 Time delay: 463587 seconds Block delay: 5952
CryticTester.inflation_pre_condition_1() from: 0x0000000000000000000000000000000000020000 Time delay: 415353 seconds Block delay: 30042
CryticTester.victim_deposit_a(103) from: 0x0000000000000000000000000000000000030000 Time delay: 45142 seconds Block delay: 4462
CryticTester.check_touch_2() from: 0x0000000000000000000000000000000000030000 Time delay: 338920 seconds Block delay: 53011
CryticTester.check_liquidation_with_no_cost_2() from: 0x0000000000000000000000000000000000020000 Time delay: 361136 seconds Block delay: 53562
CryticTester.liquidate_0_sock_check() from: 0x0000000000000000000000000000000000030000 Time delay: 490446 seconds Block delay: 23722
CryticTester.eVault2_deposit(37) from: 0x0000000000000000000000000000000000030000 Time delay: 136392 seconds Block delay: 15367
CryticTester.inflation_pre_condition_2() from: 0x0000000000000000000000000000000000010000 Time delay: 376096 seconds Block delay: 561
CryticTester.eVault_withdraw(**********) from: 0x0000000000000000000000000000000000010000 Time delay: 207289 seconds Block delay: 1123
CryticTester.check_depositRebase_2(62661611083913246561782405453700282850515800851130917385837) from: 0x0000000000000000000000000000000000010000 Time delay: 180637 seconds Block delay: 23653
CryticTester.liquidate_0_attacker_check() from: 0x0000000000000000000000000000000000010000 Time delay: 569114 seconds Block delay: 1088
CryticTester.liquidate_attacker(113811111690933962498291113673238099905091061640898462344550197291689234709851,106756276656033271850827649173686823714120512664645694136530162958851742325374) from: 0x0000000000000000000000000000000000010000 Time delay: 112444 seconds Block delay: 1123
CryticTester.sock_deposit_a(4370000) from: 0x0000000000000000000000000000000000030000 Time delay: 439556 seconds Block delay: 20243
CryticTester.check_revert_ts_2() from: 0x0000000000000000000000000000000000010000 Time delay: 50417 seconds Block delay: 800
CryticTester.check_revert_ts() from: 0x0000000000000000000000000000000000020000 Time delay: 82671 seconds Block delay: 38350
CryticTester.eVault2_withdraw(1524785991) from: 0x0000000000000000000000000000000000020000 Time delay: 136392 seconds Block delay: 2511
CryticTester.eVault_borrow(824) from: 0x0000000000000000000000000000000000030000 Time delay: 4177 seconds Block delay: 32147
CryticTester.oracle2_setPrice_coll(99) from: 0x0000000000000000000000000000000000030000 Time delay: 338920 seconds Block delay: 45261
CryticTester.check_liquidation_solvency() from: 0x0000000000000000000000000000000000020000 Time delay: 195123 seconds Block delay: 60248

[2024-07-24 17:41:22.06] [status] tests: 8/78, fuzzing: 4017/1000000, values: [], cov: 42119, corpus: 21
[2024-07-24 17:41:12.80] [Worker 1] Test check_redeem_2(uint256) falsified!
  Call sequence:
CryticTester.sock_pull_debt(64272264246531907025687649480142021475725454364882195750829610328896307605421) from: 0x0000000000000000000000000000000000020000 Time delay: 195123 seconds Block delay: 1984
CryticTester.inflation_pre_condition_1() from: 0x0000000000000000000000000000000000020000 Time delay: 207289 seconds Block delay: 19933
CryticTester.mockIrm_1setInterestRate(**********) from: 0x0000000000000000000000000000000000020000 Time delay: 116188 seconds Block delay: 47075
CryticTester.canary_max_debt() from: 0x0000000000000000000000000000000000030000 Time delay: 526194 seconds Block delay: 34841
CryticTester.canary_max_debt() from: 0x0000000000000000000000000000000000030000 Time delay: 318197 seconds Block delay: 49415
CryticTester.check_cash_2() from: 0x0000000000000000000000000000000000020000 Time delay: 297507 seconds Block delay: 40520
CryticTester.check_repay_coverage() from: 0x0000000000000000000000000000000000010000 Time delay: 361136 seconds Block delay: 34272
CryticTester.canary_max_debt() from: 0x0000000000000000000000000000000000010000 Time delay: 277232 seconds Block delay: 2497
CryticTester.check_depositRebase_1_mid(4370000) from: 0x0000000000000000000000000000000000010000 Time delay: 82670 seconds Block delay: 9966
CryticTester.check_acc() from: 0x0000000000000000000000000000000000010000 Time delay: 198598 seconds Block delay: 30842
CryticTester.attacker_pull_debt(4370000) from: 0x0000000000000000000000000000000000030000 Time delay: 554465 seconds Block delay: 53011
CryticTester.liquidate_0_attacker_check() from: 0x0000000000000000000000000000000000020000 Time delay: 361136 seconds Block delay: 800
CryticTester.check_depositRebase_2_crit(22634488943466889299252711328960504468829418200783965750424892319970386462360) from: 0x0000000000000000000000000000000000030000 Time delay: 24867 seconds Block delay: 34272
CryticTester.sock_pull_debt(64272264246531907025687649480142021475725454364882195750829610328896307605421) from: 0x0000000000000000000000000000000000010000 Time delay: 135921 seconds Block delay: 53011
CryticTester.doomsday_deposit_e6() from: 0x0000000000000000000000000000000000010000 Time delay: 490448 seconds Block delay: 53678
CryticTester.check_fees_2() from: 0x0000000000000000000000000000000000010000 Time delay: 127251 seconds Block delay: 45852
CryticTester.oracle2_setPrice_coll(**********) from: 0x0000000000000000000000000000000000030000 Time delay: 332369 seconds Block delay: 5952
CryticTester.eVault_redeem(1524785993) from: 0x0000000000000000000000000000000000030000 Time delay: 24867 seconds Block delay: 24987
CryticTester.liquidate_sock(4370000,48076062579388433476848881819620292340231758239566965453820241180602650195643) from: 0x0000000000000000000000000000000000020000 Time delay: 318197 seconds Block delay: 23722
CryticTester.sock_deposit_b(4369999) from: 0x0000000000000000000000000000000000030000 Time delay: 322247 seconds Block delay: 16089
CryticTester.eVault_mint(4370001) from: 0x0000000000000000000000000000000000030000 Time delay: 289103 seconds Block delay: 58783
CryticTester.sock_deposit_b(2773958662898725231853837332551234139906038306865900435754511245482176994765) from: 0x0000000000000000000000000000000000010000 Time delay: 90154 seconds Block delay: 12338
CryticTester.victim_deposit_a(115792089237316195423570985008687907853269984665640564039457584007913129639935) from: 0x0000000000000000000000000000000000030000 Time delay: 172101 seconds Block delay: 20840
CryticTester.eVault2_deposit(90882211300155123712369098872294867237128504323095137402077272126817656884723) from: 0x0000000000000000000000000000000000030000 Time delay: 195123 seconds Block delay: 11826
CryticTester.eVault_withdraw(115792089237316195423570985008687907853269984665640564039457584007913129639934) from: 0x0000000000000000000000000000000000020000 Time delay: 526194 seconds Block delay: 30011
CryticTester.check_cash_2() from: 0x0000000000000000000000000000000000020000 Time delay: 209930 seconds Block delay: 24987
CryticTester.eVault2_withdraw(104925695768983750340361249025271443960054455380176566915696471641610664696240) from: 0x0000000000000000000000000000000000010000 Time delay: 305572 seconds Block delay: 1123
CryticTester.liquidate_sock_clamped() from: 0x0000000000000000000000000000000000010000 Time delay: 73040 seconds Block delay: 19933
CryticTester.eVault_repay(934) from: 0x0000000000000000000000000000000000010000 Time delay: 338920 seconds Block delay: 54155
CryticTester.liquidate_0_sock_check_with_real_liq() from: 0x0000000000000000000000000000000000030000 Time delay: 82671 seconds Block delay: 12493
CryticTester.liquidate_sock(4370000,115792089237316195423570985008687907853269984665640564039457584007913129639931) from: 0x0000000000000000000000000000000000020000 Time delay: 358061 seconds Block delay: 60364
CryticTester.liquidate_0_attacker_check_with_real_liq() from: 0x0000000000000000000000000000000000010000 Time delay: 512439 seconds Block delay: 54809
CryticTester.sock_deposit_a(8497928236143637118487454809949120277053593495782025768287409455679616169313) from: 0x0000000000000000000000000000000000010000 Time delay: 412373 seconds Block delay: 38100
CryticTester.check_0_assets_1() from: 0x0000000000000000000000000000000000010000 Time delay: 67960 seconds Block delay: 32767
CryticTester.check_depositRebase_1(97219360896851757188100626029492771258912295113708459306110344610955906279906) from: 0x0000000000000000000000000000000000010000 Time delay: 448552 seconds Block delay: 12338
CryticTester.inflation_pre_condition_1() from: 0x0000000000000000000000000000000000030000 Time delay: 522178 seconds Block delay: 34720
CryticTester.liquidate_sock_clamped() from: 0x0000000000000000000000000000000000020000 Time delay: 379552 seconds Block delay: 38100
CryticTester.attacker_pull_debt(4370001) from: 0x0000000000000000000000000000000000020000 Time delay: 156190 seconds Block delay: 1123
CryticTester.eVault2_redeem(93361471945982015618036620840420330700332582826235962791529641426062347942040) from: 0x0000000000000000000000000000000000010000 Time delay: 360624 seconds Block delay: 4462
CryticTester.liquidate_sock(**********,4370000) from: 0x0000000000000000000000000000000000030000 Time delay: 156190 seconds Block delay: 4908
CryticTester.check_cash_2() from: 0x0000000000000000000000000000000000020000 Time delay: 82671 seconds Block delay: 33357
CryticTester.check_depositRebase_1(15095717422837617869303820885246513144530448367033268010511149453280953335736) from: 0x0000000000000000000000000000000000030000 Time delay: 435806 seconds Block delay: 11349
CryticTester.liquidate_sock_clamped() from: 0x0000000000000000000000000000000000030000 Time delay: 390247 seconds Block delay: 9966
CryticTester.eVault_redeem(52806318083070548715767063439315892350712624190775329021309163108880073926503) from: 0x0000000000000000000000000000000000020000 Time delay: 4177 seconds Block delay: 60364
CryticTester.check_depositRebase_1_crit(4370001) from: 0x0000000000000000000000000000000000010000 Time delay: 525476 seconds Block delay: 2497
CryticTester.check_acc() from: 0x0000000000000000000000000000000000020000 Time delay: 415353 seconds Block delay: 53166
CryticTester.sock_deposit_b(0) from: 0x0000000000000000000000000000000000010000 Time delay: 448552 seconds Block delay: 9920
CryticTester.mockIrm_2setInterestRate(66279549302271863263181239770383431033893841722854594486095932457529725702842) from: 0x0000000000000000000000000000000000030000 Time delay: 361136 seconds Block delay: 22909
CryticTester.sock_borrow_b(3137985303684348884730241372204500199359629260067751212444858229261146708812) from: 0x0000000000000000000000000000000000030000 Time delay: 419861 seconds Block delay: 30011
CryticTester.doomsday_deposit_e18() from: 0x0000000000000000000000000000000000020000 Time delay: 482712 seconds Block delay: 11905
CryticTester.check_depositRebase_2(4370001) from: 0x0000000000000000000000000000000000030000 Time delay: 305572 seconds Block delay: 23653
CryticTester.check_cash_2() from: 0x0000000000000000000000000000000000020000 Time delay: 463587 seconds Block delay: 5952
CryticTester.inflation_pre_condition_1() from: 0x0000000000000000000000000000000000020000 Time delay: 415353 seconds Block delay: 30042
CryticTester.victim_deposit_a(103) from: 0x0000000000000000000000000000000000030000 Time delay: 45142 seconds Block delay: 4462
CryticTester.check_touch_2() from: 0x0000000000000000000000000000000000030000 Time delay: 338920 seconds Block delay: 53011
CryticTester.check_liquidation_with_no_cost_2() from: 0x0000000000000000000000000000000000020000 Time delay: 361136 seconds Block delay: 53562
CryticTester.liquidate_0_sock_check() from: 0x0000000000000000000000000000000000030000 Time delay: 490446 seconds Block delay: 23722
CryticTester.eVault2_deposit(37) from: 0x0000000000000000000000000000000000030000 Time delay: 136392 seconds Block delay: 15367
CryticTester.inflation_pre_condition_2() from: 0x0000000000000000000000000000000000010000 Time delay: 376096 seconds Block delay: 561
CryticTester.eVault_withdraw(**********) from: 0x0000000000000000000000000000000000010000 Time delay: 207289 seconds Block delay: 1123
CryticTester.check_depositRebase_2(62661611083913246561782405453700282850515800851130917385837) from: 0x0000000000000000000000000000000000010000 Time delay: 180637 seconds Block delay: 23653
CryticTester.liquidate_0_attacker_check() from: 0x0000000000000000000000000000000000010000 Time delay: 569114 seconds Block delay: 1088
CryticTester.liquidate_attacker(113811111690933962498291113673238099905091061640898462344550197291689234709851,106756276656033271850827649173686823714120512664645694136530162958851742325374) from: 0x0000000000000000000000000000000000010000 Time delay: 112444 seconds Block delay: 1123
CryticTester.sock_deposit_a(4370000) from: 0x0000000000000000000000000000000000030000 Time delay: 439556 seconds Block delay: 20243
CryticTester.check_revert_ts_2() from: 0x0000000000000000000000000000000000010000 Time delay: 50417 seconds Block delay: 800
CryticTester.check_revert_ts() from: 0x0000000000000000000000000000000000020000 Time delay: 82671 seconds Block delay: 38350
CryticTester.eVault2_withdraw(1524785991) from: 0x0000000000000000000000000000000000020000 Time delay: 136392 seconds Block delay: 2511
CryticTester.eVault_borrow(824) from: 0x0000000000000000000000000000000000030000 Time delay: 4177 seconds Block delay: 32147
CryticTester.oracle2_setPrice_coll(99) from: 0x0000000000000000000000000000000000030000 Time delay: 338920 seconds Block delay: 45261
CryticTester.check_liquidation_solvency() from: 0x0000000000000000000000000000000000020000 Time delay: 195123 seconds Block delay: 60248
CryticTester.check_redeem_2(302) from: 0x0000000000000000000000000000000000020000 Time delay: 404997 seconds Block delay: 15369

[2024-07-24 17:41:12.83]  Saved reproducer to echidna/reproducers-unshrunk/381699804706901642.txt
[2024-07-24 17:41:12.92]  Saved reproducer to echidna/reproducers-unshrunk/1575306271698098469.txt
[2024-07-24 17:41:13.03] [Worker 0] New coverage: 41960 instr, 15 contracts, 11 seqs in corpus
[2024-07-24 17:41:13.10]  Saved reproducer to echidna/coverage/3819531993637514219.txt
[2024-07-24 17:41:13.30] [Worker 7] New coverage: 41963 instr, 15 contracts, 12 seqs in corpus
[2024-07-24 17:41:13.39]  Saved reproducer to echidna/coverage/316332166954077284.txt
[2024-07-24 17:41:13.63] [Worker 1] New coverage: 41963 instr, 15 contracts, 13 seqs in corpus
[2024-07-24 17:41:13.73]  Saved reproducer to echidna/coverage/7629567300718681871.txt
[2024-07-24 17:41:15.29] [Worker 4] New coverage: 42047 instr, 15 contracts, 14 seqs in corpus
[2024-07-24 17:41:15.35]  Saved reproducer to echidna/coverage/7109337450685181470.txt
[2024-07-24 17:41:15.78] [Worker 2] New coverage: 42047 instr, 15 contracts, 15 seqs in corpus
[2024-07-24 17:41:15.85]  Saved reproducer to echidna/coverage/4780754302365980687.txt
[2024-07-24 17:41:15.85] [Worker 5] New coverage: 42047 instr, 15 contracts, 16 seqs in corpus
[2024-07-24 17:41:15.92]  Saved reproducer to echidna/coverage/418898495778181642.txt
[2024-07-24 17:41:17.53] [Worker 3] New coverage: 42074 instr, 15 contracts, 17 seqs in corpus
[2024-07-24 17:41:17.56]  Saved reproducer to echidna/coverage/9117468518000505995.txt
[2024-07-24 17:41:18.13] [Worker 2] New coverage: 42077 instr, 15 contracts, 18 seqs in corpus
[2024-07-24 17:41:18.22]  Saved reproducer to echidna/coverage/6256676528883778380.txt
[2024-07-24 17:41:18.91] [Worker 7] New coverage: 42077 instr, 15 contracts, 19 seqs in corpus
[2024-07-24 17:41:19.00]  Saved reproducer to echidna/coverage/2392440728778689392.txt
[2024-07-24 17:41:20.07] [Worker 8] New coverage: 42084 instr, 15 contracts, 20 seqs in corpus
[2024-07-24 17:41:20.13]  Saved reproducer to echidna/coverage/8085732357626941221.txt
[2024-07-24 17:41:21.17] [Worker 7] New coverage: 42111 instr, 15 contracts, 21 seqs in corpus
[2024-07-24 17:41:21.26]  Saved reproducer to echidna/coverage/6365798937105137134.txt
[2024-07-24 17:41:22.58] [Worker 8] New coverage: 42119 instr, 15 contracts, 22 seqs in corpus
[2024-07-24 17:41:22.67]  Saved reproducer to echidna/coverage/391731453933853902.txt
[2024-07-24 17:41:25.06] [status] tests: 9/78, fuzzing: 4929/1000000, values: [], cov: 42169, corpus: 22
[2024-07-24 17:41:24.41] [Worker 2] Test check_liquidation_with_no_cost_2() falsified!
  Call sequence:
CryticTester.sock_pull_debt(64272264246531907025687649480142021475725454364882195750829610328896307605421) from: 0x0000000000000000000000000000000000020000 Time delay: 195123 seconds Block delay: 1984
CryticTester.inflation_pre_condition_1() from: 0x0000000000000000000000000000000000020000 Time delay: 207289 seconds Block delay: 19933
CryticTester.mockIrm_1setInterestRate(**********) from: 0x0000000000000000000000000000000000020000 Time delay: 116188 seconds Block delay: 47075
CryticTester.canary_max_debt() from: 0x0000000000000000000000000000000000030000 Time delay: 526194 seconds Block delay: 34841
CryticTester.canary_max_debt() from: 0x0000000000000000000000000000000000030000 Time delay: 318197 seconds Block delay: 49415
CryticTester.check_cash_2() from: 0x0000000000000000000000000000000000020000 Time delay: 297507 seconds Block delay: 40520
CryticTester.check_repay_coverage() from: 0x0000000000000000000000000000000000010000 Time delay: 361136 seconds Block delay: 34272
CryticTester.canary_max_debt() from: 0x0000000000000000000000000000000000010000 Time delay: 277232 seconds Block delay: 2497
CryticTester.check_depositRebase_1_mid(4370000) from: 0x0000000000000000000000000000000000010000 Time delay: 82670 seconds Block delay: 9966
CryticTester.check_acc() from: 0x0000000000000000000000000000000000010000 Time delay: 198598 seconds Block delay: 30842
CryticTester.attacker_pull_debt(4370000) from: 0x0000000000000000000000000000000000030000 Time delay: 554465 seconds Block delay: 53011
CryticTester.liquidate_0_attacker_check() from: 0x0000000000000000000000000000000000020000 Time delay: 361136 seconds Block delay: 800
CryticTester.check_depositRebase_2_crit(22634488943466889299252711328960504468829418200783965750424892319970386462360) from: 0x0000000000000000000000000000000000030000 Time delay: 24867 seconds Block delay: 34272
CryticTester.sock_pull_debt(64272264246531907025687649480142021475725454364882195750829610328896307605421) from: 0x0000000000000000000000000000000000010000 Time delay: 135921 seconds Block delay: 53011
CryticTester.check_revert_ts_2() from: 0x0000000000000000000000000000000000020000 Time delay: 292304 seconds Block delay: 47075
CryticTester.oracle2_setPrice_coll(182236372353997146171863442025904129624) from: 0x0000000000000000000000000000000000020000 Time delay: 255 seconds Block delay: 6234
CryticTester.oracle1_setPrice_debt(194369822915478657389302670854837541265) from: 0x0000000000000000000000000000000000020000 Time delay: 209930 seconds Block delay: 59552
CryticTester.oracle1_setPrice_debt(129395) from: 0x0000000000000000000000000000000000020000 Time delay: 303345 seconds Block delay: 5023
CryticTester.check_depositRebase_1_mid(115637819024610864209387441275789515428899959492583545442034277259023824945500) from: 0x0000000000000000000000000000000000010000 Time delay: 82671 seconds Block delay: 255
CryticTester.eVault_borrow(82684205880732389826597996072906696629893808635439198681764238157582405992253) from: 0x0000000000000000000000000000000000020000 Time delay: 31594 seconds Block delay: 54155
CryticTester.check_acc() from: 0x0000000000000000000000000000000000030000 Time delay: 206186 seconds Block delay: 53451
CryticTester.eVault_deposit(4369999) from: 0x0000000000000000000000000000000000030000 Time delay: 271957 seconds Block delay: 5023
CryticTester.check_depositRebase_2_crit(96774499096129376521594050287635146034158510809371304253995252274887763610865) from: 0x0000000000000000000000000000000000020000 Time delay: 415353 seconds Block delay: 2497
CryticTester.check_revert_ts() from: 0x0000000000000000000000000000000000020000 Time delay: 150273 seconds Block delay: 34720
CryticTester.liquidate_0_sock_check() from: 0x0000000000000000000000000000000000020000 Time delay: 376096 seconds Block delay: 800
CryticTester.check_depositRebase_2_mid(53767031666226907287309090427376147878055136070034524935559713232841038905653) from: 0x0000000000000000000000000000000000010000 Time delay: 115085 seconds Block delay: 23653
CryticTester.check_lost_yield_canary() from: 0x0000000000000000000000000000000000020000 Time delay: 358061 seconds Block delay: 255
CryticTester.eVault_repay(3063937477064513236301353874945876373486994027729150311077129819303748081584) from: 0x0000000000000000000000000000000000010000 Time delay: 16802 seconds Block delay: 11905
CryticTester.liquidate_0_attacker_check() from: 0x0000000000000000000000000000000000020000 Time delay: 554465 seconds Block delay: 35200
CryticTester.eVault2_redeem(8175794721713851353345034053448952993816653447356785450464190093978187230946) from: 0x0000000000000000000000000000000000030000 Time delay: 214590 seconds Block delay: 42101
CryticTester.oracle2_setPrice_coll(0) from: 0x0000000000000000000000000000000000020000 Time delay: 136393 seconds Block delay: 127
CryticTester.doomsday_deposit_e18() from: 0x0000000000000000000000000000000000020000 Time delay: 522178 seconds Block delay: 15369
CryticTester.check_depositRebase_2_mid(1524785993) from: 0x0000000000000000000000000000000000020000 Time delay: 344203 seconds Block delay: 23403
CryticTester.check_lost_debt_canary() from: 0x0000000000000000000000000000000000010000 Time delay: 136392 seconds Block delay: 38100
CryticTester.eVault_borrow(26439365280088002726) from: 0x0000000000000000000000000000000000010000 Time delay: 390247 seconds Block delay: 22699
CryticTester.check_liquidation_with_no_cost_2() from: 0x0000000000000000000000000000000000010000 Time delay: 322247 seconds Block delay: 5952

[2024-07-24 17:41:24.43]  Saved reproducer to echidna/reproducers-unshrunk/4029614044793920794.txt
[2024-07-24 17:41:25.67] [Worker 2] New coverage: 42169 instr, 15 contracts, 23 seqs in corpus
[2024-07-24 17:41:25.76]  Saved reproducer to echidna/coverage/8088853031378426184.txt
[2024-07-24 17:41:28.06] [status] tests: 9/78, fuzzing: 5939/1000000, values: [], cov: 42169, corpus: 23
[2024-07-24 17:41:29.30] [Worker 4] New coverage: 42177 instr, 15 contracts, 24 seqs in corpus
[2024-07-24 17:41:29.39]  Saved reproducer to echidna/coverage/6640576537004255205.txt
[2024-07-24 17:41:31.06] [status] tests: 9/78, fuzzing: 6747/1000000, values: [], cov: 42188, corpus: 24
[2024-07-24 17:41:31.26] [Worker 7] New coverage: 42188 instr, 15 contracts, 25 seqs in corpus
[2024-07-24 17:41:31.35]  Saved reproducer to echidna/coverage/2399399909876304849.txt
[2024-07-24 17:41:31.59] [Worker 0] New coverage: 42188 instr, 15 contracts, 26 seqs in corpus
[2024-07-24 17:41:31.68]  Saved reproducer to echidna/coverage/3971480683295070918.txt
[2024-07-24 17:41:34.07] [status] tests: 9/78, fuzzing: 7646/1000000, values: [], cov: 42188, corpus: 26
[2024-07-24 17:41:37.07] [status] tests: 9/78, fuzzing: 8567/1000000, values: [], cov: 42188, corpus: 26
[2024-07-24 17:41:40.08] [status] tests: 9/78, fuzzing: 9173/1000000, values: [], cov: 42188, corpus: 26
[2024-07-24 17:41:43.08] [status] tests: 9/78, fuzzing: 10153/1000000, values: [], cov: 42188, corpus: 26
[2024-07-24 17:41:43.85] [Worker 4] New coverage: 42280 instr, 15 contracts, 27 seqs in corpus
[2024-07-24 17:41:43.91]  Saved reproducer to echidna/coverage/93304889882578747.txt
[2024-07-24 17:41:46.08] [status] tests: 9/78, fuzzing: 10975/1000000, values: [], cov: 42280, corpus: 27
[2024-07-24 17:41:49.08] [status] tests: 10/78, fuzzing: 11781/1000000, values: [], cov: 42280, corpus: 27
[2024-07-24 17:41:47.63] [Worker 0] Test check_lost_debt_canary() falsified!
  Call sequence:
CryticTester.check_fees() from: 0x0000000000000000000000000000000000030000 Time delay: 255 seconds Block delay: 8447
CryticTester.eVault_mint(1524785991) from: 0x0000000000000000000000000000000000030000 Time delay: 38059 seconds Block delay: 23978
CryticTester.mockIrm_2setInterestRate(56078056763137345528120083913344288638649066446058766610796692426130667665865) from: 0x0000000000000000000000000000000000010000 Time delay: 127 seconds Block delay: 45852
CryticTester.attacker_pull_debt(19683267871635916278181255656400348548159491414194204231619531585296246) from: 0x0000000000000000000000000000000000030000 Time delay: 482712 seconds Block delay: 24311
CryticTester.check_total_2_no_revert() from: 0x0000000000000000000000000000000000020000 Time delay: 511438 seconds Block delay: 5023
CryticTester.eVault_repay_shares(4370001) from: 0x0000000000000000000000000000000000020000 Time delay: 289103 seconds Block delay: 54155
CryticTester.check_redeem_2(53838008622669226826458340226883018401358628325751096640011795474340745362093) from: 0x0000000000000000000000000000000000030000 Time delay: 111322 seconds Block delay: 35200
CryticTester.mockIrm_2setInterestRate(**********) from: 0x0000000000000000000000000000000000010000 Time delay: 439556 seconds Block delay: 5023
CryticTester.oracle2_setPrice_debt(275349239273020267239483095194177781006) from: 0x0000000000000000000000000000000000020000 Time delay: 482712 seconds Block delay: 1088
CryticTester.check_fees() from: 0x0000000000000000000000000000000000030000 Time delay: 206186 seconds Block delay: 54809
CryticTester.inflation_pre_condition_1() from: 0x0000000000000000000000000000000000010000 Time delay: 404997 seconds Block delay: 59982
CryticTester.doomsday_deposit_e12() from: 0x0000000000000000000000000000000000010000 Time delay: 275394 seconds Block delay: 45819
CryticTester.sock_deposit_b(109162185432039908090367485489052272227856362788200480176645250031112595805344) from: 0x0000000000000000000000000000000000020000 Time delay: 136392 seconds Block delay: 59981
CryticTester.check_acc() from: 0x0000000000000000000000000000000000020000 Time delay: 297507 seconds Block delay: 30011
CryticTester.check_repay_coverage() from: 0x0000000000000000000000000000000000010000 Time delay: 117472 seconds Block delay: 24987
CryticTester.check_depositRebase_2(43577529584110373268496982486348829938960510974469326231628762202720317516794) from: 0x0000000000000000000000000000000000030000 Time delay: 492049 seconds Block delay: 4896
CryticTester.testTheOverflow() from: 0x0000000000000000000000000000000000030000 Time delay: 156190 seconds Block delay: 34720
CryticTester.liquidate_attacker(4370001,1524785991) from: 0x0000000000000000000000000000000000010000 Time delay: 254414 seconds Block delay: 30784
CryticTester.check_0_shares_2() from: 0x0000000000000000000000000000000000020000 Time delay: 303345 seconds Block delay: 52885
CryticTester.check_lost_yield_canary() from: 0x0000000000000000000000000000000000020000 Time delay: 303345 seconds Block delay: 22909
CryticTester.victim_deposit_a(88960501159058711805927438955227781854577824187051818046560393388886238850436) from: 0x0000000000000000000000000000000000030000 Time delay: 31594 seconds Block delay: 15368
CryticTester.liquidate_0_sock_check_with_real_liq() from: 0x0000000000000000000000000000000000020000 Time delay: 478623 seconds Block delay: 38237
CryticTester.check_cash() from: 0x0000000000000000000000000000000000010000 Time delay: 448552 seconds Block delay: 24987
CryticTester.check_depositRebase_1_crit(68753477146464509631747692536068935335645391453970789917897217792849931938825) from: 0x0000000000000000000000000000000000010000 Time delay: 4177 seconds Block delay: 32147
CryticTester.testTheDivisionByZero() from: 0x0000000000000000000000000000000000020000 Time delay: 82671 seconds Block delay: 42595
CryticTester.doomsday_deposit_e12() from: 0x0000000000000000000000000000000000020000 Time delay: 414579 seconds Block delay: 55538
CryticTester.eVault_borrow(1524785993) from: 0x0000000000000000000000000000000000010000 Time delay: 322374 seconds Block delay: 5053
CryticTester.liquidate_attacker(115792089237316195423570985008687907853269984665640564039457584007913129639935,1524785993) from: 0x0000000000000000000000000000000000010000 Time delay: 447588 seconds Block delay: 23885
CryticTester.liquidate_0_sock_check() from: 0x0000000000000000000000000000000000030000 Time delay: 525476 seconds Block delay: 35248
CryticTester.eVault2_redeem(103294853300764322374363613813446999291767894063840439289944980524142351587558) from: 0x0000000000000000000000000000000000030000 Time delay: 376096 seconds Block delay: 23653
CryticTester.eVault_mint(56495142698515331553050767975448524472568965797888598730570454013233916524920) from: 0x0000000000000000000000000000000000010000 Time delay: 297507 seconds Block delay: 30784
CryticTester.eVault2_mint(66938373695316108514750191467262195989443806202402425189534758163159728933779) from: 0x0000000000000000000000000000000000030000 Time delay: 407328 seconds Block delay: 53349
CryticTester.eVault2_withdraw(**********) from: 0x0000000000000000000000000000000000030000 Time delay: 521319 seconds Block delay: 52651
CryticTester.check_lost_debt_canary() from: 0x0000000000000000000000000000000000030000 Time delay: 66543 seconds Block delay: 12338
CryticTester.testTheDivisionByZero() from: 0x0000000000000000000000000000000000010000 Time delay: 490448 seconds Block delay: 4896
CryticTester.canary_max_debt() from: 0x0000000000000000000000000000000000010000 Time delay: 82670 seconds Block delay: 30256
CryticTester.check_depositRebase_1_mid(1524785991) from: 0x0000000000000000000000000000000000020000 Time delay: 82670 seconds Block delay: 59982
CryticTester.check_lost_yield_canary() from: 0x0000000000000000000000000000000000030000 Time delay: 279131 seconds Block delay: 1362
CryticTester.check_depositRebase_1_crit(40389114543899894895460131765116100378770972132596926154221658981715157953211) from: 0x0000000000000000000000000000000000020000 Time delay: 374516 seconds Block delay: 15367
CryticTester.check_the_zero_div() from: 0x0000000000000000000000000000000000020000 Time delay: 436727 seconds Block delay: 48000
CryticTester.liquidate_sock_clamped() from: 0x0000000000000000000000000000000000010000 Time delay: 73040 seconds Block delay: 32767
CryticTester.oracle2_setPrice_coll(755) from: 0x0000000000000000000000000000000000030000 Time delay: 519847 seconds Block delay: 32
CryticTester.check_0_assets_1() from: 0x0000000000000000000000000000000000010000 Time delay: 463588 seconds Block delay: 39297
CryticTester.doomsday_deposit_e18() from: 0x0000000000000000000000000000000000020000 Time delay: 401699 seconds Block delay: 38350
CryticTester.eVault2_withdraw(100255448524919118194994958342536666721332524457110426697748954105631531892478) from: 0x0000000000000000000000000000000000030000 Time delay: 82670 seconds Block delay: 12338
CryticTester.check_lost_debt_canary() from: 0x0000000000000000000000000000000000020000 Time delay: 255 seconds Block delay: 4462
CryticTester.check_0_assets_1() from: 0x0000000000000000000000000000000000020000 Time delay: 111322 seconds Block delay: 45819
CryticTester.check_revert_ts() from: 0x0000000000000000000000000000000000020000 Time delay: 415353 seconds Block delay: 11891
CryticTester.eVault2_withdraw(40023283395416308998115663940767490089177622784733844333709260738453781802949) from: 0x0000000000000000000000000000000000020000 Time delay: 166184 seconds Block delay: 58783
CryticTester.check_fees_2() from: 0x0000000000000000000000000000000000010000 Time delay: 444463 seconds Block delay: 5140
CryticTester.testTheOverflow() from: 0x0000000000000000000000000000000000030000 Time delay: 15227 seconds Block delay: 4896
CryticTester.eVault_repay_sock(4370000) from: 0x0000000000000000000000000000000000020000 Time delay: 400981 seconds Block delay: 35248
CryticTester.canary_max_debt() from: 0x0000000000000000000000000000000000020000 Time delay: 400981 seconds Block delay: 561
CryticTester.check_debt() from: 0x0000000000000000000000000000000000030000 Time delay: 400981 seconds Block delay: 35200
CryticTester.check_fees_2() from: 0x0000000000000000000000000000000000010000 Time delay: 463587 seconds Block delay: 21778
CryticTester.oracle1_setPrice_coll(4370001) from: 0x0000000000000000000000000000000000020000 Time delay: 49735 seconds Block delay: 33357
CryticTester.eVault_borrow(66252920192954456398817348654615356102147361428228207322212090877063169575027) from: 0x0000000000000000000000000000000000020000 Time delay: 463587 seconds Block delay: 12493
CryticTester.check_touch_2() from: 0x0000000000000000000000000000000000030000 Time delay: 31594 seconds Block delay: 30256
CryticTester.oracle1_setPrice_debt(4370001) from: 0x0000000000000000000000000000000000010000 Time delay: 490446 seconds Block delay: 58783
CryticTester.check_redeem_2(**********) from: 0x0000000000000000000000000000000000030000 Time delay: 494803 seconds Block delay: 23403
CryticTester.eVault2_deposit(115792089237316195423570985008687907853269984665640564039457584007913129639935) from: 0x0000000000000000000000000000000000020000 Time delay: 136392 seconds Block delay: 32147
CryticTester.check_revert_ts_2() from: 0x0000000000000000000000000000000000030000 Time delay: 358061 seconds Block delay: 30304
CryticTester.check_lost_debt_canary() from: 0x0000000000000000000000000000000000020000 Time delay: 33271 seconds Block delay: 53451
CryticTester.inflation_pre_condition_1() from: 0x0000000000000000000000000000000000020000 Time delay: 541697 seconds Block delay: 46664
CryticTester.eVault_mint(1524785991) from: 0x0000000000000000000000000000000000020000 Time delay: 255 seconds Block delay: 27404
CryticTester.sock_pull_debt(1524785991) from: 0x0000000000000000000000000000000000010000 Time delay: 303345 seconds Block delay: 57086
CryticTester.check_revert_ta_2() from: 0x0000000000000000000000000000000000020000 Time delay: 136394 seconds Block delay: 2511
CryticTester.check_depositRebase_2(4370001) from: 0x0000000000000000000000000000000000010000 Time delay: 303345 seconds Block delay: 1123
CryticTester.check_depositRebase_2_mid(12843351818163609315430722731198728844796771027821289526355464432130569017951) from: 0x0000000000000000000000000000000000030000 Time delay: 444463 seconds Block delay: 12155
CryticTester.check_fees() from: 0x0000000000000000000000000000000000020000 Time delay: 49735 seconds Block delay: 27205
CryticTester.mockIrm_1setInterestRate(52541251985696364353248765233161483524862277317708136849314651415187045360888) from: 0x0000000000000000000000000000000000010000 Time delay: 116188 seconds Block delay: 19933
CryticTester.canary_max_debt() from: 0x0000000000000000000000000000000000030000 Time delay: 188628 seconds Block delay: 30042
CryticTester.check_depositRebase_1(115792089237316195423570985008687907853269984665640564039457584007913129639934) from: 0x0000000000000000000000000000000000030000 Time delay: 305572 seconds Block delay: 7323
CryticTester.liquidate_0_attacker_check() from: 0x0000000000000000000000000000000000010000 Time delay: 305572 seconds Block delay: 14328
CryticTester.check_the_zero_div() from: 0x0000000000000000000000000000000000030000 Time delay: 82670 seconds Block delay: 22953
CryticTester.check_repay_coverage() from: 0x0000000000000000000000000000000000030000 Time delay: 73040 seconds Block delay: 60054
CryticTester.sock_deposit_b(32294088352000740604726865843209933720210977576883312127033727169086608218961) from: 0x0000000000000000000000000000000000010000 Time delay: 490448 seconds Block delay: 30042
CryticTester.check_lost_debt_canary() from: 0x0000000000000000000000000000000000010000 Time delay: 344120 seconds Block delay: 23275

[2024-07-24 17:41:47.68]  Saved reproducer to echidna/reproducers-unshrunk/5207003291420305798.txt
[2024-07-24 17:41:52.08] [status] tests: 10/78, fuzzing: 12286/1000000, values: [], cov: 42280, corpus: 27
[2024-07-24 17:41:54.43] [Worker 4] New coverage: 42310 instr, 15 contracts, 28 seqs in corpus
[2024-07-24 17:41:54.51]  Saved reproducer to echidna/coverage/7320133948417621859.txt
[2024-07-24 17:41:55.09] [status] tests: 10/78, fuzzing: 13094/1000000, values: [], cov: 42310, corpus: 28
[2024-07-24 17:41:58.09] [status] tests: 10/78, fuzzing: 13902/1000000, values: [], cov: 42310, corpus: 28
[2024-07-24 17:41:58.19] [Worker 7] New coverage: 42310 instr, 15 contracts, 29 seqs in corpus
[2024-07-24 17:41:58.28]  Saved reproducer to echidna/coverage/8772628650878016343.txt
[2024-07-24 17:42:00.47] [Worker 3] New coverage: 42334 instr, 15 contracts, 30 seqs in corpus
[2024-07-24 17:42:00.56]  Saved reproducer to echidna/coverage/4203351275114984136.txt
[2024-07-24 17:42:01.10] [status] tests: 10/78, fuzzing: 14642/1000000, values: [], cov: 42334, corpus: 30
[2024-07-24 17:42:03.50] [Worker 8] New coverage: 42334 instr, 15 contracts, 31 seqs in corpus
[2024-07-24 17:42:03.58]  Saved reproducer to echidna/coverage/1583400212142216235.txt
[2024-07-24 17:42:04.03] [Worker 4] New coverage: 42334 instr, 15 contracts, 32 seqs in corpus
[2024-07-24 17:42:04.10] [status] tests: 10/78, fuzzing: 15346/1000000, values: [], cov: 42334, corpus: 32
[2024-07-24 17:42:04.12]  Saved reproducer to echidna/coverage/2143962473363620864.txt
[2024-07-24 17:42:07.10] [status] tests: 10/78, fuzzing: 16135/1000000, values: [], cov: 42334, corpus: 32
[2024-07-24 17:42:08.25] [Worker 4] New coverage: 42334 instr, 15 contracts, 33 seqs in corpus
[2024-07-24 17:42:08.34]  Saved reproducer to echidna/coverage/1542679676786519252.txt
[2024-07-24 17:42:10.10] [status] tests: 10/78, fuzzing: 16777/1000000, values: [], cov: 42334, corpus: 33
[2024-07-24 17:42:13.11] [status] tests: 10/78, fuzzing: 17487/1000000, values: [], cov: 42334, corpus: 33
[2024-07-24 17:42:16.11] [status] tests: 10/78, fuzzing: 18395/1000000, values: [], cov: 42334, corpus: 33
[2024-07-24 17:42:17.46] [Worker 3] New coverage: 42340 instr, 15 contracts, 34 seqs in corpus
[2024-07-24 17:42:17.55]  Saved reproducer to echidna/coverage/2553467538932673761.txt
[2024-07-24 17:42:19.11] [status] tests: 10/78, fuzzing: 19053/1000000, values: [], cov: 42340, corpus: 34
[2024-07-24 17:42:22.12] [status] tests: 10/78, fuzzing: 19760/1000000, values: [], cov: 42340, corpus: 34
[2024-07-24 17:42:25.12] [status] tests: 10/78, fuzzing: 20467/1000000, values: [], cov: 42340, corpus: 34
[2024-07-24 17:42:28.12] [status] tests: 11/78, fuzzing: 21092/1000000, values: [], cov: 42340, corpus: 34
[2024-07-24 17:42:25.39] [Worker 5] Test check_lost_yield_canary() falsified!
  Call sequence:
CryticTester.check_revert_ta() from: 0x0000000000000000000000000000000000030000 Time delay: 65535 seconds Block delay: 800
CryticTester.canary_max_debt_2() from: 0x0000000000000000000000000000000000030000 Time delay: 414736 seconds Block delay: 59981
CryticTester.eVault_borrow(4370001) from: 0x0000000000000000000000000000000000020000 Time delay: 434894 seconds Block delay: 42595
CryticTester.check_depositRebase_2(77483213215466490748670851307762735152526560875815714712691730856184349073673) from: 0x0000000000000000000000000000000000020000 Time delay: 206186 seconds Block delay: 5054
CryticTester.check_cash() from: 0x0000000000000000000000000000000000020000 Time delay: 195123 seconds Block delay: 3661
CryticTester.check_depositRebase_1_crit(4370000) from: 0x0000000000000000000000000000000000020000 Time delay: 38059 seconds Block delay: 44070
CryticTester.liquidate_sock(0,115792089237316195423570985008687907853269984665640564039457584007913129639935) from: 0x0000000000000000000000000000000000010000 Time delay: 344203 seconds Block delay: 30256
CryticTester.check_total_no_revert() from: 0x0000000000000000000000000000000000030000 Time delay: 547623 seconds Block delay: 11349
CryticTester.liquidate_sock_clamped() from: 0x0000000000000000000000000000000000030000 Time delay: 388668 seconds Block delay: 30304
CryticTester.eVault_repay_sock(4369999) from: 0x0000000000000000000000000000000000030000 Time delay: 358061 seconds Block delay: 8447
CryticTester.eVault_repay_shares_to_sock(1524785993) from: 0x0000000000000000000000000000000000010000 Time delay: 521319 seconds Block delay: 35988
CryticTester.check_the_overflow() from: 0x0000000000000000000000000000000000030000 Time delay: 275394 seconds Block delay: 45261
CryticTester.canary_max_debt() from: 0x0000000000000000000000000000000000010000 Time delay: 38059 seconds Block delay: 43114
CryticTester.testTheDivisionByZero() from: 0x0000000000000000000000000000000000010000 Time delay: 400981 seconds Block delay: 60267
CryticTester.check_fees() from: 0x0000000000000000000000000000000000030000 Time delay: 136392 seconds Block delay: 20243
CryticTester.doomsday_deposit_e18() from: 0x0000000000000000000000000000000000020000 Time delay: 254414 seconds Block delay: 19933
CryticTester.check_revert_ts_2() from: 0x0000000000000000000000000000000000030000 Time delay: 376096 seconds Block delay: 60267
CryticTester.check_liquidation_with_no_cost_2() from: 0x0000000000000000000000000000000000030000 Time delay: 292304 seconds Block delay: 15005
CryticTester.check_acc() from: 0x0000000000000000000000000000000000030000 Time delay: 112444 seconds Block delay: 23885
CryticTester.check_cash_2() from: 0x0000000000000000000000000000000000030000 Time delay: 67960 seconds Block delay: 34720
CryticTester.check_total_2_no_revert() from: 0x0000000000000000000000000000000000030000 Time delay: 379552 seconds Block delay: 255
CryticTester.check_depositRebase_2(115792089237316195423570985008687907853269984665640564039457584007913129639931) from: 0x0000000000000000000000000000000000010000 Time delay: 136394 seconds Block delay: 42101
CryticTester.check_debt() from: 0x0000000000000000000000000000000000030000 Time delay: 127251 seconds Block delay: 59982
CryticTester.sock_borrow_b(30338728920139637891494384353695090521216818787339941936822076121497063490813) from: 0x0000000000000000000000000000000000030000 Time delay: 33271 seconds Block delay: 4223
CryticTester.check_depositRebase_2_mid(1524785991) from: 0x0000000000000000000000000000000000030000 Time delay: 303345 seconds Block delay: 23403
CryticTester.eVault2_mint(26634142552332847091826028066959682462181248530420496787138462055475896247256) from: 0x0000000000000000000000000000000000010000 Time delay: 198598 seconds Block delay: 59983
CryticTester.inflation_pre_condition_2() from: 0x0000000000000000000000000000000000020000 Time delay: 332369 seconds Block delay: 5237
CryticTester.eVault_borrow(103513718107818398372804037772526569879220093513336557025266893281411545496468) from: 0x0000000000000000000000000000000000030000 Time delay: 305572 seconds Block delay: 60364
CryticTester.check_revert_ts_2() from: 0x0000000000000000000000000000000000020000 Time delay: 247121 seconds Block delay: 18429
CryticTester.oracle2_setPrice_coll(190897042275710268860753575629276518584) from: 0x0000000000000000000000000000000000030000 Time delay: 136393 seconds Block delay: 36859
CryticTester.eVault2_withdraw(0) from: 0x0000000000000000000000000000000000010000 Time delay: 32767 seconds Block delay: 6721
CryticTester.mockIrm_2setInterestRate(111779733079534569505420836265382746840157679206353053524503509334981453530050) from: 0x0000000000000000000000000000000000010000 Time delay: 255 seconds Block delay: 12493
CryticTester.inflation_pre_condition_2() from: 0x0000000000000000000000000000000000010000 Time delay: 255 seconds Block delay: 18429
CryticTester.eVault2_redeem(4370001) from: 0x0000000000000000000000000000000000010000 Time delay: 448552 seconds Block delay: 23653
CryticTester.check_depositRebase_1_crit(86792847691218969345626558271390339552809949137525781020965962812847090926504) from: 0x0000000000000000000000000000000000030000 Time delay: 547623 seconds Block delay: 16733
CryticTester.mockIrm_1setInterestRate(1524785991) from: 0x0000000000000000000000000000000000020000 Time delay: 521319 seconds Block delay: 8447
CryticTester.canary_max_debt() from: 0x0000000000000000000000000000000000030000 Time delay: 412373 seconds Block delay: 15005
CryticTester.check_revert_ts_2() from: 0x0000000000000000000000000000000000010000 Time delay: 415353 seconds Block delay: 60267
CryticTester.eVault_repay_shares_to_sock(115792089237316195423570985008687907853269984665640564039457584007913129639935) from: 0x0000000000000000000000000000000000010000 Time delay: 271957 seconds Block delay: 5054
CryticTester.check_redeem_1(32239314324531707073289269174279370358953681409765689963936815215067669428671) from: 0x0000000000000000000000000000000000020000 Time delay: 547623 seconds Block delay: 34720
CryticTester.check_0_shares_1() from: 0x0000000000000000000000000000000000030000 Time delay: 127251 seconds Block delay: 11826
CryticTester.check_0_shares_1() from: 0x0000000000000000000000000000000000020000 Time delay: 206186 seconds Block delay: 53166
CryticTester.oracle2_setPrice_debt(161613044929610126146846762702569459543) from: 0x0000000000000000000000000000000000010000 Time delay: 135921 seconds Block delay: 12053
CryticTester.eVault_repay_sock(115792089237316195423570985008687907853269984665640564039457584007913129639931) from: 0x0000000000000000000000000000000000010000 Time delay: 511822 seconds Block delay: 255
CryticTester.attacker_pull_debt(4370001) from: 0x0000000000000000000000000000000000020000 Time delay: 150273 seconds Block delay: 23275
CryticTester.eVault_borrow(92) from: 0x0000000000000000000000000000000000020000 Time delay: 332369 seconds Block delay: 18429
CryticTester.check_depositRebase_1_crit(4369999) from: 0x0000000000000000000000000000000000010000 Time delay: 172101 seconds Block delay: 45261
CryticTester.check_touch_2() from: 0x0000000000000000000000000000000000010000 Time delay: 111322 seconds Block delay: 2511
CryticTester.check_depositRebase_1_mid(4370001) from: 0x0000000000000000000000000000000000010000 Time delay: 569114 seconds Block delay: 4462
CryticTester.canary_max_debt_2() from: 0x0000000000000000000000000000000000020000 Time delay: 82671 seconds Block delay: 11905
CryticTester.check_depositRebase_2_mid(76177438326569900119935627157228194771856376320707302933865739564856077166341) from: 0x0000000000000000000000000000000000020000 Time delay: 195123 seconds Block delay: 23978
CryticTester.check_0_assets_2() from: 0x0000000000000000000000000000000000010000 Time delay: 136392 seconds Block delay: 2526
CryticTester.check_debt() from: 0x0000000000000000000000000000000000030000 Time delay: 511822 seconds Block delay: 45852
CryticTester.check_0_shares_1() from: 0x0000000000000000000000000000000000010000 Time delay: 136392 seconds Block delay: 1123
CryticTester.check_depositRebase_2(4370001) from: 0x0000000000000000000000000000000000030000 Time delay: 156933 seconds Block delay: 28142
CryticTester.eVault2_withdraw(115792089237316195423570985008687907853269984665640564039457584007913129639935) from: 0x0000000000000000000000000000000000020000 Time delay: 353648 seconds Block delay: 45281
CryticTester.victim_deposit_a(115792089237316195423570985008687907853269984665640564039457584007913129639935) from: 0x0000000000000000000000000000000000030000 Time delay: 434894 seconds Block delay: 15005
CryticTester.victim_deposit_a(115792089237316195423570985008687907853269984665640564039457584007913129639935) from: 0x0000000000000000000000000000000000010000 Time delay: 289607 seconds Block delay: 11942
CryticTester.liquidate_0_sock_check() from: 0x0000000000000000000000000000000000010000 Time delay: 45142 seconds Block delay: 15005
CryticTester.sock_pull_debt(64272264246531907025687649480142021475725454364882195750829610328896307605421) from: 0x0000000000000000000000000000000000020000 Time delay: 195123 seconds Block delay: 1984
CryticTester.inflation_pre_condition_1() from: 0x0000000000000000000000000000000000020000 Time delay: 207289 seconds Block delay: 19933
CryticTester.mockIrm_1setInterestRate(**********) from: 0x0000000000000000000000000000000000020000 Time delay: 116188 seconds Block delay: 47075
CryticTester.canary_max_debt() from: 0x0000000000000000000000000000000000030000 Time delay: 526194 seconds Block delay: 34841
CryticTester.canary_max_debt() from: 0x0000000000000000000000000000000000030000 Time delay: 318197 seconds Block delay: 49415
CryticTester.check_cash_2() from: 0x0000000000000000000000000000000000020000 Time delay: 297507 seconds Block delay: 40520
CryticTester.check_repay_coverage() from: 0x0000000000000000000000000000000000010000 Time delay: 361136 seconds Block delay: 34272
CryticTester.check_fees_2() from: 0x0000000000000000000000000000000000010000 Time delay: 127251 seconds Block delay: 12053
CryticTester.liquidate_0_attacker_check() from: 0x0000000000000000000000000000000000030000 Time delay: 338920 seconds Block delay: 22699
CryticTester.check_0_shares_1() from: 0x0000000000000000000000000000000000010000 Time delay: 332369 seconds Block delay: 30256
CryticTester.check_debt() from: 0x0000000000000000000000000000000000020000 Time delay: 520562 seconds Block delay: 33357
CryticTester.check_redeem_1(115792089237316195423570985008687907853269984665640564039457584007913129639933) from: 0x0000000000000000000000000000000000030000 Time delay: 512439 seconds Block delay: 11349
CryticTester.check_depositRebase_1_crit(1000) from: 0x0000000000000000000000000000000000030000 Time delay: 67960 seconds Block delay: 35200
CryticTester.check_0_shares_1() from: 0x0000000000000000000000000000000000010000 Time delay: 332369 seconds Block delay: 46422
CryticTester.doomsday_deposit_e12() from: 0x0000000000000000000000000000000000010000 Time delay: 289607 seconds Block delay: 47075
CryticTester.check_revert_ts_2() from: 0x0000000000000000000000000000000000010000 Time delay: 487078 seconds Block delay: 30304
CryticTester.check_revert_ta_2() from: 0x0000000000000000000000000000000000010000 Time delay: 32767 seconds Block delay: 11905
CryticTester.check_revert_ta_2() from: 0x0000000000000000000000000000000000020000 Time delay: 82671 seconds Block delay: 24987
CryticTester.check_touch() from: 0x0000000000000000000000000000000000020000 Time delay: 115085 seconds Block delay: 15005
CryticTester.oracle2_setPrice_coll(1002381896461282) from: 0x0000000000000000000000000000000000020000 Time delay: 511822 seconds Block delay: 24987
CryticTester.check_total_2_no_revert() from: 0x0000000000000000000000000000000000030000 Time delay: 569114 seconds Block delay: 57086
CryticTester.check_liquidation_with_no_cost() from: 0x0000000000000000000000000000000000030000 Time delay: 468784 seconds Block delay: 800
CryticTester.check_the_overflow() from: 0x0000000000000000000000000000000000010000 Time delay: 522178 seconds Block delay: 12338
CryticTester.check_lost_yield_canary() from: 0x0000000000000000000000000000000000030000 Time delay: 318197 seconds Block delay: 31753
CryticTester.check_lost_debt_canary() from: 0x0000000000000000000000000000000000020000 Time delay: 16802 seconds Block delay: 45852
CryticTester.eVault_repay_shares_to_sock(4370000) from: 0x0000000000000000000000000000000000030000 Time delay: 38059 seconds Block delay: 23275
CryticTester.liquidate_attacker_clamped() from: 0x0000000000000000000000000000000000020000 Time delay: 419861 seconds Block delay: 55538
CryticTester.eVault_deposit(149055780642332668048537989559421238451121938476753256908295173480816660077) from: 0x0000000000000000000000000000000000030000 Time delay: 166184 seconds Block delay: 30304
CryticTester.liquidate_0_attacker_check_with_real_liq() from: 0x0000000000000000000000000000000000030000 Time delay: 521319 seconds Block delay: 42595
CryticTester.check_repay_coverage() from: 0x0000000000000000000000000000000000020000 Time delay: 390247 seconds Block delay: 16089
CryticTester.check_0_shares_1() from: 0x0000000000000000000000000000000000020000 Time delay: 521319 seconds Block delay: 5054
CryticTester.check_fees_2() from: 0x0000000000000000000000000000000000020000 Time delay: 116188 seconds Block delay: 58783
CryticTester.liquidate_sock(115792089237316195423570985008687907853269984665640564039457584007913129639935,4370001) from: 0x0000000000000000000000000000000000010000 Time delay: 512439 seconds Block delay: 59552
CryticTester.eVault_deposit(20402164996052607624190729268151808362633094425628621310999751509878286835059) from: 0x0000000000000000000000000000000000010000 Time delay: 19029 seconds Block delay: 5140
CryticTester.testTheOverflow() from: 0x0000000000000000000000000000000000020000 Time delay: 490448 seconds Block delay: 53166
CryticTester.liquidate_0_attacker_check_with_real_liq() from: 0x0000000000000000000000000000000000010000 Time delay: 19029 seconds Block delay: 59983
CryticTester.doomsday_deposit_e18() from: 0x0000000000000000000000000000000000020000 Time delay: 401699 seconds Block delay: 35200
CryticTester.check_cash() from: 0x0000000000000000000000000000000000020000 Time delay: 481350 seconds Block delay: 32767
CryticTester.eVault_repay_shares(77260124022922916104490298277970283333620802961912589483712501304140442616155) from: 0x0000000000000000000000000000000000010000 Time delay: 437838 seconds Block delay: 59982
CryticTester.check_repay_coverage() from: 0x0000000000000000000000000000000000030000 Time delay: 65643 seconds Block delay: 12053
CryticTester.check_lost_yield_canary() from: 0x0000000000000000000000000000000000010000 Time delay: 459807 seconds Block delay: 49415

[2024-07-24 17:42:25.49]  Saved reproducer to echidna/reproducers-unshrunk/3832316584732308607.txt
[2024-07-24 17:42:31.12] [status] tests: 11/78, fuzzing: 21687/1000000, values: [], cov: 42340, corpus: 34
[2024-07-24 17:42:34.12] [status] tests: 11/78, fuzzing: 22260/1000000, values: [], cov: 42340, corpus: 34
[2024-07-24 17:42:37.13] [status] tests: 11/78, fuzzing: 22921/1000000, values: [], cov: 42340, corpus: 34
[2024-07-24 17:42:40.13] [status] tests: 11/78, fuzzing: 23389/1000000, values: [], cov: 42340, corpus: 34
[2024-07-24 17:42:43.14] [status] tests: 11/78, fuzzing: 23894/1000000, values: [], cov: 42340, corpus: 34
[2024-07-24 17:42:46.14] [status] tests: 11/78, fuzzing: 24383/1000000, values: [], cov: 42426, corpus: 34
[2024-07-24 17:42:46.17] [Worker 7] New coverage: 42426 instr, 15 contracts, 35 seqs in corpus
[2024-07-24 17:42:46.26]  Saved reproducer to echidna/coverage/1138017137145050589.txt
[2024-07-24 17:42:49.15] [status] tests: 11/78, fuzzing: 24989/1000000, values: [], cov: 42426, corpus: 35
[2024-07-24 17:42:52.15] [status] tests: 11/78, fuzzing: 25494/1000000, values: [], cov: 42426, corpus: 35
[2024-07-24 17:42:55.15] [status] tests: 11/78, fuzzing: 26100/1000000, values: [], cov: 42426, corpus: 35
[2024-07-24 17:42:58.16] [status] tests: 11/78, fuzzing: 26807/1000000, values: [], cov: 42426, corpus: 35
[2024-07-24 17:43:00.61] [Worker 8] New coverage: 42429 instr, 15 contracts, 36 seqs in corpus
[2024-07-24 17:43:00.71]  Saved reproducer to echidna/coverage/9053465805830069306.txt
[2024-07-24 17:43:01.16] [status] tests: 11/78, fuzzing: 27354/1000000, values: [], cov: 42429, corpus: 36
[2024-07-24 17:43:04.17] [status] tests: 11/78, fuzzing: 27973/1000000, values: [], cov: 42429, corpus: 36
[2024-07-24 17:43:05.84] [Worker 4] New coverage: 42443 instr, 15 contracts, 37 seqs in corpus
[2024-07-24 17:43:05.93]  Saved reproducer to echidna/coverage/4574928416457750815.txt
[2024-07-24 17:43:07.17] [status] tests: 11/78, fuzzing: 28579/1000000, values: [], cov: 42443, corpus: 37
[2024-07-24 17:43:10.18] [status] tests: 11/78, fuzzing: 29127/1000000, values: [], cov: 42443, corpus: 37
[2024-07-24 17:43:13.18] [status] tests: 11/78, fuzzing: 29723/1000000, values: [], cov: 42443, corpus: 37
[2024-07-24 17:43:16.22] [status] tests: 11/78, fuzzing: 30127/1000000, values: [], cov: 42443, corpus: 37
[2024-07-24 17:43:19.22] [status] tests: 11/78, fuzzing: 30795/1000000, values: [], cov: 42464, corpus: 37
[2024-07-24 17:43:19.61] [Worker 8] New coverage: 42464 instr, 15 contracts, 38 seqs in corpus
[2024-07-24 17:43:19.70]  Saved reproducer to echidna/coverage/2331288290992892840.txt
[2024-07-24 17:43:22.22] [status] tests: 11/78, fuzzing: 31401/1000000, values: [], cov: 42464, corpus: 38
[2024-07-24 17:43:25.23] [status] tests: 11/78, fuzzing: 31906/1000000, values: [], cov: 42464, corpus: 38
[2024-07-24 17:43:28.23] [status] tests: 11/78, fuzzing: 32512/1000000, values: [], cov: 42464, corpus: 38
[2024-07-24 17:43:31.24] [status] tests: 11/78, fuzzing: 33118/1000000, values: [], cov: 42464, corpus: 38
[2024-07-24 17:43:34.24] [status] tests: 11/78, fuzzing: 33780/1000000, values: [], cov: 42464, corpus: 38
[2024-07-24 17:43:37.24] [status] tests: 11/78, fuzzing: 34285/1000000, values: [], cov: 42464, corpus: 38
[2024-07-24 17:43:40.25] [status] tests: 11/78, fuzzing: 34991/1000000, values: [], cov: 42464, corpus: 38
[2024-07-24 17:43:43.25] [status] tests: 11/78, fuzzing: 35496/1000000, values: [], cov: 42464, corpus: 38
[2024-07-24 17:43:46.26] [status] tests: 11/78, fuzzing: 36001/1000000, values: [], cov: 42464, corpus: 38
[2024-07-24 17:43:49.26] [status] tests: 11/78, fuzzing: 36683/1000000, values: [], cov: 42464, corpus: 38
[2024-07-24 17:43:52.26] [status] tests: 11/78, fuzzing: 37221/1000000, values: [], cov: 42464, corpus: 38
[2024-07-24 17:43:55.27] [status] tests: 11/78, fuzzing: 37602/1000000, values: [], cov: 42464, corpus: 38
[2024-07-24 17:43:58.27] [status] tests: 11/78, fuzzing: 38309/1000000, values: [], cov: 42464, corpus: 38
[2024-07-24 17:44:01.27] [status] tests: 11/78, fuzzing: 38814/1000000, values: [], cov: 42464, corpus: 38
[2024-07-24 17:44:04.28] [status] tests: 11/78, fuzzing: 39319/1000000, values: [], cov: 42464, corpus: 38
[2024-07-24 17:44:07.28] [status] tests: 11/78, fuzzing: 40026/1000000, values: [], cov: 42464, corpus: 38
[2024-07-24 17:44:10.28] [status] tests: 11/78, fuzzing: 40632/1000000, values: [], cov: 42464, corpus: 38
[2024-07-24 17:44:13.28] [status] tests: 11/78, fuzzing: 41137/1000000, values: [], cov: 42464, corpus: 38
[2024-07-24 17:44:16.28] [status] tests: 11/78, fuzzing: 41699/1000000, values: [], cov: 42464, corpus: 38
[2024-07-24 17:44:19.29] [status] tests: 11/78, fuzzing: 42406/1000000, values: [], cov: 42464, corpus: 38
[2024-07-24 17:44:22.29] [status] tests: 11/78, fuzzing: 43012/1000000, values: [], cov: 42464, corpus: 38
[2024-07-24 17:44:25.30] [status] tests: 11/78, fuzzing: 43476/1000000, values: [], cov: 42464, corpus: 38
[2024-07-24 17:44:28.30] [status] tests: 11/78, fuzzing: 44061/1000000, values: [], cov: 42464, corpus: 38
[2024-07-24 17:44:31.31] [status] tests: 11/78, fuzzing: 44566/1000000, values: [], cov: 42464, corpus: 38
[2024-07-24 17:44:34.31] [status] tests: 11/78, fuzzing: 45267/1000000, values: [], cov: 42464, corpus: 38
[2024-07-24 17:44:37.31] [status] tests: 11/78, fuzzing: 45859/1000000, values: [], cov: 42464, corpus: 38
[2024-07-24 17:44:40.32] [status] tests: 11/78, fuzzing: 46364/1000000, values: [], cov: 42464, corpus: 38
[2024-07-24 17:44:43.32] [status] tests: 11/78, fuzzing: 46970/1000000, values: [], cov: 42464, corpus: 38
[2024-07-24 17:44:46.32] [status] tests: 11/78, fuzzing: 47475/1000000, values: [], cov: 42464, corpus: 38
[2024-07-24 17:44:49.32] [status] tests: 11/78, fuzzing: 47980/1000000, values: [], cov: 42464, corpus: 38
[2024-07-24 17:44:52.33] [status] tests: 11/78, fuzzing: 48621/1000000, values: [], cov: 42464, corpus: 38
[2024-07-24 17:44:55.33] [status] tests: 11/78, fuzzing: 49221/1000000, values: [], cov: 42464, corpus: 38
[2024-07-24 17:44:58.33] [status] tests: 11/78, fuzzing: 49603/1000000, values: [], cov: 42464, corpus: 38
[2024-07-24 17:45:01.34] [status] tests: 11/78, fuzzing: 50108/1000000, values: [], cov: 42464, corpus: 38
[2024-07-24 17:45:04.34] [status] tests: 11/78, fuzzing: 50714/1000000, values: [], cov: 42464, corpus: 38
[2024-07-24 17:45:07.30] [Worker 4] New coverage: 42513 instr, 15 contracts, 39 seqs in corpus
[2024-07-24 17:45:07.34] [status] tests: 11/78, fuzzing: 51402/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:45:07.39]  Saved reproducer to echidna/coverage/8551411978914349562.txt
[2024-07-24 17:45:10.34] [status] tests: 11/78, fuzzing: 51907/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:45:13.35] [status] tests: 11/78, fuzzing: 52513/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:45:16.35] [status] tests: 11/78, fuzzing: 53011/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:45:19.35] [status] tests: 11/78, fuzzing: 53718/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:45:22.36] [status] tests: 11/78, fuzzing: 54324/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:45:25.36] [status] tests: 11/78, fuzzing: 54883/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:45:28.36] [status] tests: 11/78, fuzzing: 55449/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:45:31.36] [status] tests: 11/78, fuzzing: 56055/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:45:34.37] [status] tests: 11/78, fuzzing: 56661/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:45:37.37] [status] tests: 11/78, fuzzing: 57263/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:45:40.37] [status] tests: 11/78, fuzzing: 57768/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:45:43.38] [status] tests: 11/78, fuzzing: 58361/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:45:46.38] [status] tests: 11/78, fuzzing: 58967/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:45:49.38] [status] tests: 11/78, fuzzing: 59674/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:45:52.38] [status] tests: 11/78, fuzzing: 60203/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:45:55.39] [status] tests: 11/78, fuzzing: 60915/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:45:58.39] [status] tests: 11/78, fuzzing: 61420/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:46:01.40] [status] tests: 11/78, fuzzing: 62121/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:46:04.40] [status] tests: 11/78, fuzzing: 62607/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:46:07.40] [status] tests: 11/78, fuzzing: 63302/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:46:10.40] [status] tests: 11/78, fuzzing: 63754/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:46:13.41] [status] tests: 11/78, fuzzing: 64441/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:46:16.41] [status] tests: 11/78, fuzzing: 65045/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:46:19.41] [status] tests: 11/78, fuzzing: 65550/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:46:22.42] [status] tests: 11/78, fuzzing: 66091/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:46:25.42] [status] tests: 11/78, fuzzing: 66899/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:46:28.42] [status] tests: 11/78, fuzzing: 67303/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:46:31.43] [status] tests: 11/78, fuzzing: 67909/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:46:34.43] [status] tests: 11/78, fuzzing: 68571/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:46:37.43] [status] tests: 11/78, fuzzing: 69095/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:46:40.44] [status] tests: 11/78, fuzzing: 69701/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:46:43.44] [status] tests: 12/78, fuzzing: 70205/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:46:40.50] [Worker 8] Test check_liquidation_with_no_cost() falsified!
  Call sequence:
CryticTester.eVault2_deposit(96706626614986035331780822125798068034783097484247346612948016543362348630761) from: 0x0000000000000000000000000000000000030000 Time delay: 303345 seconds Block delay: 23403
CryticTester.check_revert_ts_2() from: 0x0000000000000000000000000000000000010000 Time delay: 82672 seconds Block delay: 4485
CryticTester.check_total_2_no_revert() from: 0x0000000000000000000000000000000000010000 Time delay: 100835 seconds Block delay: 39602
CryticTester.eVault_mint(0) from: 0x0000000000000000000000000000000000030000 Time delay: 511822 seconds Block delay: 42101
CryticTester.check_revert_ta_2() from: 0x0000000000000000000000000000000000030000 Time delay: 115085 seconds Block delay: 23978
CryticTester.inflation_pre_condition_2() from: 0x0000000000000000000000000000000000030000 Time delay: 554465 seconds Block delay: 30304
CryticTester.liquidate_attacker(0,4369999) from: 0x0000000000000000000000000000000000010000 Time delay: 31594 seconds Block delay: 59981
CryticTester.attacker_pull_debt(95173209588821205723408253856837770318154572986242546514038429824561686189062) from: 0x0000000000000000000000000000000000010000 Time delay: 402615 seconds Block delay: 5053
CryticTester.liquidate_sock(43579539100236751822463620172333792598719457652038700718393147442143188414814,**********) from: 0x0000000000000000000000000000000000030000 Time delay: 415353 seconds Block delay: 12493
CryticTester.eVault2_mint(18319792898892604411521469075990742878129365022089394788346752381429680976137) from: 0x0000000000000000000000000000000000010000 Time delay: 414736 seconds Block delay: 12338
CryticTester.eVault2_deposit(4928050) from: 0x0000000000000000000000000000000000010000 Time delay: 112444 seconds Block delay: 47723
CryticTester.victim_deposit_b(1013) from: 0x0000000000000000000000000000000000010000 Time delay: 16802 seconds Block delay: 6721
CryticTester.eVault_repay_shares_to_sock(0) from: 0x0000000000000000000000000000000000020000 Time delay: 150273 seconds Block delay: 53678
CryticTester.oracle1_setPrice_debt(4370000) from: 0x0000000000000000000000000000000000030000 Time delay: 112444 seconds Block delay: 2497
CryticTester.testTheOverflow() from: 0x0000000000000000000000000000000000020000 Time delay: 289103 seconds Block delay: 19933
CryticTester.attacker_pull_debt(24700014245831639396821187438902956996600080829018171829755882113927113538602) from: 0x0000000000000000000000000000000000030000 Time delay: 590198 seconds Block delay: 54809
CryticTester.liquidate_sock_clamped() from: 0x0000000000000000000000000000000000020000 Time delay: 275394 seconds Block delay: 127
CryticTester.doomsday_deposit_e6() from: 0x0000000000000000000000000000000000010000 Time delay: 379552 seconds Block delay: 24987
CryticTester.check_total_no_revert() from: 0x0000000000000000000000000000000000010000 Time delay: 407328 seconds Block delay: 12155
CryticTester.attacker_pull_debt(15819666259) from: 0x0000000000000000000000000000000000020000 Time delay: 24867 seconds Block delay: 255
CryticTester.oracle2_setPrice_coll(0) from: 0x0000000000000000000000000000000000020000 Time delay: 136394 seconds Block delay: 24311
CryticTester.eVault_repay_sock(22060444062774202210175824081187884445037373717798628486855685838000527801669) from: 0x0000000000000000000000000000000000010000 Time delay: 401699 seconds Block delay: 58783
CryticTester.eVault2_redeem(60302460297018930334972722799667548573079449600822965022530353560919832318091) from: 0x0000000000000000000000000000000000010000 Time delay: 444463 seconds Block delay: 2526
CryticTester.check_depositRebase_1_mid(94026910709780092526402492310427858592170224712470774975719234017968071811926) from: 0x0000000000000000000000000000000000010000 Time delay: 73040 seconds Block delay: 9966
CryticTester.liquidate_attacker(36559540317499491538214082810059859815557142172256182287508085612862877126566,4370000) from: 0x0000000000000000000000000000000000020000 Time delay: 150273 seconds Block delay: 42595
CryticTester.eVault_mint(0) from: 0x0000000000000000000000000000000000030000 Time delay: 414736 seconds Block delay: 59552
CryticTester.liquidate_0_sock_check() from: 0x0000000000000000000000000000000000010000 Time delay: 37351 seconds Block delay: 59982
CryticTester.check_liquidation_solvency() from: 0x0000000000000000000000000000000000030000 Time delay: 414579 seconds Block delay: 362
CryticTester.testTheOverflow() from: 0x0000000000000000000000000000000000010000 Time delay: 434894 seconds Block delay: 42229
CryticTester.eVault_repay_sock(76844945610675493130525280216473020951098678976005057150533561866507790986217) from: 0x0000000000000000000000000000000000010000 Time delay: 305572 seconds Block delay: 7323
CryticTester.check_fees() from: 0x0000000000000000000000000000000000030000 Time delay: 255 seconds Block delay: 8447
CryticTester.eVault_mint(1524785991) from: 0x0000000000000000000000000000000000030000 Time delay: 38059 seconds Block delay: 23978
CryticTester.mockIrm_2setInterestRate(56078056763137345528120083913344288638649066446058766610796692426130667665865) from: 0x0000000000000000000000000000000000010000 Time delay: 127 seconds Block delay: 45852
CryticTester.attacker_pull_debt(19683267871635916278181255656400348548159491414194204231619531585296246) from: 0x0000000000000000000000000000000000030000 Time delay: 482712 seconds Block delay: 24311
CryticTester.check_total_2_no_revert() from: 0x0000000000000000000000000000000000020000 Time delay: 511438 seconds Block delay: 5023
CryticTester.eVault_repay_shares(4370001) from: 0x0000000000000000000000000000000000020000 Time delay: 289103 seconds Block delay: 54155
CryticTester.check_redeem_2(53838008622669226826458340226883018401358628325751096640011795474340745362093) from: 0x0000000000000000000000000000000000030000 Time delay: 111322 seconds Block delay: 35200
CryticTester.mockIrm_2setInterestRate(**********) from: 0x0000000000000000000000000000000000010000 Time delay: 439556 seconds Block delay: 5023
CryticTester.oracle2_setPrice_debt(275349239273020267239483095194177781006) from: 0x0000000000000000000000000000000000020000 Time delay: 482712 seconds Block delay: 1088
CryticTester.check_fees() from: 0x0000000000000000000000000000000000030000 Time delay: 206186 seconds Block delay: 54809
CryticTester.inflation_pre_condition_1() from: 0x0000000000000000000000000000000000010000 Time delay: 404997 seconds Block delay: 59982
CryticTester.doomsday_deposit_e12() from: 0x0000000000000000000000000000000000010000 Time delay: 275394 seconds Block delay: 45819
CryticTester.sock_deposit_b(109162185432039908090367485489052272227856362788200480176645250031112595805344) from: 0x0000000000000000000000000000000000020000 Time delay: 136392 seconds Block delay: 59981
CryticTester.check_acc() from: 0x0000000000000000000000000000000000020000 Time delay: 297507 seconds Block delay: 30011
CryticTester.check_repay_coverage() from: 0x0000000000000000000000000000000000010000 Time delay: 117472 seconds Block delay: 24987
CryticTester.check_depositRebase_2(43577529584110373268496982486348829938960510974469326231628762202720317516794) from: 0x0000000000000000000000000000000000030000 Time delay: 492049 seconds Block delay: 4896
CryticTester.testTheOverflow() from: 0x0000000000000000000000000000000000030000 Time delay: 156190 seconds Block delay: 34720
CryticTester.liquidate_attacker(4370001,1524785991) from: 0x0000000000000000000000000000000000010000 Time delay: 254414 seconds Block delay: 30784
CryticTester.check_0_shares_2() from: 0x0000000000000000000000000000000000020000 Time delay: 303345 seconds Block delay: 52885
CryticTester.check_lost_yield_canary() from: 0x0000000000000000000000000000000000020000 Time delay: 303345 seconds Block delay: 22909
CryticTester.check_depositRebase_1_mid(8590372960380611629297833876341922554553446112671746707255) from: 0x0000000000000000000000000000000000030000 Time delay: 16802 seconds Block delay: 35200
CryticTester.eVault2_withdraw(4370000) from: 0x0000000000000000000000000000000000030000 Time delay: 292304 seconds Block delay: 47075
CryticTester.oracle2_setPrice_coll(657) from: 0x0000000000000000000000000000000000010000 Time delay: 404997 seconds Block delay: 11349
CryticTester.eVault_repay_shares(499) from: 0x0000000000000000000000000000000000030000 Time delay: 292304 seconds Block delay: 42101
CryticTester.liquidate_sock_clamped() from: 0x0000000000000000000000000000000000030000 Time delay: 150273 seconds Block delay: 15005
CryticTester.sock_pull_debt(115792089237316195423570985008687907853269984665640564039457584007913129639935) from: 0x0000000000000000000000000000000000030000 Time delay: 150273 seconds Block delay: 6721
CryticTester.check_depositRebase_2(337) from: 0x0000000000000000000000000000000000030000 Time delay: 275394 seconds Block delay: 20109
CryticTester.check_depositRebase_1_crit(92451182756452412003166644847359900884125623944737975871773275635168070977574) from: 0x0000000000000000000000000000000000020000 Time delay: 485769 seconds Block delay: 3661
CryticTester.doomsday_deposit_e12() from: 0x0000000000000000000000000000000000030000 Time delay: 209930 seconds Block delay: 5237
CryticTester.liquidate_0_sock_check() from: 0x0000000000000000000000000000000000020000 Time delay: 130099 seconds Block delay: 34720
CryticTester.check_liquidation_with_no_cost() from: 0x0000000000000000000000000000000000030000 Time delay: 67960 seconds Block delay: 36517

[2024-07-24 17:46:40.53]  Saved reproducer to echidna/reproducers-unshrunk/1288624597861111086.txt
[2024-07-24 17:46:46.45] [status] tests: 12/78, fuzzing: 70710/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:46:49.45] [status] tests: 12/78, fuzzing: 71316/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:46:52.45] [status] tests: 12/78, fuzzing: 71720/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:46:55.46] [status] tests: 12/78, fuzzing: 72336/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:46:58.46] [status] tests: 12/78, fuzzing: 72942/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:47:01.46] [status] tests: 12/78, fuzzing: 73630/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:47:04.46] [status] tests: 12/78, fuzzing: 74135/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:47:07.48] [status] tests: 12/78, fuzzing: 74741/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:47:10.49] [status] tests: 12/78, fuzzing: 75448/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:47:13.49] [status] tests: 12/78, fuzzing: 75890/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:47:16.49] [status] tests: 12/78, fuzzing: 76799/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:47:19.49] [status] tests: 12/78, fuzzing: 77479/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:47:22.50] [status] tests: 12/78, fuzzing: 78447/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:47:25.50] [status] tests: 12/78, fuzzing: 79094/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:47:28.50] [status] tests: 12/78, fuzzing: 79798/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:47:31.51] [status] tests: 12/78, fuzzing: 80505/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:47:34.51] [status] tests: 12/78, fuzzing: 81187/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:47:37.52] [status] tests: 12/78, fuzzing: 81692/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:47:40.52] [status] tests: 12/78, fuzzing: 82565/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:47:43.52] [status] tests: 12/78, fuzzing: 83171/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:47:46.52] [status] tests: 12/78, fuzzing: 83811/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:47:49.53] [status] tests: 12/78, fuzzing: 84614/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:47:52.53] [status] tests: 12/78, fuzzing: 85321/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:47:55.53] [status] tests: 12/78, fuzzing: 85927/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:47:58.54] [status] tests: 12/78, fuzzing: 86789/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:48:01.55] [status] tests: 12/78, fuzzing: 87395/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:48:04.55] [status] tests: 12/78, fuzzing: 88203/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:48:07.55] [status] tests: 12/78, fuzzing: 88910/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:48:10.55] [status] tests: 12/78, fuzzing: 89640/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:48:13.56] [status] tests: 12/78, fuzzing: 90448/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:48:16.56] [status] tests: 12/78, fuzzing: 91357/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:48:19.56] [status] tests: 12/78, fuzzing: 91861/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:48:22.56] [status] tests: 12/78, fuzzing: 92664/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:48:25.57] [status] tests: 12/78, fuzzing: 93270/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:48:28.61] [status] tests: 12/78, fuzzing: 94280/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:48:31.61] [status] tests: 12/78, fuzzing: 94975/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:48:34.62] [status] tests: 12/78, fuzzing: 95752/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:48:37.62] [status] tests: 12/78, fuzzing: 96459/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:48:40.62] [status] tests: 12/78, fuzzing: 97076/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:48:43.62] [status] tests: 12/78, fuzzing: 98049/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:48:46.63] [status] tests: 12/78, fuzzing: 98584/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:48:49.64] [status] tests: 12/78, fuzzing: 99380/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:48:52.64] [status] tests: 12/78, fuzzing: 100077/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:48:55.64] [status] tests: 12/78, fuzzing: 100686/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:48:58.64] [status] tests: 12/78, fuzzing: 101382/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:49:01.65] [status] tests: 12/78, fuzzing: 102178/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:49:04.65] [status] tests: 12/78, fuzzing: 102836/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:49:07.65] [status] tests: 12/78, fuzzing: 103583/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:49:10.66] [status] tests: 12/78, fuzzing: 104364/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:49:13.66] [status] tests: 12/78, fuzzing: 105018/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:49:16.66] [status] tests: 12/78, fuzzing: 105826/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:49:19.67] [status] tests: 12/78, fuzzing: 106506/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:49:22.67] [status] tests: 12/78, fuzzing: 107270/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:49:25.68] [status] tests: 12/78, fuzzing: 108075/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:49:28.68] [status] tests: 12/78, fuzzing: 108580/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:49:31.69] [status] tests: 12/78, fuzzing: 109388/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:49:34.69] [status] tests: 12/78, fuzzing: 110140/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:49:37.70] [status] tests: 12/78, fuzzing: 110727/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:49:40.70] [status] tests: 12/78, fuzzing: 111304/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:49:43.70] [status] tests: 12/78, fuzzing: 112271/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:49:46.73] [status] tests: 12/78, fuzzing: 112870/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:49:49.73] [status] tests: 12/78, fuzzing: 113521/1000000, values: [], cov: 42513, corpus: 39
[2024-07-24 17:49:52.24] [Worker 4] New coverage: 42550 instr, 15 contracts, 40 seqs in corpus
[2024-07-24 17:49:52.31]  Saved reproducer to echidna/coverage/452715425800612106.txt
[2024-07-24 17:49:52.74] [status] tests: 12/78, fuzzing: 114415/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:49:55.74] [status] tests: 12/78, fuzzing: 114992/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:49:58.74] [status] tests: 12/78, fuzzing: 115598/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:50:01.76] [status] tests: 12/78, fuzzing: 116305/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:50:04.76] [status] tests: 12/78, fuzzing: 117012/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:50:07.76] [status] tests: 12/78, fuzzing: 117599/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:50:10.76] [status] tests: 12/78, fuzzing: 118609/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:50:13.77] [status] tests: 12/78, fuzzing: 119408/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:50:16.77] [status] tests: 12/78, fuzzing: 119984/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:50:19.77] [status] tests: 12/78, fuzzing: 120857/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:50:22.78] [status] tests: 12/78, fuzzing: 121609/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:50:25.78] [status] tests: 12/78, fuzzing: 122272/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:50:28.79] [status] tests: 12/78, fuzzing: 123044/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:50:31.79] [status] tests: 12/78, fuzzing: 123751/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:50:34.79] [status] tests: 12/78, fuzzing: 124518/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:50:37.80] [status] tests: 12/78, fuzzing: 125326/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:50:40.80] [status] tests: 12/78, fuzzing: 126081/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:50:43.80] [status] tests: 12/78, fuzzing: 126788/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:50:46.81] [status] tests: 12/78, fuzzing: 127559/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:50:49.81] [status] tests: 12/78, fuzzing: 128367/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:50:52.81] [status] tests: 12/78, fuzzing: 129074/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:50:55.81] [status] tests: 12/78, fuzzing: 129680/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:50:58.82] [status] tests: 12/78, fuzzing: 130387/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:51:01.82] [status] tests: 12/78, fuzzing: 131296/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:51:04.82] [status] tests: 12/78, fuzzing: 132205/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:51:07.82] [status] tests: 12/78, fuzzing: 133288/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:51:10.83] [status] tests: 12/78, fuzzing: 134601/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:51:13.83] [status] tests: 12/78, fuzzing: 135563/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:51:16.83] [status] tests: 12/78, fuzzing: 136573/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:51:19.84] [status] tests: 12/78, fuzzing: 137684/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:51:22.84] [status] tests: 12/78, fuzzing: 138566/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:51:25.84] [status] tests: 12/78, fuzzing: 139521/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:51:28.85] [status] tests: 12/78, fuzzing: 140808/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:51:31.85] [status] tests: 12/78, fuzzing: 141777/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:51:34.85] [status] tests: 12/78, fuzzing: 142886/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:51:37.86] [status] tests: 12/78, fuzzing: 143890/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:51:40.86] [status] tests: 12/78, fuzzing: 144948/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:51:43.86] [status] tests: 12/78, fuzzing: 146110/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:51:46.86] [status] tests: 12/78, fuzzing: 147064/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:51:49.86] [status] tests: 12/78, fuzzing: 147950/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:51:52.91] [status] tests: 12/78, fuzzing: 149096/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:51:55.91] [status] tests: 12/78, fuzzing: 150308/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:51:58.92] [status] tests: 12/78, fuzzing: 151189/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:52:01.92] [status] tests: 12/78, fuzzing: 152299/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:52:04.92] [status] tests: 12/78, fuzzing: 153402/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:52:07.93] [status] tests: 12/78, fuzzing: 154381/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:52:10.93] [status] tests: 12/78, fuzzing: 155495/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:52:13.94] [status] tests: 12/78, fuzzing: 156505/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:52:16.95] [status] tests: 12/78, fuzzing: 157373/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:52:19.95] [status] tests: 12/78, fuzzing: 158484/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:52:22.95] [status] tests: 12/78, fuzzing: 159607/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:52:25.96] [status] tests: 12/78, fuzzing: 160610/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:52:28.96] [status] tests: 12/78, fuzzing: 161718/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:52:31.96] [status] tests: 12/78, fuzzing: 162742/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:52:34.97] [status] tests: 12/78, fuzzing: 163752/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:52:37.97] [status] tests: 12/78, fuzzing: 164750/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:52:40.98] [status] tests: 13/78, fuzzing: 165962/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:52:43.98] [status] tests: 13/78, fuzzing: 166768/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:52:40.89] [Worker 6] Test check_fees_2() falsified!
  Call sequence:
CryticTester.testTheOverflow() from: 0x0000000000000000000000000000000000020000 Time delay: 50417 seconds Block delay: 53451
CryticTester.eVault2_withdraw(2213316797) from: 0x0000000000000000000000000000000000020000 Time delay: 255 seconds Block delay: 39596
CryticTester.mockIrm_2setInterestRate(290517506400722145769369647565773050783007955599280211918553530719965276210) from: 0x0000000000000000000000000000000000020000 Time delay: 419861 seconds Block delay: 53011
CryticTester.eVault2_withdraw(166) from: 0x0000000000000000000000000000000000020000 Time delay: 338920 seconds Block delay: 34272
CryticTester.sock_deposit_a(4370000) from: 0x0000000000000000000000000000000000020000 Time delay: 82672 seconds Block delay: 59981
CryticTester.check_0_shares_2() from: 0x0000000000000000000000000000000000030000 Time delay: 436727 seconds Block delay: 60267
CryticTester.sock_borrow_b(573) from: 0x0000000000000000000000000000000000020000 Time delay: 198598 seconds Block delay: 35248
CryticTester.eVault2_deposit(115792089237316195423570985008687907853269984665640564039457584007913129639931) from: 0x0000000000000000000000000000000000030000 Time delay: 407328 seconds Block delay: 45261
CryticTester.attacker_pull_debt(4370000) from: 0x0000000000000000000000000000000000010000 Time delay: 82672 seconds Block delay: 23722
CryticTester.victim_deposit_b(**********) from: 0x0000000000000000000000000000000000010000 Time delay: 414579 seconds Block delay: 22699
CryticTester.check_liquidation_with_no_cost_2() from: 0x0000000000000000000000000000000000020000 Time delay: 275394 seconds Block delay: 53011
CryticTester.check_depositRebase_1_mid(54597655577656910951800612556127061501788729574277590962575112715171744974960) from: 0x0000000000000000000000000000000000010000 Time delay: 437838 seconds Block delay: 59982
CryticTester.check_depositRebase_2_crit(7688249891718730764132880944242935000291218477906994190898661301745423303988) from: 0x0000000000000000000000000000000000020000 Time delay: 194190 seconds Block delay: 4896
CryticTester.doomsday_deposit_e12() from: 0x0000000000000000000000000000000000020000 Time delay: 447588 seconds Block delay: 59983
CryticTester.mockIrm_2setInterestRate(59768240001736942003662671257609902860163924251693153427979817610259422109391) from: 0x0000000000000000000000000000000000020000 Time delay: 73040 seconds Block delay: 53562
CryticTester.check_revert_ta() from: 0x0000000000000000000000000000000000030000 Time delay: 254414 seconds Block delay: 561
CryticTester.check_lost_debt_canary() from: 0x0000000000000000000000000000000000020000 Time delay: 271957 seconds Block delay: 12493
CryticTester.eVault_repay_shares(115792089237316195423570985008687907853269984665640564039457584007913129639932) from: 0x0000000000000000000000000000000000030000 Time delay: 9904 seconds Block delay: 800
CryticTester.eVault_mint(79791331188551349875387990535962023350666613795224512130528352571499381729982) from: 0x0000000000000000000000000000000000030000 Time delay: 439556 seconds Block delay: 1088
CryticTester.doomsday_deposit_e12() from: 0x0000000000000000000000000000000000020000 Time delay: 439556 seconds Block delay: 32767
CryticTester.check_lost_yield_canary() from: 0x0000000000000000000000000000000000020000 Time delay: 49735 seconds Block delay: 23403
CryticTester.canary_max_debt_2() from: 0x0000000000000000000000000000000000030000 Time delay: 136392 seconds Block delay: 1984
CryticTester.check_cash() from: 0x0000000000000000000000000000000000030000 Time delay: 390247 seconds Block delay: 32147
CryticTester.check_touch_2() from: 0x0000000000000000000000000000000000030000 Time delay: 225906 seconds Block delay: 15367
CryticTester.mockIrm_2setInterestRate(1524785991) from: 0x0000000000000000000000000000000000030000 Time delay: 33605 seconds Block delay: 57086
CryticTester.liquidate_0_sock_check_with_real_liq() from: 0x0000000000000000000000000000000000020000 Time delay: 303345 seconds Block delay: 49415
CryticTester.inflation_pre_condition_1() from: 0x0000000000000000000000000000000000020000 Time delay: 415353 seconds Block delay: 2497
CryticTester.doomsday_deposit_e18() from: 0x0000000000000000000000000000000000010000 Time delay: 547623 seconds Block delay: 58783
CryticTester.mockIrm_2setInterestRate(1524785993) from: 0x0000000000000000000000000000000000030000 Time delay: 289607 seconds Block delay: 5054
CryticTester.eVault_redeem(69762434729702617740920706670029047442298422968814997927287287493863981565143) from: 0x0000000000000000000000000000000000020000 Time delay: 24867 seconds Block delay: 55538
CryticTester.check_liquidation_solvency() from: 0x0000000000000000000000000000000000020000 Time delay: 379552 seconds Block delay: 30256
CryticTester.check_0_shares_1() from: 0x0000000000000000000000000000000000020000 Time delay: 136394 seconds Block delay: 38963
CryticTester.check_0_assets_2() from: 0x0000000000000000000000000000000000020000 Time delay: 50417 seconds Block delay: 12493
CryticTester.inflation_pre_condition_1() from: 0x0000000000000000000000000000000000030000 Time delay: 448552 seconds Block delay: 800
CryticTester.inflation_pre_condition_2() from: 0x0000000000000000000000000000000000010000 Time delay: 448552 seconds Block delay: 2511
CryticTester.check_lost_debt_canary() from: 0x0000000000000000000000000000000000020000 Time delay: 379552 seconds Block delay: 8447
CryticTester.eVault2_deposit(1524785991) from: 0x0000000000000000000000000000000000030000 Time delay: 592226 seconds Block delay: 5952
CryticTester.liquidate_0_sock_check_with_real_liq() from: 0x0000000000000000000000000000000000030000 Time delay: 318197 seconds Block delay: 22699
CryticTester.check_total_2_no_revert() from: 0x0000000000000000000000000000000000030000 Time delay: 19029 seconds Block delay: 24519
CryticTester.check_0_shares_1() from: 0x0000000000000000000000000000000000030000 Time delay: 521319 seconds Block delay: 15005
CryticTester.liquidate_attacker(67871483977502617339140084777075425277276928725190810241276625669628072408661,**********) from: 0x0000000000000000000000000000000000010000 Time delay: 547623 seconds Block delay: 51446
CryticTester.liquidate_0_attacker_check() from: 0x0000000000000000000000000000000000020000 Time delay: 401699 seconds Block delay: 11349
CryticTester.eVault_repay_sock(2392242370887779983162446166188513809056253096) from: 0x0000000000000000000000000000000000010000 Time delay: 82670 seconds Block delay: 49415
CryticTester.eVault_withdraw(115792089237316195423570985008687907853269984665640564039457584007913129639935) from: 0x0000000000000000000000000000000000010000 Time delay: 65535 seconds Block delay: 12493
CryticTester.check_depositRebase_2_mid(115792089237316195423570985008687907853269984665640564039457584007913129639934) from: 0x0000000000000000000000000000000000010000 Time delay: 45142 seconds Block delay: 30042
CryticTester.oracle1_setPrice_coll(4370000) from: 0x0000000000000000000000000000000000010000 Time delay: 136392 seconds Block delay: 38350
CryticTester.attacker_pull_debt(4370001) from: 0x0000000000000000000000000000000000030000 Time delay: 448552 seconds Block delay: 30042
CryticTester.check_the_overflow() from: 0x0000000000000000000000000000000000020000 Time delay: 401699 seconds Block delay: 53562
CryticTester.liquidate_0_attacker_check() from: 0x0000000000000000000000000000000000020000 Time delay: 254414 seconds Block delay: 12155
CryticTester.check_liquidation_solvency() from: 0x0000000000000000000000000000000000030000 Time delay: 519847 seconds Block delay: 45261
CryticTester.check_liquidation_with_no_cost_2() from: 0x0000000000000000000000000000000000030000 Time delay: 67960 seconds Block delay: 1362
CryticTester.eVault2_redeem(60493880108352244393118407992904623075660620875290849634176439740567909743239) from: 0x0000000000000000000000000000000000020000 Time delay: 414736 seconds Block delay: 19245
CryticTester.inflation_pre_condition_1() from: 0x0000000000000000000000000000000000020000 Time delay: 575267 seconds Block delay: 24987
CryticTester.sock_deposit_a(4369999) from: 0x0000000000000000000000000000000000020000 Time delay: 419861 seconds Block delay: 30256
CryticTester.check_0_shares_2() from: 0x0000000000000000000000000000000000030000 Time delay: 322247 seconds Block delay: 60364
CryticTester.check_the_overflow() from: 0x0000000000000000000000000000000000010000 Time delay: 344203 seconds Block delay: 45852
CryticTester.check_fees() from: 0x0000000000000000000000000000000000020000 Time delay: 519847 seconds Block delay: 59552
CryticTester.inflation_pre_condition_2() from: 0x0000000000000000000000000000000000010000 Time delay: 82670 seconds Block delay: 31232
CryticTester.eVault_repay_shares(115792089237316195423570985008687907853269984665640564039457584007913129639932) from: 0x0000000000000000000000000000000000020000 Time delay: 112444 seconds Block delay: 11826
CryticTester.eVault_repay_shares(251) from: 0x0000000000000000000000000000000000020000 Time delay: 50417 seconds Block delay: 2497
CryticTester.check_depositRebase_1_mid(4369999) from: 0x0000000000000000000000000000000000030000 Time delay: 463588 seconds Block delay: 53562
CryticTester.check_revert_ta_2() from: 0x0000000000000000000000000000000000010000 Time delay: 440097 seconds Block delay: 22699
CryticTester.eVault_borrow(68005398927290447007885062157654169515560893796672947003900819253374079550247) from: 0x0000000000000000000000000000000000020000 Time delay: 407328 seconds Block delay: 2512
CryticTester.mockIrm_1setInterestRate(4370001) from: 0x0000000000000000000000000000000000030000 Time delay: 49735 seconds Block delay: 55538
CryticTester.eVault2_redeem(60493880108352244393118407992904623075660620875290849634176439740567909743239) from: 0x0000000000000000000000000000000000010000 Time delay: 33605 seconds Block delay: 35248
CryticTester.oracle1_setPrice_debt(92186130958515545868827686035882229960) from: 0x0000000000000000000000000000000000030000 Time delay: 209930 seconds Block delay: 15005
CryticTester.liquidate_attacker_clamped() from: 0x0000000000000000000000000000000000030000 Time delay: 338920 seconds Block delay: 42101
CryticTester.check_redeem_1(781916346386360) from: 0x0000000000000000000000000000000000020000 Time delay: 207289 seconds Block delay: 4896
CryticTester.liquidate_sock(113043282908073750259015647389566037244007582284078343982140159364366351625699,4370001) from: 0x0000000000000000000000000000000000030000 Time delay: 286502 seconds Block delay: 59981
CryticTester.oracle2_setPrice_debt(1524785991) from: 0x0000000000000000000000000000000000030000 Time delay: 207289 seconds Block delay: 255
CryticTester.mockIrm_2setInterestRate(12473764199745374638249394694937454263810425654312398534847341761898282557578) from: 0x0000000000000000000000000000000000010000 Time delay: 82670 seconds Block delay: 54155
CryticTester.eVault2_withdraw(1524785991) from: 0x0000000000000000000000000000000000020000 Time delay: 361136 seconds Block delay: 49415
CryticTester.liquidate_sock_clamped() from: 0x0000000000000000000000000000000000030000 Time delay: 100835 seconds Block delay: 2512
CryticTester.liquidate_sock(50698486845168066500041970034786130961196278629901914465563919507675386424665,34020696678675820650033087909935473491599732880468544267266601555600963213785) from: 0x0000000000000000000000000000000000030000 Time delay: 412373 seconds Block delay: 4462
CryticTester.check_depositRebase_1_mid(63988584713608033120970557483487917733509653474972731974840142945919831395303) from: 0x0000000000000000000000000000000000010000 Time delay: 332369 seconds Block delay: 58783
CryticTester.check_fees_2() from: 0x0000000000000000000000000000000000010000 Time delay: 554465 seconds Block delay: 7323
CryticTester.testTheOverflow() from: 0x0000000000000000000000000000000000030000 Time delay: 344203 seconds Block delay: 23275
CryticTester.check_lost_debt_canary() from: 0x0000000000000000000000000000000000030000 Time delay: 38059 seconds Block delay: 24163
CryticTester.liquidate_attacker_clamped() from: 0x0000000000000000000000000000000000010000 Time delay: 437838 seconds Block delay: 5023
CryticTester.eVault_repay_shares(4370001) from: 0x0000000000000000000000000000000000030000 Time delay: 511822 seconds Block delay: 6721
CryticTester.canary_max_debt() from: 0x0000000000000000000000000000000000010000 Time delay: 49735 seconds Block delay: 1984
CryticTester.check_cash() from: 0x0000000000000000000000000000000000010000 Time delay: 255 seconds Block delay: 42101
CryticTester.eVault_deposit(115792089237316195423570985008687907853269984665640564039457584007913129639935) from: 0x0000000000000000000000000000000000020000 Time delay: 150273 seconds Block delay: 19933
CryticTester.check_repay_coverage() from: 0x0000000000000000000000000000000000020000 Time delay: 526194 seconds Block delay: 31232
CryticTester.testTheDivisionByZero() from: 0x0000000000000000000000000000000000020000 Time delay: 66543 seconds Block delay: 30042
CryticTester.sock_borrow_b(2326154) from: 0x0000000000000000000000000000000000020000 Time delay: 569114 seconds Block delay: 32147
CryticTester.eVault_repay_shares_to_sock(3494308688951782927129227) from: 0x0000000000000000000000000000000000020000 Time delay: 448552 seconds Block delay: 32767
CryticTester.mockIrm_1setInterestRate(1493515970) from: 0x0000000000000000000000000000000000030000 Time delay: 430312 seconds Block delay: 11349
CryticTester.eVault_withdraw(102481506659538707800775416022926917650187576074243422305636786212241725968384) from: 0x0000000000000000000000000000000000010000 Time delay: 415353 seconds Block delay: 53562
CryticTester.check_depositRebase_2(85533588947605982006142414946493620787539804338651091545267319339621032018963) from: 0x0000000000000000000000000000000000030000 Time delay: 490448 seconds Block delay: 60364
CryticTester.testTheDivisionByZero() from: 0x0000000000000000000000000000000000030000 Time delay: 111322 seconds Block delay: 11349
CryticTester.sock_pull_debt(1524785991) from: 0x0000000000000000000000000000000000010000 Time delay: 275394 seconds Block delay: 5140
CryticTester.mockIrm_2setInterestRate(1524785993) from: 0x0000000000000000000000000000000000030000 Time delay: 321929 seconds Block delay: 6234
CryticTester.check_0_assets_2() from: 0x0000000000000000000000000000000000030000 Time delay: 195123 seconds Block delay: 16089
CryticTester.check_depositRebase_2_crit(4370000) from: 0x0000000000000000000000000000000000020000 Time delay: 420078 seconds Block delay: 15369
CryticTester.check_total_2_no_revert() from: 0x0000000000000000000000000000000000020000 Time delay: 554465 seconds Block delay: 23653
CryticTester.check_fees_2() from: 0x0000000000000000000000000000000000020000 Time delay: 275394 seconds Block delay: 5140

[2024-07-24 17:52:40.99]  Saved reproducer to echidna/reproducers-unshrunk/2798091814436938574.txt
[2024-07-24 17:52:46.99] [status] tests: 13/78, fuzzing: 167669/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:52:49.99] [status] tests: 13/78, fuzzing: 168578/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:52:52.99] [status] tests: 13/78, fuzzing: 169386/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:52:56.00] [status] tests: 13/78, fuzzing: 170284/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:52:59.00] [status] tests: 13/78, fuzzing: 171348/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:53:02.00] [status] tests: 13/78, fuzzing: 172085/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:53:05.02] [status] tests: 13/78, fuzzing: 172862/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:53:08.02] [status] tests: 13/78, fuzzing: 173863/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:53:11.03] [status] tests: 13/78, fuzzing: 174562/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:53:14.04] [status] tests: 13/78, fuzzing: 175471/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:53:17.04] [status] tests: 13/78, fuzzing: 176279/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:53:20.04] [status] tests: 13/78, fuzzing: 176986/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:53:23.05] [status] tests: 13/78, fuzzing: 177884/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:53:26.05] [status] tests: 13/78, fuzzing: 178871/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:53:29.05] [status] tests: 13/78, fuzzing: 179881/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:53:32.05] [status] tests: 13/78, fuzzing: 180790/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:53:35.06] [status] tests: 13/78, fuzzing: 181699/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:53:38.06] [status] tests: 13/78, fuzzing: 182684/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:53:41.06] [status] tests: 13/78, fuzzing: 183634/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:53:44.06] [status] tests: 13/78, fuzzing: 184452/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:53:47.07] [status] tests: 13/78, fuzzing: 185272/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:53:50.07] [status] tests: 13/78, fuzzing: 186086/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:53:53.07] [status] tests: 13/78, fuzzing: 187275/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:53:56.08] [status] tests: 13/78, fuzzing: 187982/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:53:59.08] [status] tests: 13/78, fuzzing: 188885/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:54:02.09] [status] tests: 13/78, fuzzing: 189654/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:54:05.09] [status] tests: 13/78, fuzzing: 190462/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:54:08.10] [status] tests: 13/78, fuzzing: 191423/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:54:11.10] [status] tests: 13/78, fuzzing: 192332/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:54:14.10] [status] tests: 13/78, fuzzing: 193443/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:54:17.11] [status] tests: 13/78, fuzzing: 194150/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:54:20.12] [status] tests: 13/78, fuzzing: 195110/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:54:23.12] [status] tests: 13/78, fuzzing: 196019/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:54:26.12] [status] tests: 13/78, fuzzing: 196789/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:54:29.12] [status] tests: 13/78, fuzzing: 197857/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:54:32.12] [status] tests: 13/78, fuzzing: 198863/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:54:35.13] [status] tests: 13/78, fuzzing: 199873/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:54:38.13] [status] tests: 13/78, fuzzing: 200479/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:54:41.13] [status] tests: 13/78, fuzzing: 201468/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:54:44.14] [status] tests: 13/78, fuzzing: 202074/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:54:47.14] [status] tests: 13/78, fuzzing: 203185/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:54:50.15] [status] tests: 13/78, fuzzing: 203892/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:54:53.15] [status] tests: 13/78, fuzzing: 204867/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:54:56.15] [status] tests: 13/78, fuzzing: 205840/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:54:59.15] [status] tests: 13/78, fuzzing: 206634/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:55:02.16] [status] tests: 13/78, fuzzing: 207276/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:55:05.16] [status] tests: 13/78, fuzzing: 208286/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:55:08.16] [status] tests: 13/78, fuzzing: 209188/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:55:11.17] [status] tests: 13/78, fuzzing: 210088/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:55:14.17] [status] tests: 13/78, fuzzing: 211098/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:55:17.17] [status] tests: 13/78, fuzzing: 211851/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:55:20.18] [status] tests: 13/78, fuzzing: 212857/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:55:23.19] [status] tests: 13/78, fuzzing: 213476/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:55:26.19] [status] tests: 13/78, fuzzing: 214676/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:55:29.19] [status] tests: 13/78, fuzzing: 215474/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:55:32.20] [status] tests: 13/78, fuzzing: 216282/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:55:35.20] [status] tests: 13/78, fuzzing: 217375/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:55:38.20] [status] tests: 13/78, fuzzing: 218284/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:55:41.21] [status] tests: 13/78, fuzzing: 218991/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:55:44.21] [status] tests: 13/78, fuzzing: 219951/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:55:47.22] [status] tests: 13/78, fuzzing: 220720/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:55:50.22] [status] tests: 13/78, fuzzing: 221730/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:55:53.22] [status] tests: 13/78, fuzzing: 222504/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:55:56.22] [status] tests: 13/78, fuzzing: 223514/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:55:59.23] [status] tests: 13/78, fuzzing: 224417/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:56:02.23] [status] tests: 13/78, fuzzing: 225183/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:56:05.23] [status] tests: 13/78, fuzzing: 226092/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:56:08.24] [status] tests: 13/78, fuzzing: 226900/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:56:11.24] [status] tests: 13/78, fuzzing: 227878/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:56:14.25] [status] tests: 13/78, fuzzing: 228787/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:56:17.25] [status] tests: 13/78, fuzzing: 229551/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:56:20.26] [status] tests: 13/78, fuzzing: 230662/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:56:23.26] [status] tests: 13/78, fuzzing: 231470/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:56:26.26] [status] tests: 13/78, fuzzing: 232160/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:56:29.27] [status] tests: 13/78, fuzzing: 233170/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:56:32.27] [status] tests: 13/78, fuzzing: 233849/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:56:35.27] [status] tests: 13/78, fuzzing: 234652/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:56:38.28] [status] tests: 13/78, fuzzing: 235632/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:56:41.28] [status] tests: 13/78, fuzzing: 236220/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:56:44.28] [status] tests: 13/78, fuzzing: 237230/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:56:47.28] [status] tests: 13/78, fuzzing: 238038/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:56:50.28] [status] tests: 13/78, fuzzing: 238905/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:56:53.29] [status] tests: 13/78, fuzzing: 239713/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:56:56.30] [status] tests: 13/78, fuzzing: 240521/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:56:59.30] [status] tests: 13/78, fuzzing: 241504/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:57:02.30] [status] tests: 13/78, fuzzing: 242387/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:57:05.30] [status] tests: 13/78, fuzzing: 243168/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:57:08.31] [status] tests: 13/78, fuzzing: 243971/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:57:11.31] [status] tests: 13/78, fuzzing: 244880/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:57:14.31] [status] tests: 13/78, fuzzing: 245791/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:57:17.31] [status] tests: 13/78, fuzzing: 246744/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:57:20.32] [status] tests: 13/78, fuzzing: 247556/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:57:23.32] [status] tests: 13/78, fuzzing: 248443/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:57:26.33] [status] tests: 13/78, fuzzing: 249249/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:57:29.33] [status] tests: 13/78, fuzzing: 250057/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:57:32.33] [status] tests: 13/78, fuzzing: 251067/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:57:35.34] [status] tests: 13/78, fuzzing: 251923/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:57:38.34] [status] tests: 13/78, fuzzing: 253034/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:57:41.34] [status] tests: 13/78, fuzzing: 253943/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:57:44.35] [status] tests: 13/78, fuzzing: 254915/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:57:47.35] [status] tests: 13/78, fuzzing: 256182/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:57:50.35] [status] tests: 13/78, fuzzing: 257143/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:57:53.36] [status] tests: 13/78, fuzzing: 258049/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:57:56.36] [status] tests: 13/78, fuzzing: 259146/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:57:59.36] [status] tests: 13/78, fuzzing: 260141/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:58:02.37] [status] tests: 13/78, fuzzing: 261111/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:58:05.37] [status] tests: 13/78, fuzzing: 262066/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:58:08.38] [status] tests: 13/78, fuzzing: 263278/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:58:11.38] [status] tests: 13/78, fuzzing: 264225/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:58:14.38] [status] tests: 13/78, fuzzing: 265336/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:58:17.39] [status] tests: 13/78, fuzzing: 266319/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:58:20.39] [status] tests: 13/78, fuzzing: 267329/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:58:23.39] [status] tests: 13/78, fuzzing: 268440/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:58:26.40] [status] tests: 13/78, fuzzing: 269431/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:58:29.40] [status] tests: 13/78, fuzzing: 270582/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:58:32.40] [status] tests: 13/78, fuzzing: 271455/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:58:35.41] [status] tests: 13/78, fuzzing: 272465/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:58:38.41] [status] tests: 13/78, fuzzing: 273539/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:58:41.42] [status] tests: 13/78, fuzzing: 274549/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:58:44.42] [status] tests: 13/78, fuzzing: 275698/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:58:47.42] [status] tests: 13/78, fuzzing: 276761/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:58:50.43] [status] tests: 13/78, fuzzing: 277771/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:58:53.44] [status] tests: 13/78, fuzzing: 278680/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:58:56.44] [status] tests: 13/78, fuzzing: 279921/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:58:59.44] [status] tests: 13/78, fuzzing: 280931/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:59:02.44] [status] tests: 13/78, fuzzing: 282084/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:59:05.45] [status] tests: 13/78, fuzzing: 282978/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:59:08.45] [status] tests: 13/78, fuzzing: 283974/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:59:11.46] [status] tests: 13/78, fuzzing: 285085/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:59:14.46] [status] tests: 13/78, fuzzing: 286095/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:59:17.46] [status] tests: 13/78, fuzzing: 287266/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:59:20.46] [status] tests: 13/78, fuzzing: 288374/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:59:23.47] [status] tests: 13/78, fuzzing: 289373/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:59:26.47] [status] tests: 13/78, fuzzing: 290605/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:59:29.47] [status] tests: 13/78, fuzzing: 291514/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:59:32.47] [status] tests: 13/78, fuzzing: 292763/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:59:35.48] [status] tests: 13/78, fuzzing: 293631/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:59:38.48] [status] tests: 13/78, fuzzing: 294843/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:59:41.48] [status] tests: 13/78, fuzzing: 295782/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:59:44.48] [status] tests: 13/78, fuzzing: 296823/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:59:47.49] [status] tests: 13/78, fuzzing: 297861/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:59:50.49] [status] tests: 13/78, fuzzing: 298778/1000000, values: [], cov: 42550, corpus: 40
[2024-07-24 17:59:51.24] [Worker 9] New coverage: 42583 instr, 15 contracts, 41 seqs in corpus
[2024-07-24 17:59:51.33]  Saved reproducer to echidna/coverage/2398094172214473123.txt
[2024-07-24 17:59:53.50] [status] tests: 13/78, fuzzing: 299889/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 17:59:56.50] [status] tests: 13/78, fuzzing: 301038/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 17:59:59.50] [status] tests: 13/78, fuzzing: 301989/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:00:02.50] [status] tests: 13/78, fuzzing: 302894/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:00:05.51] [status] tests: 13/78, fuzzing: 304106/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:00:08.51] [status] tests: 13/78, fuzzing: 304908/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:00:11.52] [status] tests: 13/78, fuzzing: 306140/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:00:14.52] [status] tests: 13/78, fuzzing: 307150/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:00:17.52] [status] tests: 13/78, fuzzing: 308301/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:00:20.52] [status] tests: 13/78, fuzzing: 309363/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:00:23.53] [status] tests: 13/78, fuzzing: 310272/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:00:26.53] [status] tests: 13/78, fuzzing: 311155/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:00:29.53] [status] tests: 13/78, fuzzing: 312266/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:00:32.54] [status] tests: 13/78, fuzzing: 313175/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:00:35.54] [status] tests: 13/78, fuzzing: 314067/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:00:38.54] [status] tests: 13/78, fuzzing: 315077/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:00:41.55] [status] tests: 13/78, fuzzing: 316289/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:00:44.55] [status] tests: 13/78, fuzzing: 317097/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:00:47.55] [status] tests: 13/78, fuzzing: 318309/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:00:50.56] [status] tests: 13/78, fuzzing: 318981/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:00:53.56] [status] tests: 13/78, fuzzing: 320017/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:00:56.56] [status] tests: 13/78, fuzzing: 321250/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:00:59.57] [status] tests: 13/78, fuzzing: 322271/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:01:02.57] [status] tests: 13/78, fuzzing: 323382/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:01:05.57] [status] tests: 13/78, fuzzing: 324291/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:01:08.58] [status] tests: 13/78, fuzzing: 325358/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:01:11.61] [status] tests: 13/78, fuzzing: 326267/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:01:14.62] [status] tests: 13/78, fuzzing: 327378/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:01:17.62] [status] tests: 13/78, fuzzing: 328603/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:01:20.62] [status] tests: 13/78, fuzzing: 329648/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:01:23.62] [status] tests: 13/78, fuzzing: 330658/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:01:26.63] [status] tests: 13/78, fuzzing: 331828/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:01:29.64] [status] tests: 13/78, fuzzing: 332779/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:01:32.64] [status] tests: 13/78, fuzzing: 333833/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:01:35.64] [status] tests: 13/78, fuzzing: 334834/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:01:38.64] [status] tests: 13/78, fuzzing: 335690/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:01:41.65] [status] tests: 13/78, fuzzing: 336863/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:01:44.65] [status] tests: 13/78, fuzzing: 337671/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:01:47.65] [status] tests: 13/78, fuzzing: 338883/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:01:50.66] [status] tests: 13/78, fuzzing: 339613/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:01:53.66] [status] tests: 13/78, fuzzing: 340825/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:01:56.66] [status] tests: 13/78, fuzzing: 341835/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:01:59.67] [status] tests: 13/78, fuzzing: 342836/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:02:02.67] [status] tests: 13/78, fuzzing: 344048/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:02:05.67] [status] tests: 13/78, fuzzing: 345319/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:02:08.68] [status] tests: 13/78, fuzzing: 346663/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:02:11.68] [status] tests: 13/78, fuzzing: 347875/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:02:14.68] [status] tests: 13/78, fuzzing: 348976/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:02:17.68] [status] tests: 13/78, fuzzing: 349960/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:02:20.69] [status] tests: 13/78, fuzzing: 351342/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:02:23.69] [status] tests: 13/78, fuzzing: 352701/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:02:26.70] [status] tests: 13/78, fuzzing: 353711/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:02:29.70] [status] tests: 13/78, fuzzing: 354880/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:02:32.70] [status] tests: 13/78, fuzzing: 356140/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:02:35.70] [status] tests: 13/78, fuzzing: 357194/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:02:38.71] [status] tests: 13/78, fuzzing: 358387/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:02:41.71] [status] tests: 13/78, fuzzing: 359767/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:02:44.71] [status] tests: 13/78, fuzzing: 360977/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:02:47.72] [status] tests: 13/78, fuzzing: 361987/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:02:50.72] [status] tests: 13/78, fuzzing: 363165/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:02:53.73] [status] tests: 13/78, fuzzing: 364175/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:02:56.73] [status] tests: 13/78, fuzzing: 365421/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:02:59.73] [status] tests: 13/78, fuzzing: 366341/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:03:02.73] [status] tests: 13/78, fuzzing: 367686/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:03:05.74] [status] tests: 13/78, fuzzing: 368970/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:03:08.74] [status] tests: 13/78, fuzzing: 370069/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:03:11.75] [status] tests: 13/78, fuzzing: 371176/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:03:14.75] [status] tests: 13/78, fuzzing: 372574/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:03:17.76] [status] tests: 13/78, fuzzing: 374089/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:03:20.76] [status] tests: 13/78, fuzzing: 375291/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:03:23.76] [status] tests: 13/78, fuzzing: 376473/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:03:26.76] [status] tests: 13/78, fuzzing: 377749/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:03:29.77] [status] tests: 13/78, fuzzing: 379215/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:03:32.77] [status] tests: 13/78, fuzzing: 380365/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:03:35.77] [status] tests: 13/78, fuzzing: 381779/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:03:38.78] [status] tests: 13/78, fuzzing: 382991/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:03:41.78] [status] tests: 13/78, fuzzing: 384309/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:03:44.78] [status] tests: 13/78, fuzzing: 385587/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:03:47.78] [status] tests: 13/78, fuzzing: 386849/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:03:50.79] [status] tests: 13/78, fuzzing: 388019/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:03:53.79] [status] tests: 13/78, fuzzing: 389613/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:03:56.79] [status] tests: 13/78, fuzzing: 390825/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:03:59.80] [status] tests: 13/78, fuzzing: 392231/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:04:02.80] [status] tests: 13/78, fuzzing: 393483/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:04:05.80] [status] tests: 13/78, fuzzing: 394786/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:04:08.81] [status] tests: 13/78, fuzzing: 396200/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:04:11.81] [status] tests: 13/78, fuzzing: 397399/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:04:14.81] [status] tests: 13/78, fuzzing: 398815/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:04:17.82] [status] tests: 13/78, fuzzing: 400106/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:04:20.82] [status] tests: 13/78, fuzzing: 401455/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:04:23.82] [status] tests: 13/78, fuzzing: 402869/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:04:26.83] [status] tests: 13/78, fuzzing: 404182/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:04:29.83] [status] tests: 13/78, fuzzing: 405483/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:04:32.84] [status] tests: 13/78, fuzzing: 406851/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:04:35.84] [status] tests: 13/78, fuzzing: 407946/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:04:38.85] [status] tests: 13/78, fuzzing: 409334/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:04:41.85] [status] tests: 13/78, fuzzing: 410521/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:04:44.85] [status] tests: 13/78, fuzzing: 411981/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:04:47.86] [status] tests: 13/78, fuzzing: 413468/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:04:50.86] [status] tests: 13/78, fuzzing: 414739/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:04:53.86] [status] tests: 13/78, fuzzing: 416141/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:04:56.86] [status] tests: 13/78, fuzzing: 417327/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:04:59.86] [status] tests: 13/78, fuzzing: 418741/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:05:02.87] [status] tests: 13/78, fuzzing: 419852/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:05:05.88] [status] tests: 13/78, fuzzing: 421044/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:05:08.88] [status] tests: 13/78, fuzzing: 422227/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:05:11.88] [status] tests: 13/78, fuzzing: 423905/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:05:14.89] [status] tests: 13/78, fuzzing: 424984/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:05:17.89] [status] tests: 13/78, fuzzing: 426439/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:05:20.89] [status] tests: 13/78, fuzzing: 428018/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:05:23.90] [status] tests: 13/78, fuzzing: 429203/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:05:26.90] [status] tests: 13/78, fuzzing: 430792/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:05:29.90] [status] tests: 13/78, fuzzing: 431903/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:05:32.90] [status] tests: 13/78, fuzzing: 433418/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:05:35.91] [status] tests: 13/78, fuzzing: 434684/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:05:38.91] [status] tests: 13/78, fuzzing: 436072/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:05:41.91] [status] tests: 13/78, fuzzing: 437326/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:05:44.92] [status] tests: 13/78, fuzzing: 438740/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:05:47.92] [status] tests: 13/78, fuzzing: 440053/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:05:50.92] [status] tests: 13/78, fuzzing: 441332/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:05:53.92] [status] tests: 13/78, fuzzing: 442645/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:05:56.93] [status] tests: 13/78, fuzzing: 444160/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:05:59.93] [status] tests: 13/78, fuzzing: 445473/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:06:02.93] [status] tests: 13/78, fuzzing: 446921/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:06:05.93] [status] tests: 13/78, fuzzing: 448116/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:06:08.94] [status] tests: 13/78, fuzzing: 449619/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:06:11.94] [status] tests: 13/78, fuzzing: 450755/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:06:14.94] [status] tests: 13/78, fuzzing: 451866/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:06:17.94] [status] tests: 13/78, fuzzing: 453380/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:06:20.95] [status] tests: 13/78, fuzzing: 454658/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:06:23.95] [status] tests: 13/78, fuzzing: 455734/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:06:26.95] [status] tests: 13/78, fuzzing: 457139/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:06:29.95] [status] tests: 13/78, fuzzing: 458452/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:06:32.96] [status] tests: 13/78, fuzzing: 459661/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:06:35.96] [status] tests: 13/78, fuzzing: 461073/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:06:38.96] [status] tests: 13/78, fuzzing: 462243/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:06:41.96] [status] tests: 13/78, fuzzing: 463648/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:06:44.97] [status] tests: 13/78, fuzzing: 464816/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:06:47.97] [status] tests: 13/78, fuzzing: 466310/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:06:50.98] [status] tests: 13/78, fuzzing: 467526/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:06:53.98] [status] tests: 13/78, fuzzing: 468986/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:06:56.98] [status] tests: 13/78, fuzzing: 470057/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:06:59.99] [status] tests: 13/78, fuzzing: 471673/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:07:03.03] [status] tests: 13/78, fuzzing: 472852/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:07:06.03] [status] tests: 13/78, fuzzing: 474230/1000000, values: [], cov: 42583, corpus: 41
[2024-07-24 18:07:08.95] [Worker 5] New coverage: 42583 instr, 15 contracts, 42 seqs in corpus
[2024-07-24 18:07:09.04] [status] tests: 13/78, fuzzing: 475442/1000000, values: [], cov: 42583, corpus: 42
[2024-07-24 18:07:09.05]  Saved reproducer to echidna/coverage/8284673155662089212.txt
[2024-07-24 18:07:12.04] [status] tests: 13/78, fuzzing: 476856/1000000, values: [], cov: 42583, corpus: 42
[2024-07-24 18:07:15.04] [status] tests: 13/78, fuzzing: 478150/1000000, values: [], cov: 42583, corpus: 42
[2024-07-24 18:07:18.04] [status] tests: 13/78, fuzzing: 479524/1000000, values: [], cov: 42583, corpus: 42
[2024-07-24 18:07:21.04] [status] tests: 13/78, fuzzing: 480837/1000000, values: [], cov: 42583, corpus: 42
[2024-07-24 18:07:24.05] [status] tests: 13/78, fuzzing: 482049/1000000, values: [], cov: 42583, corpus: 42
[2024-07-24 18:07:27.05] [status] tests: 13/78, fuzzing: 483286/1000000, values: [], cov: 42583, corpus: 42
[2024-07-24 18:07:30.06] [status] tests: 13/78, fuzzing: 484700/1000000, values: [], cov: 42583, corpus: 42
[2024-07-24 18:07:33.06] [status] tests: 13/78, fuzzing: 486138/1000000, values: [], cov: 42583, corpus: 42
[2024-07-24 18:07:36.07] [status] tests: 13/78, fuzzing: 487420/1000000, values: [], cov: 42583, corpus: 42
[2024-07-24 18:07:39.07] [status] tests: 13/78, fuzzing: 488829/1000000, values: [], cov: 42583, corpus: 42
[2024-07-24 18:07:42.07] [status] tests: 13/78, fuzzing: 490164/1000000, values: [], cov: 42583, corpus: 42
[2024-07-24 18:07:45.08] [status] tests: 13/78, fuzzing: 491609/1000000, values: [], cov: 42583, corpus: 42
[2024-07-24 18:07:48.08] [status] tests: 13/78, fuzzing: 492689/1000000, values: [], cov: 42583, corpus: 42
[2024-07-24 18:07:51.08] [status] tests: 13/78, fuzzing: 494089/1000000, values: [], cov: 42583, corpus: 42
[2024-07-24 18:07:54.08] [status] tests: 13/78, fuzzing: 495278/1000000, values: [], cov: 42583, corpus: 42
[2024-07-24 18:07:57.09] [status] tests: 13/78, fuzzing: 496556/1000000, values: [], cov: 42583, corpus: 42
[2024-07-24 18:08:00.09] [status] tests: 13/78, fuzzing: 497768/1000000, values: [], cov: 42583, corpus: 42
[2024-07-24 18:08:03.09] [status] tests: 13/78, fuzzing: 499176/1000000, values: [], cov: 42586, corpus: 42
[2024-07-24 18:08:03.44] [Worker 2] New coverage: 42586 instr, 15 contracts, 43 seqs in corpus
[2024-07-24 18:08:03.49]  Saved reproducer to echidna/coverage/3588519054049813489.txt
[2024-07-24 18:08:06.10] [status] tests: 13/78, fuzzing: 500466/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:08:09.10] [status] tests: 13/78, fuzzing: 501739/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:08:12.10] [status] tests: 13/78, fuzzing: 502910/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:08:15.10] [status] tests: 13/78, fuzzing: 504441/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:08:18.11] [status] tests: 13/78, fuzzing: 505431/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:08:21.11] [status] tests: 13/78, fuzzing: 506845/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:08:24.11] [status] tests: 13/78, fuzzing: 508183/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:08:27.12] [status] tests: 13/78, fuzzing: 509568/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:08:30.12] [status] tests: 13/78, fuzzing: 510773/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:08:33.12] [status] tests: 13/78, fuzzing: 512102/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:08:36.13] [status] tests: 13/78, fuzzing: 513528/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:08:39.13] [status] tests: 13/78, fuzzing: 514538/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:08:42.19] [status] tests: 13/78, fuzzing: 515851/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:08:45.19] [status] tests: 13/78, fuzzing: 517273/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:08:48.20] [status] tests: 13/78, fuzzing: 518529/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:08:51.20] [status] tests: 13/78, fuzzing: 519975/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:08:54.20] [status] tests: 13/78, fuzzing: 520994/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:08:57.20] [status] tests: 13/78, fuzzing: 522489/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:09:00.21] [status] tests: 13/78, fuzzing: 523600/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:09:03.21] [status] tests: 13/78, fuzzing: 524943/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:09:06.21] [status] tests: 13/78, fuzzing: 526357/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:09:09.22] [status] tests: 13/78, fuzzing: 527548/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:09:12.23] [status] tests: 13/78, fuzzing: 529049/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:09:15.23] [status] tests: 13/78, fuzzing: 530309/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:09:18.24] [status] tests: 13/78, fuzzing: 531525/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:09:21.24] [status] tests: 13/78, fuzzing: 532603/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:09:24.24] [status] tests: 13/78, fuzzing: 534023/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:09:27.24] [status] tests: 13/78, fuzzing: 535209/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:09:30.25] [status] tests: 13/78, fuzzing: 536484/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:09:33.25] [status] tests: 13/78, fuzzing: 537938/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:09:36.26] [status] tests: 13/78, fuzzing: 539235/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:09:39.26] [status] tests: 13/78, fuzzing: 540502/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:09:42.26] [status] tests: 13/78, fuzzing: 541756/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:09:45.27] [status] tests: 13/78, fuzzing: 543214/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:09:48.27] [status] tests: 13/78, fuzzing: 544529/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:09:51.28] [status] tests: 13/78, fuzzing: 545874/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:09:54.28] [status] tests: 13/78, fuzzing: 547045/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:09:57.28] [status] tests: 13/78, fuzzing: 548347/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:10:00.29] [status] tests: 13/78, fuzzing: 549862/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:10:03.30] [status] tests: 13/78, fuzzing: 551097/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:10:06.30] [status] tests: 13/78, fuzzing: 552208/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:10:09.30] [status] tests: 13/78, fuzzing: 553452/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:10:12.31] [status] tests: 13/78, fuzzing: 554958/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:10:15.33] [status] tests: 13/78, fuzzing: 556080/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:10:18.33] [status] tests: 13/78, fuzzing: 557607/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:10:21.33] [status] tests: 13/78, fuzzing: 558718/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:10:24.34] [status] tests: 13/78, fuzzing: 559919/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:10:27.34] [status] tests: 13/78, fuzzing: 561059/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:10:30.34] [status] tests: 13/78, fuzzing: 562474/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:10:33.35] [status] tests: 13/78, fuzzing: 563877/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:10:36.35] [status] tests: 13/78, fuzzing: 565289/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:10:39.35] [status] tests: 13/78, fuzzing: 566466/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:10:42.36] [status] tests: 13/78, fuzzing: 567678/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:10:45.36] [status] tests: 13/78, fuzzing: 568846/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:10:48.36] [status] tests: 13/78, fuzzing: 570058/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:10:51.36] [status] tests: 13/78, fuzzing: 571486/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:10:54.37] [status] tests: 13/78, fuzzing: 572707/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:10:57.37] [status] tests: 13/78, fuzzing: 573892/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:11:00.38] [status] tests: 13/78, fuzzing: 575109/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:11:03.38] [status] tests: 13/78, fuzzing: 576308/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:11:06.38] [status] tests: 13/78, fuzzing: 577423/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:11:09.38] [status] tests: 13/78, fuzzing: 578635/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:11:12.39] [status] tests: 13/78, fuzzing: 579981/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:11:15.39] [status] tests: 13/78, fuzzing: 581243/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:11:18.39] [status] tests: 13/78, fuzzing: 582858/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:11:21.40] [status] tests: 13/78, fuzzing: 584196/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:11:24.40] [status] tests: 13/78, fuzzing: 585382/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:11:27.41] [status] tests: 13/78, fuzzing: 586781/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:11:30.41] [status] tests: 13/78, fuzzing: 588147/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:11:33.41] [status] tests: 13/78, fuzzing: 589306/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:11:36.41] [status] tests: 13/78, fuzzing: 590476/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:11:39.42] [status] tests: 13/78, fuzzing: 591879/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:11:42.42] [status] tests: 13/78, fuzzing: 592990/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:11:45.43] [status] tests: 13/78, fuzzing: 594436/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:11:48.43] [status] tests: 13/78, fuzzing: 595626/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:11:51.44] [status] tests: 13/78, fuzzing: 597040/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:11:54.44] [status] tests: 13/78, fuzzing: 598252/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:11:57.45] [status] tests: 13/78, fuzzing: 599464/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:12:00.45] [status] tests: 13/78, fuzzing: 600949/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:12:03.45] [status] tests: 13/78, fuzzing: 602203/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:12:06.45] [status] tests: 13/78, fuzzing: 603470/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:12:09.46] [status] tests: 13/78, fuzzing: 604724/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:12:12.46] [status] tests: 13/78, fuzzing: 605867/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:12:15.47] [status] tests: 13/78, fuzzing: 607421/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:12:18.47] [status] tests: 13/78, fuzzing: 608654/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:12:21.48] [status] tests: 13/78, fuzzing: 609808/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:12:24.48] [status] tests: 13/78, fuzzing: 611182/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:12:27.48] [status] tests: 13/78, fuzzing: 612675/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:12:30.48] [status] tests: 13/78, fuzzing: 613680/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:12:33.49] [status] tests: 13/78, fuzzing: 615140/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:12:36.49] [status] tests: 13/78, fuzzing: 616458/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:12:39.50] [status] tests: 13/78, fuzzing: 617857/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:12:42.50] [status] tests: 13/78, fuzzing: 619268/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:12:45.50] [status] tests: 13/78, fuzzing: 620727/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:12:48.50] [status] tests: 13/78, fuzzing: 622343/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:12:51.51] [status] tests: 13/78, fuzzing: 623745/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:12:54.51] [status] tests: 13/78, fuzzing: 625089/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:12:57.51] [status] tests: 13/78, fuzzing: 626705/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:13:00.52] [status] tests: 13/78, fuzzing: 628169/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:13:03.52] [status] tests: 13/78, fuzzing: 629640/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:13:06.52] [status] tests: 13/78, fuzzing: 630925/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:13:09.52] [status] tests: 13/78, fuzzing: 632529/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:13:12.53] [status] tests: 13/78, fuzzing: 633952/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:13:15.53] [status] tests: 13/78, fuzzing: 635604/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:13:18.53] [status] tests: 13/78, fuzzing: 636996/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:13:21.54] [status] tests: 13/78, fuzzing: 638670/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:13:24.54] [status] tests: 13/78, fuzzing: 639830/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:13:27.54] [status] tests: 13/78, fuzzing: 641244/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:13:30.54] [status] tests: 13/78, fuzzing: 642886/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:13:33.55] [status] tests: 13/78, fuzzing: 644199/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:13:36.56] [status] tests: 13/78, fuzzing: 645506/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:13:39.56] [status] tests: 13/78, fuzzing: 646768/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:13:42.57] [status] tests: 13/78, fuzzing: 648472/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:13:45.57] [status] tests: 13/78, fuzzing: 649634/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:13:48.58] [status] tests: 13/78, fuzzing: 651135/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:13:51.58] [status] tests: 13/78, fuzzing: 652540/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:13:54.58] [status] tests: 13/78, fuzzing: 654193/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:13:57.58] [status] tests: 13/78, fuzzing: 655506/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:14:00.59] [status] tests: 13/78, fuzzing: 657035/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:14:03.59] [status] tests: 13/78, fuzzing: 658293/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:14:06.59] [status] tests: 13/78, fuzzing: 659707/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:14:09.60] [status] tests: 13/78, fuzzing: 661281/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:14:12.61] [status] tests: 13/78, fuzzing: 662764/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:14:15.61] [status] tests: 13/78, fuzzing: 664119/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:14:18.61] [status] tests: 13/78, fuzzing: 665634/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:14:21.61] [status] tests: 13/78, fuzzing: 667149/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:14:24.62] [status] tests: 13/78, fuzzing: 668547/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:14:27.62] [status] tests: 13/78, fuzzing: 669860/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:14:30.63] [status] tests: 13/78, fuzzing: 671464/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:14:33.63] [status] tests: 13/78, fuzzing: 672743/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:14:36.64] [status] tests: 13/78, fuzzing: 674129/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:14:39.64] [status] tests: 13/78, fuzzing: 675749/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:14:42.64] [status] tests: 13/78, fuzzing: 676882/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:14:45.64] [status] tests: 13/78, fuzzing: 678356/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:14:48.64] [status] tests: 13/78, fuzzing: 679871/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:14:51.65] [status] tests: 13/78, fuzzing: 681110/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:14:54.65] [status] tests: 13/78, fuzzing: 682701/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:14:57.65] [status] tests: 13/78, fuzzing: 684033/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:15:00.66] [status] tests: 13/78, fuzzing: 685426/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:15:03.66] [status] tests: 13/78, fuzzing: 686920/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:15:06.66] [status] tests: 13/78, fuzzing: 688334/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:15:09.66] [status] tests: 13/78, fuzzing: 689748/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:15:12.67] [status] tests: 13/78, fuzzing: 691247/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:15:13.08] [Worker 4] Test limit reached. Stopping.
[2024-07-24 18:15:15.70] [status] tests: 13/78, fuzzing: 692774/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:15:18.74] [status] tests: 13/78, fuzzing: 694105/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:15:21.74] [status] tests: 13/78, fuzzing: 695555/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:15:22.85] [Worker 7] Test limit reached. Stopping.
[2024-07-24 18:15:24.74] [status] tests: 13/78, fuzzing: 697051/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:15:27.74] [status] tests: 13/78, fuzzing: 698251/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:15:30.75] [status] tests: 13/78, fuzzing: 699704/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:15:33.75] [status] tests: 13/78, fuzzing: 701118/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:15:36.75] [status] tests: 13/78, fuzzing: 702473/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:15:39.76] [status] tests: 13/78, fuzzing: 703611/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:15:42.65] [Worker 3] Test limit reached. Stopping.
[2024-07-24 18:15:42.76] [status] tests: 13/78, fuzzing: 705162/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:15:45.76] [status] tests: 13/78, fuzzing: 706273/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:15:48.76] [status] tests: 13/78, fuzzing: 707586/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:15:51.77] [status] tests: 13/78, fuzzing: 708768/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:15:54.77] [status] tests: 13/78, fuzzing: 709997/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:15:57.77] [status] tests: 13/78, fuzzing: 711062/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:16:00.78] [status] tests: 13/78, fuzzing: 712474/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:16:03.78] [status] tests: 13/78, fuzzing: 713787/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:16:06.79] [status] tests: 13/78, fuzzing: 714952/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:16:09.81] [status] tests: 13/78, fuzzing: 716129/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:16:12.82] [status] tests: 13/78, fuzzing: 717481/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:16:15.82] [status] tests: 13/78, fuzzing: 718554/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:16:18.83] [status] tests: 13/78, fuzzing: 719968/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:16:21.83] [status] tests: 13/78, fuzzing: 721209/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:16:24.83] [status] tests: 13/78, fuzzing: 722398/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:16:27.84] [status] tests: 13/78, fuzzing: 723667/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:16:30.84] [status] tests: 13/78, fuzzing: 724878/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:16:33.85] [status] tests: 13/78, fuzzing: 726040/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:16:36.85] [status] tests: 13/78, fuzzing: 727311/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:16:39.85] [status] tests: 13/78, fuzzing: 728650/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:16:42.86] [status] tests: 13/78, fuzzing: 729902/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:16:45.86] [status] tests: 13/78, fuzzing: 731017/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:16:48.87] [status] tests: 13/78, fuzzing: 732402/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:16:51.87] [status] tests: 13/78, fuzzing: 733639/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:16:54.87] [status] tests: 13/78, fuzzing: 735063/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:16:57.88] [status] tests: 13/78, fuzzing: 736407/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:17:00.88] [status] tests: 13/78, fuzzing: 737734/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:17:03.91] [status] tests: 13/78, fuzzing: 738744/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:17:06.92] [status] tests: 13/78, fuzzing: 740091/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:17:09.92] [status] tests: 13/78, fuzzing: 741505/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:17:12.92] [status] tests: 13/78, fuzzing: 742739/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:17:15.92] [status] tests: 13/78, fuzzing: 743909/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:17:18.93] [status] tests: 13/78, fuzzing: 745309/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:17:21.93] [status] tests: 13/78, fuzzing: 746400/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:17:24.94] [status] tests: 13/78, fuzzing: 747573/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:17:27.94] [status] tests: 13/78, fuzzing: 749088/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:17:30.94] [status] tests: 13/78, fuzzing: 750300/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:17:33.94] [status] tests: 13/78, fuzzing: 751536/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:17:36.94] [status] tests: 13/78, fuzzing: 752849/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:17:39.95] [status] tests: 13/78, fuzzing: 754100/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:17:42.95] [status] tests: 13/78, fuzzing: 755211/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:17:45.95] [status] tests: 13/78, fuzzing: 756524/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:17:48.96] [status] tests: 13/78, fuzzing: 757675/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:17:51.96] [status] tests: 13/78, fuzzing: 759089/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:17:54.96] [status] tests: 13/78, fuzzing: 760260/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:17:57.96] [status] tests: 13/78, fuzzing: 761416/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:18:00.97] [status] tests: 13/78, fuzzing: 762527/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:18:03.97] [status] tests: 13/78, fuzzing: 763810/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:18:06.97] [status] tests: 13/78, fuzzing: 765147/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:18:09.98] [status] tests: 13/78, fuzzing: 766460/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:18:12.98] [status] tests: 13/78, fuzzing: 767718/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:18:15.98] [status] tests: 13/78, fuzzing: 768894/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:18:18.98] [status] tests: 13/78, fuzzing: 770106/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:18:21.98] [status] tests: 13/78, fuzzing: 771449/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:18:24.99] [status] tests: 13/78, fuzzing: 772760/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:18:27.99] [status] tests: 13/78, fuzzing: 773961/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:18:30.99] [status] tests: 13/78, fuzzing: 775431/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:18:34.00] [status] tests: 13/78, fuzzing: 776304/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:18:37.00] [status] tests: 13/78, fuzzing: 777561/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:18:40.00] [status] tests: 13/78, fuzzing: 778874/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:18:43.00] [status] tests: 13/78, fuzzing: 780118/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:18:46.01] [status] tests: 13/78, fuzzing: 781330/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:18:49.01] [status] tests: 13/78, fuzzing: 782441/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:18:52.01] [status] tests: 13/78, fuzzing: 783714/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:18:55.02] [status] tests: 13/78, fuzzing: 785153/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:18:58.02] [status] tests: 13/78, fuzzing: 786260/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:19:01.02] [status] tests: 13/78, fuzzing: 787506/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:19:04.02] [status] tests: 13/78, fuzzing: 788768/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:19:07.03] [status] tests: 13/78, fuzzing: 789980/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:19:10.03] [status] tests: 13/78, fuzzing: 791091/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:19:13.04] [status] tests: 13/78, fuzzing: 792404/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:19:16.04] [status] tests: 13/78, fuzzing: 793654/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:19:18.37] [Worker 8] Test limit reached. Stopping.
[2024-07-24 18:19:19.05] [status] tests: 13/78, fuzzing: 794822/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:19:22.05] [status] tests: 13/78, fuzzing: 796148/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:19:25.05] [status] tests: 13/78, fuzzing: 797220/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:19:28.06] [status] tests: 13/78, fuzzing: 798129/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:19:31.06] [status] tests: 13/78, fuzzing: 799341/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:19:34.06] [status] tests: 13/78, fuzzing: 800564/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:19:37.07] [status] tests: 13/78, fuzzing: 801787/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:19:40.07] [status] tests: 13/78, fuzzing: 802742/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:19:43.07] [status] tests: 13/78, fuzzing: 803846/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:19:46.08] [status] tests: 13/78, fuzzing: 805032/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:19:49.08] [status] tests: 13/78, fuzzing: 806196/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:19:52.09] [status] tests: 13/78, fuzzing: 807209/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:19:55.09] [status] tests: 13/78, fuzzing: 808320/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:19:58.09] [status] tests: 13/78, fuzzing: 809493/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:20:01.10] [status] tests: 13/78, fuzzing: 810536/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:20:04.10] [status] tests: 13/78, fuzzing: 811748/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:20:07.10] [status] tests: 13/78, fuzzing: 812831/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:20:10.10] [status] tests: 13/78, fuzzing: 813935/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:20:13.11] [status] tests: 13/78, fuzzing: 815046/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:20:16.11] [status] tests: 13/78, fuzzing: 815961/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:20:19.12] [status] tests: 13/78, fuzzing: 817062/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:20:22.12] [status] tests: 13/78, fuzzing: 818045/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:20:24.48] [Worker 9] Test limit reached. Stopping.
[2024-07-24 18:20:25.12] [status] tests: 13/78, fuzzing: 819459/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:20:28.13] [status] tests: 13/78, fuzzing: 820088/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:20:31.13] [status] tests: 13/78, fuzzing: 821069/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:20:34.13] [status] tests: 13/78, fuzzing: 822051/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:20:37.14] [status] tests: 13/78, fuzzing: 823061/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:20:40.14] [status] tests: 13/78, fuzzing: 823949/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:20:43.14] [status] tests: 13/78, fuzzing: 824920/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:20:46.14] [status] tests: 13/78, fuzzing: 825825/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:20:49.15] [status] tests: 13/78, fuzzing: 826734/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:20:52.15] [status] tests: 13/78, fuzzing: 827643/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:20:55.15] [status] tests: 13/78, fuzzing: 828451/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:20:58.16] [status] tests: 13/78, fuzzing: 829394/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:21:01.16] [status] tests: 13/78, fuzzing: 830375/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:21:04.16] [status] tests: 13/78, fuzzing: 831332/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:21:07.16] [status] tests: 13/78, fuzzing: 832325/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:21:10.17] [status] tests: 13/78, fuzzing: 833234/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:21:13.17] [status] tests: 13/78, fuzzing: 834220/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:21:16.17] [status] tests: 13/78, fuzzing: 835224/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:21:19.18] [status] tests: 13/78, fuzzing: 836114/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:21:22.18] [status] tests: 13/78, fuzzing: 837133/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:21:25.18] [status] tests: 13/78, fuzzing: 837862/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:21:28.18] [status] tests: 13/78, fuzzing: 838949/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:21:31.19] [status] tests: 13/78, fuzzing: 839858/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:21:34.19] [status] tests: 13/78, fuzzing: 840812/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:21:37.20] [status] tests: 13/78, fuzzing: 841735/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:21:40.20] [status] tests: 13/78, fuzzing: 842666/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:21:43.21] [status] tests: 13/78, fuzzing: 843606/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:21:46.21] [status] tests: 13/78, fuzzing: 844638/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:21:49.21] [status] tests: 13/78, fuzzing: 845446/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:21:52.22] [status] tests: 13/78, fuzzing: 846600/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:21:55.22] [status] tests: 13/78, fuzzing: 847602/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:21:58.23] [status] tests: 13/78, fuzzing: 848468/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:22:01.23] [status] tests: 13/78, fuzzing: 849358/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:22:04.23] [status] tests: 13/78, fuzzing: 850368/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:22:07.23] [status] tests: 13/78, fuzzing: 851226/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:22:10.23] [status] tests: 13/78, fuzzing: 852236/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:22:13.24] [status] tests: 13/78, fuzzing: 853141/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:22:16.24] [status] tests: 13/78, fuzzing: 854278/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:22:19.24] [status] tests: 13/78, fuzzing: 855228/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:22:22.24] [status] tests: 13/78, fuzzing: 856238/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:22:25.25] [status] tests: 13/78, fuzzing: 857180/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:22:28.25] [status] tests: 13/78, fuzzing: 857958/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:22:31.26] [status] tests: 13/78, fuzzing: 858961/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:22:34.26] [status] tests: 13/78, fuzzing: 859969/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:22:37.26] [status] tests: 13/78, fuzzing: 860851/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:22:40.27] [status] tests: 13/78, fuzzing: 861791/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:22:43.27] [status] tests: 13/78, fuzzing: 862778/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:22:46.27] [status] tests: 13/78, fuzzing: 863676/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:22:48.01] [Worker 2] Test limit reached. Stopping.
[2024-07-24 18:22:49.28] [status] tests: 13/78, fuzzing: 864484/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:22:52.28] [status] tests: 13/78, fuzzing: 865279/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:22:55.28] [status] tests: 13/78, fuzzing: 866087/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:22:58.29] [status] tests: 13/78, fuzzing: 866794/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:23:01.29] [status] tests: 13/78, fuzzing: 867639/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:23:04.29] [status] tests: 13/78, fuzzing: 868346/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:23:07.30] [status] tests: 13/78, fuzzing: 869317/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:23:10.30] [status] tests: 13/78, fuzzing: 870125/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:23:13.30] [status] tests: 13/78, fuzzing: 870822/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:23:16.31] [status] tests: 13/78, fuzzing: 871523/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:23:19.31] [status] tests: 13/78, fuzzing: 872269/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:23:22.31] [status] tests: 13/78, fuzzing: 873089/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:23:25.32] [status] tests: 13/78, fuzzing: 873935/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:23:28.32] [status] tests: 13/78, fuzzing: 874743/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:23:31.32] [status] tests: 13/78, fuzzing: 875535/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:23:34.32] [status] tests: 13/78, fuzzing: 876056/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:23:37.33] [status] tests: 13/78, fuzzing: 876882/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:23:40.33] [status] tests: 13/78, fuzzing: 877653/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:23:43.34] [status] tests: 13/78, fuzzing: 878451/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:23:46.34] [status] tests: 13/78, fuzzing: 879219/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:23:49.34] [status] tests: 13/78, fuzzing: 880027/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:23:52.34] [status] tests: 13/78, fuzzing: 880647/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:23:55.35] [status] tests: 13/78, fuzzing: 881515/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:23:58.35] [status] tests: 13/78, fuzzing: 882316/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:24:01.36] [status] tests: 13/78, fuzzing: 883130/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:24:04.36] [status] tests: 13/78, fuzzing: 883940/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:24:07.36] [status] tests: 13/78, fuzzing: 884748/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:24:10.36] [status] tests: 13/78, fuzzing: 885455/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:24:13.37] [status] tests: 13/78, fuzzing: 886263/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:24:16.37] [status] tests: 13/78, fuzzing: 887018/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:24:19.37] [status] tests: 13/78, fuzzing: 887925/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:24:22.38] [status] tests: 13/78, fuzzing: 888531/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:24:25.38] [status] tests: 13/78, fuzzing: 889431/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:24:28.38] [status] tests: 13/78, fuzzing: 890037/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:24:31.39] [status] tests: 13/78, fuzzing: 890877/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:24:34.39] [status] tests: 13/78, fuzzing: 891767/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:24:37.39] [status] tests: 13/78, fuzzing: 892524/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:24:40.40] [status] tests: 13/78, fuzzing: 893337/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:24:43.40] [status] tests: 13/78, fuzzing: 894198/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:24:46.40] [status] tests: 13/78, fuzzing: 894905/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:24:49.41] [status] tests: 13/78, fuzzing: 895814/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:24:52.41] [status] tests: 13/78, fuzzing: 896588/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:24:55.42] [status] tests: 13/78, fuzzing: 897416/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:24:58.42] [status] tests: 13/78, fuzzing: 898123/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:25:01.42] [status] tests: 13/78, fuzzing: 898916/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:25:04.42] [status] tests: 13/78, fuzzing: 899623/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:25:07.43] [status] tests: 13/78, fuzzing: 900431/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:25:10.43] [status] tests: 13/78, fuzzing: 901326/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:25:13.44] [status] tests: 13/78, fuzzing: 902075/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:25:16.44] [status] tests: 13/78, fuzzing: 902883/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:25:19.44] [status] tests: 13/78, fuzzing: 903677/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:25:22.44] [status] tests: 13/78, fuzzing: 904381/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:25:25.45] [status] tests: 13/78, fuzzing: 905180/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:25:28.45] [status] tests: 13/78, fuzzing: 905988/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:25:31.45] [status] tests: 13/78, fuzzing: 906764/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:25:34.46] [status] tests: 13/78, fuzzing: 907556/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:25:37.46] [status] tests: 13/78, fuzzing: 908228/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:25:40.46] [status] tests: 13/78, fuzzing: 908963/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:25:43.47] [status] tests: 13/78, fuzzing: 909730/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:25:46.47] [status] tests: 13/78, fuzzing: 910428/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:25:49.47] [status] tests: 13/78, fuzzing: 911264/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:25:52.48] [status] tests: 13/78, fuzzing: 912051/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:25:55.48] [status] tests: 13/78, fuzzing: 912921/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:25:58.48] [status] tests: 13/78, fuzzing: 913721/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:26:01.48] [status] tests: 13/78, fuzzing: 914327/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:26:04.48] [status] tests: 13/78, fuzzing: 915189/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:26:07.49] [status] tests: 13/78, fuzzing: 916046/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:26:10.49] [status] tests: 13/78, fuzzing: 916683/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:26:13.49] [status] tests: 13/78, fuzzing: 917514/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:26:16.50] [status] tests: 13/78, fuzzing: 918355/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:26:19.50] [status] tests: 13/78, fuzzing: 919163/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:26:22.50] [status] tests: 13/78, fuzzing: 920072/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:26:25.50] [status] tests: 13/78, fuzzing: 920645/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:26:28.51] [status] tests: 13/78, fuzzing: 921455/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:26:31.51] [status] tests: 13/78, fuzzing: 922188/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:26:34.52] [status] tests: 13/78, fuzzing: 922895/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:26:37.52] [status] tests: 13/78, fuzzing: 923602/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:26:40.52] [status] tests: 13/78, fuzzing: 924483/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:26:43.52] [status] tests: 13/78, fuzzing: 925272/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:26:46.53] [status] tests: 13/78, fuzzing: 926080/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:26:49.53] [status] tests: 13/78, fuzzing: 926700/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:26:52.53] [status] tests: 13/78, fuzzing: 927572/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:26:55.54] [status] tests: 13/78, fuzzing: 928380/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:26:58.54] [status] tests: 13/78, fuzzing: 929052/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:27:01.54] [status] tests: 13/78, fuzzing: 929890/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:27:04.54] [status] tests: 13/78, fuzzing: 930392/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:27:07.55] [status] tests: 13/78, fuzzing: 931115/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:27:10.55] [status] tests: 13/78, fuzzing: 931987/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:27:13.55] [status] tests: 13/78, fuzzing: 932745/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:27:16.56] [status] tests: 13/78, fuzzing: 933427/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:27:19.56] [status] tests: 13/78, fuzzing: 934134/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:27:22.56] [status] tests: 13/78, fuzzing: 935033/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:27:25.57] [status] tests: 13/78, fuzzing: 935740/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:27:28.57] [status] tests: 13/78, fuzzing: 936631/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:27:31.57] [status] tests: 13/78, fuzzing: 937271/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:27:34.58] [status] tests: 13/78, fuzzing: 938180/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:27:37.58] [status] tests: 13/78, fuzzing: 938988/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:27:40.58] [status] tests: 13/78, fuzzing: 939695/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:27:43.58] [status] tests: 13/78, fuzzing: 940503/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:27:46.59] [status] tests: 13/78, fuzzing: 941210/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:27:49.59] [status] tests: 13/78, fuzzing: 941998/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:27:52.59] [status] tests: 13/78, fuzzing: 942834/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:27:55.60] [status] tests: 13/78, fuzzing: 943596/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:27:58.60] [status] tests: 13/78, fuzzing: 944286/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:28:01.60] [status] tests: 13/78, fuzzing: 944944/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:28:04.60] [status] tests: 13/78, fuzzing: 945829/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:28:07.61] [status] tests: 13/78, fuzzing: 946370/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:28:09.64] [Worker 1] Test limit reached. Stopping.
[2024-07-24 18:28:10.61] [status] tests: 13/78, fuzzing: 947282/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:28:13.61] [status] tests: 13/78, fuzzing: 947828/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:28:16.62] [status] tests: 13/78, fuzzing: 948434/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:28:19.62] [status] tests: 13/78, fuzzing: 949001/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:28:22.62] [status] tests: 13/78, fuzzing: 949565/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:28:25.62] [status] tests: 13/78, fuzzing: 950155/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:28:28.63] [status] tests: 13/78, fuzzing: 950761/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:28:31.63] [status] tests: 13/78, fuzzing: 951175/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:28:34.64] [status] tests: 13/78, fuzzing: 951906/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:28:37.64] [status] tests: 13/78, fuzzing: 952512/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:28:40.64] [status] tests: 13/78, fuzzing: 953118/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:28:43.65] [status] tests: 13/78, fuzzing: 953724/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:28:46.65] [status] tests: 13/78, fuzzing: 954320/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:28:49.65] [status] tests: 13/78, fuzzing: 954825/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:28:52.66] [status] tests: 13/78, fuzzing: 955532/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:28:55.66] [status] tests: 13/78, fuzzing: 956037/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:28:58.66] [status] tests: 13/78, fuzzing: 956564/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:29:01.67] [status] tests: 13/78, fuzzing: 957372/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:29:04.67] [status] tests: 13/78, fuzzing: 957975/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:29:07.67] [status] tests: 13/78, fuzzing: 958480/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:29:10.68] [status] tests: 13/78, fuzzing: 959067/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:29:13.68] [status] tests: 13/78, fuzzing: 959673/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:29:16.69] [status] tests: 13/78, fuzzing: 960380/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:29:19.70] [status] tests: 13/78, fuzzing: 960885/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:29:22.70] [status] tests: 13/78, fuzzing: 961474/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:29:25.71] [status] tests: 13/78, fuzzing: 962080/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:29:28.71] [status] tests: 13/78, fuzzing: 962686/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:29:31.71] [status] tests: 13/78, fuzzing: 963273/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:29:34.72] [status] tests: 13/78, fuzzing: 963953/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:29:37.72] [status] tests: 13/78, fuzzing: 964530/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:29:40.72] [status] tests: 13/78, fuzzing: 965136/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:29:43.73] [status] tests: 13/78, fuzzing: 965712/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:29:46.73] [status] tests: 13/78, fuzzing: 966419/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:29:49.73] [status] tests: 13/78, fuzzing: 966924/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:29:52.74] [status] tests: 13/78, fuzzing: 967598/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:29:55.74] [status] tests: 13/78, fuzzing: 968173/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:29:58.74] [status] tests: 13/78, fuzzing: 968678/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:30:01.75] [status] tests: 13/78, fuzzing: 969284/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:30:04.75] [status] tests: 13/78, fuzzing: 969831/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:30:07.75] [status] tests: 13/78, fuzzing: 970383/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:30:10.76] [status] tests: 13/78, fuzzing: 970989/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:30:13.76] [status] tests: 13/78, fuzzing: 971615/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:30:16.76] [status] tests: 13/78, fuzzing: 972216/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:30:19.76] [status] tests: 13/78, fuzzing: 972878/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:30:22.77] [status] tests: 13/78, fuzzing: 973436/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:30:25.77] [status] tests: 13/78, fuzzing: 974042/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:30:28.78] [status] tests: 13/78, fuzzing: 974648/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:30:31.78] [status] tests: 13/78, fuzzing: 975202/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:30:34.78] [status] tests: 13/78, fuzzing: 975848/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:30:37.79] [status] tests: 13/78, fuzzing: 976353/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:30:40.79] [status] tests: 13/78, fuzzing: 977128/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:30:41.80] [Worker 5] Test limit reached. Stopping.
[2024-07-24 18:30:43.79] [status] tests: 13/78, fuzzing: 977431/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:30:46.80] [status] tests: 13/78, fuzzing: 977835/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:30:49.80] [status] tests: 13/78, fuzzing: 978340/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:30:52.80] [status] tests: 13/78, fuzzing: 978816/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:30:55.80] [status] tests: 13/78, fuzzing: 979119/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:30:58.81] [status] tests: 13/78, fuzzing: 979582/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:31:01.82] [status] tests: 13/78, fuzzing: 979978/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:31:04.82] [status] tests: 13/78, fuzzing: 980281/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:31:07.82] [status] tests: 13/78, fuzzing: 980676/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:31:10.83] [status] tests: 13/78, fuzzing: 981159/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:31:13.83] [status] tests: 13/78, fuzzing: 981563/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:31:16.83] [status] tests: 13/78, fuzzing: 981967/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:31:19.84] [status] tests: 14/78, fuzzing: 982437/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:31:18.52] [Worker 6] Test liquidate_0_sock_check() falsified!
  Call sequence:
CryticTester.canary_max_debt_2() from: 0x0000000000000000000000000000000000010000 Time delay: 447588 seconds Block delay: 15367
CryticTester.check_0_assets_1() from: 0x0000000000000000000000000000000000030000 Time delay: 127 seconds Block delay: 45819
CryticTester.oracle2_setPrice_debt(1524785991) from: 0x0000000000000000000000000000000000030000 Time delay: 360624 seconds Block delay: 561
CryticTester.check_depositRebase_1(**********) from: 0x0000000000000000000000000000000000020000 Time delay: 82672 seconds Block delay: 45819
CryticTester.eVault_withdraw(4369999) from: 0x0000000000000000000000000000000000030000 Time delay: 150273 seconds Block delay: 6721
CryticTester.check_0_assets_1() from: 0x0000000000000000000000000000000000010000 Time delay: 405856 seconds Block delay: 59983
CryticTester.testTheDivisionByZero() from: 0x0000000000000000000000000000000000030000 Time delay: 478623 seconds Block delay: 46422
CryticTester.eVault_repay_sock(**********) from: 0x0000000000000000000000000000000000020000 Time delay: 24867 seconds Block delay: 30784
CryticTester.check_repay_coverage() from: 0x0000000000000000000000000000000000010000 Time delay: 437838 seconds Block delay: 31232
CryticTester.eVault_repay_shares_to_sock(2085316797377721888603521) from: 0x0000000000000000000000000000000000010000 Time delay: 566039 seconds Block delay: 23403
CryticTester.sock_deposit_a(42151513507343299546511792398890082921962223985792904180721326380262652620929) from: 0x0000000000000000000000000000000000020000 Time delay: 405856 seconds Block delay: 8069
CryticTester.check_touch() from: 0x0000000000000000000000000000000000020000 Time delay: 362614 seconds Block delay: 42229
CryticTester.check_redeem_1(2813083127592749467394466804135505152637422757645349936546535861216680273218) from: 0x0000000000000000000000000000000000020000 Time delay: 412373 seconds Block delay: 4791
CryticTester.check_revert_ta_2() from: 0x0000000000000000000000000000000000030000 Time delay: 490448 seconds Block delay: 1362
CryticTester.attacker_pull_debt(4369999) from: 0x0000000000000000000000000000000000010000 Time delay: 34415 seconds Block delay: 7323
CryticTester.doomsday_deposit_e6() from: 0x0000000000000000000000000000000000020000 Time delay: 136394 seconds Block delay: 2511
CryticTester.liquidate_attacker(98207856211452242645848882847308026418791911269353497826205083477065298630834,115792089237316195423570985008687907853269984665640564039457584007913129639931) from: 0x0000000000000000000000000000000000030000 Time delay: 305572 seconds Block delay: 4462
CryticTester.liquidate_0_attacker_check() from: 0x0000000000000000000000000000000000010000 Time delay: 66807 seconds Block delay: 12053
CryticTester.eVault2_deposit(4370001) from: 0x0000000000000000000000000000000000010000 Time delay: 547623 seconds Block delay: 15005
CryticTester.canary_max_debt_2() from: 0x0000000000000000000000000000000000010000 Time delay: 376096 seconds Block delay: 49415
CryticTester.check_repay_coverage() from: 0x0000000000000000000000000000000000020000 Time delay: 352560 seconds Block delay: 53451
CryticTester.eVault2_deposit(63428758898422120912245571130153308656351963588720565897621608085092442059639) from: 0x0000000000000000000000000000000000010000 Time delay: 419861 seconds Block delay: 54809
CryticTester.attacker_pull_debt(946) from: 0x0000000000000000000000000000000000030000 Time delay: 463588 seconds Block delay: 30304
CryticTester.testTheOverflow() from: 0x0000000000000000000000000000000000030000 Time delay: 361136 seconds Block delay: 35200
CryticTester.liquidate_0_attacker_check() from: 0x0000000000000000000000000000000000020000 Time delay: 303345 seconds Block delay: 45852
CryticTester.check_0_assets_2() from: 0x0000000000000000000000000000000000020000 Time delay: 519847 seconds Block delay: 32737
CryticTester.liquidate_0_attacker_check() from: 0x0000000000000000000000000000000000010000 Time delay: 36753 seconds Block delay: 45261
CryticTester.sock_pull_debt(106908678394596242066636414155158146299169223538151066077776506361348224267720) from: 0x0000000000000000000000000000000000030000 Time delay: 440097 seconds Block delay: 42229
CryticTester.sock_deposit_b(3041161075) from: 0x0000000000000000000000000000000000030000 Time delay: 332369 seconds Block delay: 561
CryticTester.eVault2_deposit(26484730254811519111295123780764164136175186195407656993267389142666263482044) from: 0x0000000000000000000000000000000000030000 Time delay: 129134 seconds Block delay: 16089
CryticTester.check_0_assets_2() from: 0x0000000000000000000000000000000000030000 Time delay: 67960 seconds Block delay: 8874
CryticTester.liquidate_attacker(84121038088062655464205862989150466888735597436653509269926851978946424790854,1524785993) from: 0x0000000000000000000000000000000000020000 Time delay: 404997 seconds Block delay: 59608
CryticTester.doomsday_deposit_e12() from: 0x0000000000000000000000000000000000030000 Time delay: 166184 seconds Block delay: 59552
CryticTester.oracle1_setPrice_coll(174774733896427442629117715463724884434) from: 0x0000000000000000000000000000000000020000 Time delay: 33271 seconds Block delay: 561
CryticTester.check_acc() from: 0x0000000000000000000000000000000000030000
CryticTester.check_liquidation_with_no_cost() from: 0x0000000000000000000000000000000000010000
CryticTester.liquidate_sock_clamped() from: 0x0000000000000000000000000000000000020000 Time delay: 198598 seconds Block delay: 23722
CryticTester.check_fees() from: 0x0000000000000000000000000000000000020000 Time delay: 554465 seconds Block delay: 24351
CryticTester.eVault2_deposit(77700723911619354571229254312405094373078714433307925538163811622541714286709) from: 0x0000000000000000000000000000000000030000 Time delay: 376096 seconds Block delay: 53166
CryticTester.check_cash() from: 0x0000000000000000000000000000000000030000 Time delay: 412373 seconds Block delay: 15368
CryticTester.check_depositRebase_1(9293329745052499234323373522844887974167002364890239027693894888790349127463) from: 0x0000000000000000000000000000000000010000
CryticTester.check_total_no_revert() from: 0x0000000000000000000000000000000000020000 Time delay: 78804 seconds Block delay: 60267
CryticTester.check_lost_yield_canary() from: 0x0000000000000000000000000000000000010000 Time delay: 31594 seconds Block delay: 45261
CryticTester.check_debt() from: 0x0000000000000000000000000000000000010000 Time delay: 519847 seconds Block delay: 46422
CryticTester.inflation_pre_condition_1() from: 0x0000000000000000000000000000000000020000 Time delay: 414736 seconds Block delay: 11942
CryticTester.inflation_pre_condition_2() from: 0x0000000000000000000000000000000000010000 Time delay: 407328 seconds Block delay: 46422
CryticTester.eVault2_mint(**********) from: 0x0000000000000000000000000000000000030000 Time delay: 136392 seconds Block delay: 34720
CryticTester.check_debt() from: 0x0000000000000000000000000000000000030000 Time delay: 407328 seconds Block delay: 9762
CryticTester.eVault_repay_shares_to_sock(40463155173407561752676630996252696983773542167540740996555441001836384758047) from: 0x0000000000000000000000000000000000010000 Time delay: 414579 seconds Block delay: 47075
CryticTester.oracle1_setPrice_debt(196162445940443349422505487497644565470) from: 0x0000000000000000000000000000000000010000
CryticTester.eVault_repay(1524785993) from: 0x0000000000000000000000000000000000020000 Time delay: 322374 seconds Block delay: 53349
CryticTester.testTheDivisionByZero() from: 0x0000000000000000000000000000000000020000 Time delay: 385152 seconds Block delay: 38350
CryticTester.victim_deposit_a(1524785991) from: 0x0000000000000000000000000000000000030000 Time delay: 82670 seconds Block delay: 32155
CryticTester.oracle1_setPrice_coll(253634385932063967232430084514260682198) from: 0x0000000000000000000000000000000000020000 Time delay: 127 seconds Block delay: 13950
CryticTester.check_debt() from: 0x0000000000000000000000000000000000020000 Time delay: 49735 seconds Block delay: 35248
CryticTester.check_depositRebase_2_mid(115792089237316195423570985008687907853269984665640564039457584007913129639935) from: 0x0000000000000000000000000000000000010000 Time delay: 547623 seconds Block delay: 4223
CryticTester.liquidate_0_sock_check_with_real_liq() from: 0x0000000000000000000000000000000000020000 Time delay: 439556 seconds Block delay: 60364
CryticTester.check_depositRebase_1_mid(36424865230736019296991726926817181108874022941775122067914706332655096700315) from: 0x0000000000000000000000000000000000010000 Time delay: 344203 seconds Block delay: 8409
CryticTester.check_debt() from: 0x0000000000000000000000000000000000010000 Time delay: 5208 seconds Block delay: 38350
CryticTester.eVault2_withdraw(50557452443060565570874939295331769389918547805125002711824846542268033346583) from: 0x0000000000000000000000000000000000020000 Time delay: 444463 seconds Block delay: 14711
CryticTester.doomsday_deposit_e6() from: 0x0000000000000000000000000000000000010000
CryticTester.liquidate_0_attacker_check() from: 0x0000000000000000000000000000000000010000 Time delay: 136392 seconds Block delay: 44573
CryticTester.check_total_no_revert() from: 0x0000000000000000000000000000000000030000 Time delay: 127251 seconds Block delay: 20243
CryticTester.doomsday_deposit_e12() from: 0x0000000000000000000000000000000000010000 Time delay: 525476 seconds Block delay: 32147
CryticTester.check_cash_2() from: 0x0000000000000000000000000000000000010000
CryticTester.oracle2_setPrice_debt(318655699957443549900917359111067868219) from: 0x0000000000000000000000000000000000030000 Time delay: 127 seconds Block delay: 11826
CryticTester.check_redeem_1(14891244364713878967853180649996452406463339989028473831816726703183896656656) from: 0x0000000000000000000000000000000000030000
CryticTester.check_0_shares_2() from: 0x0000000000000000000000000000000000010000 Time delay: 19029 seconds Block delay: 23403
CryticTester.eVault_withdraw(11479365486303803215257921051151565862144390259670591314238727643387037692810) from: 0x0000000000000000000000000000000000020000 Time delay: 434894 seconds Block delay: 5952
CryticTester.check_liquidation_solvency() from: 0x0000000000000000000000000000000000020000 Time delay: 390247 seconds Block delay: 2497
CryticTester.check_liquidation_with_no_cost_2() from: 0x0000000000000000000000000000000000030000 Time delay: 127251 seconds Block delay: 2497
CryticTester.canary_max_debt() from: 0x0000000000000000000000000000000000030000 Time delay: 82671 seconds Block delay: 53562
CryticTester.sock_deposit_a(31401358312694910716187508805099079211612756499719091495099987467634645350741) from: 0x0000000000000000000000000000000000010000 Time delay: 255 seconds Block delay: 32767
CryticTester.sock_deposit_a(454) from: 0x0000000000000000000000000000000000030000 Time delay: 448552 seconds Block delay: 4223
CryticTester.check_depositRebase_2_crit(64436488849299781928429230491839214120435322778838956737025049553964524769362) from: 0x0000000000000000000000000000000000020000 Time delay: 135921 seconds Block delay: 23403
CryticTester.canary_max_debt() from: 0x0000000000000000000000000000000000020000 Time delay: 379552 seconds Block delay: 23885
CryticTester.eVault_borrow(**********) from: 0x0000000000000000000000000000000000020000
CryticTester.liquidate_sock_clamped() from: 0x0000000000000000000000000000000000030000 Time delay: 111322 seconds Block delay: 12493
CryticTester.sock_deposit_a(885611864508426481944012542672016004737602) from: 0x0000000000000000000000000000000000020000 Time delay: 376096 seconds Block delay: 63
CryticTester.testTheOverflow() from: 0x0000000000000000000000000000000000020000 Time delay: 207289 seconds Block delay: 2585
CryticTester.oracle2_setPrice_debt(200401845158860516839935086537912812603) from: 0x0000000000000000000000000000000000010000 Time delay: 525476 seconds Block delay: 14157
CryticTester.liquidate_0_attacker_check() from: 0x0000000000000000000000000000000000010000 Time delay: 513242 seconds Block delay: 34608
CryticTester.check_total_no_revert() from: 0x0000000000000000000000000000000000030000
CryticTester.check_depositRebase_1_mid(18069047125288052662447487951280882355359120384501471991657162565332965815773) from: 0x0000000000000000000000000000000000030000 Time delay: 566039 seconds Block delay: 15368
CryticTester.liquidate_0_sock_check() from: 0x0000000000000000000000000000000000010000

[2024-07-24 18:31:18.58]  Saved reproducer to echidna/reproducers-unshrunk/8820452801254104946.txt
[2024-07-24 18:31:22.84] [status] tests: 14/78, fuzzing: 982538/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:31:25.84] [status] tests: 14/78, fuzzing: 982740/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:31:28.84] [status] tests: 14/78, fuzzing: 982969/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:31:31.85] [status] tests: 14/78, fuzzing: 983185/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:31:34.85] [status] tests: 14/78, fuzzing: 983387/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:31:37.86] [status] tests: 14/78, fuzzing: 983589/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:31:40.86] [status] tests: 14/78, fuzzing: 983690/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:31:43.86] [status] tests: 14/78, fuzzing: 983993/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:31:46.86] [status] tests: 14/78, fuzzing: 984094/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:31:49.87] [status] tests: 14/78, fuzzing: 984276/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:31:52.87] [status] tests: 14/78, fuzzing: 984478/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:31:55.87] [status] tests: 14/78, fuzzing: 984680/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:31:58.88] [status] tests: 14/78, fuzzing: 984882/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:32:01.88] [status] tests: 14/78, fuzzing: 985084/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:32:03.34] [Worker 0] Test limit reached. Stopping.
[2024-07-24 18:32:04.88] [status] tests: 14/78, fuzzing: 985185/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:32:07.89] [status] tests: 14/78, fuzzing: 985185/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:32:10.89] [status] tests: 14/78, fuzzing: 985185/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:32:13.89] [status] tests: 14/78, fuzzing: 985185/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:32:16.90] [status] tests: 14/78, fuzzing: 985185/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:32:19.90] [status] tests: 14/78, fuzzing: 985185/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:32:22.90] [status] tests: 14/78, fuzzing: 985185/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:32:25.91] [status] tests: 14/78, fuzzing: 985185/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:32:28.91] [status] tests: 14/78, fuzzing: 985185/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:32:31.91] [status] tests: 14/78, fuzzing: 985185/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:32:34.92] [status] tests: 14/78, fuzzing: 985387/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:32:37.92] [status] tests: 14/78, fuzzing: 985589/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:32:40.92] [status] tests: 14/78, fuzzing: 985791/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:32:43.93] [status] tests: 14/78, fuzzing: 985981/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:32:46.93] [status] tests: 14/78, fuzzing: 986183/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:32:49.93] [status] tests: 14/78, fuzzing: 986486/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:32:52.94] [status] tests: 14/78, fuzzing: 986688/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:32:55.94] [status] tests: 14/78, fuzzing: 986890/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:32:58.94] [status] tests: 14/78, fuzzing: 987092/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:33:01.95] [status] tests: 14/78, fuzzing: 987294/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:33:04.95] [status] tests: 14/78, fuzzing: 987496/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:33:07.96] [status] tests: 14/78, fuzzing: 987698/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:33:10.96] [status] tests: 14/78, fuzzing: 987900/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:33:13.96] [status] tests: 14/78, fuzzing: 988102/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:33:16.96] [status] tests: 14/78, fuzzing: 988304/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:33:19.97] [status] tests: 14/78, fuzzing: 988405/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:33:22.97] [status] tests: 14/78, fuzzing: 988607/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:33:25.97] [status] tests: 14/78, fuzzing: 988809/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:33:28.98] [status] tests: 14/78, fuzzing: 989112/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:33:31.98] [status] tests: 14/78, fuzzing: 989314/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:33:34.98] [status] tests: 14/78, fuzzing: 989516/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:33:37.98] [status] tests: 14/78, fuzzing: 989718/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:33:40.99] [status] tests: 14/78, fuzzing: 989920/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:33:43.99] [status] tests: 14/78, fuzzing: 990106/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:33:46.99] [status] tests: 14/78, fuzzing: 990308/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:33:50.00] [status] tests: 14/78, fuzzing: 990510/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:33:53.00] [status] tests: 14/78, fuzzing: 990770/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:33:56.00] [status] tests: 14/78, fuzzing: 990972/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:33:59.00] [status] tests: 14/78, fuzzing: 991174/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:34:02.01] [status] tests: 14/78, fuzzing: 991376/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:34:05.01] [status] tests: 14/78, fuzzing: 991578/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:34:08.01] [status] tests: 14/78, fuzzing: 991780/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:34:11.02] [status] tests: 14/78, fuzzing: 991982/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:34:14.02] [status] tests: 14/78, fuzzing: 992277/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:34:17.03] [status] tests: 14/78, fuzzing: 992453/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:34:20.03] [status] tests: 14/78, fuzzing: 992655/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:34:23.03] [status] tests: 14/78, fuzzing: 992906/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:34:26.04] [status] tests: 14/78, fuzzing: 993145/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:34:29.04] [status] tests: 14/78, fuzzing: 993347/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:34:32.04] [status] tests: 14/78, fuzzing: 993549/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:34:35.05] [status] tests: 14/78, fuzzing: 993751/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:34:38.05] [status] tests: 14/78, fuzzing: 993937/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:34:41.05] [status] tests: 14/78, fuzzing: 994139/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:34:44.06] [status] tests: 14/78, fuzzing: 994340/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:34:47.06] [status] tests: 14/78, fuzzing: 994583/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:34:50.06] [status] tests: 14/78, fuzzing: 994785/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:34:53.06] [status] tests: 14/78, fuzzing: 994987/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:34:56.07] [status] tests: 14/78, fuzzing: 995189/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:34:59.07] [status] tests: 14/78, fuzzing: 995391/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:35:02.07] [status] tests: 14/78, fuzzing: 995694/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:35:05.08] [status] tests: 14/78, fuzzing: 995896/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:35:08.08] [status] tests: 14/78, fuzzing: 996098/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:35:11.09] [status] tests: 14/78, fuzzing: 996300/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:35:14.09] [status] tests: 14/78, fuzzing: 996472/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:35:17.09] [status] tests: 14/78, fuzzing: 996674/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:35:20.10] [status] tests: 14/78, fuzzing: 996876/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:35:23.10] [status] tests: 14/78, fuzzing: 997131/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:35:26.10] [status] tests: 14/78, fuzzing: 997333/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:35:29.11] [status] tests: 14/78, fuzzing: 997535/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:35:32.11] [status] tests: 14/78, fuzzing: 997737/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:35:35.11] [status] tests: 14/78, fuzzing: 997939/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:35:38.12] [status] tests: 14/78, fuzzing: 998141/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:35:41.12] [status] tests: 14/78, fuzzing: 998444/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:35:44.12] [status] tests: 14/78, fuzzing: 998545/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:35:47.12] [status] tests: 14/78, fuzzing: 998848/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:35:50.12] [status] tests: 14/78, fuzzing: 998949/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:35:53.13] [status] tests: 14/78, fuzzing: 999252/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:35:56.13] [status] tests: 14/78, fuzzing: 999454/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:35:59.14] [status] tests: 14/78, fuzzing: 999656/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:36:02.14] [status] tests: 14/78, fuzzing: 999858/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:36:05.14] [status] tests: 14/78, fuzzing: 1000060/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:36:08.15] [status] tests: 14/78, fuzzing: 1000208/1000000, values: [], cov: 42586, corpus: 43
[2024-07-24 18:36:10.92] [Worker 6] Test limit reached. Stopping.
[2024-07-24 18:36:10.92] [status] tests: 14/78, fuzzing: 1000511/1000000, values: [], cov: 42586, corpus: 43
sock_pull_debt(uint256): passing
eVault_redeem(uint256): passing
oracle2_setPrice_coll(uint128): passing
check_touch_2(): passing
liquidate_sock_clamped(): passing
victim_deposit_b(uint256): passing
canary_max_debt_2(): failed!
  Call sequence:
    CryticTester.canary_max_debt_2()

Traces:
call 0x544E6FbB1Af8a15a48882fF15FEb68F9Ef82d882::[1mtotalBorrowsExactXD[0m() [1m(/home/<USER>/recon/test/recon/Properties.sol:23)[0m
 call [1mGenericFactory[0m::implementation()() [1m<no source map>[0m
    (0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B)
 delegatecall 0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B::[1mtotalBorrowsExactXD[0m() [1m<no source map>[0m
   call 0x544E6FbB1Af8a15a48882fF15FEb68F9Ef82d882::[1mviewDelegateXD[0m() [1m<no source map>[0m
     call [1mGenericFactory[0m::implementation()() [1m<no source map>[0m
        (0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B)
     delegatecall 0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B::[1mviewDelegateXD[0m() [1m<no source map>[0m
       delegatecall 0xcd9a70C13C88863ECE51B302a77D2EB98fBBBD65::[1mtotalBorrowsExactXD[0m() [1m<no source map>[0m
          (11150372599265311570767859136324178605506560)
        0x00000000000000000000000000007fffffffffffffffffffffffffff80000000
      0x00000000000000000000000000007fffffffffffffffffffffffffff80000000
    (11150372599265311570767859136324178605506560)
  (11150372599265311570767859136324178605506560)
emit [36mLog[0m(Found max debt 2) [1m(/home/<USER>/recon/lib/chimera/src/CryticAsserts.sol:46)[0m

check_lost_debt_canary(): failed!
  Call sequence:
    CryticTester.mockIrm_2setInterestRate(1)
    CryticTester.oracle2_setPrice_debt(505866853859179363852880)
    CryticTester.liquidate_sock_clamped()
    CryticTester.oracle2_setPrice_coll(0)
    *wait* Time delay: 1 seconds Block delay: 1
    CryticTester.check_touch_2()
    *wait* Time delay: 1 seconds Block delay: 1
    CryticTester.liquidate_0_attacker_check()
    CryticTester.check_lost_debt_canary()

Traces:
call 0x544E6FbB1Af8a15a48882fF15FEb68F9Ef82d882::[1mforgivenDebtXD[0m() [1m(/home/<USER>/recon/test/recon/properties/MaximizationProperties.sol:117)[0m
 call [1mGenericFactory[0m::implementation()() [1m<no source map>[0m
    (0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B)
 delegatecall 0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B::[1mforgivenDebtXD[0m() [1m<no source map>[0m
   call 0x544E6FbB1Af8a15a48882fF15FEb68F9Ef82d882::[1mviewDelegateXD[0m() [1m<no source map>[0m
     call [1mGenericFactory[0m::implementation()() [1m<no source map>[0m
        (0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B)
     delegatecall 0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B::[1mviewDelegateXD[0m() [1m<no source map>[0m
       delegatecall 0xcd9a70C13C88863ECE51B302a77D2EB98fBBBD65::[1mforgivenDebtXD[0m() [1m<no source map>[0m
          (1)
        0x0000000000000000000000000000000000000000000000000000000000000001
      0x0000000000000000000000000000000000000000000000000000000000000001
    (1)
  (1)
emit [36mLog[0m(Some debt lost) [1m(/home/<USER>/recon/lib/chimera/src/CryticAsserts.sol:46)[0m

check_repay_coverage(): failed!
  Call sequence:
    CryticTester.eVault_repay_shares_to_sock(1)
    CryticTester.check_repay_coverage()

Traces:
emit [36mLog[0m(RepaySock) [1m(/home/<USER>/recon/lib/chimera/src/CryticAsserts.sol:46)[0m

attacker_pull_debt(uint256): passing
check_redeem_2(uint256): failed!
  Call sequence:
    CryticTester.oracle2_setPrice_coll(0)
    CryticTester.liquidate_sock_clamped()
    CryticTester.check_redeem_2(1)

Traces:
call 0x544E6FbB1Af8a15a48882fF15FEb68F9Ef82d882::[1mpreviewRedeemXD[0m(1) [1m(/home/<USER>/recon/test/recon/properties/DoomsdayProperties.sol:149)[0m
 call [1mGenericFactory[0m::implementation()() [1m<no source map>[0m
    (0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B)
 delegatecall 0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B::[1mpreviewRedeemXD[0m(1) [1m<no source map>[0m
   call 0x544E6FbB1Af8a15a48882fF15FEb68F9Ef82d882::[1mviewDelegateXD[0m() [1m<no source map>[0m
     call [1mGenericFactory[0m::implementation()() [1m<no source map>[0m
        (0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B)
     delegatecall 0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B::[1mviewDelegateXD[0m() [1m<no source map>[0m
       delegatecall 0xb13B5e34092E21A53Cb1110A3050Bd0e117Dc15D::[1mpreviewRedeemXD[0m(1) [1m<no source map>[0m
          (0)
        0x0000000000000000000000000000000000000000000000000000000000000000
      0x0000000000000000000000000000000000000000000000000000000000000000
    (0)
  (0)
emit [36mLog[0m(Can never get zero shares) [1m(/home/<USER>/recon/lib/chimera/src/CryticAsserts.sol:46)[0m

check_liquidation_with_no_cost_2(): failed!
  Call sequence:
    CryticTester.attacker_pull_debt(1)
    CryticTester.oracle2_setPrice_coll(0)
    CryticTester.check_liquidation_with_no_cost_2()

Traces:
call 0x544E6FbB1Af8a15a48882fF15FEb68F9Ef82d882::[1mcheckLiquidationXD[0m(0x0000000000000000000000000000000000050007, [1mCryticTester[0m, 0xd57D9931b305f1bc1622B97c8Cc6747E4A9254a0) [1m(/home/<USER>/recon/test/recon/properties/DoomsdayProperties.sol:37)[0m
 call [1mGenericFactory[0m::implementation()() [1m<no source map>[0m
    (0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B)
 delegatecall 0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B::[1mcheckLiquidationXD[0m(0x0000000000000000000000000000000000050007, [1mCryticTester[0m, 0xd57D9931b305f1bc1622B97c8Cc6747E4A9254a0) [1m<no source map>[0m
   call 0x544E6FbB1Af8a15a48882fF15FEb68F9Ef82d882::[1mviewDelegateXD[0m() [1m<no source map>[0m
     call [1mGenericFactory[0m::implementation()() [1m<no source map>[0m
        (0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B)
     delegatecall 0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B::[1mviewDelegateXD[0m() [1m<no source map>[0m
       delegatecall 0x2A2B68E9Bf718DBf6dF3dF91174A136054a8A0Ec::[1mcheckLiquidationXD[0m(0x0000000000000000000000000000000000050007, [1mCryticTester[0m, 0xd57D9931b305f1bc1622B97c8Cc6747E4A9254a0) [1m<no source map>[0m
         call 0xb4c79daB8f259C7Aee6E5b2Aa729821864227e84::[1mgetCollateralsXD[0m([1mCryticTester[0m) [1m<no source map>[0m
            ([0xd57D9931b305f1bc1622B97c8Cc6747E4A9254a0])
         call 0xb4c79daB8f259C7Aee6E5b2Aa729821864227e84::[1mgetControllersXD[0m([1mCryticTester[0m) [1m<no source map>[0m
            ([0x544E6FbB1Af8a15a48882fF15FEb68F9Ef82d882])
         call 0xb4c79daB8f259C7Aee6E5b2Aa729821864227e84::[1misCollateralEnabledXD[0m([1mCryticTester[0m, 0xd57D9931b305f1bc1622B97c8Cc6747E4A9254a0) [1m<no source map>[0m
            (true)
         call 0xb4c79daB8f259C7Aee6E5b2Aa729821864227e84::[1misAccountStatusCheckDeferredXD[0m([1mCryticTester[0m) [1m<no source map>[0m
            (false)
         call 0xb4c79daB8f259C7Aee6E5b2Aa729821864227e84::[1mgetLastAccountStatusCheckTimestampXD[0m([1mCryticTester[0m) [1m<no source map>[0m
            (**********)
         call 0x000000000000000000636F6e736F6c652e6c6f67::0xc3b5563500000000000000000000000000000000000000000000000000000000000000400000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001b6c697143616368652e6c696162696c6974792e69735a65726f28290000000000 [1m<no source map>[0m
            0x
         call 0xd57D9931b305f1bc1622B97c8Cc6747E4A9254a0::[1mbalanceOfXD[0m([1mCryticTester[0m) [1m<no source map>[0m
           call [1mGenericFactory[0m::implementation()() [1m<no source map>[0m
              (0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B)
           delegatecall 0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B::[1mbalanceOfXD[0m([1mCryticTester[0m) [1m<no source map>[0m
              (2596148429267413814265248164610047)
            (2596148429267413814265248164610047)
         call 0x0A64DF94bc0E039474DB42bb52FEca0c1d540402::[1mgetQuoteXD[0m(2596148429267413814265248164610047, 0xd57D9931b305f1bc1622B97c8Cc6747E4A9254a0, 0x0000000000000000000000000000000000000001) [1m<no source map>[0m
           call 0x000000000000000000636F6e736F6c652e6c6f67::0xb60e72cc00000000000000000000000000000000000000000000000000000000000000400000000000000000000000000000000000007fffffffffffffffffffffffffff0000000000000000000000000000000000000000000000000000000000000008696e416d6f756e74000000000000000000000000000000000000000000000000 [1m<no source map>[0m
              0x
           call 0x000000000000000000636F6e736F6c652e6c6f67::0x319af3330000000000000000000000000000000000000000000000000000000000000040000000000000000000000000d57d9931b305f1bc1622b97c8cc6747e4a9254a000000000000000000000000000000000000000000000000000000000000000046261736500000000000000000000000000000000000000000000000000000000 [1m<no source map>[0m
              0x
            (0)
         call 0x0A64DF94bc0E039474DB42bb52FEca0c1d540402::[1mgetQuoteXD[0m(1, [1mTestERC20[0m, 0x0000000000000000000000000000000000000001) [1m<no source map>[0m
           call 0x000000000000000000636F6e736F6c652e6c6f67::0xb60e72cc000000000000000000000000000000000000000000000000000000000000004000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000008696e416d6f756e74000000000000000000000000000000000000000000000000 [1m<no source map>[0m
              0x
           call 0x000000000000000000636F6e736F6c652e6c6f67::0x319af3330000000000000000000000000000000000000000000000000000000000000040000000000000000000000000d5f051401ca478b34c80d0b5a119e437dc6d9df500000000000000000000000000000000000000000000000000000000000000046261736500000000000000000000000000000000000000000000000000000000 [1m<no source map>[0m
              0x
            (1)
         call 0x000000000000000000636F6e736F6c652e6c6f67::0xb60e72cc000000000000000000000000000000000000000000000000000000000000004000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000020636f6c6c61746572616c41646a757374656456616c75652066726f6d206c6971 [1m<no source map>[0m
            0x
         call 0x000000000000000000636F6e736F6c652e6c6f67::0xb60e72cc0000000000000000000000000000000000000000000000000000000000000040000000000000000000000000000000000000000000000000000000000000000100000000000000000000000000000000000000000000000000000000000000176c696162696c69747956616c75652066726f6d206c6971000000000000000000 [1m<no source map>[0m
            0x
         call 0xd57D9931b305f1bc1622B97c8Cc6747E4A9254a0::[1mbalanceOfXD[0m([1mCryticTester[0m) [1m<no source map>[0m
           call [1mGenericFactory[0m::implementation()() [1m<no source map>[0m
              (0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B)
           delegatecall 0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B::[1mbalanceOfXD[0m([1mCryticTester[0m) [1m<no source map>[0m
              (2596148429267413814265248164610047)
            (2596148429267413814265248164610047)
         call 0x0A64DF94bc0E039474DB42bb52FEca0c1d540402::[1mgetQuoteXD[0m(2596148429267413814265248164610047, 0xd57D9931b305f1bc1622B97c8Cc6747E4A9254a0, 0x0000000000000000000000000000000000000001) [1m<no source map>[0m
           call 0x000000000000000000636F6e736F6c652e6c6f67::0xb60e72cc00000000000000000000000000000000000000000000000000000000000000400000000000000000000000000000000000007fffffffffffffffffffffffffff0000000000000000000000000000000000000000000000000000000000000008696e416d6f756e74000000000000000000000000000000000000000000000000 [1m<no source map>[0m
              0x
           call 0x000000000000000000636F6e736F6c652e6c6f67::0x319af3330000000000000000000000000000000000000000000000000000000000000040000000000000000000000000d57d9931b305f1bc1622b97c8cc6747e4a9254a000000000000000000000000000000000000000000000000000000000000000046261736500000000000000000000000000000000000000000000000000000000 [1m<no source map>[0m
              0x
            (0)
         call 0x000000000000000000636F6e736F6c652e6c6f67::0x41304fac000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000064e6f207761790000000000000000000000000000000000000000000000000000 [1m<no source map>[0m
            0x
          (0, 2596148429267413814265248164610047)
        0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000007fffffffffffffffffffffffffff
      0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000007fffffffffffffffffffffffffff
    (0, 2596148429267413814265248164610047)
  (0, 2596148429267413814265248164610047)
emit [36mLog[0m(Never Free 2) [1m(/home/<USER>/recon/lib/chimera/src/CryticAsserts.sol:46)[0m

oracle1_setPrice_coll(uint128): passing
testTheDivisionByZero(): passing
eVault_repay_shares(uint256): passing
mockIrm_1setInterestRate(uint256): passing
check_lost_yield_canary(): failed!
  Call sequence:
    CryticTester.eVault_repay_sock(19)
    CryticTester.mockIrm_2setInterestRate(2002359671515665471884734173581488817864058415758499678303135189648807748)
    CryticTester.eVault_repay_shares_to_sock(115792089237316195423570985008687907853269984665640564039457584007913129639935)
    CryticTester.eVault_borrow(19)
    *wait* Time delay: 1503167 seconds Block delay: 1
    CryticTester.liquidate_attacker_clamped()
    CryticTester.check_lost_yield_canary()

Traces:
call 0x544E6FbB1Af8a15a48882fF15FEb68F9Ef82d882::[1mlostYieldXD[0m() [1m(/home/<USER>/recon/test/recon/properties/MaximizationProperties.sol:114)[0m
 call [1mGenericFactory[0m::implementation()() [1m<no source map>[0m
    (0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B)
 delegatecall 0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B::[1mlostYieldXD[0m() [1m<no source map>[0m
   call 0x544E6FbB1Af8a15a48882fF15FEb68F9Ef82d882::[1mviewDelegateXD[0m() [1m<no source map>[0m
     call [1mGenericFactory[0m::implementation()() [1m<no source map>[0m
        (0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B)
     delegatecall 0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B::[1mviewDelegateXD[0m() [1m<no source map>[0m
       delegatecall 0xcd9a70C13C88863ECE51B302a77D2EB98fBBBD65::[1mlostYieldXD[0m() [1m<no source map>[0m
          (1)
        0x0000000000000000000000000000000000000000000000000000000000000001
      0x0000000000000000000000000000000000000000000000000000000000000001
    (1)
  (1)
emit [36mLog[0m(Some wei lost due to rounding) [1m(/home/<USER>/recon/lib/chimera/src/CryticAsserts.sol:46)[0m

mockIrm_2setInterestRate(uint256): passing
eVault_repay_shares_to_sock(uint256): passing
check_liquidation_solvency(): failed!
  Call sequence:
    CryticTester.oracle2_setPrice_coll(0)
    CryticTester.check_liquidation_solvency()

Traces:
emit [36mLog[0m(Sock cannot turn self insolvent) [1m(/home/<USER>/recon/lib/chimera/src/CryticAsserts.sol:46)[0m

doomsday_deposit_e12(): passing
eVault2_mint(uint256): passing
check_revert_ts_2(): passing
check_the_overflow(): failed!
  Call sequence:
    CryticTester.check_the_overflow()

Traces:
call [1mCryticTester[0m::testTheOverflow()() [1m(/home/<USER>/recon/test/recon/targets/RevertHelper.sol:70)[0m
 [91merror[0m Revert Panic(0x4e487b710000000000000000000000000000000000000000000000000000000000000011) [1m<source not found>[0m
emit [36mLog[0m(Panic(17)) [1m(/home/<USER>/recon/lib/chimera/src/CryticAsserts.sol:46)[0m

check_acc(): passing
check_touch(): passing
eVault_withdraw(uint256): passing
liquidate_0_attacker_check(): passing
check_liquidation_with_no_cost(): failed!
  Call sequence:
    CryticTester.oracle2_setPrice_coll(0)
    CryticTester.check_liquidation_with_no_cost()

Traces:
call 0x544E6FbB1Af8a15a48882fF15FEb68F9Ef82d882::[1mcheckLiquidationXD[0m([1mCryticTester[0m, 0x0000000000000000000000000000000000050007, 0xd57D9931b305f1bc1622B97c8Cc6747E4A9254a0) [1m(/home/<USER>/recon/test/recon/properties/DoomsdayProperties.sol:33)[0m
 call [1mGenericFactory[0m::implementation()() [1m<no source map>[0m
    (0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B)
 delegatecall 0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B::[1mcheckLiquidationXD[0m([1mCryticTester[0m, 0x0000000000000000000000000000000000050007, 0xd57D9931b305f1bc1622B97c8Cc6747E4A9254a0) [1m<no source map>[0m
   call 0x544E6FbB1Af8a15a48882fF15FEb68F9Ef82d882::[1mviewDelegateXD[0m() [1m<no source map>[0m
     call [1mGenericFactory[0m::implementation()() [1m<no source map>[0m
        (0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B)
     delegatecall 0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B::[1mviewDelegateXD[0m() [1m<no source map>[0m
       delegatecall 0x2A2B68E9Bf718DBf6dF3dF91174A136054a8A0Ec::[1mcheckLiquidationXD[0m([1mCryticTester[0m, 0x0000000000000000000000000000000000050007, 0xd57D9931b305f1bc1622B97c8Cc6747E4A9254a0) [1m<no source map>[0m
         call 0xb4c79daB8f259C7Aee6E5b2Aa729821864227e84::[1mgetCollateralsXD[0m(0x0000000000000000000000000000000000050007) [1m<no source map>[0m
            ([0xd57D9931b305f1bc1622B97c8Cc6747E4A9254a0])
         call 0xb4c79daB8f259C7Aee6E5b2Aa729821864227e84::[1mgetControllersXD[0m(0x0000000000000000000000000000000000050007) [1m<no source map>[0m
            ([0x544E6FbB1Af8a15a48882fF15FEb68F9Ef82d882])
         call 0xb4c79daB8f259C7Aee6E5b2Aa729821864227e84::[1misCollateralEnabledXD[0m(0x0000000000000000000000000000000000050007, 0xd57D9931b305f1bc1622B97c8Cc6747E4A9254a0) [1m<no source map>[0m
            (true)
         call 0xb4c79daB8f259C7Aee6E5b2Aa729821864227e84::[1misAccountStatusCheckDeferredXD[0m(0x0000000000000000000000000000000000050007) [1m<no source map>[0m
            (false)
         call 0xb4c79daB8f259C7Aee6E5b2Aa729821864227e84::[1mgetLastAccountStatusCheckTimestampXD[0m(0x0000000000000000000000000000000000050007) [1m<no source map>[0m
            (**********)
         call 0x000000000000000000636F6e736F6c652e6c6f67::0xc3b5563500000000000000000000000000000000000000000000000000000000000000400000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001b6c697143616368652e6c696162696c6974792e69735a65726f28290000000000 [1m<no source map>[0m
            0x
         call 0xd57D9931b305f1bc1622B97c8Cc6747E4A9254a0::[1mbalanceOfXD[0m(0x0000000000000000000000000000000000050007) [1m<no source map>[0m
           call [1mGenericFactory[0m::implementation()() [1m<no source map>[0m
              (0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B)
           delegatecall 0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B::[1mbalanceOfXD[0m(0x0000000000000000000000000000000000050007) [1m<no source map>[0m
              (2596148429267413814265248164610047)
            (2596148429267413814265248164610047)
         call 0x0A64DF94bc0E039474DB42bb52FEca0c1d540402::[1mgetQuoteXD[0m(2596148429267413814265248164610047, 0xd57D9931b305f1bc1622B97c8Cc6747E4A9254a0, 0x0000000000000000000000000000000000000001) [1m<no source map>[0m
           call 0x000000000000000000636F6e736F6c652e6c6f67::0xb60e72cc00000000000000000000000000000000000000000000000000000000000000400000000000000000000000000000000000007fffffffffffffffffffffffffff0000000000000000000000000000000000000000000000000000000000000008696e416d6f756e74000000000000000000000000000000000000000000000000 [1m<no source map>[0m
              0x
           call 0x000000000000000000636F6e736F6c652e6c6f67::0x319af3330000000000000000000000000000000000000000000000000000000000000040000000000000000000000000d57d9931b305f1bc1622b97c8cc6747e4a9254a000000000000000000000000000000000000000000000000000000000000000046261736500000000000000000000000000000000000000000000000000000000 [1m<no source map>[0m
              0x
            (0)
         call 0x0A64DF94bc0E039474DB42bb52FEca0c1d540402::[1mgetQuoteXD[0m(***********34827628530496329220095, [1mTestERC20[0m, 0x0000000000000000000000000000000000000001) [1m<no source map>[0m
           call 0x000000000000000000636F6e736F6c652e6c6f67::0xb60e72cc0000000000000000000000000000000000000000000000000000000000000040000000000000000000000000000000000000ffffffffffffffffffffffffffff0000000000000000000000000000000000000000000000000000000000000008696e416d6f756e74000000000000000000000000000000000000000000000000 [1m<no source map>[0m
              0x
           call 0x000000000000000000636F6e736F6c652e6c6f67::0x319af3330000000000000000000000000000000000000000000000000000000000000040000000000000000000000000d5f051401ca478b34c80d0b5a119e437dc6d9df500000000000000000000000000000000000000000000000000000000000000046261736500000000000000000000000000000000000000000000000000000000 [1m<no source map>[0m
              0x
            (***********34827628530496329220095)
         call 0x000000000000000000636F6e736F6c652e6c6f67::0xb60e72cc000000000000000000000000000000000000000000000000000000000000004000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000020636f6c6c61746572616c41646a757374656456616c75652066726f6d206c6971 [1m<no source map>[0m
            0x
         call 0x000000000000000000636F6e736F6c652e6c6f67::0xb60e72cc0000000000000000000000000000000000000000000000000000000000000040000000000000000000000000000000000000ffffffffffffffffffffffffffff00000000000000000000000000000000000000000000000000000000000000176c696162696c69747956616c75652066726f6d206c6971000000000000000000 [1m<no source map>[0m
            0x
         call 0xd57D9931b305f1bc1622B97c8Cc6747E4A9254a0::[1mbalanceOfXD[0m(0x0000000000000000000000000000000000050007) [1m<no source map>[0m
           call [1mGenericFactory[0m::implementation()() [1m<no source map>[0m
              (0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B)
           delegatecall 0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B::[1mbalanceOfXD[0m(0x0000000000000000000000000000000000050007) [1m<no source map>[0m
              (2596148429267413814265248164610047)
            (2596148429267413814265248164610047)
         call 0x0A64DF94bc0E039474DB42bb52FEca0c1d540402::[1mgetQuoteXD[0m(2596148429267413814265248164610047, 0xd57D9931b305f1bc1622B97c8Cc6747E4A9254a0, 0x0000000000000000000000000000000000000001) [1m<no source map>[0m
           call 0x000000000000000000636F6e736F6c652e6c6f67::0xb60e72cc00000000000000000000000000000000000000000000000000000000000000400000000000000000000000000000000000007fffffffffffffffffffffffffff0000000000000000000000000000000000000000000000000000000000000008696e416d6f756e74000000000000000000000000000000000000000000000000 [1m<no source map>[0m
              0x
           call 0x000000000000000000636F6e736F6c652e6c6f67::0x319af3330000000000000000000000000000000000000000000000000000000000000040000000000000000000000000d57d9931b305f1bc1622b97c8cc6747e4a9254a000000000000000000000000000000000000000000000000000000000000000046261736500000000000000000000000000000000000000000000000000000000 [1m<no source map>[0m
              0x
            (0)
         call 0x000000000000000000636F6e736F6c652e6c6f67::0x41304fac000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000064e6f207761790000000000000000000000000000000000000000000000000000 [1m<no source map>[0m
            0x
          (0, 2596148429267413814265248164610047)
        0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000007fffffffffffffffffffffffffff
      0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000007fffffffffffffffffffffffffff
    (0, 2596148429267413814265248164610047)
  (0, 2596148429267413814265248164610047)
emit [36mLog[0m(Never Free) [1m(/home/<USER>/recon/lib/chimera/src/CryticAsserts.sol:46)[0m

check_revert_ts(): passing
check_total_2_no_revert(): passing
check_depositRebase_2_mid(uint256): passing
liquidate_0_sock_check_with_real_liq(): passing
check_depositRebase_2_crit(uint256): passing
canary_max_debt(): passing
inflation_pre_condition_1(): passing
doomsday_deposit_e18(): passing
eVault_borrow(uint256): passing
liquidate_0_sock_check(): failed!
  Call sequence:
    CryticTester.liquidate_0_sock_check()

Traces:
call 0x544E6FbB1Af8a15a48882fF15FEb68F9Ef82d882::[1mliquidateXD[0m(0x0000000000000000000000000000000000050007, 0xd57D9931b305f1bc1622B97c8Cc6747E4A9254a0, 0, 0) [1m(/home/<USER>/recon/test/recon/targets/LiquidationTargets.sol:74)[0m
 call [1mGenericFactory[0m::implementation()() [1m<no source map>[0m
    (0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B)
 delegatecall 0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B::[1mliquidateXD[0m(0x0000000000000000000000000000000000050007, 0xd57D9931b305f1bc1622B97c8Cc6747E4A9254a0, 0, 0) [1m<no source map>[0m
   call 0xb4c79daB8f259C7Aee6E5b2Aa729821864227e84::[1mcallXD[0m(0x544E6FbB1Af8a15a48882fF15FEb68F9Ef82d882, [1mCryticTester[0m, 0, 0xc13425740000000000000000000000000000000000000000000000000000000000050007000000000000000000000000d57d9931b305f1bc1622b97c8cc6747e4a9254a000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000) [1m<no source map>[0m
     emit [36mCallWithContext[0m(caller=0x544E6FbB1Af8a15a48882fF15FEb68F9Ef82d882, onBehalfOfAddressPrefix=0x00a329c0648769a73afac7f9381e08fb43dbea, onBehalfOfAccount=[1mCryticTester[0m, targetContract=0x544E6FbB1Af8a15a48882fF15FEb68F9Ef82d882, selector=0xc1342574) [1m<no source map>[0m
     call 0x544E6FbB1Af8a15a48882fF15FEb68F9Ef82d882::[1mliquidateXD[0m(0x0000000000000000000000000000000000050007, 0xd57D9931b305f1bc1622B97c8Cc6747E4A9254a0, 0, 0) [1m<no source map>[0m
       call [1mGenericFactory[0m::implementation()() [1m<no source map>[0m
          (0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B)
       delegatecall 0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B::[1mliquidateXD[0m(0x0000000000000000000000000000000000050007, 0xd57D9931b305f1bc1622B97c8Cc6747E4A9254a0, 0, 0) [1m<no source map>[0m
         delegatecall 0x2A2B68E9Bf718DBf6dF3dF91174A136054a8A0Ec::[1mliquidateXD[0m(0x0000000000000000000000000000000000050007, 0xd57D9931b305f1bc1622B97c8Cc6747E4A9254a0, 0, 0) [1m<no source map>[0m
           call 0xb4c79daB8f259C7Aee6E5b2Aa729821864227e84::[1mgetCurrentOnBehalfOfAccountXD[0m(0x544E6FbB1Af8a15a48882fF15FEb68F9Ef82d882) [1m<no source map>[0m
              ([1mCryticTester[0m, true)
           call 0xb4c79daB8f259C7Aee6E5b2Aa729821864227e84::[1mrequireAccountAndVaultStatusCheckXD[0m([1mCryticTester[0m) [1m<no source map>[0m
              0x
           call 0xb4c79daB8f259C7Aee6E5b2Aa729821864227e84::[1mgetCollateralsXD[0m(0x0000000000000000000000000000000000050007) [1m<no source map>[0m
              ([0xd57D9931b305f1bc1622B97c8Cc6747E4A9254a0])
           call 0xb4c79daB8f259C7Aee6E5b2Aa729821864227e84::[1mgetControllersXD[0m(0x0000000000000000000000000000000000050007) [1m<no source map>[0m
              ([0x544E6FbB1Af8a15a48882fF15FEb68F9Ef82d882])
           call 0xb4c79daB8f259C7Aee6E5b2Aa729821864227e84::[1misCollateralEnabledXD[0m(0x0000000000000000000000000000000000050007, 0xd57D9931b305f1bc1622B97c8Cc6747E4A9254a0) [1m<no source map>[0m
              (true)
           call 0xb4c79daB8f259C7Aee6E5b2Aa729821864227e84::[1misAccountStatusCheckDeferredXD[0m(0x0000000000000000000000000000000000050007) [1m<no source map>[0m
              (false)
           call 0xb4c79daB8f259C7Aee6E5b2Aa729821864227e84::[1mgetLastAccountStatusCheckTimestampXD[0m(0x0000000000000000000000000000000000050007) [1m<no source map>[0m
              (**********)
           call 0x000000000000000000636F6e736F6c652e6c6f67::0xc3b5563500000000000000000000000000000000000000000000000000000000000000400000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001b6c697143616368652e6c696162696c6974792e69735a65726f28290000000000 [1m<no source map>[0m
              0x
           call 0xd57D9931b305f1bc1622B97c8Cc6747E4A9254a0::[1mbalanceOfXD[0m(0x0000000000000000000000000000000000050007) [1m<no source map>[0m
             call [1mGenericFactory[0m::implementation()() [1m<no source map>[0m
                (0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B)
             delegatecall 0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B::[1mbalanceOfXD[0m(0x0000000000000000000000000000000000050007) [1m<no source map>[0m
                (2596148429267413814265248164610047)
              (2596148429267413814265248164610047)
           call 0x0A64DF94bc0E039474DB42bb52FEca0c1d540402::[1mgetQuoteXD[0m(2596148429267413814265248164610047, 0xd57D9931b305f1bc1622B97c8Cc6747E4A9254a0, 0x0000000000000000000000000000000000000001) [1m<no source map>[0m
             call 0x000000000000000000636F6e736F6c652e6c6f67::0xb60e72cc00000000000000000000000000000000000000000000000000000000000000400000000000000000000000000000000000007fffffffffffffffffffffffffff0000000000000000000000000000000000000000000000000000000000000008696e416d6f756e74000000000000000000000000000000000000000000000000 [1m<no source map>[0m
                0x
             call 0x000000000000000000636F6e736F6c652e6c6f67::0x319af3330000000000000000000000000000000000000000000000000000000000000040000000000000000000000000d57d9931b305f1bc1622b97c8cc6747e4a9254a000000000000000000000000000000000000000000000000000000000000000046261736500000000000000000000000000000000000000000000000000000000 [1m<no source map>[0m
                0x
              (2596148429267413814265248164610047000000)
           call 0x0A64DF94bc0E039474DB42bb52FEca0c1d540402::[1mgetQuoteXD[0m(***********34827628530496329220095, [1mTestERC20[0m, 0x0000000000000000000000000000000000000001) [1m<no source map>[0m
             call 0x000000000000000000636F6e736F6c652e6c6f67::0xb60e72cc0000000000000000000000000000000000000000000000000000000000000040000000000000000000000000000000000000ffffffffffffffffffffffffffff0000000000000000000000000000000000000000000000000000000000000008696e416d6f756e74000000000000000000000000000000000000000000000000 [1m<no source map>[0m
                0x
             call 0x000000000000000000636F6e736F6c652e6c6f67::0x319af3330000000000000000000000000000000000000000000000000000000000000040000000000000000000000000d5f051401ca478b34c80d0b5a119e437dc6d9df500000000000000000000000000000000000000000000000000000000000000046261736500000000000000000000000000000000000000000000000000000000 [1m<no source map>[0m
                0x
              (***********34827628530496329220095)
           call 0x000000000000000000636F6e736F6c652e6c6f67::0xb60e72cc0000000000000000000000000000000000000000000000000000000000000040000000000000000000000000000000073f77fffffffffffffffffffffff181100000000000000000000000000000000000000000000000000000000000000020636f6c6c61746572616c41646a757374656456616c75652066726f6d206c6971 [1m<no source map>[0m
              0x
           call 0x000000000000000000636F6e736F6c652e6c6f67::0xb60e72cc0000000000000000000000000000000000000000000000000000000000000040000000000000000000000000000000000000ffffffffffffffffffffffffffff00000000000000000000000000000000000000000000000000000000000000176c696162696c69747956616c75652066726f6d206c6971000000000000000000 [1m<no source map>[0m
              0x
           call 0xd57D9931b305f1bc1622B97c8Cc6747E4A9254a0::[1mbalanceOfXD[0m(0x0000000000000000000000000000000000050007) [1m<no source map>[0m
             call [1mGenericFactory[0m::implementation()() [1m<no source map>[0m
                (0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B)
             delegatecall 0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B::[1mbalanceOfXD[0m(0x0000000000000000000000000000000000050007) [1m<no source map>[0m
                (2596148429267413814265248164610047)
              (2596148429267413814265248164610047)
           emit [36mLiquidate[0m(liquidator=[1mCryticTester[0m, violator=0x0000000000000000000000000000000000050007, collateral=0xd57D9931b305f1bc1622B97c8Cc6747E4A9254a0, repayAssets=0, yieldBalance=0) [1m<no source map>[0m
            0x
          0x
        0x
     call 0x544E6FbB1Af8a15a48882fF15FEb68F9Ef82d882::[1mcheckAccountStatusXD[0m([1mCryticTester[0m, [0xd57D9931b305f1bc1622B97c8Cc6747E4A9254a0]) [1m<no source map>[0m
       call [1mGenericFactory[0m::implementation()() [1m<no source map>[0m
          (0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B)
       delegatecall 0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B::[1mcheckAccountStatusXD[0m([1mCryticTester[0m, [0xd57D9931b305f1bc1622B97c8Cc6747E4A9254a0]) [1m<no source map>[0m
         call 0xb4c79daB8f259C7Aee6E5b2Aa729821864227e84::[1mareChecksInProgressXD[0m() [1m<no source map>[0m
            (true)
          (0xb168c58f)
        (0xb168c58f)
     emit [36mAccountStatusCheck[0m(account=[1mCryticTester[0m, controller=0x544E6FbB1Af8a15a48882fF15FEb68F9Ef82d882) [1m<no source map>[0m
     call 0x544E6FbB1Af8a15a48882fF15FEb68F9Ef82d882::[1mcheckVaultStatusXD[0m() [1m<no source map>[0m
       call [1mGenericFactory[0m::implementation()() [1m<no source map>[0m
          (0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B)
       delegatecall 0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B::[1mcheckVaultStatusXD[0m() [1m<no source map>[0m
         call 0xb4c79daB8f259C7Aee6E5b2Aa729821864227e84::[1mareChecksInProgressXD[0m() [1m<no source map>[0m
            (true)
         call [1mMockIRM[0m::computeInterestRate(address,uint256,uint256)(0x544E6FbB1Af8a15a48882fF15FEb68F9Ef82d882, 0, ***********34827628530496329220095) [1m<no source map>[0m
            (0)
         emit [36mVaultStatus[0m(totalShares=***********34827628530496329220095, totalBorrows=***********34827628530496329220095, accumulatedFees=0, cash=0, interestAccumulator=1000000000000000000000000000, interestRate=0, timestamp=**********) [1m<no source map>[0m
          (0x4b3d1223)
        (0x4b3d1223)
     emit [36mVaultStatusCheck[0m(vault=0x544E6FbB1Af8a15a48882fF15FEb68F9Ef82d882) [1m<no source map>[0m
      ()
    0x
  0x
call 0xb4c79daB8f259C7Aee6E5b2Aa729821864227e84::[1mgetLastAccountStatusCheckTimestampXD[0m(0x0000000000000000000000000000000000050007) [1m(/home/<USER>/recon/test/recon/targets/LiquidationTargets.sol:44)[0m
  (**********)
emit [36mLog[0m(You should not be able to reset account check via liquidation of 0) [1m(/home/<USER>/recon/lib/chimera/src/CryticAsserts.sol:46)[0m

testTheOverflow(): passing
oracle2_setPrice_debt(uint128): passing
eVault_repay(uint256): passing
check_the_zero_div(): failed!
  Call sequence:
    CryticTester.check_the_zero_div()

Traces:
call [1mCryticTester[0m::testTheDivisionByZero()() [1m(/home/<USER>/recon/test/recon/targets/RevertHelper.sol:76)[0m
 [91merror[0m Revert Panic(0x4e487b710000000000000000000000000000000000000000000000000000000000000012) [1m<source not found>[0m
emit [36mLog[0m(Panic(18)) [1m(/home/<USER>/recon/lib/chimera/src/CryticAsserts.sol:46)[0m

check_fees_2(): failed!
  Call sequence:
    CryticTester.mockIrm_2setInterestRate(514221)
    CryticTester.eVault_repay_shares(0)
    *wait* Time delay: 2224 seconds Block delay: 1
    CryticTester.eVault_repay_shares_to_sock(****************)
    CryticTester.mockIrm_2setInterestRate(505571)
    CryticTester.check_fees_2()

Traces:
call 0xd57D9931b305f1bc1622B97c8Cc6747E4A9254a0::[1minterestAccumulatorXD[0m() [1m(/home/<USER>/recon/test/recon/BeforeAfter.sol:35)[0m
 call [1mGenericFactory[0m::implementation()() [1m<no source map>[0m
    (0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B)
 delegatecall 0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B::[1minterestAccumulatorXD[0m() [1m<no source map>[0m
   call 0xd57D9931b305f1bc1622B97c8Cc6747E4A9254a0::[1mviewDelegateXD[0m() [1m<no source map>[0m
     call [1mGenericFactory[0m::implementation()() [1m<no source map>[0m
        (0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B)
     delegatecall 0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B::[1mviewDelegateXD[0m() [1m<no source map>[0m
       delegatecall 0xcd9a70C13C88863ECE51B302a77D2EB98fBBBD65::[1minterestAccumulatorXD[0m() [1m<no source map>[0m
          (1000000000000000000000000000)
        0x0000000000000000000000000000000000000000033b2e3c9fd0803ce8000000
      0x0000000000000000000000000000000000000000033b2e3c9fd0803ce8000000
    (1000000000000000000000000000)
  (1000000000000000000000000000)
call 0x544E6FbB1Af8a15a48882fF15FEb68F9Ef82d882::[1minterestAccumulatorXD[0m() [1m(/home/<USER>/recon/test/recon/BeforeAfter.sol:36)[0m
 call [1mGenericFactory[0m::implementation()() [1m<no source map>[0m
    (0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B)
 delegatecall 0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B::[1minterestAccumulatorXD[0m() [1m<no source map>[0m
   call 0x544E6FbB1Af8a15a48882fF15FEb68F9Ef82d882::[1mviewDelegateXD[0m() [1m<no source map>[0m
     call [1mGenericFactory[0m::implementation()() [1m<no source map>[0m
        (0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B)
     delegatecall 0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B::[1mviewDelegateXD[0m() [1m<no source map>[0m
       delegatecall 0xcd9a70C13C88863ECE51B302a77D2EB98fBBBD65::[1minterestAccumulatorXD[0m() [1m<no source map>[0m
         call 0x000000000000000000636F6e736F6c652e6c6f67::0x41304fac00000000000000000000000000000000000000000000000000000000000000200000000000000000000000000000000000000000000000000000000000000011436865636b2050726f6669742031653132000000000000000000000000000000 [1m<no source map>[0m
            0x
         call 0x000000000000000000636F6e736F6c652e6c6f67::0x41304fac00000000000000000000000000000000000000000000000000000000000000200000000000000000000000000000000000000000000000000000000000000010436865636b2050726f6669742031653600000000000000000000000000000000 [1m<no source map>[0m
            0x
         call 0x000000000000000000636F6e736F6c652e6c6f67::0x41304fac00000000000000000000000000000000000000000000000000000000000000200000000000000000000000000000000000000000000000000000000000000010436865636b2050726f6669742031653400000000000000000000000000000000 [1m<no source map>[0m
            0x
          (1000000000000000000000000000)
        0x0000000000000000000000000000000000000000033b2e3c9fd0803ce8000000
      0x0000000000000000000000000000000000000000033b2e3c9fd0803ce8000000
    (1000000000000000000000000000)
  (1000000000000000000000000000)
call 0x544E6FbB1Af8a15a48882fF15FEb68F9Ef82d882::[1mcheckLiquidationXD[0m(0x0000000000000000000000000000000000050007, [1mCryticTester[0m, 0xd57D9931b305f1bc1622B97c8Cc6747E4A9254a0) [1m(/home/<USER>/recon/test/recon/BeforeAfter.sol:55)[0m
 call [1mGenericFactory[0m::implementation()() [1m<no source map>[0m
    (0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B)
 delegatecall 0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B::[1mcheckLiquidationXD[0m(0x0000000000000000000000000000000000050007, [1mCryticTester[0m, 0xd57D9931b305f1bc1622B97c8Cc6747E4A9254a0) [1m<no source map>[0m
   call 0x544E6FbB1Af8a15a48882fF15FEb68F9Ef82d882::[1mviewDelegateXD[0m() [1m<no source map>[0m
     call [1mGenericFactory[0m::implementation()() [1m<no source map>[0m
        (0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B)
     delegatecall 0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B::[1mviewDelegateXD[0m() [1m<no source map>[0m
       delegatecall 0x2A2B68E9Bf718DBf6dF3dF91174A136054a8A0Ec::[1mcheckLiquidationXD[0m(0x0000000000000000000000000000000000050007, [1mCryticTester[0m, 0xd57D9931b305f1bc1622B97c8Cc6747E4A9254a0) [1m<no source map>[0m
         call 0x000000000000000000636F6e736F6c652e6c6f67::0x41304fac00000000000000000000000000000000000000000000000000000000000000200000000000000000000000000000000000000000000000000000000000000011436865636b2050726f6669742031653132000000000000000000000000000000 [1m<no source map>[0m
            0x
         call 0x000000000000000000636F6e736F6c652e6c6f67::0x41304fac00000000000000000000000000000000000000000000000000000000000000200000000000000000000000000000000000000000000000000000000000000010436865636b2050726f6669742031653600000000000000000000000000000000 [1m<no source map>[0m
            0x
         call 0x000000000000000000636F6e736F6c652e6c6f67::0x41304fac00000000000000000000000000000000000000000000000000000000000000200000000000000000000000000000000000000000000000000000000000000010436865636b2050726f6669742031653400000000000000000000000000000000 [1m<no source map>[0m
            0x
         call 0xb4c79daB8f259C7Aee6E5b2Aa729821864227e84::[1mgetCollateralsXD[0m([1mCryticTester[0m) [1m<no source map>[0m
            ([0xd57D9931b305f1bc1622B97c8Cc6747E4A9254a0])
         call 0xb4c79daB8f259C7Aee6E5b2Aa729821864227e84::[1mgetControllersXD[0m([1mCryticTester[0m) [1m<no source map>[0m
            ([0x544E6FbB1Af8a15a48882fF15FEb68F9Ef82d882])
         call 0xb4c79daB8f259C7Aee6E5b2Aa729821864227e84::[1misCollateralEnabledXD[0m([1mCryticTester[0m, 0xd57D9931b305f1bc1622B97c8Cc6747E4A9254a0) [1m<no source map>[0m
            (true)
         call 0xb4c79daB8f259C7Aee6E5b2Aa729821864227e84::[1misAccountStatusCheckDeferredXD[0m([1mCryticTester[0m) [1m<no source map>[0m
            (false)
         call 0xb4c79daB8f259C7Aee6E5b2Aa729821864227e84::[1mgetLastAccountStatusCheckTimestampXD[0m([1mCryticTester[0m) [1m<no source map>[0m
            (**********)
         call 0x000000000000000000636F6e736F6c652e6c6f67::0xc3b5563500000000000000000000000000000000000000000000000000000000000000400000000000000000000000000000000000000000000000000000000000000001000000000000000000000000000000000000000000000000000000000000001b6c697143616368652e6c696162696c6974792e69735a65726f28290000000000 [1m<no source map>[0m
            0x
          (0, 0)
        0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
      0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
    (0, 0)
  (0, 0)
call 0x544E6FbB1Af8a15a48882fF15FEb68F9Ef82d882::[1mcheckLiquidationXD[0m([1mCryticTester[0m, 0x0000000000000000000000000000000000050007, 0xd57D9931b305f1bc1622B97c8Cc6747E4A9254a0) [1m(/home/<USER>/recon/test/recon/BeforeAfter.sol:55)[0m
 call [1mGenericFactory[0m::implementation()() [1m<no source map>[0m
    (0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B)
 delegatecall 0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B::[1mcheckLiquidationXD[0m([1mCryticTester[0m, 0x0000000000000000000000000000000000050007, 0xd57D9931b305f1bc1622B97c8Cc6747E4A9254a0) [1m<no source map>[0m
   call 0x544E6FbB1Af8a15a48882fF15FEb68F9Ef82d882::[1mviewDelegateXD[0m() [1m<no source map>[0m
     call [1mGenericFactory[0m::implementation()() [1m<no source map>[0m
        (0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B)
     delegatecall 0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B::[1mviewDelegateXD[0m() [1m<no source map>[0m
       delegatecall 0x2A2B68E9Bf718DBf6dF3dF91174A136054a8A0Ec::[1mcheckLiquidationXD[0m([1mCryticTester[0m, 0x0000000000000000000000000000000000050007, 0xd57D9931b305f1bc1622B97c8Cc6747E4A9254a0) [1m<no source map>[0m
         call 0x000000000000000000636F6e736F6c652e6c6f67::0x41304fac00000000000000000000000000000000000000000000000000000000000000200000000000000000000000000000000000000000000000000000000000000011436865636b2050726f6669742031653132000000000000000000000000000000 [1m<no source map>[0m
            0x
         call 0x000000000000000000636F6e736F6c652e6c6f67::0x41304fac00000000000000000000000000000000000000000000000000000000000000200000000000000000000000000000000000000000000000000000000000000010436865636b2050726f6669742031653600000000000000000000000000000000 [1m<no source map>[0m
            0x
         call 0x000000000000000000636F6e736F6c652e6c6f67::0x41304fac00000000000000000000000000000000000000000000000000000000000000200000000000000000000000000000000000000000000000000000000000000010436865636b2050726f6669742031653400000000000000000000000000000000 [1m<no source map>[0m
            0x
         call 0xb4c79daB8f259C7Aee6E5b2Aa729821864227e84::[1mgetCollateralsXD[0m(0x0000000000000000000000000000000000050007) [1m<no source map>[0m
            ([0xd57D9931b305f1bc1622B97c8Cc6747E4A9254a0])
         call 0xb4c79daB8f259C7Aee6E5b2Aa729821864227e84::[1mgetControllersXD[0m(0x0000000000000000000000000000000000050007) [1m<no source map>[0m
            ([0x544E6FbB1Af8a15a48882fF15FEb68F9Ef82d882])
         call 0xb4c79daB8f259C7Aee6E5b2Aa729821864227e84::[1misCollateralEnabledXD[0m(0x0000000000000000000000000000000000050007, 0xd57D9931b305f1bc1622B97c8Cc6747E4A9254a0) [1m<no source map>[0m
            (true)
         call 0xb4c79daB8f259C7Aee6E5b2Aa729821864227e84::[1misAccountStatusCheckDeferredXD[0m(0x0000000000000000000000000000000000050007) [1m<no source map>[0m
            (false)
         call 0xb4c79daB8f259C7Aee6E5b2Aa729821864227e84::[1mgetLastAccountStatusCheckTimestampXD[0m(0x0000000000000000000000000000000000050007) [1m<no source map>[0m
            (**********)
         call 0x000000000000000000636F6e736F6c652e6c6f67::0xc3b5563500000000000000000000000000000000000000000000000000000000000000400000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001b6c697143616368652e6c696162696c6974792e69735a65726f28290000000000 [1m<no source map>[0m
            0x
         call 0xd57D9931b305f1bc1622B97c8Cc6747E4A9254a0::[1mbalanceOfXD[0m(0x0000000000000000000000000000000000050007) [1m<no source map>[0m
           call [1mGenericFactory[0m::implementation()() [1m<no source map>[0m
              (0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B)
           delegatecall 0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B::[1mbalanceOfXD[0m(0x0000000000000000000000000000000000050007) [1m<no source map>[0m
              (2596148429267413814265248164610047)
            (2596148429267413814265248164610047)
         call 0x0A64DF94bc0E039474DB42bb52FEca0c1d540402::[1mgetQuoteXD[0m(2596148429267413814265248164610047, 0xd57D9931b305f1bc1622B97c8Cc6747E4A9254a0, 0x0000000000000000000000000000000000000001) [1m<no source map>[0m
           call 0x000000000000000000636F6e736F6c652e6c6f67::0xb60e72cc00000000000000000000000000000000000000000000000000000000000000400000000000000000000000000000000000007fffffffffffffffffffffffffff0000000000000000000000000000000000000000000000000000000000000008696e416d6f756e74000000000000000000000000000000000000000000000000 [1m<no source map>[0m
              0x
           call 0x000000000000000000636F6e736F6c652e6c6f67::0x319af3330000000000000000000000000000000000000000000000000000000000000040000000000000000000000000d57d9931b305f1bc1622b97c8cc6747e4a9254a000000000000000000000000000000000000000000000000000000000000000046261736500000000000000000000000000000000000000000000000000000000 [1m<no source map>[0m
              0x
            (2596148429267413814265248164610047000000)
         call 0x0A64DF94bc0E039474DB42bb52FEca0c1d540402::[1mgetQuoteXD[0m(***********34827622644590310485196, [1mTestERC20[0m, 0x0000000000000000000000000000000000000001) [1m<no source map>[0m
           call 0x000000000000000000636F6e736F6c652e6c6f67::0xb60e72cc0000000000000000000000000000000000000000000000000000000000000040000000000000000000000000000000000000ffffffffffffffeb16ccb6926ccc0000000000000000000000000000000000000000000000000000000000000008696e416d6f756e74000000000000000000000000000000000000000000000000 [1m<no source map>[0m
              0x
           call 0x000000000000000000636F6e736F6c652e6c6f67::0x319af3330000000000000000000000000000000000000000000000000000000000000040000000000000000000000000d5f051401ca478b34c80d0b5a119e437dc6d9df500000000000000000000000000000000000000000000000000000000000000046261736500000000000000000000000000000000000000000000000000000000 [1m<no source map>[0m
              0x
            (***********34827622644590310485196)
         call 0x000000000000000000636F6e736F6c652e6c6f67::0xb60e72cc0000000000000000000000000000000000000000000000000000000000000040000000000000000000000000000000073f77fffffffffffffffffffffff181100000000000000000000000000000000000000000000000000000000000000020636f6c6c61746572616c41646a757374656456616c75652066726f6d206c6971 [1m<no source map>[0m
            0x
         call 0x000000000000000000636F6e736F6c652e6c6f67::0xb60e72cc0000000000000000000000000000000000000000000000000000000000000040000000000000000000000000000000000000ffffffffffffffeb16ccb6926ccc00000000000000000000000000000000000000000000000000000000000000176c696162696c69747956616c75652066726f6d206c6971000000000000000000 [1m<no source map>[0m
            0x
          (0, 0)
        0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
      0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
    (0, 0)
  (0, 0)
call 0x544E6FbB1Af8a15a48882fF15FEb68F9Ef82d882::[1mconvertToAssetsXD[0m(1000000000000000000) [1m(/home/<USER>/recon/test/recon/BeforeAfter.sol:23)[0m
 call [1mGenericFactory[0m::implementation()() [1m<no source map>[0m
    (0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B)
 delegatecall 0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B::[1mconvertToAssetsXD[0m(1000000000000000000) [1m<no source map>[0m
   call 0x000000000000000000636F6e736F6c652e6c6f67::0x41304fac00000000000000000000000000000000000000000000000000000000000000200000000000000000000000000000000000000000000000000000000000000011436865636b2050726f6669742031653132000000000000000000000000000000 [1m<no source map>[0m
      0x
   call 0x000000000000000000636F6e736F6c652e6c6f67::0x41304fac00000000000000000000000000000000000000000000000000000000000000200000000000000000000000000000000000000000000000000000000000000010436865636b2050726f6669742031653600000000000000000000000000000000 [1m<no source map>[0m
      0x
   call 0x000000000000000000636F6e736F6c652e6c6f67::0x41304fac00000000000000000000000000000000000000000000000000000000000000200000000000000000000000000000000000000000000000000000000000000010436865636b2050726f6669742031653400000000000000000000000000000000 [1m<no source map>[0m
      0x
    (1000000000000000000)
  (1000000000000000000)
call 0x544E6FbB1Af8a15a48882fF15FEb68F9Ef82d882::[1mconvertFeesXD[0m() [1m(/home/<USER>/recon/test/recon/properties/AccountingProperties.sol:96)[0m
 call [1mGenericFactory[0m::implementation()() [1m<no source map>[0m
    (0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B)
 delegatecall 0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B::[1mconvertFeesXD[0m() [1m<no source map>[0m
   call 0xb4c79daB8f259C7Aee6E5b2Aa729821864227e84::[1mcallXD[0m(0x544E6FbB1Af8a15a48882fF15FEb68F9Ef82d882, [1mCryticTester[0m, 0, 0x2b5335c3) [1m<no source map>[0m
     emit [36mCallWithContext[0m(caller=0x544E6FbB1Af8a15a48882fF15FEb68F9Ef82d882, onBehalfOfAddressPrefix=0x00a329c0648769a73afac7f9381e08fb43dbea, onBehalfOfAccount=[1mCryticTester[0m, targetContract=0x544E6FbB1Af8a15a48882fF15FEb68F9Ef82d882, selector=0x2b5335c3) [1m<no source map>[0m
     call 0x544E6FbB1Af8a15a48882fF15FEb68F9Ef82d882::[1mconvertFeesXD[0m() [1m<no source map>[0m
       call [1mGenericFactory[0m::implementation()() [1m<no source map>[0m
          (0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B)
       delegatecall 0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B::[1mconvertFeesXD[0m() [1m<no source map>[0m
         delegatecall 0x84b51678F9A4869E384F737ed2a5D56c8ca16c81::[1mconvertFeesXD[0m() [1m<no source map>[0m
           call 0x000000000000000000636F6e736F6c652e6c6f67::0x41304fac00000000000000000000000000000000000000000000000000000000000000200000000000000000000000000000000000000000000000000000000000000011436865636b2050726f6669742031653132000000000000000000000000000000 [1m<no source map>[0m
              0x
           call 0x000000000000000000636F6e736F6c652e6c6f67::0x41304fac00000000000000000000000000000000000000000000000000000000000000200000000000000000000000000000000000000000000000000000000000000010436865636b2050726f6669742031653600000000000000000000000000000000 [1m<no source map>[0m
              0x
           call 0x000000000000000000636F6e736F6c652e6c6f67::0x41304fac00000000000000000000000000000000000000000000000000000000000000200000000000000000000000000000000000000000000000000000000000000010436865636b2050726f6669742031653400000000000000000000000000000000 [1m<no source map>[0m
              0x
           call 0xb4c79daB8f259C7Aee6E5b2Aa729821864227e84::[1mgetCurrentOnBehalfOfAccountXD[0m(0x0000000000000000000000000000000000000000) [1m<no source map>[0m
              ([1mCryticTester[0m, false)
           call 0xb4c79daB8f259C7Aee6E5b2Aa729821864227e84::[1mrequireVaultStatusCheckXD[0m() [1m<no source map>[0m
              0x
            0x
          0x
        0x
     call 0x544E6FbB1Af8a15a48882fF15FEb68F9Ef82d882::[1mcheckVaultStatusXD[0m() [1m<no source map>[0m
       call [1mGenericFactory[0m::implementation()() [1m<no source map>[0m
          (0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B)
       delegatecall 0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B::[1mcheckVaultStatusXD[0m() [1m<no source map>[0m
         call 0xb4c79daB8f259C7Aee6E5b2Aa729821864227e84::[1mareChecksInProgressXD[0m() [1m<no source map>[0m
            (true)
         call 0x000000000000000000636F6e736F6c652e6c6f67::0x41304fac00000000000000000000000000000000000000000000000000000000000000200000000000000000000000000000000000000000000000000000000000000011436865636b2050726f6669742031653132000000000000000000000000000000 [1m<no source map>[0m
            0x
         call 0x000000000000000000636F6e736F6c652e6c6f67::0x41304fac00000000000000000000000000000000000000000000000000000000000000200000000000000000000000000000000000000000000000000000000000000010436865636b2050726f6669742031653600000000000000000000000000000000 [1m<no source map>[0m
            0x
         call 0x000000000000000000636F6e736F6c652e6c6f67::0x41304fac00000000000000000000000000000000000000000000000000000000000000200000000000000000000000000000000000000000000000000000000000000010436865636b2050726f6669742031653400000000000000000000000000000000 [1m<no source map>[0m
            0x
         call [1mMockIRM[0m::computeInterestRate(address,uint256,uint256)(0x544E6FbB1Af8a15a48882fF15FEb68F9Ef82d882, 0, ***********34827622644590310485196) [1m<no source map>[0m
            (505571)
         emit [36mVaultStatus[0m(totalShares=***********34827622644590310485196, totalBorrows=***********34827622644590310485196, accumulatedFees=0, cash=0, interestAccumulator=1000000000000000000000000000, interestRate=505571, timestamp=**********) [1m<no source map>[0m
          (0x4b3d1223)
        (0x4b3d1223)
     emit [36mVaultStatusCheck[0m(vault=0x544E6FbB1Af8a15a48882fF15FEb68F9Ef82d882) [1m<no source map>[0m
      ()
    0x
  0x
call 0x544E6FbB1Af8a15a48882fF15FEb68F9Ef82d882::[1mconvertToAssetsXD[0m(1000000000000000000) [1m(/home/<USER>/recon/test/recon/BeforeAfter.sol:23)[0m
 call [1mGenericFactory[0m::implementation()() [1m<no source map>[0m
    (0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B)
 delegatecall 0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B::[1mconvertToAssetsXD[0m(1000000000000000000) [1m<no source map>[0m
   call 0x000000000000000000636F6e736F6c652e6c6f67::0x41304fac00000000000000000000000000000000000000000000000000000000000000200000000000000000000000000000000000000000000000000000000000000011436865636b2050726f6669742031653132000000000000000000000000000000 [1m<no source map>[0m
      0x
   call 0x000000000000000000636F6e736F6c652e6c6f67::0x41304fac00000000000000000000000000000000000000000000000000000000000000200000000000000000000000000000000000000000000000000000000000000010436865636b2050726f6669742031653600000000000000000000000000000000 [1m<no source map>[0m
      0x
   call 0x000000000000000000636F6e736F6c652e6c6f67::0x41304fac00000000000000000000000000000000000000000000000000000000000000200000000000000000000000000000000000000000000000000000000000000010436865636b2050726f6669742031653400000000000000000000000000000000 [1m<no source map>[0m
      0x
    (1000000000000000001)
  (1000000000000000001)
emit [36mLog[0m(ppfs doesn't change on fee dist 2) [1m(/home/<USER>/recon/lib/chimera/src/CryticAsserts.sol:46)[0m

sock_deposit_a(uint256): passing
check_cash(): passing
check_depositRebase_1(uint256): passing
check_0_assets_2(): failed!
  Call sequence:
    CryticTester.oracle2_setPrice_coll(0)
    CryticTester.liquidate_sock_clamped()
    CryticTester.check_0_assets_2()

Traces:
call 0x544E6FbB1Af8a15a48882fF15FEb68F9Ef82d882::[1mtotalAssetsXD[0m() [1m(/home/<USER>/recon/test/recon/properties/DoomsdayProperties.sol:97)[0m
 call [1mGenericFactory[0m::implementation()() [1m<no source map>[0m
    (0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B)
 delegatecall 0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B::[1mtotalAssetsXD[0m() [1m<no source map>[0m
   call 0x544E6FbB1Af8a15a48882fF15FEb68F9Ef82d882::[1mviewDelegateXD[0m() [1m<no source map>[0m
     call [1mGenericFactory[0m::implementation()() [1m<no source map>[0m
        (0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B)
     delegatecall 0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B::[1mviewDelegateXD[0m() [1m<no source map>[0m
       delegatecall 0xb13B5e34092E21A53Cb1110A3050Bd0e117Dc15D::[1mtotalAssetsXD[0m() [1m<no source map>[0m
          (0)
        0x0000000000000000000000000000000000000000000000000000000000000000
      0x0000000000000000000000000000000000000000000000000000000000000000
    (0)
  (0)
call 0x544E6FbB1Af8a15a48882fF15FEb68F9Ef82d882::[1mtotalSupplyXD[0m() [1m(/home/<USER>/recon/test/recon/properties/DoomsdayProperties.sol:98)[0m
 call [1mGenericFactory[0m::implementation()() [1m<no source map>[0m
    (0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B)
 delegatecall 0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B::[1mtotalSupplyXD[0m() [1m<no source map>[0m
   call 0x544E6FbB1Af8a15a48882fF15FEb68F9Ef82d882::[1mviewDelegateXD[0m() [1m<no source map>[0m
     call [1mGenericFactory[0m::implementation()() [1m<no source map>[0m
        (0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B)
     delegatecall 0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B::[1mviewDelegateXD[0m() [1m<no source map>[0m
       delegatecall 0x46662E22D131Ea49249E0920C286E1484FEEf76E::[1mtotalSupplyXD[0m() [1m<no source map>[0m
          (***********34827628530496329220095)
        0x000000000000000000000000000000000000ffffffffffffffffffffffffffff
      0x000000000000000000000000000000000000ffffffffffffffffffffffffffff
    (***********34827628530496329220095)
  (***********34827628530496329220095)
emit [36mLog[0m(Must have 0 supply on 0 assets) [1m(/home/<USER>/recon/lib/chimera/src/CryticAsserts.sol:46)[0m

check_revert_ta(): passing
check_0_assets_1(): passing
check_fees(): passing
check_redeem_1(uint256): passing
victim_deposit_a(uint256): passing
eVault_mint(uint256): passing
doomsday_deposit_e6(): passing
check_debt(): passing
check_depositRebase_2(uint256): passing
eVault2_withdraw(uint256): passing
sock_borrow_b(uint256): passing
check_depositRebase_1_mid(uint256): passing
check_0_shares_1(): passing
liquidate_0_attacker_check_with_real_liq(): passing
liquidate_attacker(uint256,uint256): passing
eVault2_deposit(uint256): passing
eVault2_redeem(uint256): passing
liquidate_sock(uint256,uint256): passing
liquidate_attacker_clamped(): passing
check_revert_ta_2(): passing
check_cash_2(): passing
check_total_no_revert(): passing
check_0_shares_2(): passing
eVault_deposit(uint256): passing
sock_deposit_b(uint256): passing
check_depositRebase_1_crit(uint256): passing
inflation_pre_condition_2(): failed!
  Call sequence:
    CryticTester.oracle2_setPrice_coll(0)
    CryticTester.liquidate_sock_clamped()
    CryticTester.inflation_pre_condition_2()

Traces:
call 0x544E6FbB1Af8a15a48882fF15FEb68F9Ef82d882::[1mtotalAssetsXD[0m() [1m(/home/<USER>/recon/test/recon/properties/DoomsdayProperties.sol:56)[0m
 call [1mGenericFactory[0m::implementation()() [1m<no source map>[0m
    (0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B)
 delegatecall 0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B::[1mtotalAssetsXD[0m() [1m<no source map>[0m
   call 0x544E6FbB1Af8a15a48882fF15FEb68F9Ef82d882::[1mviewDelegateXD[0m() [1m<no source map>[0m
     call [1mGenericFactory[0m::implementation()() [1m<no source map>[0m
        (0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B)
     delegatecall 0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B::[1mviewDelegateXD[0m() [1m<no source map>[0m
       delegatecall 0xb13B5e34092E21A53Cb1110A3050Bd0e117Dc15D::[1mtotalAssetsXD[0m() [1m<no source map>[0m
          (0)
        0x0000000000000000000000000000000000000000000000000000000000000000
      0x0000000000000000000000000000000000000000000000000000000000000000
    (0)
  (0)
call 0x544E6FbB1Af8a15a48882fF15FEb68F9Ef82d882::[1mtotalSupplyXD[0m() [1m(/home/<USER>/recon/test/recon/properties/DoomsdayProperties.sol:56)[0m
 call [1mGenericFactory[0m::implementation()() [1m<no source map>[0m
    (0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B)
 delegatecall 0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B::[1mtotalSupplyXD[0m() [1m<no source map>[0m
   call 0x544E6FbB1Af8a15a48882fF15FEb68F9Ef82d882::[1mviewDelegateXD[0m() [1m<no source map>[0m
     call [1mGenericFactory[0m::implementation()() [1m<no source map>[0m
        (0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B)
     delegatecall 0x9c91C0A72C9Dde79Be48469F4954f87F694E1F0B::[1mviewDelegateXD[0m() [1m<no source map>[0m
       delegatecall 0x46662E22D131Ea49249E0920C286E1484FEEf76E::[1mtotalSupplyXD[0m() [1m<no source map>[0m
          (***********34827628530496329220095)
        0x000000000000000000000000000000000000ffffffffffffffffffffffffffff
      0x000000000000000000000000000000000000ffffffffffffffffffffffffffff
    (***********34827628530496329220095)
  (***********34827628530496329220095)
emit [36mLog[0m(Supply always less than assets unless a rebase or redistribution happens 2) [1m(/home/<USER>/recon/lib/chimera/src/CryticAsserts.sol:46)[0m

oracle1_setPrice_debt(uint128): passing
eVault_repay_sock(uint256): passing
AssertionFailed(..): passing


Unique instructions: 42586
Unique codehashes: 15
Corpus size: 43
Seed: 8430860062200034526

[2024-07-24 18:36:12.16] Saving test reproducers... Done! (0.005068024s)
[2024-07-24 18:36:12.16] Saving corpus... Done! (3.61489859s)
[2024-07-24 18:36:15.78] Saving coverage... Done! (8.005295717s)
