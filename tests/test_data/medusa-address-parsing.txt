Test for method "CryticTester.crytic_solvent_shares_bitcorn()" failed after the following call sequence:
[Call Sequence]
CryticTester.TwTAP_participate(******************************************, 61918091110726083752813338766276191038263083433366584018688591674621790705619, 2361016) (block=28082, time=17868248, gas=12500000, gasprice=1, value=0, sender=******************************************)
CryticTester.TwTAP_participate(******************************************, 32172841401752504929929596747405730936747446706072364556831924065220547509282, 10400) (block=49048, time=31590648, gas=12500000, gasprice=1, value=0, sender=******************************************)
CryticTester.TwTAP_exitPosition(11155120, ******************************************) (block=49048, time=31590648, gas=12500000, gasprice=1, value=0, sender=******************************************)
cution Trace]
[call] CryticTester.TwTAP_exitPosition(11155120, ******************************************) (addr=0xA647ff3c36cFab592509E13860ab8c4F28781a66, value=0, sender=******************************************)
 [call] TwTAP.exitPosition(1, ******************************************) (addr=0x01375317AA980daaBF22f990a378ECCaD9B40dc0, value=0, sender=0xA647ff3c36cFab592509E13860ab8c4F28781a66)
> [event] AMLDivergence(0, 954446, 1)
> [call] TapToken.transfer(******************************************, 12719283591674621790705619) (addr=0x54919A19522Ce7c842E25735a9cFEcef1c0a06dA, value=0, sender=0x01375317AA980daaBF22f990a378ECCaD9B40dc0)
=> [event] Transfer(0x01375317aa980daabf22f990a378eccad9b40dc0, ******************************************, 12719283591674621790705619)
=> [return (true)]
> [event] ExitPosition(1, 12719283591674621790705619)
> [return (12719283591674621790705619)]
 [return ()]
