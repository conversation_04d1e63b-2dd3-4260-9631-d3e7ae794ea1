[2024-12-06 22:19:04.32] [Worker 7] Sequence replayed from corpus file 4643497012340105866.txt (104/105)
[2024-12-06 22:19:04.34] [Worker 1] Test doomsday_isServicer() falsified!
  Call sequence:
CryticTester.securedLine_abort_clamped() from: 0x0000000000000000000000000000000000030000 Time delay: 277043 seconds Block delay: 60290
CryticTester.activeEscrow_updateStableCoin<PERSON>hitelist_clamped(2000000000000000001) from: 0x0000000000000000000000000000000000030000 Time delay: 471495 seconds Block delay: 5033
CryticTester.securedLine_revokeConsent(89,"\206\247\228\SO0l\238\228\196") from: 0x0000000000000000000000000000000000030000 Time delay: 322326 seconds Block delay: 59552
CryticTester.target_clamped_deployNewConfig(101,604,43415,511,3376100,96,true) from: 0x0000000000000000000000000000000000020000 Time delay: 474986 seconds Block delay: 3905
CryticTester.activeSpigot_updateBorrowerContractSplit(0x2fffffffd,102) from: 0x0000000000000000000000000000000000030000 Time delay: 388256 seconds Block delay: 18556
CryticTester.activeEscrow_setUniswapV3Manager(0xffffffff) from: 0x0000000000000000000000000000000000010000 Time delay: 487293 seconds Block delay: 58560
CryticTester.activeSpigot_updateBeneficiaryPoolAddress(0xf09,0xff2) from: 0x0000000000000000000000000000000000030000 Time delay: 354425 seconds Block delay: 39756
CryticTester.activeEscrow_addUniV3Position_clamped(3630930776621094979908799587096158545459935489390654966338907567791372518427) from: 0x0000000000000000000000000000000000020000 Time delay: 482712 seconds Block delay: 24963
CryticTester.securedLine_RefinanceCredit_clamped(289360649475804117325992437073955993279,123604477410235737453214744888291969445,58806457570827203175833457681964305935029511721526489988808285675648851834727,834,40027348292230338684959355073809980983960236056142901779893782343187617707223) from: 0x0000000000000000000000000000000000030000 Time delay: 289103 seconds Block delay: 404
CryticTester.activeEscrow_revokeConsent(115792089237316195423570985008687907853269984665640564039457584007913129639808,"}\197*S\203e\ESC\175+\SOHU\214\216\211\ETBP\204\188(\ACK\212\fTS\210\184$`\180") from: 0x0000000000000000000000000000000000010000 Time delay: 282471 seconds Block delay: 129
CryticTester.reconTokenOracle_setTokenStale(0x1fffffffe,false) from: 0x0000000000000000000000000000000000030000 Time delay: 586023 seconds Block delay: 31372
CryticTester.activeEscrow_updateStableCoinWhitelist(0x1,false) from: 0x0000000000000000000000000000000000030000 Time delay: 11 seconds Block delay: 34461
CryticTester.strategyVault_setRevertBehaviour(53,253) from: 0x0000000000000000000000000000000000020000 Time delay: 385928 seconds Block delay: 13012
CryticTester.activeSpigot_repayBeneficiaryWithTradedTokens_clamped(115792089237316195423570985008687907853269984665640564039457584007913129574400) from: 0x0000000000000000000000000000000000030000 Time delay: 171712 seconds Block delay: 23884
CryticTester.securedLine_recoverTokens_clamped(87897699959871522957205086659064146857096509081816748721696165936153450335034,0) from: 0x0000000000000000000000000000000000030000 Time delay: 149944 seconds Block delay: 6458
CryticTester.activeSpigot_updateOwner(0x1fffffffe) from: 0x0000000000000000000000000000000000020000 Time delay: 400981 seconds Block delay: 16585
CryticTester.securedLine_recoverTokens_clamped(115792089237316195423570985008687907853269984665640564039457584007912934194779,52466366141051561348197941249160326383903223202952538524094093330650237252868) from: 0x0000000000000000000000000000000000010000 Time delay: 256838 seconds Block delay: 20245
CryticTester.securedLine_borrow(58878454570500678740406653684482509060260322058889550711893144375544741906112,115792089237316195423570985008687907853269984665640564039457584003913129639935,0x2fffffffd) from: 0x0000000000000000000000000000000000010000 Time delay: 322144 seconds Block delay: 27405
CryticTester.securedLine_close_clamped_2(115792089237316195423570985008687907853269984665640564039457584007913129639934) from: 0x0000000000000000000000000000000000030000 Time delay: 425374 seconds Block delay: 60098
CryticTester.securedLine_clearProposals() from: 0x0000000000000000000000000000000000030000 Time delay: 566604 seconds Block delay: 25101
CryticTester.activeEscrow_setUniswapV3Manager(0xff1) from: 0x0000000000000000000000000000000000020000 Time delay: 46951 seconds Block delay: 3023
CryticTester.activeEscrow_recoverUniV3Positions(0x1fffffffe) from: 0x0000000000000000000000000000000000030000 Time delay: 519609 seconds Block delay: 46483
CryticTester.activeSpigot_recoverSpigotTokens(0xffffffff,0x2fffffffd) from: 0x0000000000000000000000000000000000030000 Time delay: 511265 seconds Block delay: 23001
CryticTester.securedLine_abort() from: 0x0000000000000000000000000000000000030000 Time delay: 266362 seconds Block delay: 16383
CryticTester.strategyVault_increaseYield(115792089237316195423570985008687907853269984665640564039457584007913129639680) from: 0x0000000000000000000000000000000000020000 Time delay: 100000 seconds Block delay: 35266
CryticTester.activeEscrow_updateOwner(0xffffffff) from: 0x0000000000000000000000000000000000020000 Time delay: 511213 seconds Block delay: 85
CryticTester.activeEscrow_revokeConsent(44542410518368265106714337513459370114335218080009499800558331416467749287668,"\225\240;\140\152\208\ETX\147\207D\166\220d\DC4\160w") from: 0x0000000000000000000000000000000000030000 Time delay: 119686 seconds Block delay: 35200
CryticTester.activeSpigot_pullTokens(0x1fffffffe,0x1,"\241FP\226j\219\165]<s\164L\DC2\193\182pIl") from: 0x0000000000000000000000000000000000010000 Time delay: 95 seconds Block delay: 1999
CryticTester.activeEscrow_getCollateralValue() from: 0x0000000000000000000000000000000000030000 Time delay: 604711 seconds Block delay: 12346
CryticTester.securedLine_revokeConsent_RefinanceCredit_clamped(223711230,22444022061522210258544439222958816369,115792089237316195423570985008687907853269984665640564039457584007913129639934,40445,61943295865704747632297933896328533572096427112724608741193061739785732482546) from: 0x0000000000000000000000000000000000010000 Time delay: 256375 seconds Block delay: 9243
CryticTester.activeEscrow_releaseCollateral(112913198804496738420917365401474698653906858390421022413313559905403873779592,0x1fffffffe,0x1fffffffe) from: 0x0000000000000000000000000000000000030000 Time delay: 385974 seconds Block delay: 42229
CryticTester.property_repayBeneficiaryWithTradedTokens_repay_amount() from: 0x0000000000000000000000000000000000020000 Time delay: 500001 seconds Block delay: 2000
CryticTester.securedLine_sweep_clamped(87669685029446143969622728351657928474475003003890285357579422900236317053841,0x1fffffffe,21735630977748572750434689483835441818289232305483490297099131202798539701274) from: 0x0000000000000000000000000000000000030000 Time delay: 559971 seconds Block delay: 9168
CryticTester.activeEscrow_registerBorrowerContract(0x2fffffffd,true) from: 0x0000000000000000000000000000000000010000 Time delay: 50898 seconds Block delay: 48920
CryticTester.securedLine_abort() from: 0x0000000000000000000000000000000000020000 Time delay: 317376 seconds Block delay: 60098
CryticTester.property_claimEarlyWithdrawalFees_from_borrower() from: 0x0000000000000000000000000000000000020000 Time delay: 522178 seconds Block delay: 53166
CryticTester.property_operateOnUnregisteredContract() from: 0x0000000000000000000000000000000000020000 Time delay: 201659 seconds Block delay: 52681
CryticTester.securedLine_sweep_clamped(238188595,0x11,115792089237316195423570985008687907853269984665640563939457584007913129639936) from: 0x0000000000000000000000000000000000020000 Time delay: 271959 seconds Block delay: 12876
CryticTester.reconTokenOracle_setPrice(0x788110457822fb0505ae9c6bb63abc624e41f67c,49020767117374913749641505459505983244869263464361534644512870100226184853242) from: 0x0000000000000000000000000000000000010000 Time delay: 45334 seconds Block delay: 60451
CryticTester.reconUniV3Oracle_setPrice_clamped(115792089237316195423570985008687907853269984665640564039457584007913129639682,57634994100178429579260531877691514060258419164787084077474051721562001872330) from: 0x0000000000000000000000000000000000020000 Time delay: 66000 seconds Block delay: 37834
CryticTester.activeSpigot_claimOwnerTokens(0x1fffffffe) from: 0x0000000000000000000000000000000000030000 Time delay: 592068 seconds Block delay: 26410
CryticTester.doomsday_getOwnerTokensAborted() from: 0x0000000000000000000000000000000000030000 Time delay: 50418 seconds Block delay: 120
CryticTester.securedLine_RefinanceCredit_clamped(133039444576222547092702330664784099449,338468299075148829430681688679759192903,78484432844402883193173585831902047738101776436022877699808482065302389443468,11170,115792089237316195423570985008687907853269984665640564039447584007913129639935) from: 0x0000000000000000000000000000000000010000 Time delay: 475087 seconds Block delay: 45036
CryticTester.securedLine_stepQ() from: 0x0000000000000000000000000000000000030000 Time delay: 153540 seconds Block delay: 47594
CryticTester.activeEscrow_updateStableCoinWhitelist(0x97fcbc96ed23e4e9f0714008c8f137d57b4d6c97,false) from: 0x0000000000000000000000000000000000030000 Time delay: 86223 seconds Block delay: 25430
CryticTester.securedLine_refinanceCredit(998999999999999999999,101512412847022910777618202173607175251614686837319494483220103511799660128875,78463677965560230358211943597446569759983071878415425095675116719477481305243,50,389,115792089237316195423570985008687907853269984665640564039457584007912688385582) from: 0x0000000000000000000000000000000000030000 Time delay: 322075 seconds Block delay: 300
CryticTester.strategyVault_withdraw(115792089237316195423570985008687907853269984665640564039457584007913129639932,0x2fffffffd,0x33caa79875977b3d515ad34f64bb17576125dfc9) from: 0x0000000000000000000000000000000000010000 Time delay: 258883 seconds Block delay: 32
CryticTester.securedLine_updateOutstandingDebt() from: 0x0000000000000000000000000000000000010000 Time delay: 388904 seconds Block delay: 45262
CryticTester.activeSpigot_updateOperator(0x2fffffffd) from: 0x0000000000000000000000000000000000030000 Time delay: 4000 seconds Block delay: 4989
CryticTester.securedLine_stepQ() from: 0x0000000000000000000000000000000000030000 Time delay: 258422 seconds Block delay: 57086
CryticTester.strategyVault_decreaseYield(1000000) from: 0x0000000000000000000000000000000000030000 Time delay: 185597 seconds Block delay: 57396
CryticTester.activeSpigot_claimOwnerTokens(0xa8ce8aee21bc2a48a5ef670afcc9274c7bbbc035) from: 0x0000000000000000000000000000000000030000 Time delay: 415963 seconds Block delay: 31206
CryticTester.doomsday_isServicer() from: 0x0000000000000000000000000000000000020000 Time delay: 122375 seconds Block delay: 3368

[2024-12-06 22:19:04.38]  Saved reproducer to echidna/reproducers-unshrunk/5713735461348390596.txt
[2024-12-06 22:19:04.50] [Worker 8] Sequence replayed from corpus file 7214681578481809352.txt (102/105)
