[2024-09-19 17:22:33.64] Compiling .... Done! (82.793100666s)
Analyzing contract: /recon/contracts/src/test/recon/CryticTester.sol:CryticTester
stderr:WARNING: Running
stderr: slither failed. Echidna will continue, however fuzzing will likely be less
stderr:effective.


[2024-09-19 17:23:58.29] Running slither on .... Done! (110.425074819s)
Loaded 33 transaction sequences from echidna/reproducers
Loaded 44 transaction sequences from echidna/coverage
[2024-09-19 17:25:49.66] [Worker 3] New coverage: 277 instr, 1 contracts, 1 seqs in corpus
[2024-09-19 17:25:49.66] [Worker 3] Sequence replayed from corpus file 3497916357187991725.txt (1/8)
[2024-09-19 17:25:49.66]  Saved reproducer to echidna/coverage/3497916357187991725.txt
[2024-09-19 17:25:50.22] [Worker 1] New coverage: 22792 instr, 13 contracts, 2 seqs in corpus
[2024-09-19 17:25:50.22] [Worker 4] Test borrower_removeInterestIndividualDelegate(uint256) falsified!
  Call sequence:
CryticTester.target_switchBranch()
CryticTester.target_switchBranch()
CryticTester.target_changePrice(2192842895332067800297)
CryticTester.target_clamped_openTrove(30641527800500361923999724271639171446953862,183463851105273065049098032222246166315847,36487383171804607989417270152064585051080168106339112654969330835,653944058407443874960800682064599993102457,3232818227462348824015057058066238586095)
*wait* Time delay: 1 seconds Block delay: 1
CryticTester.borrower_removeInterestIndividualDelegate(0)

[2024-09-19 17:25:50.23] [Worker 4] New coverage: 23773 instr, 13 contracts, 3 seqs in corpus
[2024-09-19 17:25:50.23]  Saved reproducer to echidna/coverage/8346905194604605113.txt
[2024-09-19 17:25:50.23]  Saved reproducer to echidna/reproducers-unshrunk/8331391758362596858.txt
[2024-09-19 17:25:50.23]  Saved reproducer to echidna/coverage/8331391758362596858.txt
[2024-09-19 17:25:50.23] [Worker 0] Test borrower_adjustUnredeemableTrove(uint256,uint256,bool,uint256,bool,uint256,uint256,uint256) falsified!
  Call sequence:
CryticTester.target_switchBranch()
CryticTester.target_switchBranch()
CryticTester.target_changePrice(2242623479800000713327)
CryticTester.target_clamped_openTrove(33459148639948892875328086643506449,2155469195126378008694527439508027312685524062,369420530119749338416688617604298040895869819408493976142,2618378386293348066258680665,25439588495959383320581518320967184)
*wait* Time delay: 1 seconds Block delay: 1
CryticTester.borrower_adjustUnredeemableTrove(0,0,false,0,false,0,0,0)

[2024-09-19 17:25:50.23] [Worker 0] New coverage: 23812 instr, 13 contracts, 4 seqs in corpus
[2024-09-19 17:25:50.23]  Saved reproducer to echidna/reproducers-unshrunk/5723980372722341639.txt
[2024-09-19 17:25:50.23]  Saved reproducer to echidna/coverage/5723980372722341639.txt
[2024-09-19 17:25:50.25] [Worker 1] Sequence replayed from corpus file 8346905194604605113.txt (1/8)
[2024-09-19 17:25:50.25] [Worker 4] Sequence replayed from corpus file 8331391758362596858.txt (1/8)
[2024-09-19 17:25:50.25] [Worker 0] Sequence replayed from corpus file 5723980372722341639.txt (1/8)
[2024-09-19 17:25:50.34] [Worker 3] Test trove_urgentRedemption(uint256,uint256,uint256) falsified!
  Call sequence:
CryticTester.target_switchBranch()
CryticTester.target_switchBranch()
CryticTester.target_changePrice(2092397960438470095002)
CryticTester.target_clamped_openTrove(25371759969972169926260488663922429476512497848658513,508505791066729635220367204353156779495245,48545546909847794813898812418215594478639287554802706173852003224455,406691225412218738051401683567919931144654042,18141756088845415095121794532705808007359537541703)
*wait* Time delay: 1 seconds Block delay: 1
CryticTester.trove_urgentRedemption(0,0,0)

[2024-09-19 17:25:50.34] [Worker 2] Test pool_claimAllCollGains() falsified!
  Call sequence:
CryticTester.target_switchBranch()
CryticTester.target_switchBranch()
CryticTester.target_changePrice(1978208411201327543955)
CryticTester.target_clamped_openTrove(5242564638275186101948199856244371808707,12574379631255635628877451571041879,648670979771745351076491648902613380821839904682156281419900023258359,3514084680826839960071057484352846053873263431933,1265452527359053167357347475768406090843685840806)
*wait* Time delay: 1 seconds Block delay: 1
CryticTester.pool_claimAllCollGains()

[2024-09-19 17:25:50.34] [Worker 2] New coverage: 24101 instr, 13 contracts, 5 seqs in corpus
[2024-09-19 17:25:50.34] [Worker 3] Sequence replayed from corpus file 6272946786366084845.txt (2/8)
[2024-09-19 17:25:50.34]  Saved reproducer to echidna/reproducers-unshrunk/6272946786366084845.txt
[2024-09-19 17:25:50.34] [Worker 2] Sequence replayed from corpus file 6009849195853177898.txt (1/8)
[2024-09-19 17:25:50.34]  Saved reproducer to echidna/reproducers-unshrunk/6009849195853177898.txt
[2024-09-19 17:25:50.34]  Saved reproducer to echidna/coverage/6009849195853177898.txt
[2024-09-19 17:25:50.37] [Worker 1] Test borrower_removeFromBatch(uint256,uint256,uint256,uint256,uint256) falsified!
  Call sequence:
CryticTester.target_changePrice(2117180215289704281049)
CryticTester.target_clamped_openTrove(5265302460102627455827969953004342437880979,140534832422739804290673789827062670687,4428334824694511319055108721671914350397465234135153628120,8892634523716225932239737708619084143252823701,938874400706835933891361100186211331735)
*wait* Time delay: 1 seconds Block delay: 1
CryticTester.borrower_removeFromBatch(0,0,0,0,0)

[2024-09-19 17:25:50.37]  Saved reproducer to echidna/reproducers-unshrunk/836912825925282009.txt
[2024-09-19 17:25:50.37] [Worker 1] Sequence replayed from corpus file 836912825925282009.txt (2/8)
[2024-09-19 17:25:50.38] [Worker 0] Test borrower_setInterestIndividualDelegate(uint256,address,uint128,uint128,uint256,uint256,uint256,uint256) falsified!
  Call sequence:
CryticTester.target_changePrice(2045939408491371539624)
CryticTester.target_clamped_openTrove(38861132653970457636208537943707466403049404,907684621884857162463923413018637056855004454189555,1101110462206867071788870865679486243338348209401500497899807029606000,6307047264604755418079765046933609670930273366267441,2119658840627749442737517863651422898044387685943)
*wait* Time delay: 1 seconds Block delay: 1
CryticTester.borrower_setInterestIndividualDelegate(0,0x0,0,0,0,0,0,0)

[2024-09-19 17:25:50.38]  Saved reproducer to echidna/reproducers-unshrunk/1364703994590848229.txt
[2024-09-19 17:25:50.38] [Worker 0] Sequence replayed from corpus file 1364703994590848229.txt (2/8)
[2024-09-19 17:25:50.42] [Worker 9] New coverage: 27779 instr, 13 contracts, 6 seqs in corpus
[2024-09-19 17:25:50.43] [Worker 8] New coverage: 28286 instr, 13 contracts, 7 seqs in corpus
[2024-09-19 17:25:50.44] [Worker 2] New coverage: 28306 instr, 13 contracts, 8 seqs in corpus
[2024-09-19 17:25:50.44] [Worker 9] Sequence replayed from corpus file 4317924282912716228.txt (1/5)
[2024-09-19 17:25:50.44] [Worker 2] Sequence replayed from corpus file 8216307532899259345.txt (2/8)
[2024-09-19 17:25:50.45] [Worker 6] New coverage: 28322 instr, 13 contracts, 9 seqs in corpus
[2024-09-19 17:25:50.45] [Worker 8] Sequence replayed from corpus file 6718773283085384835.txt (1/8)
[2024-09-19 17:25:50.45] [Worker 3] New coverage: 28322 instr, 13 contracts, 10 seqs in corpus
[2024-09-19 17:25:50.45] [Worker 3] Sequence replayed from corpus file 6355948066041189840.txt (3/8)
[2024-09-19 17:25:50.45] [Worker 7] New coverage: 28322 instr, 13 contracts, 11 seqs in corpus
[2024-09-19 17:25:50.46] [Worker 1] Test borrower_registerBatchManager(uint128,uint128,uint128,uint128,uint128) falsified!
  Call sequence:
CryticTester.target_switchBranch()
CryticTester.target_switchBranch()
CryticTester.target_changePrice(2123131161372207499305)
CryticTester.target_clamped_openTrove(3416331781290588414922606412477576308870165892005,567229755775580077606565559659672700270410235,57137701504322230145369292950683383846724272284693414011335677911,28257205517379953489413696210732622292225802,16536257899562006106924675020921240048234)
*wait* Time delay: 1 seconds Block delay: 1
CryticTester.borrower_registerBatchManager(0,0,0,0,0)

[2024-09-19 17:25:50.46] [Worker 1] Sequence replayed from corpus file 7183347344743277923.txt (3/8)
[2024-09-19 17:25:50.46] [Worker 6] Sequence replayed from corpus file 6255285325909652052.txt (1/8)
[2024-09-19 17:25:50.46] [Worker 7] Sequence replayed from corpus file 8795667281353125244.txt (1/8)
[2024-09-19 17:25:52.24] [status] tests: 29/71, fuzzing: 750/50000, values: [], cov: 31426, corpus: 30
[2024-09-19 17:25:50.47] [Worker 4] Test property_AP01() falsified!
  Call sequence:
CryticTester.target_switchBranch() from: 0x0000000000000000000000000000000000030000 Time delay: 604535 seconds Block delay: 45819
CryticTester.property_SR02() from: 0x0000000000000000000000000000000000010000 Time delay: 127 seconds Block delay: 38350
CryticTester.check_withdrawBold() from: 0x0000000000000000000000000000000000020000 Time delay: 39499 seconds Block delay: 16089
CryticTester.check_adjustUnredeemableTrove() from: 0x0000000000000000000000000000000000030000 Time delay: 407328 seconds Block delay: 45261
CryticTester.borrower_withdrawColl(1524785993,20371423967322543348166130790527990415353884455755310220802371525126607430342) from: 0x0000000000000000000000000000000000030000 Time delay: 19029 seconds Block delay: 561
CryticTester.borrower_withdrawColl(1524785993,20371423967322543348166130790527990415353884455755310220802371525126607430342) from: 0x0000000000000000000000000000000000020000 Time delay: 344203 seconds Block delay: 11905
CryticTester.pool_withdrawFromSP(234280891644529721636,true) from: 0x0000000000000000000000000000000000020000 Time delay: 297507 seconds Block delay: 12053
CryticTester.check_aggBatchManagementFees_zero() from: 0x0000000000000000000000000000000000010000 Time delay: 38059 seconds Block delay: 60267
CryticTester.check_redeemCollateral() from: 0x0000000000000000000000000000000000010000 Time delay: 55534 seconds Block delay: 38350
CryticTester.pool_provideToSP(1673659462206822280136005485972587501452011600000264578513884453059,true) from: 0x0000000000000000000000000000000000030000 Time delay: 564960 seconds Block delay: 30256
CryticTester.borrower_adjustTroveInterestRate(115792089237316195423570985008687907853269984665640564039457584007913129639935,6,44036204391197776786324621912938136849676345653166550973734456013315555211709,115792089237316195423570985008687907853269984665640564039457584007913129639935,60295583979270105117777295141234798687764895433482669493498021962903931316130) from: 0x0000000000000000000000000000000000020000 Time delay: 303345 seconds Block delay: 4462
CryticTester.borrower_repayBold(1524785992,94804695032005548288581232014667159761923935998248951985344170054049234281557) from: 0x0000000000000000000000000000000000030000 Time delay: 38059 seconds Block delay: 30042
CryticTester.pool_withdrawFromSP(2078929346529529887739437983016979261226396399427394818019855008716801372218,true) from: 0x0000000000000000000000000000000000030000 Time delay: 45142 seconds Block delay: 9966
CryticTester.borrower_adjustTrove(1524785991,36613774563958022831000067707734997123352444763486346660668396298735428149679,true,0,true,2948412829) from: 0x0000000000000000000000000000000000030000 Time delay: 588255 seconds Block delay: 45852
CryticTester.borrower_closeTrove(4370000) from: 0x0000000000000000000000000000000000020000 Time delay: 66543 seconds Block delay: 32
CryticTester.borrower_adjustTrove(37671483587724,30601217278257410016143523601371951390002190189042480047358660164323486630905,true,1524785993,false,0) from: 0x0000000000000000000000000000000000020000 Time delay: 73040 seconds Block delay: 7122
CryticTester.check_batch_zero_shares() from: 0x0000000000000000000000000000000000010000 Time delay: 447588 seconds Block delay: 12493
CryticTester.check_setInterestBatchManager() from: 0x0000000000000000000000000000000000010000 Time delay: 344203 seconds Block delay: 5140
CryticTester.check_batchLiquidateTroves() from: 0x0000000000000000000000000000000000010000 Time delay: 65535 seconds Block delay: 53451
CryticTester.property_GL01(31554803209172076480661426473220060214584877504583840785680143887956082537269,18897065245005506108266552773000027057443781914770582903306292860866289957048) from: 0x0000000000000000000000000000000000020000 Time delay: 136393 seconds Block delay: 45819
CryticTester.check_setInterestIndividualDelegate() from: 0x0000000000000000000000000000000000020000 Time delay: 358612 seconds Block delay: 53562
CryticTester.property_CS05() from: 0x0000000000000000000000000000000000020000 Time delay: 412373 seconds Block delay: 3661
CryticTester.borrower_openTrove(4369999,15765288365102653277087323490861195256370118303578458839335254968770077862383,115792089237316195423570985008687907853269984665640564039457584007913129639932,819,29451546569338409879428755317950596679019473569059977490971459284298604345618,2866632765558401205705924536650590418198133749829489179175127684450360012752,4349515205195986913993488665381725497499115022808205567282689982099450765846,0x2fffffffd,0x10000,0xbffb01bb2ddb4efa87cb78eecb8115afae6d2032) from: 0x0000000000000000000000000000000000010000 Time delay: 116188 seconds Block delay: 45261
CryticTester.borrower_removeFromBatch(1524785992,0,64570147780839795145042836487775821326909125110714231576655947648719482944596,4369999,115792089237316195423570985008687907853269984665640564039457584007913129639935) from: 0x0000000000000000000000000000000000030000 Time delay: 173012 seconds Block delay: 23978
CryticTester.borrower_lowerBatchManagementFee(4370001) from: 0x0000000000000000000000000000000000020000 Time delay: 404739 seconds Block delay: 27585
CryticTester.check_repayBold() from: 0x0000000000000000000000000000000000010000 Time delay: 360624 seconds Block delay: 22995
CryticTester.borrower_adjustTrove(115792089237316195423570985008687907853269984665640564039457584007913129639935,32888464549256464762378240849253838304411798635043499614197176220825874121387,false,4369999,false,52021214786108809071697236384768719891167837770612563637216453046991223777669) from: 0x0000000000000000000000000000000000010000 Time delay: 318197 seconds Block delay: 8447
CryticTester.borrower_setBatchManagerAnnualInterestRate(263875216653683219038972646229662346223,57226327961326796071795551370183958204724485019911075532810176379757378943003,63545442283025874336752696934353731219505687675733390856447659522443626366027,4370001) from: 0x0000000000000000000000000000000000020000 Time delay: 412373 seconds Block delay: 48801
CryticTester.check_adjustTroveInterestRate() from: 0x0000000000000000000000000000000000010000 Time delay: 332369 seconds Block delay: 37522
CryticTester.check_adjustTrove() from: 0x0000000000000000000000000000000000020000 Time delay: 332369 seconds Block delay: 2512
CryticTester.check_provideToSP() from: 0x0000000000000000000000000000000000020000 Time delay: 379552 seconds Block delay: 39277
CryticTester.target_clamped_openTrove(4370001,3267440775571433487495992715433912597835118784232719571780376420438026268472,115792089237316195423570985008687907853269984665640564039457584007913129639934,115792089237316195423570985008687907853269984665640564039457584007913129639935,107707231000066593938848296348333519806375928805701331763789730297623115451317) from: 0x0000000000000000000000000000000000030000 Time delay: 405856 seconds Block delay: 4462
CryticTester.borrower_repayBold(68360059409186723274559902539822281430298767802887026800442350917576968918253,1524785991) from: 0x0000000000000000000000000000000000030000 Time delay: 32767 seconds Block delay: 4896
CryticTester.property_GL01(4370000,204) from: 0x0000000000000000000000000000000000020000 Time delay: 542800 seconds Block delay: 2526
CryticTester.target_clamped_openTrove(115792089237316195423570985008687907853269984665640564039457584007913129639935,78,14695270523411766503609359262747086702970250955302394653108216269908392463707,0,18214220166620607282881935341042412291747739468456413388400640613669321567666) from: 0x0000000000000000000000000000000000020000 Time delay: 289103 seconds Block delay: 45819
CryticTester.borrower_shutdown() from: 0x0000000000000000000000000000000000030000 Time delay: 448552 seconds Block delay: 38350
CryticTester.check_adjustTroveInterestRate() from: 0x0000000000000000000000000000000000010000 Time delay: 136394 seconds Block delay: 60054
CryticTester.borrower_lowerBatchManagementFee(94214356609897496060004170564844315021538192110978109925925065079414928846688) from: 0x0000000000000000000000000000000000030000 Time delay: 254414 seconds Block delay: 2512
CryticTester.property_BT01() from: 0x0000000000000000000000000000000000020000 Time delay: 112444 seconds Block delay: 5952
CryticTester.trove_batchLiquidateTroves(38600418570429235383235636502418458268312091064314163557365030394357069123507) from: 0x0000000000000000000000000000000000030000 Time delay: 554465 seconds Block delay: 60267
CryticTester.property_CS05() from: 0x0000000000000000000000000000000000030000 Time delay: 478623 seconds Block delay: 18429
CryticTester.borrower_openTroveAndJoinInterestBatchManager(1524785992,1524785991,4370001,7673636352793001185754796880279795073165545602575931724805096096144436663966,7906117544669251187966502892250394937349574484725327853728326341276845505575,0x3fa387ab1db837680029bf9896fae3e8ab44d31d,27789751040676587139265371841689741508238342764282269709941045201535591243106,0x1fffffffe,0xffffffff,0x63fb907fd752a801f71cad07f19c58e3d182dfc1) from: 0x0000000000000000000000000000000000020000 Time delay: 305572 seconds Block delay: 12053
CryticTester.borrower_adjustTroveInterestRate(102736940320062768440400785930895164265616433235965040674023425432536678715684,4370000,4370001,4370001,21321111292895891924779243859389181748579588561890222673331028109487084202314) from: 0x0000000000000000000000000000000000030000 Time delay: 5871 seconds Block delay: 2497
CryticTester.borrower_openTrove(113135278283442727243189110466216394328325812746267107947947413281875635836507,115792089237316195423570985008687907853269984665640564039457584007913129639935,62684679605347214085531345854013540324174604110251647416393659638037310409265,112804507348799170939689305168896204360546924477055974861050600095318804730533,39596245088773537137435470850951522874672491267822940167898479534953118775060,115792089237316195423570985008687907853269984665640564039457584007913129639932,19307664968305754514706443690742330901295412326527376912179179988747170865547,0xefc56627233b02ea95bae7e19f648d7dcd5bb132,0x1fffffffe,0xffffffff) from: 0x0000000000000000000000000000000000030000 Time delay: 322247 seconds Block delay: 33357
CryticTester.check_urgentRedemption() from: 0x0000000000000000000000000000000000020000
CryticTester.borrower_setInterestIndividualDelegate(35821391630369492863977451065040001321796714472378532395244546771486858742994,0x1fffffffe,4370001,195193111252274341382365307949373941561,0,1524785991,2605238128580880897403991168382144513149824355067808472857103669974131160395,831) from: 0x0000000000000000000000000000000000020000 Time delay: 50309 seconds Block delay: 2512
CryticTester.check_removeInterestIndividualDelegate() from: 0x0000000000000000000000000000000000030000 Time delay: 50417 seconds Block delay: 38100
CryticTester.check_batch_zero_debt() from: 0x0000000000000000000000000000000000030000 Time delay: 401699 seconds Block delay: 16089
CryticTester.check_addColl() from: 0x0000000000000000000000000000000000030000 Time delay: 82670 seconds Block delay: 5023
CryticTester.borrower_shutdown() from: 0x0000000000000000000000000000000000010000 Time delay: 490446 seconds Block delay: 32147
CryticTester.property_CS05() from: 0x0000000000000000000000000000000000010000 Time delay: 82671 seconds Block delay: 20243
CryticTester.check_repayBold() from: 0x0000000000000000000000000000000000030000 Time delay: 254414 seconds Block delay: 15369
CryticTester.target_switchBranch() from: 0x0000000000000000000000000000000000030000 Time delay: 569114 seconds Block delay: 19933
CryticTester.check_claimCollateral() from: 0x0000000000000000000000000000000000030000 Time delay: 16802 seconds Block delay: 50880
CryticTester.target_changePrice(9304213673874071895829) from: 0x0000000000000000000000000000000000010000 Time delay: 31594 seconds Block delay: 59983
CryticTester.borrower_openTroveAndJoinInterestBatchManager(72476792677662743137159727716361219064114011618143878321469603903445664726352,8971090820166726815194974547892585242688298398100371799659544510979163602,4370000,58472950080766825801072582196326380573086768530325640942403754028639637366381,1524785991,0xffffffff,1524785993,0xffffffff,0xce71065d4017f316ec606fe4422e11eb2c47c246,0xd01001c0261679fea6b5dd286e8f8cd227ba4f5b) from: 0x0000000000000000000000000000000000030000 Time delay: 14744 seconds Block delay: 800
CryticTester.property_CS05() from: 0x0000000000000000000000000000000000030000 Time delay: 79643 seconds Block delay: 18429
CryticTester.check_provideToSP() from: 0x0000000000000000000000000000000000030000 Time delay: 65250 seconds Block delay: 53069
CryticTester.check_hasRedistributedDebt() from: 0x0000000000000000000000000000000000020000 Time delay: 448552 seconds Block delay: 9966
CryticTester.property_GL01(4369999,1524785993) from: 0x0000000000000000000000000000000000010000 Time delay: 195123 seconds Block delay: 53678
CryticTester.check_lowerBatchManagementFee() from: 0x0000000000000000000000000000000000010000 Time delay: 207289 seconds Block delay: 5023
CryticTester.check_lowerBatchManagementFee() from: 0x0000000000000000000000000000000000010000 Time delay: 4541 seconds Block delay: 24987
CryticTester.check_withdrawColl() from: 0x0000000000000000000000000000000000030000 Time delay: 292304 seconds Block delay: 15369
CryticTester.property_AP01() from: 0x0000000000000000000000000000000000030000 Time delay: 49735 seconds Block delay: 45261
CryticTester.check_adjustTrove() from: 0x0000000000000000000000000000000000020000 Time delay: 490448 seconds Block delay: 54809
CryticTester.property_CS05() from: 0x0000000000000000000000000000000000010000 Time delay: 414736 seconds Block delay: 22909
CryticTester.borrower_applyPendingDebt(105267168053855894345507083933188747931593114173768994802826414950315812548640,546,7700308879144502315947427388820540363626542145622646390111817325669178454365) from: 0x0000000000000000000000000000000000020000 Time delay: 82672 seconds Block delay: 1984
CryticTester.check_claimCollateral() from: 0x0000000000000000000000000000000000020000 Time delay: 451926 seconds Block delay: 60364
CryticTester.check_aggBatchManagementFees_zero() from: 0x0000000000000000000000000000000000030000 Time delay: 521319 seconds Block delay: 5237
CryticTester.check_singleDebt() from: 0x0000000000000000000000000000000000010000 Time delay: 390247 seconds Block delay: 12493
CryticTester.check_provideToSP() from: 0x0000000000000000000000000000000000010000 Time delay: 111322 seconds Block delay: 42530
CryticTester.property_CS05() from: 0x0000000000000000000000000000000000010000 Time delay: 303345 seconds Block delay: 26645
CryticTester.target_new_clamped_openTrove(58067667070453973851154453755609385574996883427392481626357558689466148976885,46,1524785991,2115511031506683241384245349869770476235429005802205427152618048654556198718) from: 0x0000000000000000000000000000000000030000 Time delay: 379552 seconds Block delay: 58783
CryticTester.target_new_clamped_openTrove(21742893295406513953727140654136955550498021547698567457865303669392890226971,4369999,102315194664388176692710250422331321822112816506631485264069383110519071218437,19268033790077602888684900149351324857221725537937406755149443639962491472117) from: 0x0000000000000000000000000000000000010000 Time delay: 225906 seconds Block delay: 16089
CryticTester.property_GL01(0,1524785991) from: 0x0000000000000000000000000000000000020000 Time delay: 136392 seconds Block delay: 22699
CryticTester.check_setInterestIndividualDelegate() from: 0x0000000000000000000000000000000000020000 Time delay: 407328 seconds Block delay: 1362
CryticTester.borrower_withdrawBold(115792089237316195423570985008687907853269984665640564039457584007913129639934,65879676268752388550238811471266123543893583500005882468507412754198098280410,88147477110208935604919403050964979725401419862277745050026658640124092754008) from: 0x0000000000000000000000000000000000010000 Time delay: 318197 seconds Block delay: 2511
CryticTester.check_adjustUnredeemableTrove() from: 0x0000000000000000000000000000000000020000 Time delay: 3208 seconds Block delay: 3898
CryticTester.borrower_setInterestBatchManager(38518708017459749970089511119816079445446710561860404850784709614705893413729,224,604,1524785992) from: 0x0000000000000000000000000000000000020000 Time delay: 127 seconds Block delay: 32
CryticTester.check_shutdown() from: 0x0000000000000000000000000000000000010000 Time delay: 322374 seconds Block delay: 16617
CryticTester.check_withdrawFromSP() from: 0x0000000000000000000000000000000000030000 Time delay: 338920 seconds Block delay: 6721
CryticTester.target_clamped_openTrove(13650788885973381801577328194376964874477573824240962794717973268441563220965,63286368368718520898662540306717572677388716178968970877578612053200501515021,79525945717432498952660953873728924282131153312236167289295564229278081287187,11243017345616817865156746056645160464756496708563542968729295401858370191465,115792089237316195423570985008687907853269984665640564039457584007913129639934) from: 0x0000000000000000000000000000000000030000 Time delay: 156190 seconds Block delay: 38350
CryticTester.property_AP01() from: 0x0000000000000000000000000000000000030000 Time delay: 136393 seconds Block delay: 53349

[2024-09-19 17:25:50.47] [Worker 0] Test borrower_openTroveAndJoinInterestBatchManager(uint256,uint256,uint256,uint256,uint256,address,uint256,address,address,address) falsified!
  Call sequence:
CryticTester.target_switchBranch()
CryticTester.target_switchBranch()
CryticTester.target_changePrice(2061528955591962753246)
CryticTester.target_clamped_openTrove(7722058330588668479236176902788762319435227180,1959286714276758458986248267781016924111269,132623975319774039462675975535722648705179194440821019274938265888494,116020861021882985501995251992516893254505912638252,6060472475981608612629339660136267305544901350782)
*wait* Time delay: 1 seconds Block delay: 1
CryticTester.borrower_openTroveAndJoinInterestBatchManager(0,32586703,0,0,0,0x0,84745819992,0x0,0x0,0x0)

[2024-09-19 17:25:50.47] [Worker 0] Sequence replayed from corpus file 201120595268296457.txt (3/8)
[2024-09-19 17:25:55.25] [status] tests: 29/71, fuzzing: 6800/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:25:50.47] [Worker 4] Test borrower_openTrove(uint256,uint256,uint256,uint256,uint256,uint256,uint256,address,address,address) falsified!
  Call sequence:
CryticTester.target_switchBranch() from: 0x0000000000000000000000000000000000030000 Time delay: 604535 seconds Block delay: 45819
CryticTester.property_SR02() from: 0x0000000000000000000000000000000000010000 Time delay: 127 seconds Block delay: 38350
CryticTester.check_withdrawBold() from: 0x0000000000000000000000000000000000020000 Time delay: 39499 seconds Block delay: 16089
CryticTester.check_adjustUnredeemableTrove() from: 0x0000000000000000000000000000000000030000 Time delay: 407328 seconds Block delay: 45261
CryticTester.borrower_withdrawColl(1524785993,20371423967322543348166130790527990415353884455755310220802371525126607430342) from: 0x0000000000000000000000000000000000030000 Time delay: 19029 seconds Block delay: 561
CryticTester.borrower_withdrawColl(1524785993,20371423967322543348166130790527990415353884455755310220802371525126607430342) from: 0x0000000000000000000000000000000000020000 Time delay: 344203 seconds Block delay: 11905
CryticTester.pool_withdrawFromSP(234280891644529721636,true) from: 0x0000000000000000000000000000000000020000 Time delay: 297507 seconds Block delay: 12053
CryticTester.check_aggBatchManagementFees_zero() from: 0x0000000000000000000000000000000000010000 Time delay: 38059 seconds Block delay: 60267
CryticTester.check_redeemCollateral() from: 0x0000000000000000000000000000000000010000 Time delay: 55534 seconds Block delay: 38350
CryticTester.pool_provideToSP(1673659462206822280136005485972587501452011600000264578513884453059,true) from: 0x0000000000000000000000000000000000030000 Time delay: 564960 seconds Block delay: 30256
CryticTester.borrower_adjustTroveInterestRate(115792089237316195423570985008687907853269984665640564039457584007913129639935,6,44036204391197776786324621912938136849676345653166550973734456013315555211709,115792089237316195423570985008687907853269984665640564039457584007913129639935,60295583979270105117777295141234798687764895433482669493498021962903931316130) from: 0x0000000000000000000000000000000000020000 Time delay: 303345 seconds Block delay: 4462
CryticTester.borrower_repayBold(1524785992,94804695032005548288581232014667159761923935998248951985344170054049234281557) from: 0x0000000000000000000000000000000000030000 Time delay: 38059 seconds Block delay: 30042
CryticTester.pool_withdrawFromSP(2078929346529529887739437983016979261226396399427394818019855008716801372218,true) from: 0x0000000000000000000000000000000000030000 Time delay: 45142 seconds Block delay: 9966
CryticTester.borrower_adjustTrove(1524785991,36613774563958022831000067707734997123352444763486346660668396298735428149679,true,0,true,2948412829) from: 0x0000000000000000000000000000000000030000 Time delay: 588255 seconds Block delay: 45852
CryticTester.borrower_closeTrove(4370000) from: 0x0000000000000000000000000000000000020000 Time delay: 66543 seconds Block delay: 32
CryticTester.borrower_adjustTrove(37671483587724,30601217278257410016143523601371951390002190189042480047358660164323486630905,true,1524785993,false,0) from: 0x0000000000000000000000000000000000020000 Time delay: 73040 seconds Block delay: 7122
CryticTester.check_batch_zero_shares() from: 0x0000000000000000000000000000000000010000 Time delay: 447588 seconds Block delay: 12493
CryticTester.check_setInterestBatchManager() from: 0x0000000000000000000000000000000000010000 Time delay: 344203 seconds Block delay: 5140
CryticTester.check_batchLiquidateTroves() from: 0x0000000000000000000000000000000000010000 Time delay: 65535 seconds Block delay: 53451
CryticTester.property_GL01(31554803209172076480661426473220060214584877504583840785680143887956082537269,18897065245005506108266552773000027057443781914770582903306292860866289957048) from: 0x0000000000000000000000000000000000020000 Time delay: 136393 seconds Block delay: 45819
CryticTester.check_setInterestIndividualDelegate() from: 0x0000000000000000000000000000000000020000 Time delay: 358612 seconds Block delay: 53562
CryticTester.property_CS05() from: 0x0000000000000000000000000000000000020000 Time delay: 412373 seconds Block delay: 3661
CryticTester.borrower_openTrove(4369999,15765288365102653277087323490861195256370118303578458839335254968770077862383,115792089237316195423570985008687907853269984665640564039457584007913129639932,819,29451546569338409879428755317950596679019473569059977490971459284298604345618,2866632765558401205705924536650590418198133749829489179175127684450360012752,4349515205195986913993488665381725497499115022808205567282689982099450765846,0x2fffffffd,0x10000,0xbffb01bb2ddb4efa87cb78eecb8115afae6d2032) from: 0x0000000000000000000000000000000000010000 Time delay: 116188 seconds Block delay: 45261
CryticTester.borrower_removeFromBatch(1524785992,0,64570147780839795145042836487775821326909125110714231576655947648719482944596,4369999,115792089237316195423570985008687907853269984665640564039457584007913129639935) from: 0x0000000000000000000000000000000000030000 Time delay: 173012 seconds Block delay: 23978
CryticTester.borrower_lowerBatchManagementFee(4370001) from: 0x0000000000000000000000000000000000020000 Time delay: 404739 seconds Block delay: 27585
CryticTester.check_repayBold() from: 0x0000000000000000000000000000000000010000 Time delay: 360624 seconds Block delay: 22995
CryticTester.borrower_adjustTrove(115792089237316195423570985008687907853269984665640564039457584007913129639935,32888464549256464762378240849253838304411798635043499614197176220825874121387,false,4369999,false,52021214786108809071697236384768719891167837770612563637216453046991223777669) from: 0x0000000000000000000000000000000000010000 Time delay: 318197 seconds Block delay: 8447
CryticTester.borrower_setBatchManagerAnnualInterestRate(263875216653683219038972646229662346223,57226327961326796071795551370183958204724485019911075532810176379757378943003,63545442283025874336752696934353731219505687675733390856447659522443626366027,4370001) from: 0x0000000000000000000000000000000000020000 Time delay: 412373 seconds Block delay: 48801
CryticTester.check_adjustTroveInterestRate() from: 0x0000000000000000000000000000000000010000 Time delay: 332369 seconds Block delay: 37522
CryticTester.check_adjustTrove() from: 0x0000000000000000000000000000000000020000 Time delay: 332369 seconds Block delay: 2512
CryticTester.check_provideToSP() from: 0x0000000000000000000000000000000000020000 Time delay: 379552 seconds Block delay: 39277
CryticTester.target_clamped_openTrove(4370001,3267440775571433487495992715433912597835118784232719571780376420438026268472,115792089237316195423570985008687907853269984665640564039457584007913129639934,115792089237316195423570985008687907853269984665640564039457584007913129639935,107707231000066593938848296348333519806375928805701331763789730297623115451317) from: 0x0000000000000000000000000000000000030000 Time delay: 405856 seconds Block delay: 4462
CryticTester.borrower_repayBold(68360059409186723274559902539822281430298767802887026800442350917576968918253,1524785991) from: 0x0000000000000000000000000000000000030000 Time delay: 32767 seconds Block delay: 4896
CryticTester.property_GL01(4370000,204) from: 0x0000000000000000000000000000000000020000 Time delay: 542800 seconds Block delay: 2526
CryticTester.target_clamped_openTrove(115792089237316195423570985008687907853269984665640564039457584007913129639935,78,14695270523411766503609359262747086702970250955302394653108216269908392463707,0,18214220166620607282881935341042412291747739468456413388400640613669321567666) from: 0x0000000000000000000000000000000000020000 Time delay: 289103 seconds Block delay: 45819
CryticTester.borrower_shutdown() from: 0x0000000000000000000000000000000000030000 Time delay: 448552 seconds Block delay: 38350
CryticTester.check_adjustTroveInterestRate() from: 0x0000000000000000000000000000000000010000 Time delay: 136394 seconds Block delay: 60054
CryticTester.borrower_lowerBatchManagementFee(94214356609897496060004170564844315021538192110978109925925065079414928846688) from: 0x0000000000000000000000000000000000030000 Time delay: 254414 seconds Block delay: 2512
CryticTester.property_BT01() from: 0x0000000000000000000000000000000000020000 Time delay: 112444 seconds Block delay: 5952
CryticTester.trove_batchLiquidateTroves(38600418570429235383235636502418458268312091064314163557365030394357069123507) from: 0x0000000000000000000000000000000000030000 Time delay: 554465 seconds Block delay: 60267
CryticTester.property_CS05() from: 0x0000000000000000000000000000000000030000 Time delay: 478623 seconds Block delay: 18429
CryticTester.borrower_openTroveAndJoinInterestBatchManager(1524785992,1524785991,4370001,7673636352793001185754796880279795073165545602575931724805096096144436663966,7906117544669251187966502892250394937349574484725327853728326341276845505575,0x3fa387ab1db837680029bf9896fae3e8ab44d31d,27789751040676587139265371841689741508238342764282269709941045201535591243106,0x1fffffffe,0xffffffff,0x63fb907fd752a801f71cad07f19c58e3d182dfc1) from: 0x0000000000000000000000000000000000020000 Time delay: 305572 seconds Block delay: 12053
CryticTester.borrower_adjustTroveInterestRate(102736940320062768440400785930895164265616433235965040674023425432536678715684,4370000,4370001,4370001,21321111292895891924779243859389181748579588561890222673331028109487084202314) from: 0x0000000000000000000000000000000000030000 Time delay: 5871 seconds Block delay: 2497
CryticTester.borrower_openTrove(113135278283442727243189110466216394328325812746267107947947413281875635836507,115792089237316195423570985008687907853269984665640564039457584007913129639935,62684679605347214085531345854013540324174604110251647416393659638037310409265,112804507348799170939689305168896204360546924477055974861050600095318804730533,39596245088773537137435470850951522874672491267822940167898479534953118775060,115792089237316195423570985008687907853269984665640564039457584007913129639932,19307664968305754514706443690742330901295412326527376912179179988747170865547,0xefc56627233b02ea95bae7e19f648d7dcd5bb132,0x1fffffffe,0xffffffff) from: 0x0000000000000000000000000000000000030000 Time delay: 322247 seconds Block delay: 33357
CryticTester.check_urgentRedemption() from: 0x0000000000000000000000000000000000020000
CryticTester.borrower_setInterestIndividualDelegate(35821391630369492863977451065040001321796714472378532395244546771486858742994,0x1fffffffe,4370001,195193111252274341382365307949373941561,0,1524785991,2605238128580880897403991168382144513149824355067808472857103669974131160395,831) from: 0x0000000000000000000000000000000000020000 Time delay: 50309 seconds Block delay: 2512
CryticTester.check_removeInterestIndividualDelegate() from: 0x0000000000000000000000000000000000030000 Time delay: 50417 seconds Block delay: 38100
CryticTester.check_batch_zero_debt() from: 0x0000000000000000000000000000000000030000 Time delay: 401699 seconds Block delay: 16089
CryticTester.check_addColl() from: 0x0000000000000000000000000000000000030000 Time delay: 82670 seconds Block delay: 5023
CryticTester.borrower_shutdown() from: 0x0000000000000000000000000000000000010000 Time delay: 490446 seconds Block delay: 32147
CryticTester.property_CS05() from: 0x0000000000000000000000000000000000010000 Time delay: 82671 seconds Block delay: 20243
CryticTester.check_repayBold() from: 0x0000000000000000000000000000000000030000 Time delay: 254414 seconds Block delay: 15369
CryticTester.target_switchBranch() from: 0x0000000000000000000000000000000000030000 Time delay: 569114 seconds Block delay: 19933
CryticTester.check_claimCollateral() from: 0x0000000000000000000000000000000000030000 Time delay: 16802 seconds Block delay: 50880
CryticTester.target_changePrice(9304213673874071895829) from: 0x0000000000000000000000000000000000010000 Time delay: 31594 seconds Block delay: 59983
CryticTester.borrower_openTroveAndJoinInterestBatchManager(72476792677662743137159727716361219064114011618143878321469603903445664726352,8971090820166726815194974547892585242688298398100371799659544510979163602,4370000,58472950080766825801072582196326380573086768530325640942403754028639637366381,1524785991,0xffffffff,1524785993,0xffffffff,0xce71065d4017f316ec606fe4422e11eb2c47c246,0xd01001c0261679fea6b5dd286e8f8cd227ba4f5b) from: 0x0000000000000000000000000000000000030000 Time delay: 14744 seconds Block delay: 800
CryticTester.property_CS05() from: 0x0000000000000000000000000000000000030000 Time delay: 79643 seconds Block delay: 18429
CryticTester.check_provideToSP() from: 0x0000000000000000000000000000000000030000 Time delay: 65250 seconds Block delay: 53069
CryticTester.check_hasRedistributedDebt() from: 0x0000000000000000000000000000000000020000 Time delay: 448552 seconds Block delay: 9966
CryticTester.property_GL01(4369999,1524785993) from: 0x0000000000000000000000000000000000010000 Time delay: 195123 seconds Block delay: 53678
CryticTester.check_lowerBatchManagementFee() from: 0x0000000000000000000000000000000000010000 Time delay: 207289 seconds Block delay: 5023
CryticTester.check_lowerBatchManagementFee() from: 0x0000000000000000000000000000000000010000 Time delay: 4541 seconds Block delay: 24987
CryticTester.check_withdrawColl() from: 0x0000000000000000000000000000000000030000 Time delay: 292304 seconds Block delay: 15369
CryticTester.property_AP01() from: 0x0000000000000000000000000000000000030000 Time delay: 49735 seconds Block delay: 45261
CryticTester.check_adjustTrove() from: 0x0000000000000000000000000000000000020000 Time delay: 490448 seconds Block delay: 54809
CryticTester.property_CS05() from: 0x0000000000000000000000000000000000010000 Time delay: 414736 seconds Block delay: 22909
CryticTester.borrower_applyPendingDebt(105267168053855894345507083933188747931593114173768994802826414950315812548640,546,7700308879144502315947427388820540363626542145622646390111817325669178454365) from: 0x0000000000000000000000000000000000020000 Time delay: 82672 seconds Block delay: 1984
CryticTester.check_claimCollateral() from: 0x0000000000000000000000000000000000020000 Time delay: 451926 seconds Block delay: 60364
CryticTester.check_aggBatchManagementFees_zero() from: 0x0000000000000000000000000000000000030000 Time delay: 521319 seconds Block delay: 5237
CryticTester.check_singleDebt() from: 0x0000000000000000000000000000000000010000 Time delay: 390247 seconds Block delay: 12493
CryticTester.check_provideToSP() from: 0x0000000000000000000000000000000000010000 Time delay: 111322 seconds Block delay: 42530
CryticTester.property_CS05() from: 0x0000000000000000000000000000000000010000 Time delay: 303345 seconds Block delay: 26645
CryticTester.target_new_clamped_openTrove(58067667070453973851154453755609385574996883427392481626357558689466148976885,46,1524785991,2115511031506683241384245349869770476235429005802205427152618048654556198718) from: 0x0000000000000000000000000000000000030000 Time delay: 379552 seconds Block delay: 58783
CryticTester.target_new_clamped_openTrove(21742893295406513953727140654136955550498021547698567457865303669392890226971,4369999,102315194664388176692710250422331321822112816506631485264069383110519071218437,19268033790077602888684900149351324857221725537937406755149443639962491472117) from: 0x0000000000000000000000000000000000010000 Time delay: 225906 seconds Block delay: 16089
CryticTester.property_GL01(0,1524785991) from: 0x0000000000000000000000000000000000020000 Time delay: 136392 seconds Block delay: 22699
CryticTester.check_setInterestIndividualDelegate() from: 0x0000000000000000000000000000000000020000 Time delay: 407328 seconds Block delay: 1362
CryticTester.borrower_withdrawBold(115792089237316195423570985008687907853269984665640564039457584007913129639934,65879676268752388550238811471266123543893583500005882468507412754198098280410,88147477110208935604919403050964979725401419862277745050026658640124092754008) from: 0x0000000000000000000000000000000000010000 Time delay: 318197 seconds Block delay: 2511
CryticTester.check_adjustUnredeemableTrove() from: 0x0000000000000000000000000000000000020000 Time delay: 3208 seconds Block delay: 3898
CryticTester.borrower_setInterestBatchManager(38518708017459749970089511119816079445446710561860404850784709614705893413729,224,604,1524785992) from: 0x0000000000000000000000000000000000020000 Time delay: 127 seconds Block delay: 32
CryticTester.check_shutdown() from: 0x0000000000000000000000000000000000010000 Time delay: 322374 seconds Block delay: 16617
CryticTester.check_withdrawFromSP() from: 0x0000000000000000000000000000000000030000 Time delay: 338920 seconds Block delay: 6721
CryticTester.target_clamped_openTrove(13650788885973381801577328194376964874477573824240962794717973268441563220965,63286368368718520898662540306717572677388716178968970877578612053200501515021,79525945717432498952660953873728924282131153312236167289295564229278081287187,11243017345616817865156746056645160464756496708563542968729295401858370191465,115792089237316195423570985008687907853269984665640564039457584007913129639934) from: 0x0000000000000000000000000000000000030000 Time delay: 156190 seconds Block delay: 38350
CryticTester.property_AP01() from: 0x0000000000000000000000000000000000030000 Time delay: 136393 seconds Block delay: 53349
CryticTester.borrower_openTrove(113496225954133688563487006427021297991423689993626660167409373752358768245705,99991248315195084926044715277146657216841610744470997859352824212505477035478,12682814948690206837512925383803447850761289548355809737696291388647567420760,88082951178478487456311520934457716453077947327965428904949182253950125348508,787,1524785993,57367465394523363943212974063311169478867391537801206162217880152418860798414,0xd5d575e71245442009ee208e8dcebfbcf958b8b6,0xffffffff,0x1dd17af470f2caa13d29c02ac190a3a1eddc4e84) from: 0x0000000000000000000000000000000000010000 Time delay: 400981 seconds Block delay: 8447

[2024-09-19 17:25:58.25] [status] tests: 29/71, fuzzing: 9048/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:25:50.49] [Worker 4] Test borrower_switchBatchManager(uint256,uint256,uint256,address,uint256,uint256,uint256) falsified!
  Call sequence:
CryticTester.target_switchBranch() from: 0x0000000000000000000000000000000000030000 Time delay: 604535 seconds Block delay: 45819
CryticTester.property_SR02() from: 0x0000000000000000000000000000000000010000 Time delay: 127 seconds Block delay: 38350
CryticTester.check_withdrawBold() from: 0x0000000000000000000000000000000000020000 Time delay: 39499 seconds Block delay: 16089
CryticTester.check_adjustUnredeemableTrove() from: 0x0000000000000000000000000000000000030000 Time delay: 407328 seconds Block delay: 45261
CryticTester.borrower_withdrawColl(1524785993,20371423967322543348166130790527990415353884455755310220802371525126607430342) from: 0x0000000000000000000000000000000000030000 Time delay: 19029 seconds Block delay: 561
CryticTester.borrower_withdrawColl(1524785993,20371423967322543348166130790527990415353884455755310220802371525126607430342) from: 0x0000000000000000000000000000000000020000 Time delay: 344203 seconds Block delay: 11905
CryticTester.pool_withdrawFromSP(234280891644529721636,true) from: 0x0000000000000000000000000000000000020000 Time delay: 297507 seconds Block delay: 12053
CryticTester.check_aggBatchManagementFees_zero() from: 0x0000000000000000000000000000000000010000 Time delay: 38059 seconds Block delay: 60267
CryticTester.check_redeemCollateral() from: 0x0000000000000000000000000000000000010000 Time delay: 55534 seconds Block delay: 38350
CryticTester.pool_provideToSP(1673659462206822280136005485972587501452011600000264578513884453059,true) from: 0x0000000000000000000000000000000000030000 Time delay: 564960 seconds Block delay: 30256
CryticTester.borrower_adjustTroveInterestRate(115792089237316195423570985008687907853269984665640564039457584007913129639935,6,44036204391197776786324621912938136849676345653166550973734456013315555211709,115792089237316195423570985008687907853269984665640564039457584007913129639935,60295583979270105117777295141234798687764895433482669493498021962903931316130) from: 0x0000000000000000000000000000000000020000 Time delay: 303345 seconds Block delay: 4462
CryticTester.borrower_repayBold(1524785992,94804695032005548288581232014667159761923935998248951985344170054049234281557) from: 0x0000000000000000000000000000000000030000 Time delay: 38059 seconds Block delay: 30042
CryticTester.pool_withdrawFromSP(2078929346529529887739437983016979261226396399427394818019855008716801372218,true) from: 0x0000000000000000000000000000000000030000 Time delay: 45142 seconds Block delay: 9966
CryticTester.borrower_adjustTrove(1524785991,36613774563958022831000067707734997123352444763486346660668396298735428149679,true,0,true,2948412829) from: 0x0000000000000000000000000000000000030000 Time delay: 588255 seconds Block delay: 45852
CryticTester.borrower_closeTrove(4370000) from: 0x0000000000000000000000000000000000020000 Time delay: 66543 seconds Block delay: 32
CryticTester.borrower_adjustTrove(37671483587724,30601217278257410016143523601371951390002190189042480047358660164323486630905,true,1524785993,false,0) from: 0x0000000000000000000000000000000000020000 Time delay: 73040 seconds Block delay: 7122
CryticTester.check_batch_zero_shares() from: 0x0000000000000000000000000000000000010000 Time delay: 447588 seconds Block delay: 12493
CryticTester.check_setInterestBatchManager() from: 0x0000000000000000000000000000000000010000 Time delay: 344203 seconds Block delay: 5140
CryticTester.check_batchLiquidateTroves() from: 0x0000000000000000000000000000000000010000 Time delay: 65535 seconds Block delay: 53451
CryticTester.property_GL01(31554803209172076480661426473220060214584877504583840785680143887956082537269,18897065245005506108266552773000027057443781914770582903306292860866289957048) from: 0x0000000000000000000000000000000000020000 Time delay: 136393 seconds Block delay: 45819
CryticTester.check_setInterestIndividualDelegate() from: 0x0000000000000000000000000000000000020000 Time delay: 358612 seconds Block delay: 53562
CryticTester.property_CS05() from: 0x0000000000000000000000000000000000020000 Time delay: 412373 seconds Block delay: 3661
CryticTester.borrower_openTrove(4369999,15765288365102653277087323490861195256370118303578458839335254968770077862383,115792089237316195423570985008687907853269984665640564039457584007913129639932,819,29451546569338409879428755317950596679019473569059977490971459284298604345618,2866632765558401205705924536650590418198133749829489179175127684450360012752,4349515205195986913993488665381725497499115022808205567282689982099450765846,0x2fffffffd,0x10000,0xbffb01bb2ddb4efa87cb78eecb8115afae6d2032) from: 0x0000000000000000000000000000000000010000 Time delay: 116188 seconds Block delay: 45261
CryticTester.borrower_removeFromBatch(1524785992,0,64570147780839795145042836487775821326909125110714231576655947648719482944596,4369999,115792089237316195423570985008687907853269984665640564039457584007913129639935) from: 0x0000000000000000000000000000000000030000 Time delay: 173012 seconds Block delay: 23978
CryticTester.borrower_lowerBatchManagementFee(4370001) from: 0x0000000000000000000000000000000000020000 Time delay: 404739 seconds Block delay: 27585
CryticTester.check_repayBold() from: 0x0000000000000000000000000000000000010000 Time delay: 360624 seconds Block delay: 22995
CryticTester.borrower_adjustTrove(115792089237316195423570985008687907853269984665640564039457584007913129639935,32888464549256464762378240849253838304411798635043499614197176220825874121387,false,4369999,false,52021214786108809071697236384768719891167837770612563637216453046991223777669) from: 0x0000000000000000000000000000000000010000 Time delay: 318197 seconds Block delay: 8447
CryticTester.borrower_setBatchManagerAnnualInterestRate(263875216653683219038972646229662346223,57226327961326796071795551370183958204724485019911075532810176379757378943003,63545442283025874336752696934353731219505687675733390856447659522443626366027,4370001) from: 0x0000000000000000000000000000000000020000 Time delay: 412373 seconds Block delay: 48801
CryticTester.check_adjustTroveInterestRate() from: 0x0000000000000000000000000000000000010000 Time delay: 332369 seconds Block delay: 37522
CryticTester.check_adjustTrove() from: 0x0000000000000000000000000000000000020000 Time delay: 332369 seconds Block delay: 2512
CryticTester.check_provideToSP() from: 0x0000000000000000000000000000000000020000 Time delay: 379552 seconds Block delay: 39277
CryticTester.target_clamped_openTrove(4370001,3267440775571433487495992715433912597835118784232719571780376420438026268472,115792089237316195423570985008687907853269984665640564039457584007913129639934,115792089237316195423570985008687907853269984665640564039457584007913129639935,107707231000066593938848296348333519806375928805701331763789730297623115451317) from: 0x0000000000000000000000000000000000030000 Time delay: 405856 seconds Block delay: 4462
CryticTester.borrower_repayBold(68360059409186723274559902539822281430298767802887026800442350917576968918253,1524785991) from: 0x0000000000000000000000000000000000030000 Time delay: 32767 seconds Block delay: 4896
CryticTester.property_GL01(4370000,204) from: 0x0000000000000000000000000000000000020000 Time delay: 542800 seconds Block delay: 2526
CryticTester.target_clamped_openTrove(115792089237316195423570985008687907853269984665640564039457584007913129639935,78,14695270523411766503609359262747086702970250955302394653108216269908392463707,0,18214220166620607282881935341042412291747739468456413388400640613669321567666) from: 0x0000000000000000000000000000000000020000 Time delay: 289103 seconds Block delay: 45819
CryticTester.borrower_shutdown() from: 0x0000000000000000000000000000000000030000 Time delay: 448552 seconds Block delay: 38350
CryticTester.check_adjustTroveInterestRate() from: 0x0000000000000000000000000000000000010000 Time delay: 136394 seconds Block delay: 60054
CryticTester.borrower_lowerBatchManagementFee(94214356609897496060004170564844315021538192110978109925925065079414928846688) from: 0x0000000000000000000000000000000000030000 Time delay: 254414 seconds Block delay: 2512
CryticTester.property_BT01() from: 0x0000000000000000000000000000000000020000 Time delay: 112444 seconds Block delay: 5952
CryticTester.trove_batchLiquidateTroves(38600418570429235383235636502418458268312091064314163557365030394357069123507) from: 0x0000000000000000000000000000000000030000 Time delay: 554465 seconds Block delay: 60267
CryticTester.property_CS05() from: 0x0000000000000000000000000000000000030000 Time delay: 478623 seconds Block delay: 18429
CryticTester.borrower_openTroveAndJoinInterestBatchManager(1524785992,1524785991,4370001,7673636352793001185754796880279795073165545602575931724805096096144436663966,7906117544669251187966502892250394937349574484725327853728326341276845505575,0x3fa387ab1db837680029bf9896fae3e8ab44d31d,27789751040676587139265371841689741508238342764282269709941045201535591243106,0x1fffffffe,0xffffffff,0x63fb907fd752a801f71cad07f19c58e3d182dfc1) from: 0x0000000000000000000000000000000000020000 Time delay: 305572 seconds Block delay: 12053
CryticTester.borrower_adjustTroveInterestRate(102736940320062768440400785930895164265616433235965040674023425432536678715684,4370000,4370001,4370001,21321111292895891924779243859389181748579588561890222673331028109487084202314) from: 0x0000000000000000000000000000000000030000 Time delay: 5871 seconds Block delay: 2497
CryticTester.borrower_openTrove(113135278283442727243189110466216394328325812746267107947947413281875635836507,115792089237316195423570985008687907853269984665640564039457584007913129639935,62684679605347214085531345854013540324174604110251647416393659638037310409265,112804507348799170939689305168896204360546924477055974861050600095318804730533,39596245088773537137435470850951522874672491267822940167898479534953118775060,115792089237316195423570985008687907853269984665640564039457584007913129639932,19307664968305754514706443690742330901295412326527376912179179988747170865547,0xefc56627233b02ea95bae7e19f648d7dcd5bb132,0x1fffffffe,0xffffffff) from: 0x0000000000000000000000000000000000030000 Time delay: 322247 seconds Block delay: 33357
CryticTester.check_urgentRedemption() from: 0x0000000000000000000000000000000000020000
CryticTester.borrower_setInterestIndividualDelegate(35821391630369492863977451065040001321796714472378532395244546771486858742994,0x1fffffffe,4370001,195193111252274341382365307949373941561,0,1524785991,2605238128580880897403991168382144513149824355067808472857103669974131160395,831) from: 0x0000000000000000000000000000000000020000 Time delay: 50309 seconds Block delay: 2512
CryticTester.check_removeInterestIndividualDelegate() from: 0x0000000000000000000000000000000000030000 Time delay: 50417 seconds Block delay: 38100
CryticTester.check_batch_zero_debt() from: 0x0000000000000000000000000000000000030000 Time delay: 401699 seconds Block delay: 16089
CryticTester.check_addColl() from: 0x0000000000000000000000000000000000030000 Time delay: 82670 seconds Block delay: 5023
CryticTester.borrower_shutdown() from: 0x0000000000000000000000000000000000010000 Time delay: 490446 seconds Block delay: 32147
CryticTester.property_CS05() from: 0x0000000000000000000000000000000000010000 Time delay: 82671 seconds Block delay: 20243
CryticTester.check_repayBold() from: 0x0000000000000000000000000000000000030000 Time delay: 254414 seconds Block delay: 15369
CryticTester.target_switchBranch() from: 0x0000000000000000000000000000000000030000 Time delay: 569114 seconds Block delay: 19933
CryticTester.check_claimCollateral() from: 0x0000000000000000000000000000000000030000 Time delay: 16802 seconds Block delay: 50880
CryticTester.target_changePrice(9304213673874071895829) from: 0x0000000000000000000000000000000000010000 Time delay: 31594 seconds Block delay: 59983
CryticTester.borrower_openTroveAndJoinInterestBatchManager(72476792677662743137159727716361219064114011618143878321469603903445664726352,8971090820166726815194974547892585242688298398100371799659544510979163602,4370000,58472950080766825801072582196326380573086768530325640942403754028639637366381,1524785991,0xffffffff,1524785993,0xffffffff,0xce71065d4017f316ec606fe4422e11eb2c47c246,0xd01001c0261679fea6b5dd286e8f8cd227ba4f5b) from: 0x0000000000000000000000000000000000030000 Time delay: 14744 seconds Block delay: 800
CryticTester.property_CS05() from: 0x0000000000000000000000000000000000030000 Time delay: 79643 seconds Block delay: 18429
CryticTester.check_provideToSP() from: 0x0000000000000000000000000000000000030000 Time delay: 65250 seconds Block delay: 53069
CryticTester.check_hasRedistributedDebt() from: 0x0000000000000000000000000000000000020000 Time delay: 448552 seconds Block delay: 9966
CryticTester.property_GL01(4369999,1524785993) from: 0x0000000000000000000000000000000000010000 Time delay: 195123 seconds Block delay: 53678
CryticTester.check_lowerBatchManagementFee() from: 0x0000000000000000000000000000000000010000 Time delay: 207289 seconds Block delay: 5023
CryticTester.check_lowerBatchManagementFee() from: 0x0000000000000000000000000000000000010000 Time delay: 4541 seconds Block delay: 24987
CryticTester.check_withdrawColl() from: 0x0000000000000000000000000000000000030000 Time delay: 292304 seconds Block delay: 15369
CryticTester.property_AP01() from: 0x0000000000000000000000000000000000030000 Time delay: 49735 seconds Block delay: 45261
CryticTester.check_adjustTrove() from: 0x0000000000000000000000000000000000020000 Time delay: 490448 seconds Block delay: 54809
CryticTester.property_CS05() from: 0x0000000000000000000000000000000000010000 Time delay: 414736 seconds Block delay: 22909
CryticTester.borrower_applyPendingDebt(105267168053855894345507083933188747931593114173768994802826414950315812548640,546,7700308879144502315947427388820540363626542145622646390111817325669178454365) from: 0x0000000000000000000000000000000000020000 Time delay: 82672 seconds Block delay: 1984
CryticTester.check_claimCollateral() from: 0x0000000000000000000000000000000000020000 Time delay: 451926 seconds Block delay: 60364
CryticTester.check_aggBatchManagementFees_zero() from: 0x0000000000000000000000000000000000030000 Time delay: 521319 seconds Block delay: 5237
CryticTester.check_singleDebt() from: 0x0000000000000000000000000000000000010000 Time delay: 390247 seconds Block delay: 12493
CryticTester.check_provideToSP() from: 0x0000000000000000000000000000000000010000 Time delay: 111322 seconds Block delay: 42530
CryticTester.property_CS05() from: 0x0000000000000000000000000000000000010000 Time delay: 303345 seconds Block delay: 26645
CryticTester.target_new_clamped_openTrove(58067667070453973851154453755609385574996883427392481626357558689466148976885,46,1524785991,2115511031506683241384245349869770476235429005802205427152618048654556198718) from: 0x0000000000000000000000000000000000030000 Time delay: 379552 seconds Block delay: 58783
CryticTester.target_new_clamped_openTrove(21742893295406513953727140654136955550498021547698567457865303669392890226971,4369999,102315194664388176692710250422331321822112816506631485264069383110519071218437,19268033790077602888684900149351324857221725537937406755149443639962491472117) from: 0x0000000000000000000000000000000000010000 Time delay: 225906 seconds Block delay: 16089
CryticTester.property_GL01(0,1524785991) from: 0x0000000000000000000000000000000000020000 Time delay: 136392 seconds Block delay: 22699
CryticTester.check_setInterestIndividualDelegate() from: 0x0000000000000000000000000000000000020000 Time delay: 407328 seconds Block delay: 1362
CryticTester.borrower_withdrawBold(115792089237316195423570985008687907853269984665640564039457584007913129639934,65879676268752388550238811471266123543893583500005882468507412754198098280410,88147477110208935604919403050964979725401419862277745050026658640124092754008) from: 0x0000000000000000000000000000000000010000 Time delay: 318197 seconds Block delay: 2511
CryticTester.check_adjustUnredeemableTrove() from: 0x0000000000000000000000000000000000020000 Time delay: 3208 seconds Block delay: 3898
CryticTester.borrower_setInterestBatchManager(38518708017459749970089511119816079445446710561860404850784709614705893413729,224,604,1524785992) from: 0x0000000000000000000000000000000000020000 Time delay: 127 seconds Block delay: 32
CryticTester.check_shutdown() from: 0x0000000000000000000000000000000000010000 Time delay: 322374 seconds Block delay: 16617
CryticTester.check_withdrawFromSP() from: 0x0000000000000000000000000000000000030000 Time delay: 338920 seconds Block delay: 6721
CryticTester.target_clamped_openTrove(13650788885973381801577328194376964874477573824240962794717973268441563220965,63286368368718520898662540306717572677388716178968970877578612053200501515021,79525945717432498952660953873728924282131153312236167289295564229278081287187,11243017345616817865156746056645160464756496708563542968729295401858370191465,115792089237316195423570985008687907853269984665640564039457584007913129639934) from: 0x0000000000000000000000000000000000030000 Time delay: 156190 seconds Block delay: 38350
CryticTester.property_AP01() from: 0x0000000000000000000000000000000000030000 Time delay: 136393 seconds Block delay: 53349
CryticTester.borrower_openTrove(113496225954133688563487006427021297991423689993626660167409373752358768245705,99991248315195084926044715277146657216841610744470997859352824212505477035478,12682814948690206837512925383803447850761289548355809737696291388647567420760,88082951178478487456311520934457716453077947327965428904949182253950125348508,787,1524785993,57367465394523363943212974063311169478867391537801206162217880152418860798414,0xd5d575e71245442009ee208e8dcebfbcf958b8b6,0xffffffff,0x1dd17af470f2caa13d29c02ac190a3a1eddc4e84) from: 0x0000000000000000000000000000000000010000 Time delay: 400981 seconds Block delay: 8447
CryticTester.borrower_addCollateral(41789995147194818024844431936836040421957171263506634033041017574546480206715,49751109776127344971135657924557314159076764081085402247480638341711710832018) from: 0x0000000000000000000000000000000000020000
CryticTester.property_AP01() from: 0x0000000000000000000000000000000000020000 Time delay: 111322 seconds Block delay: 42595
CryticTester.borrower_switchBatchManager(28765607025103249319689586176245919880439508759866430308436844490588936422816,24829930659353345419879991390466803778568236933415002773715705697409941106547,1524785991,0xffffffff,1524785993,1524785991,4370001) from: 0x0000000000000000000000000000000000030000 Time delay: 271957 seconds Block delay: 12053

[2024-09-19 17:25:50.49] [Worker 5] New coverage: 28969 instr, 13 contracts, 12 seqs in corpus
[2024-09-19 17:25:50.49] [Worker 4] Test borrower_setInterestBatchManager(uint256,uint256,uint256,uint256) falsified!
  Call sequence:
CryticTester.target_switchBranch() from: 0x0000000000000000000000000000000000030000 Time delay: 604535 seconds Block delay: 45819
CryticTester.property_SR02() from: 0x0000000000000000000000000000000000010000 Time delay: 127 seconds Block delay: 38350
CryticTester.check_withdrawBold() from: 0x0000000000000000000000000000000000020000 Time delay: 39499 seconds Block delay: 16089
CryticTester.check_adjustUnredeemableTrove() from: 0x0000000000000000000000000000000000030000 Time delay: 407328 seconds Block delay: 45261
CryticTester.borrower_withdrawColl(1524785993,20371423967322543348166130790527990415353884455755310220802371525126607430342) from: 0x0000000000000000000000000000000000030000 Time delay: 19029 seconds Block delay: 561
CryticTester.borrower_withdrawColl(1524785993,20371423967322543348166130790527990415353884455755310220802371525126607430342) from: 0x0000000000000000000000000000000000020000 Time delay: 344203 seconds Block delay: 11905
CryticTester.pool_withdrawFromSP(234280891644529721636,true) from: 0x0000000000000000000000000000000000020000 Time delay: 297507 seconds Block delay: 12053
CryticTester.check_aggBatchManagementFees_zero() from: 0x0000000000000000000000000000000000010000 Time delay: 38059 seconds Block delay: 60267
CryticTester.check_redeemCollateral() from: 0x0000000000000000000000000000000000010000 Time delay: 55534 seconds Block delay: 38350
CryticTester.pool_provideToSP(1673659462206822280136005485972587501452011600000264578513884453059,true) from: 0x0000000000000000000000000000000000030000 Time delay: 564960 seconds Block delay: 30256
CryticTester.borrower_adjustTroveInterestRate(115792089237316195423570985008687907853269984665640564039457584007913129639935,6,44036204391197776786324621912938136849676345653166550973734456013315555211709,115792089237316195423570985008687907853269984665640564039457584007913129639935,60295583979270105117777295141234798687764895433482669493498021962903931316130) from: 0x0000000000000000000000000000000000020000 Time delay: 303345 seconds Block delay: 4462
CryticTester.borrower_repayBold(1524785992,94804695032005548288581232014667159761923935998248951985344170054049234281557) from: 0x0000000000000000000000000000000000030000 Time delay: 38059 seconds Block delay: 30042
CryticTester.pool_withdrawFromSP(2078929346529529887739437983016979261226396399427394818019855008716801372218,true) from: 0x0000000000000000000000000000000000030000 Time delay: 45142 seconds Block delay: 9966
CryticTester.borrower_adjustTrove(1524785991,36613774563958022831000067707734997123352444763486346660668396298735428149679,true,0,true,2948412829) from: 0x0000000000000000000000000000000000030000 Time delay: 588255 seconds Block delay: 45852
CryticTester.borrower_closeTrove(4370000) from: 0x0000000000000000000000000000000000020000 Time delay: 66543 seconds Block delay: 32
CryticTester.borrower_adjustTrove(37671483587724,30601217278257410016143523601371951390002190189042480047358660164323486630905,true,1524785993,false,0) from: 0x0000000000000000000000000000000000020000 Time delay: 73040 seconds Block delay: 7122
CryticTester.check_batch_zero_shares() from: 0x0000000000000000000000000000000000010000 Time delay: 447588 seconds Block delay: 12493
CryticTester.check_setInterestBatchManager() from: 0x0000000000000000000000000000000000010000 Time delay: 344203 seconds Block delay: 5140
CryticTester.check_batchLiquidateTroves() from: 0x0000000000000000000000000000000000010000 Time delay: 65535 seconds Block delay: 53451
CryticTester.property_GL01(31554803209172076480661426473220060214584877504583840785680143887956082537269,18897065245005506108266552773000027057443781914770582903306292860866289957048) from: 0x0000000000000000000000000000000000020000 Time delay: 136393 seconds Block delay: 45819
CryticTester.check_setInterestIndividualDelegate() from: 0x0000000000000000000000000000000000020000 Time delay: 358612 seconds Block delay: 53562
CryticTester.property_CS05() from: 0x0000000000000000000000000000000000020000 Time delay: 412373 seconds Block delay: 3661
CryticTester.borrower_openTrove(4369999,15765288365102653277087323490861195256370118303578458839335254968770077862383,115792089237316195423570985008687907853269984665640564039457584007913129639932,819,29451546569338409879428755317950596679019473569059977490971459284298604345618,2866632765558401205705924536650590418198133749829489179175127684450360012752,4349515205195986913993488665381725497499115022808205567282689982099450765846,0x2fffffffd,0x10000,0xbffb01bb2ddb4efa87cb78eecb8115afae6d2032) from: 0x0000000000000000000000000000000000010000 Time delay: 116188 seconds Block delay: 45261
CryticTester.borrower_removeFromBatch(1524785992,0,64570147780839795145042836487775821326909125110714231576655947648719482944596,4369999,115792089237316195423570985008687907853269984665640564039457584007913129639935) from: 0x0000000000000000000000000000000000030000 Time delay: 173012 seconds Block delay: 23978
CryticTester.borrower_lowerBatchManagementFee(4370001) from: 0x0000000000000000000000000000000000020000 Time delay: 404739 seconds Block delay: 27585
CryticTester.check_repayBold() from: 0x0000000000000000000000000000000000010000 Time delay: 360624 seconds Block delay: 22995
CryticTester.borrower_adjustTrove(115792089237316195423570985008687907853269984665640564039457584007913129639935,32888464549256464762378240849253838304411798635043499614197176220825874121387,false,4369999,false,52021214786108809071697236384768719891167837770612563637216453046991223777669) from: 0x0000000000000000000000000000000000010000 Time delay: 318197 seconds Block delay: 8447
CryticTester.borrower_setBatchManagerAnnualInterestRate(263875216653683219038972646229662346223,57226327961326796071795551370183958204724485019911075532810176379757378943003,63545442283025874336752696934353731219505687675733390856447659522443626366027,4370001) from: 0x0000000000000000000000000000000000020000 Time delay: 412373 seconds Block delay: 48801
CryticTester.check_adjustTroveInterestRate() from: 0x0000000000000000000000000000000000010000 Time delay: 332369 seconds Block delay: 37522
CryticTester.check_adjustTrove() from: 0x0000000000000000000000000000000000020000 Time delay: 332369 seconds Block delay: 2512
CryticTester.check_provideToSP() from: 0x0000000000000000000000000000000000020000 Time delay: 379552 seconds Block delay: 39277
CryticTester.target_clamped_openTrove(4370001,3267440775571433487495992715433912597835118784232719571780376420438026268472,115792089237316195423570985008687907853269984665640564039457584007913129639934,115792089237316195423570985008687907853269984665640564039457584007913129639935,107707231000066593938848296348333519806375928805701331763789730297623115451317) from: 0x0000000000000000000000000000000000030000 Time delay: 405856 seconds Block delay: 4462
CryticTester.borrower_repayBold(68360059409186723274559902539822281430298767802887026800442350917576968918253,1524785991) from: 0x0000000000000000000000000000000000030000 Time delay: 32767 seconds Block delay: 4896
CryticTester.property_GL01(4370000,204) from: 0x0000000000000000000000000000000000020000 Time delay: 542800 seconds Block delay: 2526
CryticTester.target_clamped_openTrove(115792089237316195423570985008687907853269984665640564039457584007913129639935,78,14695270523411766503609359262747086702970250955302394653108216269908392463707,0,18214220166620607282881935341042412291747739468456413388400640613669321567666) from: 0x0000000000000000000000000000000000020000 Time delay: 289103 seconds Block delay: 45819
CryticTester.borrower_shutdown() from: 0x0000000000000000000000000000000000030000 Time delay: 448552 seconds Block delay: 38350
CryticTester.check_adjustTroveInterestRate() from: 0x0000000000000000000000000000000000010000 Time delay: 136394 seconds Block delay: 60054
CryticTester.borrower_lowerBatchManagementFee(94214356609897496060004170564844315021538192110978109925925065079414928846688) from: 0x0000000000000000000000000000000000030000 Time delay: 254414 seconds Block delay: 2512
CryticTester.property_BT01() from: 0x0000000000000000000000000000000000020000 Time delay: 112444 seconds Block delay: 5952
CryticTester.trove_batchLiquidateTroves(38600418570429235383235636502418458268312091064314163557365030394357069123507) from: 0x0000000000000000000000000000000000030000 Time delay: 554465 seconds Block delay: 60267
CryticTester.property_CS05() from: 0x0000000000000000000000000000000000030000 Time delay: 478623 seconds Block delay: 18429
CryticTester.borrower_openTroveAndJoinInterestBatchManager(1524785992,1524785991,4370001,7673636352793001185754796880279795073165545602575931724805096096144436663966,7906117544669251187966502892250394937349574484725327853728326341276845505575,0x3fa387ab1db837680029bf9896fae3e8ab44d31d,27789751040676587139265371841689741508238342764282269709941045201535591243106,0x1fffffffe,0xffffffff,0x63fb907fd752a801f71cad07f19c58e3d182dfc1) from: 0x0000000000000000000000000000000000020000 Time delay: 305572 seconds Block delay: 12053
CryticTester.borrower_adjustTroveInterestRate(102736940320062768440400785930895164265616433235965040674023425432536678715684,4370000,4370001,4370001,21321111292895891924779243859389181748579588561890222673331028109487084202314) from: 0x0000000000000000000000000000000000030000 Time delay: 5871 seconds Block delay: 2497
CryticTester.borrower_openTrove(113135278283442727243189110466216394328325812746267107947947413281875635836507,115792089237316195423570985008687907853269984665640564039457584007913129639935,62684679605347214085531345854013540324174604110251647416393659638037310409265,112804507348799170939689305168896204360546924477055974861050600095318804730533,39596245088773537137435470850951522874672491267822940167898479534953118775060,115792089237316195423570985008687907853269984665640564039457584007913129639932,19307664968305754514706443690742330901295412326527376912179179988747170865547,0xefc56627233b02ea95bae7e19f648d7dcd5bb132,0x1fffffffe,0xffffffff) from: 0x0000000000000000000000000000000000030000 Time delay: 322247 seconds Block delay: 33357
CryticTester.check_urgentRedemption() from: 0x0000000000000000000000000000000000020000
CryticTester.borrower_setInterestIndividualDelegate(35821391630369492863977451065040001321796714472378532395244546771486858742994,0x1fffffffe,4370001,195193111252274341382365307949373941561,0,1524785991,2605238128580880897403991168382144513149824355067808472857103669974131160395,831) from: 0x0000000000000000000000000000000000020000 Time delay: 50309 seconds Block delay: 2512
CryticTester.check_removeInterestIndividualDelegate() from: 0x0000000000000000000000000000000000030000 Time delay: 50417 seconds Block delay: 38100
CryticTester.check_batch_zero_debt() from: 0x0000000000000000000000000000000000030000 Time delay: 401699 seconds Block delay: 16089
CryticTester.check_addColl() from: 0x0000000000000000000000000000000000030000 Time delay: 82670 seconds Block delay: 5023
CryticTester.borrower_shutdown() from: 0x0000000000000000000000000000000000010000 Time delay: 490446 seconds Block delay: 32147
CryticTester.property_CS05() from: 0x0000000000000000000000000000000000010000 Time delay: 82671 seconds Block delay: 20243
CryticTester.check_repayBold() from: 0x0000000000000000000000000000000000030000 Time delay: 254414 seconds Block delay: 15369
CryticTester.target_switchBranch() from: 0x0000000000000000000000000000000000030000 Time delay: 569114 seconds Block delay: 19933
CryticTester.check_claimCollateral() from: 0x0000000000000000000000000000000000030000 Time delay: 16802 seconds Block delay: 50880
CryticTester.target_changePrice(9304213673874071895829) from: 0x0000000000000000000000000000000000010000 Time delay: 31594 seconds Block delay: 59983
CryticTester.borrower_openTroveAndJoinInterestBatchManager(72476792677662743137159727716361219064114011618143878321469603903445664726352,8971090820166726815194974547892585242688298398100371799659544510979163602,4370000,58472950080766825801072582196326380573086768530325640942403754028639637366381,1524785991,0xffffffff,1524785993,0xffffffff,0xce71065d4017f316ec606fe4422e11eb2c47c246,0xd01001c0261679fea6b5dd286e8f8cd227ba4f5b) from: 0x0000000000000000000000000000000000030000 Time delay: 14744 seconds Block delay: 800
CryticTester.property_CS05() from: 0x0000000000000000000000000000000000030000 Time delay: 79643 seconds Block delay: 18429
CryticTester.check_provideToSP() from: 0x0000000000000000000000000000000000030000 Time delay: 65250 seconds Block delay: 53069
CryticTester.check_hasRedistributedDebt() from: 0x0000000000000000000000000000000000020000 Time delay: 448552 seconds Block delay: 9966
CryticTester.property_GL01(4369999,1524785993) from: 0x0000000000000000000000000000000000010000 Time delay: 195123 seconds Block delay: 53678
CryticTester.check_lowerBatchManagementFee() from: 0x0000000000000000000000000000000000010000 Time delay: 207289 seconds Block delay: 5023
CryticTester.check_lowerBatchManagementFee() from: 0x0000000000000000000000000000000000010000 Time delay: 4541 seconds Block delay: 24987
CryticTester.check_withdrawColl() from: 0x0000000000000000000000000000000000030000 Time delay: 292304 seconds Block delay: 15369
CryticTester.property_AP01() from: 0x0000000000000000000000000000000000030000 Time delay: 49735 seconds Block delay: 45261
CryticTester.check_adjustTrove() from: 0x0000000000000000000000000000000000020000 Time delay: 490448 seconds Block delay: 54809
CryticTester.property_CS05() from: 0x0000000000000000000000000000000000010000 Time delay: 414736 seconds Block delay: 22909
CryticTester.borrower_applyPendingDebt(105267168053855894345507083933188747931593114173768994802826414950315812548640,546,7700308879144502315947427388820540363626542145622646390111817325669178454365) from: 0x0000000000000000000000000000000000020000 Time delay: 82672 seconds Block delay: 1984
CryticTester.check_claimCollateral() from: 0x0000000000000000000000000000000000020000 Time delay: 451926 seconds Block delay: 60364
CryticTester.check_aggBatchManagementFees_zero() from: 0x0000000000000000000000000000000000030000 Time delay: 521319 seconds Block delay: 5237
CryticTester.check_singleDebt() from: 0x0000000000000000000000000000000000010000 Time delay: 390247 seconds Block delay: 12493
CryticTester.check_provideToSP() from: 0x0000000000000000000000000000000000010000 Time delay: 111322 seconds Block delay: 42530
CryticTester.property_CS05() from: 0x0000000000000000000000000000000000010000 Time delay: 303345 seconds Block delay: 26645
CryticTester.target_new_clamped_openTrove(58067667070453973851154453755609385574996883427392481626357558689466148976885,46,1524785991,2115511031506683241384245349869770476235429005802205427152618048654556198718) from: 0x0000000000000000000000000000000000030000 Time delay: 379552 seconds Block delay: 58783
CryticTester.target_new_clamped_openTrove(21742893295406513953727140654136955550498021547698567457865303669392890226971,4369999,102315194664388176692710250422331321822112816506631485264069383110519071218437,19268033790077602888684900149351324857221725537937406755149443639962491472117) from: 0x0000000000000000000000000000000000010000 Time delay: 225906 seconds Block delay: 16089
CryticTester.property_GL01(0,1524785991) from: 0x0000000000000000000000000000000000020000 Time delay: 136392 seconds Block delay: 22699
CryticTester.check_setInterestIndividualDelegate() from: 0x0000000000000000000000000000000000020000 Time delay: 407328 seconds Block delay: 1362
CryticTester.borrower_withdrawBold(115792089237316195423570985008687907853269984665640564039457584007913129639934,65879676268752388550238811471266123543893583500005882468507412754198098280410,88147477110208935604919403050964979725401419862277745050026658640124092754008) from: 0x0000000000000000000000000000000000010000 Time delay: 318197 seconds Block delay: 2511
CryticTester.check_adjustUnredeemableTrove() from: 0x0000000000000000000000000000000000020000 Time delay: 3208 seconds Block delay: 3898
CryticTester.borrower_setInterestBatchManager(38518708017459749970089511119816079445446710561860404850784709614705893413729,224,604,1524785992) from: 0x0000000000000000000000000000000000020000 Time delay: 127 seconds Block delay: 32
CryticTester.check_shutdown() from: 0x0000000000000000000000000000000000010000 Time delay: 322374 seconds Block delay: 16617
CryticTester.check_withdrawFromSP() from: 0x0000000000000000000000000000000000030000 Time delay: 338920 seconds Block delay: 6721
CryticTester.target_clamped_openTrove(13650788885973381801577328194376964874477573824240962794717973268441563220965,63286368368718520898662540306717572677388716178968970877578612053200501515021,79525945717432498952660953873728924282131153312236167289295564229278081287187,11243017345616817865156746056645160464756496708563542968729295401858370191465,115792089237316195423570985008687907853269984665640564039457584007913129639934) from: 0x0000000000000000000000000000000000030000 Time delay: 156190 seconds Block delay: 38350
CryticTester.property_AP01() from: 0x0000000000000000000000000000000000030000 Time delay: 136393 seconds Block delay: 53349
CryticTester.borrower_openTrove(113496225954133688563487006427021297991423689993626660167409373752358768245705,99991248315195084926044715277146657216841610744470997859352824212505477035478,12682814948690206837512925383803447850761289548355809737696291388647567420760,88082951178478487456311520934457716453077947327965428904949182253950125348508,787,1524785993,57367465394523363943212974063311169478867391537801206162217880152418860798414,0xd5d575e71245442009ee208e8dcebfbcf958b8b6,0xffffffff,0x1dd17af470f2caa13d29c02ac190a3a1eddc4e84) from: 0x0000000000000000000000000000000000010000 Time delay: 400981 seconds Block delay: 8447
CryticTester.borrower_addCollateral(41789995147194818024844431936836040421957171263506634033041017574546480206715,49751109776127344971135657924557314159076764081085402247480638341711710832018) from: 0x0000000000000000000000000000000000020000
CryticTester.property_AP01() from: 0x0000000000000000000000000000000000020000 Time delay: 111322 seconds Block delay: 42595
CryticTester.borrower_switchBatchManager(28765607025103249319689586176245919880439508759866430308436844490588936422816,24829930659353345419879991390466803778568236933415002773715705697409941106547,1524785991,0xffffffff,1524785993,1524785991,4370001) from: 0x0000000000000000000000000000000000030000 Time delay: 271957 seconds Block delay: 12053
CryticTester.borrower_setInterestBatchManager(95060676442977373795386898761678908138,51392215865353883345203044389368999499961098542814243465040295557610694650545,54623093119038667977569389375213604335275988974875323043147017930160104028306,11402581618520061717871808119510603970338) from: 0x0000000000000000000000000000000000010000 Time delay: 588255 seconds Block delay: 53011

[2024-09-19 17:25:50.51]  Saved reproducer to echidna/coverage/4317924282912716228.txt
[2024-09-19 17:25:50.51] [Worker 5] Sequence replayed from corpus file 1003753213421706225.txt (1/8)
[2024-09-19 17:25:50.52] [Worker 2] Test borrower_claimCollateral() falsified!
  Call sequence:
CryticTester.target_switchBranch()
CryticTester.target_switchBranch()
CryticTester.target_changePrice(2079377620832671811807)
CryticTester.target_clamped_openTrove(999869329253541198144785370013092342,51440707414928237908219435958567579366353,293576623098344595551911643110885941133878123135765326189559252733142586,16182096995199142667107729927243903464507760185472,6152504643294261020263984236501)
CryticTester.borrower_claimCollateral() Time delay: 1 seconds Block delay: 1

[2024-09-19 17:25:50.53] [Worker 2] Sequence replayed from corpus file 6839905541430491134.txt (3/8)
[2024-09-19 17:25:50.53] [Worker 4] New coverage: 28969 instr, 13 contracts, 13 seqs in corpus
[2024-09-19 17:25:50.54] [Worker 3] Test trove_batchLiquidateTroves(uint256) falsified!
  Call sequence:
CryticTester.target_switchBranch()
CryticTester.target_switchBranch()
CryticTester.target_changePrice(2072958901696706022666)
CryticTester.target_clamped_openTrove(357895663906058847915307812426575902808046880285,1266821025199480292067703754786682377567401780714033122790,6465670352594822135171401017479726506670607322973690526861493063529816601455,700407772437259747504356212142839442405772102442463517,1506594088107034986991874755636531592734824085)
*wait* Time delay: 1 seconds Block delay: 1
CryticTester.trove_batchLiquidateTroves(0)

[2024-09-19 17:25:50.54] [Worker 3] Sequence replayed from corpus file 6267121365106731913.txt (4/8)
[2024-09-19 17:25:50.54] [Worker 1] New coverage: 29024 instr, 13 contracts, 14 seqs in corpus
[2024-09-19 17:25:50.55] [Worker 1] Sequence replayed from corpus file 1002637077104915949.txt (4/8)
[2024-09-19 17:25:50.55] [Worker 4] Sequence replayed from corpus file 6598517407538113013.txt (2/8)
[2024-09-19 17:25:50.56] [Worker 0] Sequence replayed from corpus file 3648023693797877231.txt (4/8)
[2024-09-19 17:25:50.61] [Worker 1] Test pool_provideToSP(uint256,bool) falsified!
  Call sequence:
CryticTester.target_changePrice(2193954337075088751103)
CryticTester.target_clamped_openTrove(6124512642866604047066647302633363410904,24049421083097194215544002391209288,1628657399599707265519707797380254236969650812850376011494690649,19659083809794181513260199822392309699857,9681584120078550663825791595938358205846091462)
CryticTester.pool_provideToSP(0,false) Time delay: 1 seconds Block delay: 1

[2024-09-19 17:25:50.61] [Worker 1] Sequence replayed from corpus file 1266481692813137188.txt (5/8)
[2024-09-19 17:26:01.26] [status] tests: 29/71, fuzzing: 11680/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:25:50.62] [Worker 2] Test borrower_applyPendingDebt(uint256,uint256,uint256) falsified!
  Call sequence:
CryticTester.target_switchBranch()
CryticTester.target_switchBranch()
CryticTester.target_changePrice(2014381690261125649259)
CryticTester.target_clamped_openTrove(1130549511948970275792887584475376422563462419413456672735,11648483103839789293220458178942415204059998683444137144,740490673096811509890568711233860898738369367777672219210050516890162258571,293206601818555577274120035387899321285527251744,7363273225027650222226512560830342375118056140804637)
*wait* Time delay: 1 seconds Block delay: 1
CryticTester.borrower_applyPendingDebt(0,0,0)

[2024-09-19 17:25:50.62] [Worker 2] Sequence replayed from corpus file 7962377868573669767.txt (4/8)
[2024-09-19 17:25:50.62]  Saved reproducer to echidna/coverage/6718773283085384835.txt
[2024-09-19 17:25:50.62]  Saved reproducer to echidna/coverage/8216307532899259345.txt
[2024-09-19 17:25:50.63] [Worker 7] New coverage: 29126 instr, 13 contracts, 15 seqs in corpus
[2024-09-19 17:25:50.63] [Worker 3] Test borrower_withdrawColl(uint256,uint256) falsified!
  Call sequence:
CryticTester.target_switchBranch()
CryticTester.target_switchBranch()
CryticTester.target_changePrice(2079652246808121795799)
CryticTester.target_clamped_openTrove(27955419631557953418307075720906967207035,204026445808971237374890367851632064896335,39335989414416013912587872538793554265055357900639754272258,52030669238681586580918153637446103171063,14180159381949345252044683880031662103624)
*wait* Time delay: 1 seconds Block delay: 1
CryticTester.borrower_withdrawColl(0,0)

[2024-09-19 17:25:50.63] [Worker 3] Sequence replayed from corpus file 5042352675885177373.txt (5/8)
[2024-09-19 17:25:50.63] [Worker 6] New coverage: 29126 instr, 13 contracts, 16 seqs in corpus
[2024-09-19 17:25:50.65] [Worker 7] Sequence replayed from corpus file 8995040719195695559.txt (2/8)
[2024-09-19 17:25:50.65] [Worker 6] Sequence replayed from corpus file 2914067880601179093.txt (2/8)
[2024-09-19 17:25:50.65] [Worker 0] Test borrower_withdrawBold(uint256,uint256,uint256) falsified!
  Call sequence:
CryticTester.target_switchBranch()
CryticTester.target_switchBranch()
CryticTester.target_changePrice(2254535115149393049646)
CryticTester.target_clamped_openTrove(935334977377336160797871893068,56774080802255272906262878039456017839,3496229118387980451617435057271577723579562842936387635096407280727190,846116528176692181468088979961767916,3595245846805297338496229434171938454841)
*wait* Time delay: 1 seconds Block delay: 1
CryticTester.borrower_withdrawBold(0,0,0)

[2024-09-19 17:25:50.65] [Worker 0] Sequence replayed from corpus file 1698746476953659691.txt (5/8)
[2024-09-19 17:25:50.66] [Worker 5] Test borrower_setBatchManagerAnnualInterestRate(uint128,uint256,uint256,uint256) falsified!
  Call sequence:
CryticTester.check_withdrawBold() from: 0x0000000000000000000000000000000000020000 Time delay: 420078 seconds Block delay: 42229
CryticTester.target_changePrice(7326250084395686627766881604045061000817729031357313044139018660608197051160) from: 0x0000000000000000000000000000000000030000 Time delay: 444463 seconds Block delay: 32147
CryticTester.borrower_removeInterestIndividualDelegate(1885331737) from: 0x0000000000000000000000000000000000010000 Time delay: 150273 seconds Block delay: 59981
CryticTester.check_withdrawFromSP() from: 0x0000000000000000000000000000000000030000 Time delay: 442705 seconds Block delay: 32
CryticTester.check_batch_zero_shares() from: 0x0000000000000000000000000000000000030000 Time delay: 199729 seconds Block delay: 6721
CryticTester.check_openedTrove() from: 0x0000000000000000000000000000000000020000 Time delay: 254414 seconds Block delay: 24311
CryticTester.property_SR02() from: 0x0000000000000000000000000000000000030000 Time delay: 379552 seconds Block delay: 14536
CryticTester.borrower_adjustTroveInterestRate(4369999,0,329049042651851431111065107979940808952782892866122799258281626943982713,90184875984040431282879798455920471245709377114840576742068364204031735006223,858) from: 0x0000000000000000000000000000000000010000 Time delay: 225906 seconds Block delay: 30042
CryticTester.borrower_switchBatchManager(72264784271332405438109244884821050726283507505466461953351325900247556625045,115792089237316195423570985008687907853269984665640564039457584007913129639935,115792089237316195423570985008687907853269984665640564039457584007913129639935,0x3fa387ab1db837680029bf9896fae3e8ab44d31d,453,1524785993,112227527828651173280892835999214663920648181445005488510293674557213336999237) from: 0x0000000000000000000000000000000000020000 Time delay: 360624 seconds Block delay: 561
CryticTester.check_addColl() from: 0x0000000000000000000000000000000000010000 Time delay: 66543 seconds Block delay: 23722
CryticTester.check_claimCollateral() from: 0x0000000000000000000000000000000000010000 Time delay: 16802 seconds Block delay: 33357
CryticTester.check_adjustUnredeemableTrove() from: 0x0000000000000000000000000000000000010000 Time delay: 379552 seconds Block delay: 30011
CryticTester.check_lowerBatchManagementFee() from: 0x0000000000000000000000000000000000010000 Time delay: 82670 seconds Block delay: 18429
CryticTester.borrower_withdrawColl(4370000,71563998240162546149536585981011486526307539460540115829654224329472623840220) from: 0x0000000000000000000000000000000000020000 Time delay: 136393 seconds Block delay: 31232
CryticTester.check_singleDebt() from: 0x0000000000000000000000000000000000020000 Time delay: 338920 seconds Block delay: 54809
CryticTester.check_hasRedistributedDebt() from: 0x0000000000000000000000000000000000010000 Time delay: 150273 seconds Block delay: 10254
CryticTester.check_aggBatchManagementFees_zero() from: 0x0000000000000000000000000000000000010000 Time delay: 198598 seconds Block delay: 53011
CryticTester.target_changePrice(115792089237316195423570985008687907853269984665640564039457584007913129639935) from: 0x0000000000000000000000000000000000030000
CryticTester.check_setInterestIndividualDelegate() from: 0x0000000000000000000000000000000000020000 Time delay: 31594 seconds Block delay: 12338
CryticTester.pool_provideToSP(95620426761189026261062871620439975265875778093547962812610419439055678057472,false) from: 0x0000000000000000000000000000000000030000 Time delay: 115085 seconds Block delay: 22909
CryticTester.check_adjustTrove() from: 0x0000000000000000000000000000000000030000 Time delay: 436727 seconds Block delay: 42229
CryticTester.borrower_setBatchManagerAnnualInterestRate(110498281317749178274882588178823167269,50647923768696330323487754731073618966455162089024,60351268644200483684097570667341264754020033513599925349085495432354733611853,43792123286884668899859628368547233926581966292231822280630832482420039023082) from: 0x0000000000000000000000000000000000020000 Time delay: 434894 seconds Block delay: 60054
CryticTester.borrower_openTroveAndJoinInterestBatchManager(597,115792089237316195423570985008687907853269984665640564039457584007913129639932,4370000,4370000,296,0x33ae292c80d9ef512a73962ccacd9929db29e820,18454183536593481772689511351241551461317708818741039367334162298601907961957,0x4c443830e34610922c09f9ede74555eac9cc9760,0x2fffffffd,0xd01001c0261679fea6b5dd286e8f8cd227ba4f5b) from: 0x0000000000000000000000000000000000030000 Time delay: 136392 seconds Block delay: 31232
CryticTester.property_BT01() from: 0x0000000000000000000000000000000000020000 Time delay: 115085 seconds Block delay: 31592
CryticTester.check_urgentRedemption() from: 0x0000000000000000000000000000000000010000 Time delay: 82672 seconds Block delay: 12053
CryticTester.check_lowerBatchManagementFee() from: 0x0000000000000000000000000000000000030000 Time delay: 100835 seconds Block delay: 42595
CryticTester.check_aggBatchManagementFees_zero() from: 0x0000000000000000000000000000000000010000 Time delay: 57853 seconds Block delay: 45261
CryticTester.borrower_closeTrove(0) from: 0x0000000000000000000000000000000000020000 Time delay: 569114 seconds Block delay: 561
CryticTester.target_changePrice(2239623662133908660723) from: 0x0000000000000000000000000000000000020000 Time delay: 135921 seconds Block delay: 11942
CryticTester.target_new_clamped_openTrove(57712017290850107671598879501769315621784929821994649182222198450013461428079,4370001,41594862609769565373852729249266715484733417621378322758279953835982247360876,115792089237316195423570985008687907853269984665640564039457584007913129639931) from: 0x0000000000000000000000000000000000030000 Time delay: 286875 seconds Block delay: 32737
CryticTester.check_withdrawFromSP() from: 0x0000000000000000000000000000000000030000 Time delay: 31594 seconds Block delay: 1243
CryticTester.borrower_shutdown() from: 0x0000000000000000000000000000000000020000 Time delay: 490446 seconds Block delay: 24987
CryticTester.borrower_openTroveAndJoinInterestBatchManager(17879153616263872676853220739983471220061637615311421720902068361680437964086,1524785993,37961023032293391450438892908920255312981535376949419470422881137649276595786,260,115792089237316195423570985008687907853269984665640564039457584007913129639933,0x2fffffffd,41388224538590050780676908839687030806185246902164741277538361415143892253285,0x1fffffffe,0xffffffff,0x1fffffffe) from: 0x0000000000000000000000000000000000010000 Time delay: 292304 seconds Block delay: 12155
CryticTester.property_SR02() from: 0x0000000000000000000000000000000000010000 Time delay: 358061 seconds Block delay: 60364
CryticTester.check_openedTrove() from: 0x0000000000000000000000000000000000030000 Time delay: 16802 seconds Block delay: 59982
CryticTester.borrower_closeTrove(4370001) from: 0x0000000000000000000000000000000000030000 Time delay: 289103 seconds Block delay: 4223
CryticTester.check_provideToSP() from: 0x0000000000000000000000000000000000010000 Time delay: 455586 seconds Block delay: 49415
CryticTester.check_batch_zero_debt() from: 0x0000000000000000000000000000000000010000 Time delay: 525476 seconds Block delay: 59983
CryticTester.borrower_switchBatchManager(28237000140005353088779237418928716310895761116068504770247853351578520098434,103470273852820867109608058051862537782469894324580679964772941758988432911697,0,0x1fffffffe,27984861990457949764255593355951142130295599968772352581654118913015637154546,4370001,115792089237316195423570985008687907853269984665640564039457584007913129639935) from: 0x0000000000000000000000000000000000020000 Time delay: 401699 seconds Block delay: 24987
CryticTester.borrower_registerBatchManager(271556695527833100934427500668215143742,202375965460491464633525429730725423275,340282366920938463463374607431768211455,340282366920938463463374607431768211455,304573699295209312725458340639169357029) from: 0x0000000000000000000000000000000000020000 Time delay: 136393 seconds Block delay: 23885
CryticTester.check_batch_zero_debt() from: 0x0000000000000000000000000000000000010000 Time delay: 463587 seconds Block delay: 4223
CryticTester.check_aggBatchManagementFees_zero() from: 0x0000000000000000000000000000000000010000 Time delay: 547623 seconds Block delay: 36859
CryticTester.property_CS05() from: 0x0000000000000000000000000000000000030000 Time delay: 255 seconds Block delay: 34720
CryticTester.check_singleDebt() from: 0x0000000000000000000000000000000000020000 Time delay: 100835 seconds Block delay: 59552
CryticTester.check_batchLiquidateTroves() from: 0x0000000000000000000000000000000000010000 Time delay: 111322 seconds Block delay: 2497
CryticTester.check_withdrawFromSP() from: 0x0000000000000000000000000000000000020000 Time delay: 419861 seconds Block delay: 8447
CryticTester.property_BT01() from: 0x0000000000000000000000000000000000020000 Time delay: 525476 seconds Block delay: 32767
CryticTester.trove_urgentRedemption(17989711048916770229340086543623010934114356552340257037016052312539611209538,7821983367087922618651701059790029181168745403279355912731340244983382453341,112164165317069838272134901407288157659922701373949990963146525843231639334631) from: 0x0000000000000000000000000000000000020000 Time delay: 65535 seconds Block delay: 9966
CryticTester.check_openedTrove() from: 0x0000000000000000000000000000000000030000 Time delay: 407328 seconds Block delay: 42101
CryticTester.check_removeFromBatch() from: 0x0000000000000000000000000000000000030000 Time delay: 490448 seconds Block delay: 4909
CryticTester.borrower_repayBold(115792089237316195423570985008687907853269984665640564039457584007913129639933,4369999) from: 0x0000000000000000000000000000000000030000 Time delay: 305572 seconds Block delay: 53166
CryticTester.borrower_adjustTrove(79918360365314133192365354436560389700174598815462641438311457990869603735135,62318521188960590701738487703280569497594723509551390766918274993278750318711,true,4369999,false,1524785993) from: 0x0000000000000000000000000000000000010000 Time delay: 65535 seconds Block delay: 32737
CryticTester.borrower_removeFromBatch(79198622263650919617297780421768808329012275208372001047914165278412909357513,477,91356906051138820744666145574551413321809650865144241036603466854436272033817,115792089237316195423570985008687907853269984665640564039457584007913129639934,115792089237316195423570985008687907853269984665640564039457584007913129639935) from: 0x0000000000000000000000000000000000010000 Time delay: 521319 seconds Block delay: 5952
CryticTester.check_adjustTroveInterestRate() from: 0x0000000000000000000000000000000000010000 Time delay: 526194 seconds Block delay: 11942
CryticTester.check_adjustUnredeemableTrove() from: 0x0000000000000000000000000000000000030000 Time delay: 289607 seconds Block delay: 46422
CryticTester.check_singleDebt() from: 0x0000000000000000000000000000000000030000 Time delay: 447588 seconds Block delay: 54809
CryticTester.borrower_setInterestBatchManager(115792089237316195423570985008687907853269984665640564039457584007913129639932,1524785992,115792089237316195423570985008687907853269984665640564039457584007913129639931,11775653694320040059194169244520396438398233875512981097975669969688028190904) from: 0x0000000000000000000000000000000000030000 Time delay: 4177 seconds Block delay: 12155
CryticTester.borrower_repayBold(115792089237316195423570985008687907853269984665640564039457584007913129639935,6145454653307317656215353535938039777769330225021551974545043709727479485713) from: 0x0000000000000000000000000000000000020000 Time delay: 400033 seconds Block delay: 38350
CryticTester.target_clamped_openTrove(43973690139353477108,4664284375626519063860475876541176180,1078965428809195510147324087454664930421276482212786465236859,2437183478656557408184667832532445751348608691713,28575275650637667017397419268721031) from: 0x0000000000000000000000000000000000020000
CryticTester.borrower_withdrawColl(1524785993,115792089237316195423570985008687907853269984665640564039457584007913129639935) from: 0x0000000000000000000000000000000000030000
CryticTester.borrower_setBatchManagerAnnualInterestRate(340282366920938463463374607431768211453,28471343730612069633477296280435784154415938483104948610340573726262572609511,31865454967533471595628154348784053107711625288598248822030061268468850750041,4369999) from: 0x0000000000000000000000000000000000030000 Time delay: 478623 seconds Block delay: 32147

[2024-09-19 17:25:50.67] [Worker 9] Sequence replayed from corpus file 4326114689947117486.txt (2/5)
[2024-09-19 17:25:50.67] [Worker 1] Sequence replayed from corpus file 1985260863246463537.txt (6/8)
[2024-09-19 17:25:50.70] [Worker 2] Test borrower_closeTrove(uint256) falsified!
  Call sequence:
CryticTester.target_changePrice(2112018926867102560360)
CryticTester.target_clamped_openTrove(986517447518966760491012880145934644558610734,9985225239404475651477824251313710970038268559480,992130925542421962143249189109336953668935662406523973501298156908,4116718283855174651884992773121085735676306040,3141903942175759821267729722365848264320058)
*wait* Time delay: 1 seconds Block delay: 1
CryticTester.borrower_closeTrove(0)

[2024-09-19 17:25:50.70] [Worker 2] Sequence replayed from corpus file 6739888555756173204.txt (5/8)
[2024-09-19 17:25:50.71] [Worker 8] Sequence replayed from corpus file 629726080639174046.txt (2/8)
[2024-09-19 17:26:04.26] [status] tests: 29/71, fuzzing: 13316/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:25:50.71] [Worker 5] Test borrower_lowerBatchManagementFee(uint256) falsified!
  Call sequence:
CryticTester.check_withdrawBold() from: 0x0000000000000000000000000000000000020000 Time delay: 420078 seconds Block delay: 42229
CryticTester.target_changePrice(7326250084395686627766881604045061000817729031357313044139018660608197051160) from: 0x0000000000000000000000000000000000030000 Time delay: 444463 seconds Block delay: 32147
CryticTester.borrower_removeInterestIndividualDelegate(1885331737) from: 0x0000000000000000000000000000000000010000 Time delay: 150273 seconds Block delay: 59981
CryticTester.check_withdrawFromSP() from: 0x0000000000000000000000000000000000030000 Time delay: 442705 seconds Block delay: 32
CryticTester.check_batch_zero_shares() from: 0x0000000000000000000000000000000000030000 Time delay: 199729 seconds Block delay: 6721
CryticTester.check_openedTrove() from: 0x0000000000000000000000000000000000020000 Time delay: 254414 seconds Block delay: 24311
CryticTester.property_SR02() from: 0x0000000000000000000000000000000000030000 Time delay: 379552 seconds Block delay: 14536
CryticTester.borrower_adjustTroveInterestRate(4369999,0,329049042651851431111065107979940808952782892866122799258281626943982713,90184875984040431282879798455920471245709377114840576742068364204031735006223,858) from: 0x0000000000000000000000000000000000010000 Time delay: 225906 seconds Block delay: 30042
CryticTester.borrower_switchBatchManager(72264784271332405438109244884821050726283507505466461953351325900247556625045,115792089237316195423570985008687907853269984665640564039457584007913129639935,115792089237316195423570985008687907853269984665640564039457584007913129639935,0x3fa387ab1db837680029bf9896fae3e8ab44d31d,453,1524785993,112227527828651173280892835999214663920648181445005488510293674557213336999237) from: 0x0000000000000000000000000000000000020000 Time delay: 360624 seconds Block delay: 561
CryticTester.check_addColl() from: 0x0000000000000000000000000000000000010000 Time delay: 66543 seconds Block delay: 23722
CryticTester.check_claimCollateral() from: 0x0000000000000000000000000000000000010000 Time delay: 16802 seconds Block delay: 33357
CryticTester.check_adjustUnredeemableTrove() from: 0x0000000000000000000000000000000000010000 Time delay: 379552 seconds Block delay: 30011
CryticTester.check_lowerBatchManagementFee() from: 0x0000000000000000000000000000000000010000 Time delay: 82670 seconds Block delay: 18429
CryticTester.borrower_withdrawColl(4370000,71563998240162546149536585981011486526307539460540115829654224329472623840220) from: 0x0000000000000000000000000000000000020000 Time delay: 136393 seconds Block delay: 31232
CryticTester.check_singleDebt() from: 0x0000000000000000000000000000000000020000 Time delay: 338920 seconds Block delay: 54809
CryticTester.check_hasRedistributedDebt() from: 0x0000000000000000000000000000000000010000 Time delay: 150273 seconds Block delay: 10254
CryticTester.check_aggBatchManagementFees_zero() from: 0x0000000000000000000000000000000000010000 Time delay: 198598 seconds Block delay: 53011
CryticTester.target_changePrice(115792089237316195423570985008687907853269984665640564039457584007913129639935) from: 0x0000000000000000000000000000000000030000
CryticTester.check_setInterestIndividualDelegate() from: 0x0000000000000000000000000000000000020000 Time delay: 31594 seconds Block delay: 12338
CryticTester.pool_provideToSP(95620426761189026261062871620439975265875778093547962812610419439055678057472,false) from: 0x0000000000000000000000000000000000030000 Time delay: 115085 seconds Block delay: 22909
CryticTester.check_adjustTrove() from: 0x0000000000000000000000000000000000030000 Time delay: 436727 seconds Block delay: 42229
CryticTester.borrower_setBatchManagerAnnualInterestRate(110498281317749178274882588178823167269,50647923768696330323487754731073618966455162089024,60351268644200483684097570667341264754020033513599925349085495432354733611853,43792123286884668899859628368547233926581966292231822280630832482420039023082) from: 0x0000000000000000000000000000000000020000 Time delay: 434894 seconds Block delay: 60054
CryticTester.borrower_openTroveAndJoinInterestBatchManager(597,115792089237316195423570985008687907853269984665640564039457584007913129639932,4370000,4370000,296,0x33ae292c80d9ef512a73962ccacd9929db29e820,18454183536593481772689511351241551461317708818741039367334162298601907961957,0x4c443830e34610922c09f9ede74555eac9cc9760,0x2fffffffd,0xd01001c0261679fea6b5dd286e8f8cd227ba4f5b) from: 0x0000000000000000000000000000000000030000 Time delay: 136392 seconds Block delay: 31232
CryticTester.property_BT01() from: 0x0000000000000000000000000000000000020000 Time delay: 115085 seconds Block delay: 31592
CryticTester.check_urgentRedemption() from: 0x0000000000000000000000000000000000010000 Time delay: 82672 seconds Block delay: 12053
CryticTester.check_lowerBatchManagementFee() from: 0x0000000000000000000000000000000000030000 Time delay: 100835 seconds Block delay: 42595
CryticTester.check_aggBatchManagementFees_zero() from: 0x0000000000000000000000000000000000010000 Time delay: 57853 seconds Block delay: 45261
CryticTester.borrower_closeTrove(0) from: 0x0000000000000000000000000000000000020000 Time delay: 569114 seconds Block delay: 561
CryticTester.target_changePrice(2239623662133908660723) from: 0x0000000000000000000000000000000000020000 Time delay: 135921 seconds Block delay: 11942
CryticTester.target_new_clamped_openTrove(57712017290850107671598879501769315621784929821994649182222198450013461428079,4370001,41594862609769565373852729249266715484733417621378322758279953835982247360876,115792089237316195423570985008687907853269984665640564039457584007913129639931) from: 0x0000000000000000000000000000000000030000 Time delay: 286875 seconds Block delay: 32737
CryticTester.check_withdrawFromSP() from: 0x0000000000000000000000000000000000030000 Time delay: 31594 seconds Block delay: 1243
CryticTester.borrower_shutdown() from: 0x0000000000000000000000000000000000020000 Time delay: 490446 seconds Block delay: 24987
CryticTester.borrower_openTroveAndJoinInterestBatchManager(17879153616263872676853220739983471220061637615311421720902068361680437964086,1524785993,37961023032293391450438892908920255312981535376949419470422881137649276595786,260,115792089237316195423570985008687907853269984665640564039457584007913129639933,0x2fffffffd,41388224538590050780676908839687030806185246902164741277538361415143892253285,0x1fffffffe,0xffffffff,0x1fffffffe) from: 0x0000000000000000000000000000000000010000 Time delay: 292304 seconds Block delay: 12155
CryticTester.property_SR02() from: 0x0000000000000000000000000000000000010000 Time delay: 358061 seconds Block delay: 60364
CryticTester.check_openedTrove() from: 0x0000000000000000000000000000000000030000 Time delay: 16802 seconds Block delay: 59982
CryticTester.borrower_closeTrove(4370001) from: 0x0000000000000000000000000000000000030000 Time delay: 289103 seconds Block delay: 4223
CryticTester.check_provideToSP() from: 0x0000000000000000000000000000000000010000 Time delay: 455586 seconds Block delay: 49415
CryticTester.check_batch_zero_debt() from: 0x0000000000000000000000000000000000010000 Time delay: 525476 seconds Block delay: 59983
CryticTester.borrower_switchBatchManager(28237000140005353088779237418928716310895761116068504770247853351578520098434,103470273852820867109608058051862537782469894324580679964772941758988432911697,0,0x1fffffffe,27984861990457949764255593355951142130295599968772352581654118913015637154546,4370001,115792089237316195423570985008687907853269984665640564039457584007913129639935) from: 0x0000000000000000000000000000000000020000 Time delay: 401699 seconds Block delay: 24987
CryticTester.borrower_registerBatchManager(271556695527833100934427500668215143742,202375965460491464633525429730725423275,340282366920938463463374607431768211455,340282366920938463463374607431768211455,304573699295209312725458340639169357029) from: 0x0000000000000000000000000000000000020000 Time delay: 136393 seconds Block delay: 23885
CryticTester.check_batch_zero_debt() from: 0x0000000000000000000000000000000000010000 Time delay: 463587 seconds Block delay: 4223
CryticTester.check_aggBatchManagementFees_zero() from: 0x0000000000000000000000000000000000010000 Time delay: 547623 seconds Block delay: 36859
CryticTester.property_CS05() from: 0x0000000000000000000000000000000000030000 Time delay: 255 seconds Block delay: 34720
CryticTester.check_singleDebt() from: 0x0000000000000000000000000000000000020000 Time delay: 100835 seconds Block delay: 59552
CryticTester.check_batchLiquidateTroves() from: 0x0000000000000000000000000000000000010000 Time delay: 111322 seconds Block delay: 2497
CryticTester.check_withdrawFromSP() from: 0x0000000000000000000000000000000000020000 Time delay: 419861 seconds Block delay: 8447
CryticTester.property_BT01() from: 0x0000000000000000000000000000000000020000 Time delay: 525476 seconds Block delay: 32767
CryticTester.trove_urgentRedemption(17989711048916770229340086543623010934114356552340257037016052312539611209538,7821983367087922618651701059790029181168745403279355912731340244983382453341,112164165317069838272134901407288157659922701373949990963146525843231639334631) from: 0x0000000000000000000000000000000000020000 Time delay: 65535 seconds Block delay: 9966
CryticTester.check_openedTrove() from: 0x0000000000000000000000000000000000030000 Time delay: 407328 seconds Block delay: 42101
CryticTester.check_removeFromBatch() from: 0x0000000000000000000000000000000000030000 Time delay: 490448 seconds Block delay: 4909
CryticTester.borrower_repayBold(115792089237316195423570985008687907853269984665640564039457584007913129639933,4369999) from: 0x0000000000000000000000000000000000030000 Time delay: 305572 seconds Block delay: 53166
CryticTester.borrower_adjustTrove(79918360365314133192365354436560389700174598815462641438311457990869603735135,62318521188960590701738487703280569497594723509551390766918274993278750318711,true,4369999,false,1524785993) from: 0x0000000000000000000000000000000000010000 Time delay: 65535 seconds Block delay: 32737
CryticTester.borrower_removeFromBatch(79198622263650919617297780421768808329012275208372001047914165278412909357513,477,91356906051138820744666145574551413321809650865144241036603466854436272033817,115792089237316195423570985008687907853269984665640564039457584007913129639934,115792089237316195423570985008687907853269984665640564039457584007913129639935) from: 0x0000000000000000000000000000000000010000 Time delay: 521319 seconds Block delay: 5952
CryticTester.check_adjustTroveInterestRate() from: 0x0000000000000000000000000000000000010000 Time delay: 526194 seconds Block delay: 11942
CryticTester.check_adjustUnredeemableTrove() from: 0x0000000000000000000000000000000000030000 Time delay: 289607 seconds Block delay: 46422
CryticTester.check_singleDebt() from: 0x0000000000000000000000000000000000030000 Time delay: 447588 seconds Block delay: 54809
CryticTester.borrower_setInterestBatchManager(115792089237316195423570985008687907853269984665640564039457584007913129639932,1524785992,115792089237316195423570985008687907853269984665640564039457584007913129639931,11775653694320040059194169244520396438398233875512981097975669969688028190904) from: 0x0000000000000000000000000000000000030000 Time delay: 4177 seconds Block delay: 12155
CryticTester.borrower_repayBold(115792089237316195423570985008687907853269984665640564039457584007913129639935,6145454653307317656215353535938039777769330225021551974545043709727479485713) from: 0x0000000000000000000000000000000000020000 Time delay: 400033 seconds Block delay: 38350
CryticTester.target_clamped_openTrove(43973690139353477108,4664284375626519063860475876541176180,1078965428809195510147324087454664930421276482212786465236859,2437183478656557408184667832532445751348608691713,28575275650637667017397419268721031) from: 0x0000000000000000000000000000000000020000
CryticTester.borrower_withdrawColl(1524785993,115792089237316195423570985008687907853269984665640564039457584007913129639935) from: 0x0000000000000000000000000000000000030000
CryticTester.borrower_setBatchManagerAnnualInterestRate(340282366920938463463374607431768211453,28471343730612069633477296280435784154415938483104948610340573726262572609511,31865454967533471595628154348784053107711625288598248822030061268468850750041,4369999) from: 0x0000000000000000000000000000000000030000 Time delay: 478623 seconds Block delay: 32147
CryticTester.check_unredeemableTrove() from: 0x0000000000000000000000000000000000030000 Time delay: 33271 seconds Block delay: 42101
CryticTester.pool_claimAllCollGains() from: 0x0000000000000000000000000000000000020000 Time delay: 376096 seconds Block delay: 15369
CryticTester.check_openTroveAndJoinInterestBatchManager() from: 0x0000000000000000000000000000000000030000 Time delay: 487078 seconds Block delay: 29948
CryticTester.borrower_switchBatchManager(1524785991,32009261965628005456032229141310566214841351531052315840058618704470340062641,4370001,0x2fffffffd,95358926488251457202505607309416003331536332636148062452917153924119969329785,28352615893367211944265602813777963859375133089073458505273838322063429326055,107341204860798769337968452335377765347841722465247669403831021402738965972080) from: 0x0000000000000000000000000000000000030000 Time delay: 448552 seconds Block delay: 5237
CryticTester.pool_provideToSP(2473001063061824903678848,false) from: 0x0000000000000000000000000000000000030000 Time delay: 38059 seconds Block delay: 59982
CryticTester.check_openTroveAndJoinInterestBatchManager() from: 0x0000000000000000000000000000000000020000 Time delay: 414736 seconds Block delay: 45819
CryticTester.check_provideToSP() from: 0x0000000000000000000000000000000000020000 Time delay: 127251 seconds Block delay: 53451
CryticTester.pool_provideToSP(28613973816350169433390883870941438478939378077757598238101945912971005715990,false) from: 0x0000000000000000000000000000000000030000 Time delay: 32767 seconds Block delay: 42595
CryticTester.check_removeInterestIndividualDelegate() from: 0x0000000000000000000000000000000000030000 Time delay: 16802 seconds Block delay: 23275
CryticTester.check_openedTrove() from: 0x0000000000000000000000000000000000010000 Time delay: 400981 seconds Block delay: 53166
CryticTester.borrower_lowerBatchManagementFee(4369999) from: 0x0000000000000000000000000000000000010000 Time delay: 172101 seconds Block delay: 255

[2024-09-19 17:25:50.72] [Worker 3] Test borrower_adjustTroveInterestRate(uint256,uint256,uint256,uint256,uint256) falsified!
  Call sequence:
CryticTester.target_switchBranch()
CryticTester.target_switchBranch()
CryticTester.target_changePrice(1973221613012002699721)
CryticTester.target_clamped_openTrove(2847710733919506167285094246870674174795304774583332244,580309045296778471487201955583308217090130383,5641328349084110751617504254411614013214179330489730055196652934710518428,94825434518840846144189971153795908749934853909358996215728894,831656886737624090333575251962082255411651546355613182160)
*wait* Time delay: 1 seconds Block delay: 1
CryticTester.borrower_adjustTroveInterestRate(0,0,0,0,0)

[2024-09-19 17:25:50.72] [Worker 3] Sequence replayed from corpus file 8149312326648861842.txt (6/8)
[2024-09-19 17:25:50.72] [Worker 0] Test borrower_shutdown() falsified!
  Call sequence:
CryticTester.target_switchBranch()
CryticTester.target_switchBranch()
CryticTester.target_changePrice(2130446723812172582627)
CryticTester.target_clamped_openTrove(1364977073278418865370952538219726354856903734048221,11401112264375250949037194582849945336447744,579910453667618855899615117496251213455305997237561907145840067306,47857563829277982031620691022834242699317062483,1079798984350448288087936077776727579267207004)
*wait* Time delay: 1 seconds Block delay: 1
CryticTester.borrower_shutdown()

[2024-09-19 17:25:50.72] [Worker 0] Sequence replayed from corpus file 8018868819186514748.txt (6/8)
[2024-09-19 17:25:50.73]  Saved reproducer to echidna/coverage/6255285325909652052.txt
[2024-09-19 17:25:50.73]  Saved reproducer to echidna/coverage/6355948066041189840.txt
[2024-09-19 17:25:50.74] [Worker 2] New coverage: 29266 instr, 13 contracts, 17 seqs in corpus
[2024-09-19 17:25:50.74] [Worker 2] Sequence replayed from corpus file 4739272189992418453.txt (6/8)
[2024-09-19 17:25:50.75] [Worker 4] Sequence replayed from corpus file 6467787013345516289.txt (3/8)
[2024-09-19 17:25:50.76] [Worker 1] New coverage: 30470 instr, 13 contracts, 18 seqs in corpus
[2024-09-19 17:25:50.76] [Worker 1] Sequence replayed from corpus file 1168073363806135586.txt (7/8)
[2024-09-19 17:26:07.26] [status] tests: 29/71, fuzzing: 13316/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:25:50.77] [Worker 5] Test target_new_clamped_openTrove(uint256,uint256,uint256,uint256) falsified!
  Call sequence:
CryticTester.check_withdrawBold() from: 0x0000000000000000000000000000000000020000 Time delay: 420078 seconds Block delay: 42229
CryticTester.target_changePrice(7326250084395686627766881604045061000817729031357313044139018660608197051160) from: 0x0000000000000000000000000000000000030000 Time delay: 444463 seconds Block delay: 32147
CryticTester.borrower_removeInterestIndividualDelegate(1885331737) from: 0x0000000000000000000000000000000000010000 Time delay: 150273 seconds Block delay: 59981
CryticTester.check_withdrawFromSP() from: 0x0000000000000000000000000000000000030000 Time delay: 442705 seconds Block delay: 32
CryticTester.check_batch_zero_shares() from: 0x0000000000000000000000000000000000030000 Time delay: 199729 seconds Block delay: 6721
CryticTester.check_openedTrove() from: 0x0000000000000000000000000000000000020000 Time delay: 254414 seconds Block delay: 24311
CryticTester.property_SR02() from: 0x0000000000000000000000000000000000030000 Time delay: 379552 seconds Block delay: 14536
CryticTester.borrower_adjustTroveInterestRate(4369999,0,329049042651851431111065107979940808952782892866122799258281626943982713,90184875984040431282879798455920471245709377114840576742068364204031735006223,858) from: 0x0000000000000000000000000000000000010000 Time delay: 225906 seconds Block delay: 30042
CryticTester.borrower_switchBatchManager(72264784271332405438109244884821050726283507505466461953351325900247556625045,115792089237316195423570985008687907853269984665640564039457584007913129639935,115792089237316195423570985008687907853269984665640564039457584007913129639935,0x3fa387ab1db837680029bf9896fae3e8ab44d31d,453,1524785993,112227527828651173280892835999214663920648181445005488510293674557213336999237) from: 0x0000000000000000000000000000000000020000 Time delay: 360624 seconds Block delay: 561
CryticTester.check_addColl() from: 0x0000000000000000000000000000000000010000 Time delay: 66543 seconds Block delay: 23722
CryticTester.check_claimCollateral() from: 0x0000000000000000000000000000000000010000 Time delay: 16802 seconds Block delay: 33357
CryticTester.check_adjustUnredeemableTrove() from: 0x0000000000000000000000000000000000010000 Time delay: 379552 seconds Block delay: 30011
CryticTester.check_lowerBatchManagementFee() from: 0x0000000000000000000000000000000000010000 Time delay: 82670 seconds Block delay: 18429
CryticTester.borrower_withdrawColl(4370000,71563998240162546149536585981011486526307539460540115829654224329472623840220) from: 0x0000000000000000000000000000000000020000 Time delay: 136393 seconds Block delay: 31232
CryticTester.check_singleDebt() from: 0x0000000000000000000000000000000000020000 Time delay: 338920 seconds Block delay: 54809
CryticTester.check_hasRedistributedDebt() from: 0x0000000000000000000000000000000000010000 Time delay: 150273 seconds Block delay: 10254
CryticTester.check_aggBatchManagementFees_zero() from: 0x0000000000000000000000000000000000010000 Time delay: 198598 seconds Block delay: 53011
CryticTester.target_changePrice(115792089237316195423570985008687907853269984665640564039457584007913129639935) from: 0x0000000000000000000000000000000000030000
CryticTester.check_setInterestIndividualDelegate() from: 0x0000000000000000000000000000000000020000 Time delay: 31594 seconds Block delay: 12338
CryticTester.pool_provideToSP(95620426761189026261062871620439975265875778093547962812610419439055678057472,false) from: 0x0000000000000000000000000000000000030000 Time delay: 115085 seconds Block delay: 22909
CryticTester.check_adjustTrove() from: 0x0000000000000000000000000000000000030000 Time delay: 436727 seconds Block delay: 42229
CryticTester.borrower_setBatchManagerAnnualInterestRate(110498281317749178274882588178823167269,50647923768696330323487754731073618966455162089024,60351268644200483684097570667341264754020033513599925349085495432354733611853,43792123286884668899859628368547233926581966292231822280630832482420039023082) from: 0x0000000000000000000000000000000000020000 Time delay: 434894 seconds Block delay: 60054
CryticTester.borrower_openTroveAndJoinInterestBatchManager(597,115792089237316195423570985008687907853269984665640564039457584007913129639932,4370000,4370000,296,0x33ae292c80d9ef512a73962ccacd9929db29e820,18454183536593481772689511351241551461317708818741039367334162298601907961957,0x4c443830e34610922c09f9ede74555eac9cc9760,0x2fffffffd,0xd01001c0261679fea6b5dd286e8f8cd227ba4f5b) from: 0x0000000000000000000000000000000000030000 Time delay: 136392 seconds Block delay: 31232
CryticTester.property_BT01() from: 0x0000000000000000000000000000000000020000 Time delay: 115085 seconds Block delay: 31592
CryticTester.check_urgentRedemption() from: 0x0000000000000000000000000000000000010000 Time delay: 82672 seconds Block delay: 12053
CryticTester.check_lowerBatchManagementFee() from: 0x0000000000000000000000000000000000030000 Time delay: 100835 seconds Block delay: 42595
CryticTester.check_aggBatchManagementFees_zero() from: 0x0000000000000000000000000000000000010000 Time delay: 57853 seconds Block delay: 45261
CryticTester.borrower_closeTrove(0) from: 0x0000000000000000000000000000000000020000 Time delay: 569114 seconds Block delay: 561
CryticTester.target_changePrice(2239623662133908660723) from: 0x0000000000000000000000000000000000020000 Time delay: 135921 seconds Block delay: 11942
CryticTester.target_new_clamped_openTrove(57712017290850107671598879501769315621784929821994649182222198450013461428079,4370001,41594862609769565373852729249266715484733417621378322758279953835982247360876,115792089237316195423570985008687907853269984665640564039457584007913129639931) from: 0x0000000000000000000000000000000000030000 Time delay: 286875 seconds Block delay: 32737
CryticTester.check_withdrawFromSP() from: 0x0000000000000000000000000000000000030000 Time delay: 31594 seconds Block delay: 1243
CryticTester.borrower_shutdown() from: 0x0000000000000000000000000000000000020000 Time delay: 490446 seconds Block delay: 24987
CryticTester.borrower_openTroveAndJoinInterestBatchManager(17879153616263872676853220739983471220061637615311421720902068361680437964086,1524785993,37961023032293391450438892908920255312981535376949419470422881137649276595786,260,115792089237316195423570985008687907853269984665640564039457584007913129639933,0x2fffffffd,41388224538590050780676908839687030806185246902164741277538361415143892253285,0x1fffffffe,0xffffffff,0x1fffffffe) from: 0x0000000000000000000000000000000000010000 Time delay: 292304 seconds Block delay: 12155
CryticTester.property_SR02() from: 0x0000000000000000000000000000000000010000 Time delay: 358061 seconds Block delay: 60364
CryticTester.check_openedTrove() from: 0x0000000000000000000000000000000000030000 Time delay: 16802 seconds Block delay: 59982
CryticTester.borrower_closeTrove(4370001) from: 0x0000000000000000000000000000000000030000 Time delay: 289103 seconds Block delay: 4223
CryticTester.check_provideToSP() from: 0x0000000000000000000000000000000000010000 Time delay: 455586 seconds Block delay: 49415
CryticTester.check_batch_zero_debt() from: 0x0000000000000000000000000000000000010000 Time delay: 525476 seconds Block delay: 59983
CryticTester.borrower_switchBatchManager(28237000140005353088779237418928716310895761116068504770247853351578520098434,103470273852820867109608058051862537782469894324580679964772941758988432911697,0,0x1fffffffe,27984861990457949764255593355951142130295599968772352581654118913015637154546,4370001,115792089237316195423570985008687907853269984665640564039457584007913129639935) from: 0x0000000000000000000000000000000000020000 Time delay: 401699 seconds Block delay: 24987
CryticTester.borrower_registerBatchManager(271556695527833100934427500668215143742,202375965460491464633525429730725423275,340282366920938463463374607431768211455,340282366920938463463374607431768211455,304573699295209312725458340639169357029) from: 0x0000000000000000000000000000000000020000 Time delay: 136393 seconds Block delay: 23885
CryticTester.check_batch_zero_debt() from: 0x0000000000000000000000000000000000010000 Time delay: 463587 seconds Block delay: 4223
CryticTester.check_aggBatchManagementFees_zero() from: 0x0000000000000000000000000000000000010000 Time delay: 547623 seconds Block delay: 36859
CryticTester.property_CS05() from: 0x0000000000000000000000000000000000030000 Time delay: 255 seconds Block delay: 34720
CryticTester.check_singleDebt() from: 0x0000000000000000000000000000000000020000 Time delay: 100835 seconds Block delay: 59552
CryticTester.check_batchLiquidateTroves() from: 0x0000000000000000000000000000000000010000 Time delay: 111322 seconds Block delay: 2497
CryticTester.check_withdrawFromSP() from: 0x0000000000000000000000000000000000020000 Time delay: 419861 seconds Block delay: 8447
CryticTester.property_BT01() from: 0x0000000000000000000000000000000000020000 Time delay: 525476 seconds Block delay: 32767
CryticTester.trove_urgentRedemption(17989711048916770229340086543623010934114356552340257037016052312539611209538,7821983367087922618651701059790029181168745403279355912731340244983382453341,112164165317069838272134901407288157659922701373949990963146525843231639334631) from: 0x0000000000000000000000000000000000020000 Time delay: 65535 seconds Block delay: 9966
CryticTester.check_openedTrove() from: 0x0000000000000000000000000000000000030000 Time delay: 407328 seconds Block delay: 42101
CryticTester.check_removeFromBatch() from: 0x0000000000000000000000000000000000030000 Time delay: 490448 seconds Block delay: 4909
CryticTester.borrower_repayBold(115792089237316195423570985008687907853269984665640564039457584007913129639933,4369999) from: 0x0000000000000000000000000000000000030000 Time delay: 305572 seconds Block delay: 53166
CryticTester.borrower_adjustTrove(79918360365314133192365354436560389700174598815462641438311457990869603735135,62318521188960590701738487703280569497594723509551390766918274993278750318711,true,4369999,false,1524785993) from: 0x0000000000000000000000000000000000010000 Time delay: 65535 seconds Block delay: 32737
CryticTester.borrower_removeFromBatch(79198622263650919617297780421768808329012275208372001047914165278412909357513,477,91356906051138820744666145574551413321809650865144241036603466854436272033817,115792089237316195423570985008687907853269984665640564039457584007913129639934,115792089237316195423570985008687907853269984665640564039457584007913129639935) from: 0x0000000000000000000000000000000000010000 Time delay: 521319 seconds Block delay: 5952
CryticTester.check_adjustTroveInterestRate() from: 0x0000000000000000000000000000000000010000 Time delay: 526194 seconds Block delay: 11942
CryticTester.check_adjustUnredeemableTrove() from: 0x0000000000000000000000000000000000030000 Time delay: 289607 seconds Block delay: 46422
CryticTester.check_singleDebt() from: 0x0000000000000000000000000000000000030000 Time delay: 447588 seconds Block delay: 54809
CryticTester.borrower_setInterestBatchManager(115792089237316195423570985008687907853269984665640564039457584007913129639932,1524785992,115792089237316195423570985008687907853269984665640564039457584007913129639931,11775653694320040059194169244520396438398233875512981097975669969688028190904) from: 0x0000000000000000000000000000000000030000 Time delay: 4177 seconds Block delay: 12155
CryticTester.borrower_repayBold(115792089237316195423570985008687907853269984665640564039457584007913129639935,6145454653307317656215353535938039777769330225021551974545043709727479485713) from: 0x0000000000000000000000000000000000020000 Time delay: 400033 seconds Block delay: 38350
CryticTester.target_clamped_openTrove(43973690139353477108,4664284375626519063860475876541176180,1078965428809195510147324087454664930421276482212786465236859,2437183478656557408184667832532445751348608691713,28575275650637667017397419268721031) from: 0x0000000000000000000000000000000000020000
CryticTester.borrower_withdrawColl(1524785993,115792089237316195423570985008687907853269984665640564039457584007913129639935) from: 0x0000000000000000000000000000000000030000
CryticTester.borrower_setBatchManagerAnnualInterestRate(340282366920938463463374607431768211453,28471343730612069633477296280435784154415938483104948610340573726262572609511,31865454967533471595628154348784053107711625288598248822030061268468850750041,4369999) from: 0x0000000000000000000000000000000000030000 Time delay: 478623 seconds Block delay: 32147
CryticTester.check_unredeemableTrove() from: 0x0000000000000000000000000000000000030000 Time delay: 33271 seconds Block delay: 42101
CryticTester.pool_claimAllCollGains() from: 0x0000000000000000000000000000000000020000 Time delay: 376096 seconds Block delay: 15369
CryticTester.check_openTroveAndJoinInterestBatchManager() from: 0x0000000000000000000000000000000000030000 Time delay: 487078 seconds Block delay: 29948
CryticTester.borrower_switchBatchManager(1524785991,32009261965628005456032229141310566214841351531052315840058618704470340062641,4370001,0x2fffffffd,95358926488251457202505607309416003331536332636148062452917153924119969329785,28352615893367211944265602813777963859375133089073458505273838322063429326055,107341204860798769337968452335377765347841722465247669403831021402738965972080) from: 0x0000000000000000000000000000000000030000 Time delay: 448552 seconds Block delay: 5237
CryticTester.pool_provideToSP(2473001063061824903678848,false) from: 0x0000000000000000000000000000000000030000 Time delay: 38059 seconds Block delay: 59982
CryticTester.check_openTroveAndJoinInterestBatchManager() from: 0x0000000000000000000000000000000000020000 Time delay: 414736 seconds Block delay: 45819
CryticTester.check_provideToSP() from: 0x0000000000000000000000000000000000020000 Time delay: 127251 seconds Block delay: 53451
CryticTester.pool_provideToSP(28613973816350169433390883870941438478939378077757598238101945912971005715990,false) from: 0x0000000000000000000000000000000000030000 Time delay: 32767 seconds Block delay: 42595
CryticTester.check_removeInterestIndividualDelegate() from: 0x0000000000000000000000000000000000030000 Time delay: 16802 seconds Block delay: 23275
CryticTester.check_openedTrove() from: 0x0000000000000000000000000000000000010000 Time delay: 400981 seconds Block delay: 53166
CryticTester.borrower_lowerBatchManagementFee(4369999) from: 0x0000000000000000000000000000000000010000 Time delay: 172101 seconds Block delay: 255
CryticTester.borrower_openTrove(51233385326072741481242368135612180681389649270480328500647294375734222594848,28534371144395792261022040061067429093760154863814731803991643385006508676253,17555509759658104031178796622295141269223509429892073008357824950126059753355,4370001,0,4370000,28856848951294797746116987648895062062049057064978417305298214500090282338093,0x20000,0xffffffff,0xffffffff) from: 0x0000000000000000000000000000000000010000 Time delay: 155224 seconds Block delay: 34135
CryticTester.trove_urgentRedemption(107067766788498358900356665843552185839547397854238059059260388350932532152928,97181345804070866492145819873749975135498293151167496634952613613638553883222,42056616087592146414327511646540996663254601568684019266650209875831261274331) from: 0x0000000000000000000000000000000000010000 Time delay: 447588 seconds Block delay: 27883
CryticTester.check_urgentRedemption() from: 0x0000000000000000000000000000000000020000 Time delay: 206186 seconds Block delay: 24987
CryticTester.check_adjustTrove() from: 0x0000000000000000000000000000000000020000 Time delay: 478623 seconds Block delay: 8447
CryticTester.check_openTroveAndJoinInterestBatchManager() from: 0x0000000000000000000000000000000000020000 Time delay: 335258 seconds Block delay: 15368
CryticTester.borrower_applyPendingDebt(115792089237316195423570985008687907853269984665640564039457584007913129639935,4369999,4370000) from: 0x0000000000000000000000000000000000010000 Time delay: 436727 seconds Block delay: 46422
CryticTester.borrower_setInterestIndividualDelegate(673,0xf5a2fe45f4f1308502b1c136b9ef8af136141382,263011521263694886254676400107012885624,41318777007323728301796596307032089460,83639892226184370287306333174517822201972633139380414309919488301192937282642,4370000,114548487179680500048454724643474438979697892547052423626870725745480535550310,568) from: 0x0000000000000000000000000000000000010000 Time delay: 16802 seconds Block delay: 49415
CryticTester.check_withdrawFromSP() from: 0x0000000000000000000000000000000000020000 Time delay: 136394 seconds Block delay: 28743
CryticTester.check_shutdown() from: 0x0000000000000000000000000000000000010000 Time delay: 463587 seconds Block delay: 32767
CryticTester.check_redeemCollateral() from: 0x0000000000000000000000000000000000030000 Time delay: 100835 seconds Block delay: 30256
CryticTester.check_repayBold() from: 0x0000000000000000000000000000000000020000 Time delay: 379552 seconds Block delay: 5054
CryticTester.target_new_clamped_openTrove(94130321432215895954282505956843456952620860384602824418633900410330145787036,61228097789887046543748115104661289434456318084485258246049223704681187216932,0,115792089237316195423570985008687907853269984665640564039457584007913129639932) from: 0x0000000000000000000000000000000000030000 Time delay: 136392 seconds Block delay: 50499

[2024-09-19 17:25:50.80] [Worker 0] Test pool_withdrawFromSP(uint256,bool) falsified!
  Call sequence:
CryticTester.target_switchBranch()
CryticTester.target_switchBranch()
CryticTester.target_changePrice(2034590756990073974841)
CryticTester.target_clamped_openTrove(1091307598658058032031505072563397194341186,5544378834571781327389319004420911984471373980,72898117388073293380963818873506321958542201515220212562503336223017282,2014459594448279171219987360151655710312037353,5762150114197535157513827315929438689666664925184074805)
*wait* Time delay: 1 seconds Block delay: 1
CryticTester.pool_withdrawFromSP(0,false)

[2024-09-19 17:25:50.80] [Worker 0] Sequence replayed from corpus file 2455930381591840763.txt (7/8)
[2024-09-19 17:25:50.81] [Worker 2] Sequence replayed from corpus file 8905063179697819520.txt (7/8)
[2024-09-19 17:25:50.82] [Worker 3] New coverage: 30537 instr, 13 contracts, 19 seqs in corpus
[2024-09-19 17:25:50.82] [Worker 3] Sequence replayed from corpus file 1953986734335579328.txt (7/8)
[2024-09-19 17:25:50.84] [Worker 5] New coverage: 30626 instr, 13 contracts, 20 seqs in corpus
[2024-09-19 17:25:50.84] [Worker 1] Sequence replayed from corpus file 8308054366385604264.txt (8/8)
[2024-09-19 17:25:50.85] [Worker 5] Sequence replayed from corpus file 3913219529665685474.txt (2/8)
[2024-09-19 17:25:50.86] [Worker 8] Test target_clamped_openTrove(uint256,uint256,uint256,uint256,uint256) falsified!
  Call sequence:
CryticTester.check_withdrawBold() from: 0x0000000000000000000000000000000000020000 Time delay: 420078 seconds Block delay: 42229
CryticTester.target_changePrice(7326250084395686627766881604045061000817729031357313044139018660608197051160) from: 0x0000000000000000000000000000000000030000 Time delay: 444463 seconds Block delay: 32147
CryticTester.borrower_removeInterestIndividualDelegate(1885331737) from: 0x0000000000000000000000000000000000010000 Time delay: 150273 seconds Block delay: 59981
CryticTester.check_withdrawFromSP() from: 0x0000000000000000000000000000000000030000 Time delay: 442705 seconds Block delay: 32
CryticTester.check_batch_zero_shares() from: 0x0000000000000000000000000000000000030000 Time delay: 199729 seconds Block delay: 6721
CryticTester.check_openedTrove() from: 0x0000000000000000000000000000000000020000 Time delay: 254414 seconds Block delay: 24311
CryticTester.property_SR02() from: 0x0000000000000000000000000000000000030000 Time delay: 379552 seconds Block delay: 14536
CryticTester.borrower_adjustTroveInterestRate(4369999,0,329049042651851431111065107979940808952782892866122799258281626943982713,90184875984040431282879798455920471245709377114840576742068364204031735006223,858) from: 0x0000000000000000000000000000000000010000 Time delay: 225906 seconds Block delay: 30042
CryticTester.borrower_switchBatchManager(72264784271332405438109244884821050726283507505466461953351325900247556625045,115792089237316195423570985008687907853269984665640564039457584007913129639935,115792089237316195423570985008687907853269984665640564039457584007913129639935,0x3fa387ab1db837680029bf9896fae3e8ab44d31d,453,1524785993,112227527828651173280892835999214663920648181445005488510293674557213336999237) from: 0x0000000000000000000000000000000000020000 Time delay: 360624 seconds Block delay: 561
CryticTester.check_addColl() from: 0x0000000000000000000000000000000000010000 Time delay: 66543 seconds Block delay: 23722
CryticTester.check_claimCollateral() from: 0x0000000000000000000000000000000000010000 Time delay: 16802 seconds Block delay: 33357
CryticTester.check_adjustUnredeemableTrove() from: 0x0000000000000000000000000000000000010000 Time delay: 379552 seconds Block delay: 30011
CryticTester.check_lowerBatchManagementFee() from: 0x0000000000000000000000000000000000010000 Time delay: 82670 seconds Block delay: 18429
CryticTester.borrower_withdrawColl(4370000,71563998240162546149536585981011486526307539460540115829654224329472623840220) from: 0x0000000000000000000000000000000000020000 Time delay: 136393 seconds Block delay: 31232
CryticTester.check_singleDebt() from: 0x0000000000000000000000000000000000020000 Time delay: 338920 seconds Block delay: 54809
CryticTester.check_hasRedistributedDebt() from: 0x0000000000000000000000000000000000010000 Time delay: 150273 seconds Block delay: 10254
CryticTester.check_aggBatchManagementFees_zero() from: 0x0000000000000000000000000000000000010000 Time delay: 198598 seconds Block delay: 53011
CryticTester.target_changePrice(115792089237316195423570985008687907853269984665640564039457584007913129639935) from: 0x0000000000000000000000000000000000030000
CryticTester.check_setInterestIndividualDelegate() from: 0x0000000000000000000000000000000000020000 Time delay: 31594 seconds Block delay: 12338
CryticTester.pool_provideToSP(95620426761189026261062871620439975265875778093547962812610419439055678057472,false) from: 0x0000000000000000000000000000000000030000 Time delay: 115085 seconds Block delay: 22909
CryticTester.check_adjustTrove() from: 0x0000000000000000000000000000000000030000 Time delay: 436727 seconds Block delay: 42229
CryticTester.borrower_setBatchManagerAnnualInterestRate(110498281317749178274882588178823167269,50647923768696330323487754731073618966455162089024,60351268644200483684097570667341264754020033513599925349085495432354733611853,43792123286884668899859628368547233926581966292231822280630832482420039023082) from: 0x0000000000000000000000000000000000020000 Time delay: 434894 seconds Block delay: 60054
CryticTester.borrower_openTroveAndJoinInterestBatchManager(597,115792089237316195423570985008687907853269984665640564039457584007913129639932,4370000,4370000,296,0x33ae292c80d9ef512a73962ccacd9929db29e820,18454183536593481772689511351241551461317708818741039367334162298601907961957,0x4c443830e34610922c09f9ede74555eac9cc9760,0x2fffffffd,0xd01001c0261679fea6b5dd286e8f8cd227ba4f5b) from: 0x0000000000000000000000000000000000030000 Time delay: 136392 seconds Block delay: 31232
CryticTester.property_BT01() from: 0x0000000000000000000000000000000000020000 Time delay: 115085 seconds Block delay: 31592
CryticTester.check_urgentRedemption() from: 0x0000000000000000000000000000000000010000 Time delay: 82672 seconds Block delay: 12053
CryticTester.check_lowerBatchManagementFee() from: 0x0000000000000000000000000000000000030000 Time delay: 100835 seconds Block delay: 42595
CryticTester.check_aggBatchManagementFees_zero() from: 0x0000000000000000000000000000000000010000 Time delay: 57853 seconds Block delay: 45261
CryticTester.borrower_closeTrove(0) from: 0x0000000000000000000000000000000000020000 Time delay: 569114 seconds Block delay: 561
CryticTester.target_changePrice(2239623662133908660723) from: 0x0000000000000000000000000000000000020000 Time delay: 135921 seconds Block delay: 11942
CryticTester.target_new_clamped_openTrove(57712017290850107671598879501769315621784929821994649182222198450013461428079,4370001,41594862609769565373852729249266715484733417621378322758279953835982247360876,115792089237316195423570985008687907853269984665640564039457584007913129639931) from: 0x0000000000000000000000000000000000030000 Time delay: 286875 seconds Block delay: 32737
CryticTester.check_withdrawFromSP() from: 0x0000000000000000000000000000000000030000 Time delay: 31594 seconds Block delay: 1243
CryticTester.borrower_shutdown() from: 0x0000000000000000000000000000000000020000 Time delay: 490446 seconds Block delay: 24987
CryticTester.borrower_openTroveAndJoinInterestBatchManager(17879153616263872676853220739983471220061637615311421720902068361680437964086,1524785993,37961023032293391450438892908920255312981535376949419470422881137649276595786,260,115792089237316195423570985008687907853269984665640564039457584007913129639933,0x2fffffffd,41388224538590050780676908839687030806185246902164741277538361415143892253285,0x1fffffffe,0xffffffff,0x1fffffffe) from: 0x0000000000000000000000000000000000010000 Time delay: 292304 seconds Block delay: 12155
CryticTester.property_SR02() from: 0x0000000000000000000000000000000000010000 Time delay: 358061 seconds Block delay: 60364
CryticTester.check_openedTrove() from: 0x0000000000000000000000000000000000030000 Time delay: 16802 seconds Block delay: 59982
CryticTester.borrower_closeTrove(4370001) from: 0x0000000000000000000000000000000000030000 Time delay: 289103 seconds Block delay: 4223
CryticTester.check_provideToSP() from: 0x0000000000000000000000000000000000010000 Time delay: 455586 seconds Block delay: 49415
CryticTester.check_batch_zero_debt() from: 0x0000000000000000000000000000000000010000 Time delay: 525476 seconds Block delay: 59983
CryticTester.borrower_switchBatchManager(28237000140005353088779237418928716310895761116068504770247853351578520098434,103470273852820867109608058051862537782469894324580679964772941758988432911697,0,0x1fffffffe,27984861990457949764255593355951142130295599968772352581654118913015637154546,4370001,115792089237316195423570985008687907853269984665640564039457584007913129639935) from: 0x0000000000000000000000000000000000020000 Time delay: 401699 seconds Block delay: 24987
CryticTester.borrower_registerBatchManager(271556695527833100934427500668215143742,202375965460491464633525429730725423275,340282366920938463463374607431768211455,340282366920938463463374607431768211455,304573699295209312725458340639169357029) from: 0x0000000000000000000000000000000000020000 Time delay: 136393 seconds Block delay: 23885
CryticTester.check_batch_zero_debt() from: 0x0000000000000000000000000000000000010000 Time delay: 463587 seconds Block delay: 4223
CryticTester.check_aggBatchManagementFees_zero() from: 0x0000000000000000000000000000000000010000 Time delay: 547623 seconds Block delay: 36859
CryticTester.property_CS05() from: 0x0000000000000000000000000000000000030000 Time delay: 255 seconds Block delay: 34720
CryticTester.check_singleDebt() from: 0x0000000000000000000000000000000000020000 Time delay: 100835 seconds Block delay: 59552
CryticTester.check_batchLiquidateTroves() from: 0x0000000000000000000000000000000000010000 Time delay: 111322 seconds Block delay: 2497
CryticTester.check_withdrawFromSP() from: 0x0000000000000000000000000000000000020000 Time delay: 419861 seconds Block delay: 8447
CryticTester.property_BT01() from: 0x0000000000000000000000000000000000020000 Time delay: 525476 seconds Block delay: 32767
CryticTester.trove_urgentRedemption(17989711048916770229340086543623010934114356552340257037016052312539611209538,7821983367087922618651701059790029181168745403279355912731340244983382453341,112164165317069838272134901407288157659922701373949990963146525843231639334631) from: 0x0000000000000000000000000000000000020000 Time delay: 65535 seconds Block delay: 9966
CryticTester.check_openedTrove() from: 0x0000000000000000000000000000000000030000 Time delay: 407328 seconds Block delay: 42101
CryticTester.check_removeFromBatch() from: 0x0000000000000000000000000000000000030000 Time delay: 490448 seconds Block delay: 4909
CryticTester.borrower_repayBold(115792089237316195423570985008687907853269984665640564039457584007913129639933,4369999) from: 0x0000000000000000000000000000000000030000 Time delay: 305572 seconds Block delay: 53166
CryticTester.borrower_adjustTrove(79918360365314133192365354436560389700174598815462641438311457990869603735135,62318521188960590701738487703280569497594723509551390766918274993278750318711,true,4369999,false,1524785993) from: 0x0000000000000000000000000000000000010000 Time delay: 65535 seconds Block delay: 32737
CryticTester.borrower_removeFromBatch(79198622263650919617297780421768808329012275208372001047914165278412909357513,477,91356906051138820744666145574551413321809650865144241036603466854436272033817,115792089237316195423570985008687907853269984665640564039457584007913129639934,115792089237316195423570985008687907853269984665640564039457584007913129639935) from: 0x0000000000000000000000000000000000010000 Time delay: 521319 seconds Block delay: 5952
CryticTester.check_adjustTroveInterestRate() from: 0x0000000000000000000000000000000000010000 Time delay: 526194 seconds Block delay: 11942
CryticTester.check_adjustUnredeemableTrove() from: 0x0000000000000000000000000000000000030000 Time delay: 289607 seconds Block delay: 46422
CryticTester.check_singleDebt() from: 0x0000000000000000000000000000000000030000 Time delay: 447588 seconds Block delay: 54809
CryticTester.borrower_setInterestBatchManager(115792089237316195423570985008687907853269984665640564039457584007913129639932,1524785992,115792089237316195423570985008687907853269984665640564039457584007913129639931,11775653694320040059194169244520396438398233875512981097975669969688028190904) from: 0x0000000000000000000000000000000000030000 Time delay: 4177 seconds Block delay: 12155
CryticTester.borrower_repayBold(115792089237316195423570985008687907853269984665640564039457584007913129639935,6145454653307317656215353535938039777769330225021551974545043709727479485713) from: 0x0000000000000000000000000000000000020000 Time delay: 400033 seconds Block delay: 38350
CryticTester.target_clamped_openTrove(43973690139353477108,4664284375626519063860475876541176180,1078965428809195510147324087454664930421276482212786465236859,2437183478656557408184667832532445751348608691713,28575275650637667017397419268721031) from: 0x0000000000000000000000000000000000020000
CryticTester.borrower_removeFromBatch(4369999,8711892428104996485729273030794657573034282970328469505516032262630440643021,15739854934705974374729431699919883014086672632462346304686177005803316515242,563,4369999) from: 0x0000000000000000000000000000000000010000
CryticTester.target_switchBranch() from: 0x0000000000000000000000000000000000010000 Time delay: 420078 seconds Block delay: 33357
CryticTester.property_BT02(1524785991,115792089237316195423570985008687907853269984665640564039457584007913129639935,115792089237316195423570985008687907853269984665640564039457584007913129639935,49240419484260196078865974789659686986169040116556033238153962626328803069314) from: 0x0000000000000000000000000000000000030000 Time delay: 24867 seconds Block delay: 127
CryticTester.target_clamped_openTrove(111660099097094899867340585080361133754172348723825794449917577541246187633269,4370000,32363116951895142820824395169580506812462027119180873150122121828276830801459,115792089237316195423570985008687907853269984665640564039457584007913129639935,4370001) from: 0x0000000000000000000000000000000000020000 Time delay: 512439 seconds Block delay: 34720

[2024-09-19 17:26:10.27] [status] tests: 29/71, fuzzing: 13316/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:25:50.88] [Worker 8] Test borrower_repayBold(uint256,uint256) falsified!
  Call sequence:
CryticTester.check_withdrawBold() from: 0x0000000000000000000000000000000000020000 Time delay: 420078 seconds Block delay: 42229
CryticTester.target_changePrice(7326250084395686627766881604045061000817729031357313044139018660608197051160) from: 0x0000000000000000000000000000000000030000 Time delay: 444463 seconds Block delay: 32147
CryticTester.borrower_removeInterestIndividualDelegate(1885331737) from: 0x0000000000000000000000000000000000010000 Time delay: 150273 seconds Block delay: 59981
CryticTester.check_withdrawFromSP() from: 0x0000000000000000000000000000000000030000 Time delay: 442705 seconds Block delay: 32
CryticTester.check_batch_zero_shares() from: 0x0000000000000000000000000000000000030000 Time delay: 199729 seconds Block delay: 6721
CryticTester.check_openedTrove() from: 0x0000000000000000000000000000000000020000 Time delay: 254414 seconds Block delay: 24311
CryticTester.property_SR02() from: 0x0000000000000000000000000000000000030000 Time delay: 379552 seconds Block delay: 14536
CryticTester.borrower_adjustTroveInterestRate(4369999,0,329049042651851431111065107979940808952782892866122799258281626943982713,90184875984040431282879798455920471245709377114840576742068364204031735006223,858) from: 0x0000000000000000000000000000000000010000 Time delay: 225906 seconds Block delay: 30042
CryticTester.borrower_switchBatchManager(72264784271332405438109244884821050726283507505466461953351325900247556625045,115792089237316195423570985008687907853269984665640564039457584007913129639935,115792089237316195423570985008687907853269984665640564039457584007913129639935,0x3fa387ab1db837680029bf9896fae3e8ab44d31d,453,1524785993,112227527828651173280892835999214663920648181445005488510293674557213336999237) from: 0x0000000000000000000000000000000000020000 Time delay: 360624 seconds Block delay: 561
CryticTester.check_addColl() from: 0x0000000000000000000000000000000000010000 Time delay: 66543 seconds Block delay: 23722
CryticTester.check_claimCollateral() from: 0x0000000000000000000000000000000000010000 Time delay: 16802 seconds Block delay: 33357
CryticTester.check_adjustUnredeemableTrove() from: 0x0000000000000000000000000000000000010000 Time delay: 379552 seconds Block delay: 30011
CryticTester.check_lowerBatchManagementFee() from: 0x0000000000000000000000000000000000010000 Time delay: 82670 seconds Block delay: 18429
CryticTester.borrower_withdrawColl(4370000,71563998240162546149536585981011486526307539460540115829654224329472623840220) from: 0x0000000000000000000000000000000000020000 Time delay: 136393 seconds Block delay: 31232
CryticTester.check_singleDebt() from: 0x0000000000000000000000000000000000020000 Time delay: 338920 seconds Block delay: 54809
CryticTester.check_hasRedistributedDebt() from: 0x0000000000000000000000000000000000010000 Time delay: 150273 seconds Block delay: 10254
CryticTester.check_aggBatchManagementFees_zero() from: 0x0000000000000000000000000000000000010000 Time delay: 198598 seconds Block delay: 53011
CryticTester.target_changePrice(115792089237316195423570985008687907853269984665640564039457584007913129639935) from: 0x0000000000000000000000000000000000030000
CryticTester.check_setInterestIndividualDelegate() from: 0x0000000000000000000000000000000000020000 Time delay: 31594 seconds Block delay: 12338
CryticTester.pool_provideToSP(95620426761189026261062871620439975265875778093547962812610419439055678057472,false) from: 0x0000000000000000000000000000000000030000 Time delay: 115085 seconds Block delay: 22909
CryticTester.check_adjustTrove() from: 0x0000000000000000000000000000000000030000 Time delay: 436727 seconds Block delay: 42229
CryticTester.borrower_setBatchManagerAnnualInterestRate(110498281317749178274882588178823167269,50647923768696330323487754731073618966455162089024,60351268644200483684097570667341264754020033513599925349085495432354733611853,43792123286884668899859628368547233926581966292231822280630832482420039023082) from: 0x0000000000000000000000000000000000020000 Time delay: 434894 seconds Block delay: 60054
CryticTester.borrower_openTroveAndJoinInterestBatchManager(597,115792089237316195423570985008687907853269984665640564039457584007913129639932,4370000,4370000,296,0x33ae292c80d9ef512a73962ccacd9929db29e820,18454183536593481772689511351241551461317708818741039367334162298601907961957,0x4c443830e34610922c09f9ede74555eac9cc9760,0x2fffffffd,0xd01001c0261679fea6b5dd286e8f8cd227ba4f5b) from: 0x0000000000000000000000000000000000030000 Time delay: 136392 seconds Block delay: 31232
CryticTester.property_BT01() from: 0x0000000000000000000000000000000000020000 Time delay: 115085 seconds Block delay: 31592
CryticTester.check_urgentRedemption() from: 0x0000000000000000000000000000000000010000 Time delay: 82672 seconds Block delay: 12053
CryticTester.check_lowerBatchManagementFee() from: 0x0000000000000000000000000000000000030000 Time delay: 100835 seconds Block delay: 42595
CryticTester.check_aggBatchManagementFees_zero() from: 0x0000000000000000000000000000000000010000 Time delay: 57853 seconds Block delay: 45261
CryticTester.borrower_closeTrove(0) from: 0x0000000000000000000000000000000000020000 Time delay: 569114 seconds Block delay: 561
CryticTester.target_changePrice(2239623662133908660723) from: 0x0000000000000000000000000000000000020000 Time delay: 135921 seconds Block delay: 11942
CryticTester.target_new_clamped_openTrove(57712017290850107671598879501769315621784929821994649182222198450013461428079,4370001,41594862609769565373852729249266715484733417621378322758279953835982247360876,115792089237316195423570985008687907853269984665640564039457584007913129639931) from: 0x0000000000000000000000000000000000030000 Time delay: 286875 seconds Block delay: 32737
CryticTester.check_withdrawFromSP() from: 0x0000000000000000000000000000000000030000 Time delay: 31594 seconds Block delay: 1243
CryticTester.borrower_shutdown() from: 0x0000000000000000000000000000000000020000 Time delay: 490446 seconds Block delay: 24987
CryticTester.borrower_openTroveAndJoinInterestBatchManager(17879153616263872676853220739983471220061637615311421720902068361680437964086,1524785993,37961023032293391450438892908920255312981535376949419470422881137649276595786,260,115792089237316195423570985008687907853269984665640564039457584007913129639933,0x2fffffffd,41388224538590050780676908839687030806185246902164741277538361415143892253285,0x1fffffffe,0xffffffff,0x1fffffffe) from: 0x0000000000000000000000000000000000010000 Time delay: 292304 seconds Block delay: 12155
CryticTester.property_SR02() from: 0x0000000000000000000000000000000000010000 Time delay: 358061 seconds Block delay: 60364
CryticTester.check_openedTrove() from: 0x0000000000000000000000000000000000030000 Time delay: 16802 seconds Block delay: 59982
CryticTester.borrower_closeTrove(4370001) from: 0x0000000000000000000000000000000000030000 Time delay: 289103 seconds Block delay: 4223
CryticTester.check_provideToSP() from: 0x0000000000000000000000000000000000010000 Time delay: 455586 seconds Block delay: 49415
CryticTester.check_batch_zero_debt() from: 0x0000000000000000000000000000000000010000 Time delay: 525476 seconds Block delay: 59983
CryticTester.borrower_switchBatchManager(28237000140005353088779237418928716310895761116068504770247853351578520098434,103470273852820867109608058051862537782469894324580679964772941758988432911697,0,0x1fffffffe,27984861990457949764255593355951142130295599968772352581654118913015637154546,4370001,115792089237316195423570985008687907853269984665640564039457584007913129639935) from: 0x0000000000000000000000000000000000020000 Time delay: 401699 seconds Block delay: 24987
CryticTester.borrower_registerBatchManager(271556695527833100934427500668215143742,202375965460491464633525429730725423275,340282366920938463463374607431768211455,340282366920938463463374607431768211455,304573699295209312725458340639169357029) from: 0x0000000000000000000000000000000000020000 Time delay: 136393 seconds Block delay: 23885
CryticTester.check_batch_zero_debt() from: 0x0000000000000000000000000000000000010000 Time delay: 463587 seconds Block delay: 4223
CryticTester.check_aggBatchManagementFees_zero() from: 0x0000000000000000000000000000000000010000 Time delay: 547623 seconds Block delay: 36859
CryticTester.property_CS05() from: 0x0000000000000000000000000000000000030000 Time delay: 255 seconds Block delay: 34720
CryticTester.check_singleDebt() from: 0x0000000000000000000000000000000000020000 Time delay: 100835 seconds Block delay: 59552
CryticTester.check_batchLiquidateTroves() from: 0x0000000000000000000000000000000000010000 Time delay: 111322 seconds Block delay: 2497
CryticTester.check_withdrawFromSP() from: 0x0000000000000000000000000000000000020000 Time delay: 419861 seconds Block delay: 8447
CryticTester.property_BT01() from: 0x0000000000000000000000000000000000020000 Time delay: 525476 seconds Block delay: 32767
CryticTester.trove_urgentRedemption(17989711048916770229340086543623010934114356552340257037016052312539611209538,7821983367087922618651701059790029181168745403279355912731340244983382453341,112164165317069838272134901407288157659922701373949990963146525843231639334631) from: 0x0000000000000000000000000000000000020000 Time delay: 65535 seconds Block delay: 9966
CryticTester.check_openedTrove() from: 0x0000000000000000000000000000000000030000 Time delay: 407328 seconds Block delay: 42101
CryticTester.check_removeFromBatch() from: 0x0000000000000000000000000000000000030000 Time delay: 490448 seconds Block delay: 4909
CryticTester.borrower_repayBold(115792089237316195423570985008687907853269984665640564039457584007913129639933,4369999) from: 0x0000000000000000000000000000000000030000 Time delay: 305572 seconds Block delay: 53166
CryticTester.borrower_adjustTrove(79918360365314133192365354436560389700174598815462641438311457990869603735135,62318521188960590701738487703280569497594723509551390766918274993278750318711,true,4369999,false,1524785993) from: 0x0000000000000000000000000000000000010000 Time delay: 65535 seconds Block delay: 32737
CryticTester.borrower_removeFromBatch(79198622263650919617297780421768808329012275208372001047914165278412909357513,477,91356906051138820744666145574551413321809650865144241036603466854436272033817,115792089237316195423570985008687907853269984665640564039457584007913129639934,115792089237316195423570985008687907853269984665640564039457584007913129639935) from: 0x0000000000000000000000000000000000010000 Time delay: 521319 seconds Block delay: 5952
CryticTester.check_adjustTroveInterestRate() from: 0x0000000000000000000000000000000000010000 Time delay: 526194 seconds Block delay: 11942
CryticTester.check_adjustUnredeemableTrove() from: 0x0000000000000000000000000000000000030000 Time delay: 289607 seconds Block delay: 46422
CryticTester.check_singleDebt() from: 0x0000000000000000000000000000000000030000 Time delay: 447588 seconds Block delay: 54809
CryticTester.borrower_setInterestBatchManager(115792089237316195423570985008687907853269984665640564039457584007913129639932,1524785992,115792089237316195423570985008687907853269984665640564039457584007913129639931,11775653694320040059194169244520396438398233875512981097975669969688028190904) from: 0x0000000000000000000000000000000000030000 Time delay: 4177 seconds Block delay: 12155
CryticTester.borrower_repayBold(115792089237316195423570985008687907853269984665640564039457584007913129639935,6145454653307317656215353535938039777769330225021551974545043709727479485713) from: 0x0000000000000000000000000000000000020000 Time delay: 400033 seconds Block delay: 38350
CryticTester.target_clamped_openTrove(43973690139353477108,4664284375626519063860475876541176180,1078965428809195510147324087454664930421276482212786465236859,2437183478656557408184667832532445751348608691713,28575275650637667017397419268721031) from: 0x0000000000000000000000000000000000020000
CryticTester.borrower_removeFromBatch(4369999,8711892428104996485729273030794657573034282970328469505516032262630440643021,15739854934705974374729431699919883014086672632462346304686177005803316515242,563,4369999) from: 0x0000000000000000000000000000000000010000
CryticTester.target_switchBranch() from: 0x0000000000000000000000000000000000010000 Time delay: 420078 seconds Block delay: 33357
CryticTester.property_BT02(1524785991,115792089237316195423570985008687907853269984665640564039457584007913129639935,115792089237316195423570985008687907853269984665640564039457584007913129639935,49240419484260196078865974789659686986169040116556033238153962626328803069314) from: 0x0000000000000000000000000000000000030000 Time delay: 24867 seconds Block delay: 127
CryticTester.target_clamped_openTrove(111660099097094899867340585080361133754172348723825794449917577541246187633269,4370000,32363116951895142820824395169580506812462027119180873150122121828276830801459,115792089237316195423570985008687907853269984665640564039457584007913129639935,4370001) from: 0x0000000000000000000000000000000000020000 Time delay: 512439 seconds Block delay: 34720
CryticTester.target_new_clamped_openTrove(89625363093063628890997255442116879065618377902075446294085952251220675374234,96094866366939607888885280412478686634208229057223768529806538367039574510725,4369999,4369999) from: 0x0000000000000000000000000000000000030000 Time delay: 305572 seconds Block delay: 11826
CryticTester.check_shutdown() from: 0x0000000000000000000000000000000000020000 Time delay: 344203 seconds Block delay: 58783
CryticTester.check_withdrawColl() from: 0x0000000000000000000000000000000000010000 Time delay: 526194 seconds Block delay: 32147
CryticTester.check_aggBatchManagementFees_zero() from: 0x0000000000000000000000000000000000010000 Time delay: 16802 seconds Block delay: 2496
CryticTester.check_claimCollateral() from: 0x0000000000000000000000000000000000030000 Time delay: 206186 seconds Block delay: 59982
CryticTester.check_adjustTroveInterestRate() from: 0x0000000000000000000000000000000000030000 Time delay: 136394 seconds Block delay: 2511
CryticTester.check_withdrawColl() from: 0x0000000000000000000000000000000000010000 Time delay: 166184 seconds Block delay: 5952
CryticTester.target_changePrice(0) from: 0x0000000000000000000000000000000000010000 Time delay: 65535 seconds Block delay: 561
CryticTester.borrower_setBatchManagerAnnualInterestRate(340282366920938463463374607431768211453,1524785991,115792089237316195423570985008687907853269984665640564039457584007913129639934,3763327094257572094102919489797713821665210169217763365457540916878983284838) from: 0x0000000000000000000000000000000000020000 Time delay: 206186 seconds Block delay: 50499
CryticTester.property_CS05() from: 0x0000000000000000000000000000000000010000 Time delay: 420078 seconds Block delay: 53562
CryticTester.check_provideToSP() from: 0x0000000000000000000000000000000000020000 Time delay: 401699 seconds Block delay: 54498
CryticTester.check_repayBold() from: 0x0000000000000000000000000000000000030000 Time delay: 463587 seconds Block delay: 15367
CryticTester.check_urgentRedemption() from: 0x0000000000000000000000000000000000030000 Time delay: 127251 seconds Block delay: 4462
CryticTester.borrower_repayBold(7859620643505450376940086012352192926524513225307486278323976075775574000306,1524785992) from: 0x0000000000000000000000000000000000030000 Time delay: 360624 seconds Block delay: 32147

[2024-09-19 17:25:50.88] [Worker 2] Sequence replayed from corpus file 2869158469034921471.txt (8/8)
[2024-09-19 17:25:50.89] [Worker 9] New coverage: 30642 instr, 13 contracts, 21 seqs in corpus
[2024-09-19 17:25:50.89] [Worker 0] Sequence replayed from corpus file 4682658601826625585.txt (8/8)
[2024-09-19 17:25:50.89] [Worker 7] Sequence replayed from corpus file 6237935422924205753.txt (3/8)
[2024-09-19 17:25:50.90] [Worker 3] Sequence replayed from corpus file 6293214397266705529.txt (8/8)
[2024-09-19 17:25:50.90]  Saved reproducer to echidna/coverage/8795667281353125244.txt
[2024-09-19 17:25:50.90]  Saved reproducer to echidna/reproducers-unshrunk/7183347344743277923.txt
[2024-09-19 17:25:50.91] [Worker 8] New coverage: 30938 instr, 13 contracts, 22 seqs in corpus
[2024-09-19 17:25:50.91] [Worker 9] Sequence replayed from corpus file 5794509728139513155.txt (3/5)
[2024-09-19 17:25:50.92] [Worker 6] Test borrower_adjustTrove(uint256,uint256,bool,uint256,bool,uint256) falsified!
  Call sequence:
CryticTester.target_switchBranch() from: 0x0000000000000000000000000000000000030000 Time delay: 604535 seconds Block delay: 45819
CryticTester.property_SR02() from: 0x0000000000000000000000000000000000010000 Time delay: 127 seconds Block delay: 38350
CryticTester.check_withdrawBold() from: 0x0000000000000000000000000000000000020000 Time delay: 39499 seconds Block delay: 16089
CryticTester.check_adjustUnredeemableTrove() from: 0x0000000000000000000000000000000000030000 Time delay: 407328 seconds Block delay: 45261
CryticTester.borrower_withdrawColl(1524785993,20371423967322543348166130790527990415353884455755310220802371525126607430342) from: 0x0000000000000000000000000000000000030000 Time delay: 19029 seconds Block delay: 561
CryticTester.borrower_withdrawColl(1524785993,20371423967322543348166130790527990415353884455755310220802371525126607430342) from: 0x0000000000000000000000000000000000020000 Time delay: 344203 seconds Block delay: 11905
CryticTester.pool_withdrawFromSP(234280891644529721636,true) from: 0x0000000000000000000000000000000000020000 Time delay: 297507 seconds Block delay: 12053
CryticTester.check_aggBatchManagementFees_zero() from: 0x0000000000000000000000000000000000010000 Time delay: 38059 seconds Block delay: 60267
CryticTester.check_redeemCollateral() from: 0x0000000000000000000000000000000000010000 Time delay: 55534 seconds Block delay: 38350
CryticTester.pool_provideToSP(1673659462206822280136005485972587501452011600000264578513884453059,true) from: 0x0000000000000000000000000000000000030000 Time delay: 564960 seconds Block delay: 30256
CryticTester.borrower_adjustTroveInterestRate(115792089237316195423570985008687907853269984665640564039457584007913129639935,6,44036204391197776786324621912938136849676345653166550973734456013315555211709,115792089237316195423570985008687907853269984665640564039457584007913129639935,60295583979270105117777295141234798687764895433482669493498021962903931316130) from: 0x0000000000000000000000000000000000020000 Time delay: 303345 seconds Block delay: 4462
CryticTester.borrower_repayBold(1524785992,94804695032005548288581232014667159761923935998248951985344170054049234281557) from: 0x0000000000000000000000000000000000030000 Time delay: 38059 seconds Block delay: 30042
CryticTester.pool_withdrawFromSP(2078929346529529887739437983016979261226396399427394818019855008716801372218,true) from: 0x0000000000000000000000000000000000030000 Time delay: 45142 seconds Block delay: 9966
CryticTester.borrower_adjustTrove(1524785991,36613774563958022831000067707734997123352444763486346660668396298735428149679,true,0,true,2948412829) from: 0x0000000000000000000000000000000000030000 Time delay: 588255 seconds Block delay: 45852
CryticTester.borrower_closeTrove(4370000) from: 0x0000000000000000000000000000000000020000 Time delay: 66543 seconds Block delay: 32
CryticTester.borrower_adjustTrove(37671483587724,30601217278257410016143523601371951390002190189042480047358660164323486630905,true,1524785993,false,0) from: 0x0000000000000000000000000000000000020000 Time delay: 73040 seconds Block delay: 7122
CryticTester.check_batch_zero_shares() from: 0x0000000000000000000000000000000000010000 Time delay: 447588 seconds Block delay: 12493
CryticTester.check_setInterestBatchManager() from: 0x0000000000000000000000000000000000010000 Time delay: 344203 seconds Block delay: 5140
CryticTester.check_batchLiquidateTroves() from: 0x0000000000000000000000000000000000010000 Time delay: 65535 seconds Block delay: 53451
CryticTester.property_GL01(31554803209172076480661426473220060214584877504583840785680143887956082537269,18897065245005506108266552773000027057443781914770582903306292860866289957048) from: 0x0000000000000000000000000000000000020000 Time delay: 136393 seconds Block delay: 45819
CryticTester.check_setInterestIndividualDelegate() from: 0x0000000000000000000000000000000000020000 Time delay: 358612 seconds Block delay: 53562
CryticTester.property_CS05() from: 0x0000000000000000000000000000000000020000 Time delay: 412373 seconds Block delay: 3661
CryticTester.borrower_openTrove(4369999,15765288365102653277087323490861195256370118303578458839335254968770077862383,115792089237316195423570985008687907853269984665640564039457584007913129639932,819,29451546569338409879428755317950596679019473569059977490971459284298604345618,2866632765558401205705924536650590418198133749829489179175127684450360012752,4349515205195986913993488665381725497499115022808205567282689982099450765846,0x2fffffffd,0x10000,0xbffb01bb2ddb4efa87cb78eecb8115afae6d2032) from: 0x0000000000000000000000000000000000010000 Time delay: 116188 seconds Block delay: 45261
CryticTester.borrower_removeFromBatch(1524785992,0,64570147780839795145042836487775821326909125110714231576655947648719482944596,4369999,115792089237316195423570985008687907853269984665640564039457584007913129639935) from: 0x0000000000000000000000000000000000030000 Time delay: 173012 seconds Block delay: 23978
CryticTester.borrower_lowerBatchManagementFee(4370001) from: 0x0000000000000000000000000000000000020000 Time delay: 404739 seconds Block delay: 27585
CryticTester.check_repayBold() from: 0x0000000000000000000000000000000000010000 Time delay: 360624 seconds Block delay: 22995
CryticTester.borrower_adjustTrove(115792089237316195423570985008687907853269984665640564039457584007913129639935,32888464549256464762378240849253838304411798635043499614197176220825874121387,false,4369999,false,52021214786108809071697236384768719891167837770612563637216453046991223777669) from: 0x0000000000000000000000000000000000010000 Time delay: 318197 seconds Block delay: 8447
CryticTester.borrower_setBatchManagerAnnualInterestRate(263875216653683219038972646229662346223,57226327961326796071795551370183958204724485019911075532810176379757378943003,63545442283025874336752696934353731219505687675733390856447659522443626366027,4370001) from: 0x0000000000000000000000000000000000020000 Time delay: 412373 seconds Block delay: 48801
CryticTester.check_adjustTroveInterestRate() from: 0x0000000000000000000000000000000000010000 Time delay: 332369 seconds Block delay: 37522
CryticTester.check_adjustTrove() from: 0x0000000000000000000000000000000000020000 Time delay: 332369 seconds Block delay: 2512
CryticTester.check_provideToSP() from: 0x0000000000000000000000000000000000020000 Time delay: 379552 seconds Block delay: 39277
CryticTester.target_clamped_openTrove(4370001,3267440775571433487495992715433912597835118784232719571780376420438026268472,115792089237316195423570985008687907853269984665640564039457584007913129639934,115792089237316195423570985008687907853269984665640564039457584007913129639935,107707231000066593938848296348333519806375928805701331763789730297623115451317) from: 0x0000000000000000000000000000000000030000 Time delay: 405856 seconds Block delay: 4462
CryticTester.borrower_repayBold(68360059409186723274559902539822281430298767802887026800442350917576968918253,1524785991) from: 0x0000000000000000000000000000000000030000 Time delay: 32767 seconds Block delay: 4896
CryticTester.property_GL01(4370000,204) from: 0x0000000000000000000000000000000000020000 Time delay: 542800 seconds Block delay: 2526
CryticTester.target_clamped_openTrove(115792089237316195423570985008687907853269984665640564039457584007913129639935,78,14695270523411766503609359262747086702970250955302394653108216269908392463707,0,18214220166620607282881935341042412291747739468456413388400640613669321567666) from: 0x0000000000000000000000000000000000020000 Time delay: 289103 seconds Block delay: 45819
CryticTester.borrower_shutdown() from: 0x0000000000000000000000000000000000030000 Time delay: 448552 seconds Block delay: 38350
CryticTester.check_adjustTroveInterestRate() from: 0x0000000000000000000000000000000000010000 Time delay: 136394 seconds Block delay: 60054
CryticTester.borrower_lowerBatchManagementFee(94214356609897496060004170564844315021538192110978109925925065079414928846688) from: 0x0000000000000000000000000000000000030000 Time delay: 254414 seconds Block delay: 2512
CryticTester.property_BT01() from: 0x0000000000000000000000000000000000020000 Time delay: 112444 seconds Block delay: 5952
CryticTester.trove_batchLiquidateTroves(38600418570429235383235636502418458268312091064314163557365030394357069123507) from: 0x0000000000000000000000000000000000030000 Time delay: 554465 seconds Block delay: 60267
CryticTester.property_CS05() from: 0x0000000000000000000000000000000000030000 Time delay: 478623 seconds Block delay: 18429
CryticTester.borrower_openTroveAndJoinInterestBatchManager(1524785992,1524785991,4370001,7673636352793001185754796880279795073165545602575931724805096096144436663966,7906117544669251187966502892250394937349574484725327853728326341276845505575,0x3fa387ab1db837680029bf9896fae3e8ab44d31d,27789751040676587139265371841689741508238342764282269709941045201535591243106,0x1fffffffe,0xffffffff,0x63fb907fd752a801f71cad07f19c58e3d182dfc1) from: 0x0000000000000000000000000000000000020000 Time delay: 305572 seconds Block delay: 12053
CryticTester.borrower_adjustTroveInterestRate(102736940320062768440400785930895164265616433235965040674023425432536678715684,4370000,4370001,4370001,21321111292895891924779243859389181748579588561890222673331028109487084202314) from: 0x0000000000000000000000000000000000030000 Time delay: 5871 seconds Block delay: 2497
CryticTester.borrower_openTrove(113135278283442727243189110466216394328325812746267107947947413281875635836507,115792089237316195423570985008687907853269984665640564039457584007913129639935,62684679605347214085531345854013540324174604110251647416393659638037310409265,112804507348799170939689305168896204360546924477055974861050600095318804730533,39596245088773537137435470850951522874672491267822940167898479534953118775060,115792089237316195423570985008687907853269984665640564039457584007913129639932,19307664968305754514706443690742330901295412326527376912179179988747170865547,0xefc56627233b02ea95bae7e19f648d7dcd5bb132,0x1fffffffe,0xffffffff) from: 0x0000000000000000000000000000000000030000 Time delay: 322247 seconds Block delay: 33357
CryticTester.check_urgentRedemption() from: 0x0000000000000000000000000000000000020000
CryticTester.borrower_setInterestIndividualDelegate(35821391630369492863977451065040001321796714472378532395244546771486858742994,0x1fffffffe,4370001,195193111252274341382365307949373941561,0,1524785991,2605238128580880897403991168382144513149824355067808472857103669974131160395,831) from: 0x0000000000000000000000000000000000020000 Time delay: 50309 seconds Block delay: 2512
CryticTester.check_removeInterestIndividualDelegate() from: 0x0000000000000000000000000000000000030000 Time delay: 50417 seconds Block delay: 38100
CryticTester.check_batch_zero_debt() from: 0x0000000000000000000000000000000000030000 Time delay: 401699 seconds Block delay: 16089
CryticTester.check_addColl() from: 0x0000000000000000000000000000000000030000 Time delay: 82670 seconds Block delay: 5023
CryticTester.borrower_shutdown() from: 0x0000000000000000000000000000000000010000 Time delay: 490446 seconds Block delay: 32147
CryticTester.property_CS05() from: 0x0000000000000000000000000000000000010000 Time delay: 82671 seconds Block delay: 20243
CryticTester.check_repayBold() from: 0x0000000000000000000000000000000000030000 Time delay: 254414 seconds Block delay: 15369
CryticTester.target_switchBranch() from: 0x0000000000000000000000000000000000030000 Time delay: 569114 seconds Block delay: 19933
CryticTester.check_claimCollateral() from: 0x0000000000000000000000000000000000030000 Time delay: 16802 seconds Block delay: 50880
CryticTester.target_changePrice(9304213673874071895829) from: 0x0000000000000000000000000000000000010000 Time delay: 31594 seconds Block delay: 59983
CryticTester.trove_batchLiquidateTroves(38600418570429235383235636502418458268312091064314163557365030394357069123507) from: 0x0000000000000000000000000000000000030000 Time delay: 439556 seconds Block delay: 58783
CryticTester.check_adjustUnredeemableTrove() from: 0x0000000000000000000000000000000000020000 Time delay: 136394 seconds Block delay: 16089
CryticTester.borrower_withdrawColl(7610136635229252991098505284196452792908216177486061616680668678549054827866,1524785993) from: 0x0000000000000000000000000000000000030000 Time delay: 82672 seconds Block delay: 42229
CryticTester.check_withdrawFromSP() from: 0x0000000000000000000000000000000000010000 Time delay: 448552 seconds Block delay: 4896
CryticTester.check_lowerBatchManagementFee() from: 0x0000000000000000000000000000000000030000 Time delay: 416051 seconds Block delay: 6721
CryticTester.target_clamped_openTrove(13650788885973381801577328194376964874477573824240962794717973268441563220965,63286368368718520898662540306717572677388716178968970877578612053200501515021,79525945717432498952660953873728924282131153312236167289295564229278081287187,11243017345616817865156746056645160464756496708563542968729295401858370191465,115792089237316195423570985008687907853269984665640564039457584007913129639934) from: 0x0000000000000000000000000000000000030000 Time delay: 490448 seconds Block delay: 12493
CryticTester.borrower_removeInterestIndividualDelegate(64607506443930901055829285978785573941411831767329216639073598168117938485227) from: 0x0000000000000000000000000000000000030000 Time delay: 318197 seconds Block delay: 45852
CryticTester.borrower_removeFromBatch(4370001,0,0,115792089237316195423570985008687907853269984665640564039457584007913129639935,4369999) from: 0x0000000000000000000000000000000000010000 Time delay: 463587 seconds Block delay: 255
CryticTester.check_openedTrove() from: 0x0000000000000000000000000000000000010000 Time delay: 45142 seconds Block delay: 30011
CryticTester.check_withdrawColl() from: 0x0000000000000000000000000000000000030000 Time delay: 344203 seconds Block delay: 53451
CryticTester.check_claimAllCollGains() from: 0x0000000000000000000000000000000000030000 Time delay: 335398 seconds Block delay: 57086
CryticTester.check_openTroveAndJoinInterestBatchManager() from: 0x0000000000000000000000000000000000030000 Time delay: 127251 seconds Block delay: 5054
CryticTester.check_applyPendingDebt() from: 0x0000000000000000000000000000000000010000 Time delay: 415353 seconds Block delay: 22699
CryticTester.borrower_removeInterestIndividualDelegate(115792089237316195423570985008687907853269984665640564039457584007913129639932) from: 0x0000000000000000000000000000000000020000 Time delay: 65535 seconds Block delay: 5952
CryticTester.check_aggBatchManagementFees_zero() from: 0x0000000000000000000000000000000000010000 Time delay: 376096 seconds Block delay: 11942
CryticTester.property_GL01(4370000,4369999) from: 0x0000000000000000000000000000000000030000 Time delay: 526194 seconds Block delay: 45819
CryticTester.borrower_repayBold(1524785993,8196275) from: 0x0000000000000000000000000000000000030000 Time delay: 322374 seconds Block delay: 45819
CryticTester.check_registerBatchManager() from: 0x0000000000000000000000000000000000020000 Time delay: 521319 seconds Block delay: 6234
CryticTester.target_changePrice(0) from: 0x0000000000000000000000000000000000030000 Time delay: 322247 seconds Block delay: 2645
CryticTester.property_CS04() from: 0x0000000000000000000000000000000000010000 Time delay: 404997 seconds Block delay: 53011
CryticTester.check_withdrawBold() from: 0x0000000000000000000000000000000000030000 Time delay: 305572 seconds Block delay: 11826
CryticTester.check_withdrawColl() from: 0x0000000000000000000000000000000000010000 Time delay: 305572 seconds Block delay: 32737
CryticTester.borrower_closeTrove(2757952912807056960918746788407) from: 0x0000000000000000000000000000000000010000 Time delay: 136394 seconds Block delay: 23653
CryticTester.check_removeInterestIndividualDelegate() from: 0x0000000000000000000000000000000000020000 Time delay: 337463 seconds Block delay: 60248
CryticTester.check_urgentRedemption() from: 0x0000000000000000000000000000000000020000 Time delay: 400981 seconds Block delay: 30256
CryticTester.property_GL01(55144679747083347298929099178282717050727431862430285703932797883207306388373,89784992455120723165425579145624460444364024250785964261421404813336508208792) from: 0x0000000000000000000000000000000000030000 Time delay: 437838 seconds Block delay: 15368
CryticTester.check_aggBatchManagementFees_zero() from: 0x0000000000000000000000000000000000010000 Time delay: 311164 seconds Block delay: 60248
CryticTester.pool_withdrawFromSP(1524785993,false) from: 0x0000000000000000000000000000000000020000 Time delay: 598861 seconds Block delay: 30256
CryticTester.check_repayBold() from: 0x0000000000000000000000000000000000020000 Time delay: 67960 seconds Block delay: 54809
CryticTester.borrower_adjustTrove(4369999,115792089237316195423570985008687907853269984665640564039457584007913129639931,false,1524785991,true,115792089237316195423570985008687907853269984665640564039457584007913129639932) from: 0x0000000000000000000000000000000000020000 Time delay: 414736 seconds Block delay: 60364

[2024-09-19 17:25:50.93] [Worker 8] Sequence replayed from corpus file 5950387040246032596.txt (3/8)
[2024-09-19 17:25:50.97] [Worker 4] Sequence replayed from corpus file 186636001506536895.txt (4/8)
[2024-09-19 17:25:50.97] [Worker 6] New coverage: 30938 instr, 13 contracts, 23 seqs in corpus
[2024-09-19 17:25:50.99] [Worker 6] Sequence replayed from corpus file 7932393449045658395.txt (3/8)
[2024-09-19 17:25:50.99]  Saved reproducer to echidna/reproducers-unshrunk/1089374709117817811.txt
[2024-09-19 17:25:50.99]  Saved reproducer to echidna/reproducers-unshrunk/201120595268296457.txt
[2024-09-19 17:25:51.09]  Saved reproducer to echidna/reproducers-unshrunk/7140665633324853077.txt
[2024-09-19 17:25:51.14] [Worker 7] Sequence replayed from corpus file 1254097032691672866.txt (4/8)
[2024-09-19 17:25:51.19]  Saved reproducer to echidna/reproducers-unshrunk/9183801473162664031.txt
[2024-09-19 17:25:51.20] [Worker 8] New coverage: 31186 instr, 13 contracts, 24 seqs in corpus
[2024-09-19 17:25:51.20] [Worker 9] New coverage: 31186 instr, 13 contracts, 25 seqs in corpus
[2024-09-19 17:25:51.22] [Worker 8] Sequence replayed from corpus file 4040365207396570551.txt (4/8)
[2024-09-19 17:25:51.23] [Worker 9] Sequence replayed from corpus file 3390448387882001623.txt (4/5)
[2024-09-19 17:25:51.24] [Worker 5] New coverage: 31186 instr, 13 contracts, 26 seqs in corpus
[2024-09-19 17:25:51.26] [Worker 5] Sequence replayed from corpus file 4041228505617616839.txt (3/8)
[2024-09-19 17:25:51.28] [Worker 4] Sequence replayed from corpus file 2013648927146343332.txt (5/8)
[2024-09-19 17:25:51.29]  Saved reproducer to echidna/coverage/1003753213421706225.txt
[2024-09-19 17:25:51.34] [Worker 7] Sequence replayed from corpus file 1421761994568112468.txt (5/8)
[2024-09-19 17:25:51.35] [Worker 6] New coverage: 31186 instr, 13 contracts, 27 seqs in corpus
[2024-09-19 17:25:51.37] [Worker 6] Sequence replayed from corpus file 2140651152445229604.txt (4/8)
[2024-09-19 17:25:51.40]  Saved reproducer to echidna/reproducers-unshrunk/1964158115405610976.txt
[2024-09-19 17:25:51.40]  Saved reproducer to echidna/reproducers-unshrunk/6839905541430491134.txt
[2024-09-19 17:26:13.27] [status] tests: 29/71, fuzzing: 13316/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:25:51.44] [Worker 5] Test borrower_addCollateral(uint256,uint256) falsified!
  Call sequence:
CryticTester.check_withdrawBold() from: 0x0000000000000000000000000000000000020000 Time delay: 420078 seconds Block delay: 42229
CryticTester.target_changePrice(7326250084395686627766881604045061000817729031357313044139018660608197051160) from: 0x0000000000000000000000000000000000030000 Time delay: 444463 seconds Block delay: 32147
CryticTester.borrower_removeInterestIndividualDelegate(1885331737) from: 0x0000000000000000000000000000000000010000 Time delay: 150273 seconds Block delay: 59981
CryticTester.check_withdrawFromSP() from: 0x0000000000000000000000000000000000030000 Time delay: 442705 seconds Block delay: 32
CryticTester.check_batch_zero_shares() from: 0x0000000000000000000000000000000000030000 Time delay: 199729 seconds Block delay: 6721
CryticTester.check_openedTrove() from: 0x0000000000000000000000000000000000020000 Time delay: 254414 seconds Block delay: 24311
CryticTester.property_SR02() from: 0x0000000000000000000000000000000000030000 Time delay: 379552 seconds Block delay: 14536
CryticTester.borrower_adjustTroveInterestRate(4369999,0,329049042651851431111065107979940808952782892866122799258281626943982713,90184875984040431282879798455920471245709377114840576742068364204031735006223,858) from: 0x0000000000000000000000000000000000010000 Time delay: 225906 seconds Block delay: 30042
CryticTester.borrower_switchBatchManager(72264784271332405438109244884821050726283507505466461953351325900247556625045,115792089237316195423570985008687907853269984665640564039457584007913129639935,115792089237316195423570985008687907853269984665640564039457584007913129639935,0x3fa387ab1db837680029bf9896fae3e8ab44d31d,453,1524785993,112227527828651173280892835999214663920648181445005488510293674557213336999237) from: 0x0000000000000000000000000000000000020000 Time delay: 360624 seconds Block delay: 561
CryticTester.check_addColl() from: 0x0000000000000000000000000000000000010000 Time delay: 66543 seconds Block delay: 23722
CryticTester.check_claimCollateral() from: 0x0000000000000000000000000000000000010000 Time delay: 16802 seconds Block delay: 33357
CryticTester.check_adjustUnredeemableTrove() from: 0x0000000000000000000000000000000000010000 Time delay: 379552 seconds Block delay: 30011
CryticTester.check_lowerBatchManagementFee() from: 0x0000000000000000000000000000000000010000 Time delay: 82670 seconds Block delay: 18429
CryticTester.borrower_withdrawColl(4370000,71563998240162546149536585981011486526307539460540115829654224329472623840220) from: 0x0000000000000000000000000000000000020000 Time delay: 136393 seconds Block delay: 31232
CryticTester.check_singleDebt() from: 0x0000000000000000000000000000000000020000 Time delay: 338920 seconds Block delay: 54809
CryticTester.check_hasRedistributedDebt() from: 0x0000000000000000000000000000000000010000 Time delay: 150273 seconds Block delay: 10254
CryticTester.check_aggBatchManagementFees_zero() from: 0x0000000000000000000000000000000000010000 Time delay: 198598 seconds Block delay: 53011
CryticTester.target_changePrice(115792089237316195423570985008687907853269984665640564039457584007913129639935) from: 0x0000000000000000000000000000000000030000
CryticTester.check_setInterestIndividualDelegate() from: 0x0000000000000000000000000000000000020000 Time delay: 31594 seconds Block delay: 12338
CryticTester.pool_provideToSP(95620426761189026261062871620439975265875778093547962812610419439055678057472,false) from: 0x0000000000000000000000000000000000030000 Time delay: 115085 seconds Block delay: 22909
CryticTester.check_adjustTrove() from: 0x0000000000000000000000000000000000030000 Time delay: 436727 seconds Block delay: 42229
CryticTester.borrower_setBatchManagerAnnualInterestRate(110498281317749178274882588178823167269,50647923768696330323487754731073618966455162089024,60351268644200483684097570667341264754020033513599925349085495432354733611853,43792123286884668899859628368547233926581966292231822280630832482420039023082) from: 0x0000000000000000000000000000000000020000 Time delay: 434894 seconds Block delay: 60054
CryticTester.borrower_openTroveAndJoinInterestBatchManager(597,115792089237316195423570985008687907853269984665640564039457584007913129639932,4370000,4370000,296,0x33ae292c80d9ef512a73962ccacd9929db29e820,18454183536593481772689511351241551461317708818741039367334162298601907961957,0x4c443830e34610922c09f9ede74555eac9cc9760,0x2fffffffd,0xd01001c0261679fea6b5dd286e8f8cd227ba4f5b) from: 0x0000000000000000000000000000000000030000 Time delay: 136392 seconds Block delay: 31232
CryticTester.property_BT01() from: 0x0000000000000000000000000000000000020000 Time delay: 115085 seconds Block delay: 31592
CryticTester.check_urgentRedemption() from: 0x0000000000000000000000000000000000010000 Time delay: 82672 seconds Block delay: 12053
CryticTester.check_lowerBatchManagementFee() from: 0x0000000000000000000000000000000000030000 Time delay: 100835 seconds Block delay: 42595
CryticTester.check_aggBatchManagementFees_zero() from: 0x0000000000000000000000000000000000010000 Time delay: 57853 seconds Block delay: 45261
CryticTester.borrower_closeTrove(0) from: 0x0000000000000000000000000000000000020000 Time delay: 569114 seconds Block delay: 561
CryticTester.target_changePrice(2239623662133908660723) from: 0x0000000000000000000000000000000000020000 Time delay: 135921 seconds Block delay: 11942
CryticTester.target_new_clamped_openTrove(57712017290850107671598879501769315621784929821994649182222198450013461428079,4370001,41594862609769565373852729249266715484733417621378322758279953835982247360876,115792089237316195423570985008687907853269984665640564039457584007913129639931) from: 0x0000000000000000000000000000000000030000 Time delay: 286875 seconds Block delay: 32737
CryticTester.check_withdrawFromSP() from: 0x0000000000000000000000000000000000030000 Time delay: 31594 seconds Block delay: 1243
CryticTester.borrower_shutdown() from: 0x0000000000000000000000000000000000020000 Time delay: 490446 seconds Block delay: 24987
CryticTester.borrower_openTroveAndJoinInterestBatchManager(17879153616263872676853220739983471220061637615311421720902068361680437964086,1524785993,37961023032293391450438892908920255312981535376949419470422881137649276595786,260,115792089237316195423570985008687907853269984665640564039457584007913129639933,0x2fffffffd,41388224538590050780676908839687030806185246902164741277538361415143892253285,0x1fffffffe,0xffffffff,0x1fffffffe) from: 0x0000000000000000000000000000000000010000 Time delay: 292304 seconds Block delay: 12155
CryticTester.property_SR02() from: 0x0000000000000000000000000000000000010000 Time delay: 358061 seconds Block delay: 60364
CryticTester.check_openedTrove() from: 0x0000000000000000000000000000000000030000 Time delay: 16802 seconds Block delay: 59982
CryticTester.borrower_closeTrove(4370001) from: 0x0000000000000000000000000000000000030000 Time delay: 289103 seconds Block delay: 4223
CryticTester.check_provideToSP() from: 0x0000000000000000000000000000000000010000 Time delay: 455586 seconds Block delay: 49415
CryticTester.check_batch_zero_debt() from: 0x0000000000000000000000000000000000010000 Time delay: 525476 seconds Block delay: 59983
CryticTester.borrower_switchBatchManager(28237000140005353088779237418928716310895761116068504770247853351578520098434,103470273852820867109608058051862537782469894324580679964772941758988432911697,0,0x1fffffffe,27984861990457949764255593355951142130295599968772352581654118913015637154546,4370001,115792089237316195423570985008687907853269984665640564039457584007913129639935) from: 0x0000000000000000000000000000000000020000 Time delay: 401699 seconds Block delay: 24987
CryticTester.borrower_registerBatchManager(271556695527833100934427500668215143742,202375965460491464633525429730725423275,340282366920938463463374607431768211455,340282366920938463463374607431768211455,304573699295209312725458340639169357029) from: 0x0000000000000000000000000000000000020000 Time delay: 136393 seconds Block delay: 23885
CryticTester.check_batch_zero_debt() from: 0x0000000000000000000000000000000000010000 Time delay: 463587 seconds Block delay: 4223
CryticTester.check_aggBatchManagementFees_zero() from: 0x0000000000000000000000000000000000010000 Time delay: 547623 seconds Block delay: 36859
CryticTester.property_CS05() from: 0x0000000000000000000000000000000000030000 Time delay: 255 seconds Block delay: 34720
CryticTester.check_singleDebt() from: 0x0000000000000000000000000000000000020000 Time delay: 100835 seconds Block delay: 59552
CryticTester.check_batchLiquidateTroves() from: 0x0000000000000000000000000000000000010000 Time delay: 111322 seconds Block delay: 2497
CryticTester.check_withdrawFromSP() from: 0x0000000000000000000000000000000000020000 Time delay: 419861 seconds Block delay: 8447
CryticTester.property_BT01() from: 0x0000000000000000000000000000000000020000 Time delay: 525476 seconds Block delay: 32767
CryticTester.trove_urgentRedemption(17989711048916770229340086543623010934114356552340257037016052312539611209538,7821983367087922618651701059790029181168745403279355912731340244983382453341,112164165317069838272134901407288157659922701373949990963146525843231639334631) from: 0x0000000000000000000000000000000000020000 Time delay: 65535 seconds Block delay: 9966
CryticTester.check_openedTrove() from: 0x0000000000000000000000000000000000030000 Time delay: 407328 seconds Block delay: 42101
CryticTester.check_removeFromBatch() from: 0x0000000000000000000000000000000000030000 Time delay: 490448 seconds Block delay: 4909
CryticTester.borrower_repayBold(115792089237316195423570985008687907853269984665640564039457584007913129639933,4369999) from: 0x0000000000000000000000000000000000030000 Time delay: 305572 seconds Block delay: 53166
CryticTester.borrower_adjustTrove(79918360365314133192365354436560389700174598815462641438311457990869603735135,62318521188960590701738487703280569497594723509551390766918274993278750318711,true,4369999,false,1524785993) from: 0x0000000000000000000000000000000000010000 Time delay: 65535 seconds Block delay: 32737
CryticTester.borrower_removeFromBatch(79198622263650919617297780421768808329012275208372001047914165278412909357513,477,91356906051138820744666145574551413321809650865144241036603466854436272033817,115792089237316195423570985008687907853269984665640564039457584007913129639934,115792089237316195423570985008687907853269984665640564039457584007913129639935) from: 0x0000000000000000000000000000000000010000 Time delay: 521319 seconds Block delay: 5952
CryticTester.check_adjustTroveInterestRate() from: 0x0000000000000000000000000000000000010000 Time delay: 526194 seconds Block delay: 11942
CryticTester.check_adjustUnredeemableTrove() from: 0x0000000000000000000000000000000000030000 Time delay: 289607 seconds Block delay: 46422
CryticTester.check_singleDebt() from: 0x0000000000000000000000000000000000030000 Time delay: 447588 seconds Block delay: 54809
CryticTester.borrower_setInterestBatchManager(115792089237316195423570985008687907853269984665640564039457584007913129639932,1524785992,115792089237316195423570985008687907853269984665640564039457584007913129639931,11775653694320040059194169244520396438398233875512981097975669969688028190904) from: 0x0000000000000000000000000000000000030000 Time delay: 4177 seconds Block delay: 12155
CryticTester.borrower_repayBold(115792089237316195423570985008687907853269984665640564039457584007913129639935,6145454653307317656215353535938039777769330225021551974545043709727479485713) from: 0x0000000000000000000000000000000000020000 Time delay: 400033 seconds Block delay: 38350
CryticTester.target_clamped_openTrove(43973690139353477108,4664284375626519063860475876541176180,1078965428809195510147324087454664930421276482212786465236859,2437183478656557408184667832532445751348608691713,28575275650637667017397419268721031) from: 0x0000000000000000000000000000000000020000
CryticTester.borrower_repayBold(1524785992,115792089237316195423570985008687907853269984665640564039457584007913129639933) from: 0x0000000000000000000000000000000000020000
CryticTester.trove_batchLiquidateTroves(102620768092686254390599834771633395076842860636282452424188137587721928160177) from: 0x0000000000000000000000000000000000010000
CryticTester.check_applyPendingDebt() from: 0x0000000000000000000000000000000000030000 Time delay: 111322 seconds Block delay: 22909
CryticTester.borrower_addCollateral(41789995147194818024844431936836040421957171263506634033041017574546480206715,49751109776127344971135657924557314159076764081085402247480638341711710832018) from: 0x0000000000000000000000000000000000030000 Time delay: 206186 seconds Block delay: 53349

[2024-09-19 17:25:51.45] [Worker 4] Sequence replayed from corpus file 1107433926446403941.txt (6/8)
[2024-09-19 17:25:51.48] [Worker 8] New coverage: 31407 instr, 13 contracts, 28 seqs in corpus
[2024-09-19 17:25:51.49] [Worker 9] Sequence replayed from corpus file 5826536300764505092.txt (5/5)
[2024-09-19 17:25:51.50] [Worker 8] Sequence replayed from corpus file 6980154365601209535.txt (5/8)
[2024-09-19 17:25:51.53]  Saved reproducer to echidna/coverage/6598517407538113013.txt
[2024-09-19 17:25:51.53]  Saved reproducer to echidna/reproducers-unshrunk/6267121365106731913.txt
[2024-09-19 17:25:51.54]  Saved reproducer to echidna/coverage/1002637077104915949.txt
[2024-09-19 17:25:51.54]  Saved reproducer to echidna/reproducers-unshrunk/1266481692813137188.txt
[2024-09-19 17:25:51.54]  Saved reproducer to echidna/reproducers-unshrunk/7962377868573669767.txt
[2024-09-19 17:25:51.66]  Saved reproducer to echidna/coverage/8995040719195695559.txt
[2024-09-19 17:25:51.66]  Saved reproducer to echidna/reproducers-unshrunk/5042352675885177373.txt
[2024-09-19 17:25:51.76]  Saved reproducer to echidna/coverage/2914067880601179093.txt
[2024-09-19 17:25:51.76]  Saved reproducer to echidna/reproducers-unshrunk/1698746476953659691.txt
[2024-09-19 17:25:51.81]  Saved reproducer to echidna/reproducers-unshrunk/7186819387567718669.txt
[2024-09-19 17:25:51.81]  Saved reproducer to echidna/reproducers-unshrunk/6739888555756173204.txt
[2024-09-19 17:25:51.90]  Saved reproducer to echidna/reproducers-unshrunk/7513626078739773969.txt
[2024-09-19 17:25:51.91]  Saved reproducer to echidna/reproducers-unshrunk/8149312326648861842.txt
[2024-09-19 17:25:51.91]  Saved reproducer to echidna/reproducers-unshrunk/8018868819186514748.txt
[2024-09-19 17:25:51.91]  Saved reproducer to echidna/coverage/4739272189992418453.txt
[2024-09-19 17:25:51.91]  Saved reproducer to echidna/coverage/1168073363806135586.txt
[2024-09-19 17:25:52.00]  Saved reproducer to echidna/reproducers-unshrunk/5393597727191107640.txt
[2024-09-19 17:25:52.00]  Saved reproducer to echidna/reproducers-unshrunk/2455930381591840763.txt
[2024-09-19 17:25:52.00]  Saved reproducer to echidna/coverage/1953986734335579328.txt
[2024-09-19 17:25:52.05] [Worker 7] Sequence replayed from corpus file 6565604198555424553.txt (6/8)
[2024-09-19 17:25:52.11]  Saved reproducer to echidna/coverage/3913219529665685474.txt
[2024-09-19 17:25:52.15] [Worker 5] New coverage: 31410 instr, 13 contracts, 29 seqs in corpus
[2024-09-19 17:25:52.15] [Worker 4] Sequence replayed from corpus file 7414369793193076658.txt (7/8)
[2024-09-19 17:25:52.17] [Worker 5] Sequence replayed from corpus file 3426360004470604221.txt (4/8)
[2024-09-19 17:25:52.18]  Saved reproducer to echidna/reproducers-unshrunk/3715976604328731709.txt
[2024-09-19 17:25:52.23] [Worker 6] New coverage: 31426 instr, 13 contracts, 30 seqs in corpus
[2024-09-19 17:25:52.25] [Worker 6] Sequence replayed from corpus file 6847640249785169379.txt (5/8)
[2024-09-19 17:25:52.25]  Saved reproducer to echidna/reproducers-unshrunk/2607770467150714732.txt
[2024-09-19 17:25:52.26] [Worker 5] Sequence replayed from corpus file 8346905194604605113.txt (5/8)
[2024-09-19 17:25:52.30] [Worker 7] Sequence replayed from corpus file 7372344780591166698.txt (7/8)
[2024-09-19 17:25:52.33] [Worker 8] New coverage: 31429 instr, 13 contracts, 31 seqs in corpus
[2024-09-19 17:25:52.33] [Worker 9] New coverage: 31429 instr, 13 contracts, 32 seqs in corpus
[2024-09-19 17:25:52.34] [Worker 8] Sequence replayed from corpus file 4061148652818569585.txt (6/8)
[2024-09-19 17:25:52.36]  Saved reproducer to echidna/coverage/5794509728139513155.txt
[2024-09-19 17:25:52.49] [Worker 5] Sequence replayed from corpus file 4806862386552225167.txt (6/8)
[2024-09-19 17:25:52.49]  Saved reproducer to echidna/coverage/5950387040246032596.txt
[2024-09-19 17:25:52.55] [Worker 8] Sequence replayed from corpus file 7268421948231903109.txt (7/8)
[2024-09-19 17:25:52.56] [Worker 7] Sequence replayed from corpus file 6024566112568107688.txt (8/8)
[2024-09-19 17:25:52.57] [Worker 6] New coverage: 31637 instr, 13 contracts, 33 seqs in corpus
[2024-09-19 17:25:52.58] [Worker 4] Sequence replayed from corpus file 8670941652152383760.txt (8/8)
[2024-09-19 17:25:52.58]  Saved reproducer to echidna/reproducers-unshrunk/879160805733063225.txt
[2024-09-19 17:25:52.58] [Worker 6] Sequence replayed from corpus file 4374576779454317402.txt (6/8)
[2024-09-19 17:25:52.70]  Saved reproducer to echidna/coverage/7932393449045658395.txt
[2024-09-19 17:25:52.72] [Worker 5] Sequence replayed from corpus file 1890672376505971326.txt (7/8)
[2024-09-19 17:25:52.81] [Worker 8] New coverage: 31653 instr, 13 contracts, 34 seqs in corpus
[2024-09-19 17:25:52.82] [Worker 6] Sequence replayed from corpus file 1304755687478965732.txt (7/8)
[2024-09-19 17:25:52.87] [Worker 8] Sequence replayed from corpus file 4830069928625163004.txt (8/8)
[2024-09-19 17:25:52.87]  Saved reproducer to echidna/coverage/4040365207396570551.txt
[2024-09-19 17:25:52.99]  Saved reproducer to echidna/coverage/3390448387882001623.txt
[2024-09-19 17:25:53.05] [Worker 5] Sequence replayed from corpus file 5660033046567021001.txt (8/8)
[2024-09-19 17:25:53.08] [Worker 6] Sequence replayed from corpus file 2351341207700230327.txt (8/8)
[2024-09-19 17:25:53.10]  Saved reproducer to echidna/coverage/4041228505617616839.txt
[2024-09-19 17:25:53.20]  Saved reproducer to echidna/coverage/2140651152445229604.txt
[2024-09-19 17:25:53.26]  Saved reproducer to echidna/reproducers-unshrunk/245794431138699783.txt
[2024-09-19 17:25:53.38]  Saved reproducer to echidna/coverage/6980154365601209535.txt
[2024-09-19 17:25:53.50]  Saved reproducer to echidna/coverage/3426360004470604221.txt
[2024-09-19 17:25:53.62]  Saved reproducer to echidna/coverage/6847640249785169379.txt
[2024-09-19 17:25:53.75]  Saved reproducer to echidna/coverage/4061148652818569585.txt
[2024-09-19 17:25:53.88]  Saved reproducer to echidna/coverage/1599215263942843206.txt
[2024-09-19 17:25:54.00]  Saved reproducer to echidna/coverage/4374576779454317402.txt
[2024-09-19 17:25:54.13]  Saved reproducer to echidna/coverage/4830069928625163004.txt
[2024-09-19 17:26:02.58] [Worker 7] Test limit reached. Stopping.
[2024-09-19 17:26:03.03] [Worker 9] Test limit reached. Stopping.
[2024-09-19 17:26:16.28] [status] tests: 29/71, fuzzing: 13316/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:26:19.28] [status] tests: 29/71, fuzzing: 13316/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:26:22.29] [status] tests: 29/71, fuzzing: 13316/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:26:25.29] [status] tests: 29/71, fuzzing: 13316/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:26:28.29] [status] tests: 29/71, fuzzing: 13316/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:26:31.30] [status] tests: 29/71, fuzzing: 13316/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:26:34.30] [status] tests: 29/71, fuzzing: 13316/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:26:37.30] [status] tests: 29/71, fuzzing: 13316/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:26:40.31] [status] tests: 29/71, fuzzing: 13316/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:26:43.32] [status] tests: 29/71, fuzzing: 13316/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:26:46.33] [status] tests: 29/71, fuzzing: 13316/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:26:49.33] [status] tests: 29/71, fuzzing: 13316/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:26:52.34] [status] tests: 29/71, fuzzing: 13316/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:26:55.34] [status] tests: 29/71, fuzzing: 13316/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:26:58.35] [status] tests: 29/71, fuzzing: 13316/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:27:01.35] [status] tests: 29/71, fuzzing: 13316/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:27:04.36] [status] tests: 29/71, fuzzing: 13316/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:27:07.36] [status] tests: 29/71, fuzzing: 13316/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:27:10.36] [status] tests: 29/71, fuzzing: 13316/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:27:13.37] [status] tests: 29/71, fuzzing: 13316/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:27:16.38] [status] tests: 29/71, fuzzing: 13316/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:27:19.38] [status] tests: 29/71, fuzzing: 13316/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:27:22.39] [status] tests: 29/71, fuzzing: 13316/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:27:25.39] [status] tests: 29/71, fuzzing: 13316/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:27:28.39] [status] tests: 29/71, fuzzing: 13316/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:27:31.40] [status] tests: 29/71, fuzzing: 13316/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:27:34.40] [status] tests: 29/71, fuzzing: 13316/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:27:37.40] [status] tests: 29/71, fuzzing: 13316/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:27:40.41] [status] tests: 29/71, fuzzing: 13316/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:27:43.41] [status] tests: 29/71, fuzzing: 13316/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:27:46.42] [status] tests: 29/71, fuzzing: 13316/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:27:49.42] [status] tests: 29/71, fuzzing: 13316/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:27:52.42] [status] tests: 29/71, fuzzing: 13316/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:27:55.43] [status] tests: 29/71, fuzzing: 13316/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:27:58.43] [status] tests: 29/71, fuzzing: 13316/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:28:01.44] [status] tests: 29/71, fuzzing: 13316/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:28:04.44] [status] tests: 29/71, fuzzing: 13316/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:28:07.45] [status] tests: 29/71, fuzzing: 13316/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:28:10.45] [status] tests: 29/71, fuzzing: 13316/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:28:13.45] [status] tests: 29/71, fuzzing: 13316/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:28:16.46] [status] tests: 29/71, fuzzing: 13316/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:28:19.46] [status] tests: 29/71, fuzzing: 13316/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:28:22.47] [status] tests: 29/71, fuzzing: 13316/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:28:25.47] [status] tests: 29/71, fuzzing: 13316/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:28:28.47] [status] tests: 29/71, fuzzing: 13316/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:28:31.48] [status] tests: 29/71, fuzzing: 13619/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:28:34.48] [status] tests: 29/71, fuzzing: 14953/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:28:37.49] [status] tests: 29/71, fuzzing: 16369/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:28:40.01] [Worker 6] Test limit reached. Stopping.
[2024-09-19 17:28:40.49] [status] tests: 29/71, fuzzing: 17577/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:28:43.49] [status] tests: 29/71, fuzzing: 17577/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:28:46.50] [status] tests: 29/71, fuzzing: 17577/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:28:49.50] [status] tests: 29/71, fuzzing: 17577/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:28:52.50] [status] tests: 29/71, fuzzing: 17577/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:28:55.51] [status] tests: 29/71, fuzzing: 17577/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:28:58.51] [status] tests: 29/71, fuzzing: 17577/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:29:01.52] [status] tests: 29/71, fuzzing: 17577/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:29:04.52] [status] tests: 29/71, fuzzing: 17577/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:29:07.53] [status] tests: 29/71, fuzzing: 17577/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:29:10.53] [status] tests: 29/71, fuzzing: 17577/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:29:13.53] [status] tests: 29/71, fuzzing: 17577/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:29:16.54] [status] tests: 29/71, fuzzing: 17577/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:29:19.54] [status] tests: 29/71, fuzzing: 17577/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:29:22.55] [status] tests: 29/71, fuzzing: 17577/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:29:25.55] [status] tests: 29/71, fuzzing: 17577/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:29:28.56] [status] tests: 29/71, fuzzing: 17577/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:29:31.56] [status] tests: 29/71, fuzzing: 17577/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:29:34.57] [status] tests: 29/71, fuzzing: 17577/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:29:37.57] [status] tests: 29/71, fuzzing: 17577/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:29:40.58] [status] tests: 29/71, fuzzing: 17577/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:29:43.58] [status] tests: 29/71, fuzzing: 17577/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:29:46.58] [status] tests: 29/71, fuzzing: 17577/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:29:49.59] [status] tests: 29/71, fuzzing: 17577/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:29:52.59] [status] tests: 29/71, fuzzing: 17577/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:29:55.60] [status] tests: 29/71, fuzzing: 17577/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:29:58.60] [status] tests: 29/71, fuzzing: 17577/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:30:01.60] [status] tests: 29/71, fuzzing: 17577/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:30:04.61] [status] tests: 29/71, fuzzing: 17577/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:30:07.61] [status] tests: 29/71, fuzzing: 17577/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:30:10.62] [status] tests: 29/71, fuzzing: 17577/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:30:13.62] [status] tests: 29/71, fuzzing: 17577/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:30:16.63] [status] tests: 29/71, fuzzing: 17577/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:30:19.63] [status] tests: 29/71, fuzzing: 17577/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:30:22.63] [status] tests: 29/71, fuzzing: 17577/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:30:25.64] [status] tests: 29/71, fuzzing: 17577/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:30:28.64] [status] tests: 29/71, fuzzing: 17577/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:30:31.64] [status] tests: 29/71, fuzzing: 17577/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:30:34.65] [status] tests: 29/71, fuzzing: 17577/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:30:37.65] [status] tests: 29/71, fuzzing: 17577/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:30:40.66] [status] tests: 29/71, fuzzing: 17577/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:30:43.66] [status] tests: 29/71, fuzzing: 17577/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:30:46.67] [status] tests: 29/71, fuzzing: 17577/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:30:49.67] [status] tests: 29/71, fuzzing: 17577/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:30:52.67] [status] tests: 29/71, fuzzing: 17577/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:30:55.68] [status] tests: 29/71, fuzzing: 17577/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:30:58.68] [status] tests: 29/71, fuzzing: 17577/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:31:01.69] [status] tests: 29/71, fuzzing: 17577/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:31:04.69] [status] tests: 29/71, fuzzing: 17577/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:31:07.69] [status] tests: 29/71, fuzzing: 17577/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:31:10.70] [status] tests: 29/71, fuzzing: 17577/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:31:13.70] [status] tests: 29/71, fuzzing: 17577/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:31:16.71] [status] tests: 29/71, fuzzing: 17577/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:31:19.71] [status] tests: 29/71, fuzzing: 17577/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:31:22.71] [status] tests: 29/71, fuzzing: 17577/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:31:25.72] [status] tests: 29/71, fuzzing: 17577/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:31:28.72] [status] tests: 29/71, fuzzing: 17577/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:31:31.73] [status] tests: 29/71, fuzzing: 17577/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:31:34.73] [status] tests: 29/71, fuzzing: 17577/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:31:37.73] [status] tests: 29/71, fuzzing: 17577/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:31:40.74] [status] tests: 29/71, fuzzing: 17577/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:31:43.74] [status] tests: 29/71, fuzzing: 17577/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:31:46.74] [status] tests: 29/71, fuzzing: 17577/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:31:49.75] [status] tests: 29/71, fuzzing: 17577/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:31:52.75] [status] tests: 29/71, fuzzing: 17577/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:31:55.76] [status] tests: 29/71, fuzzing: 17577/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:31:58.76] [status] tests: 29/71, fuzzing: 17577/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:32:01.77] [status] tests: 29/71, fuzzing: 18486/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:32:04.77] [status] tests: 29/71, fuzzing: 19754/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:32:07.77] [status] tests: 29/71, fuzzing: 21269/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:32:09.28] [Worker 8] Test limit reached. Stopping.
[2024-09-19 17:32:10.78] [status] tests: 29/71, fuzzing: 21851/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:32:13.78] [status] tests: 29/71, fuzzing: 21851/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:32:16.78] [status] tests: 29/71, fuzzing: 21851/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:32:19.79] [status] tests: 29/71, fuzzing: 23284/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:32:22.79] [status] tests: 29/71, fuzzing: 24686/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:32:25.80] [status] tests: 29/71, fuzzing: 26375/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:32:26.71] [Worker 1] Test limit reached. Stopping.
[2024-09-19 17:32:28.80] [status] tests: 29/71, fuzzing: 26838/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:32:31.80] [status] tests: 29/71, fuzzing: 26838/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:32:34.81] [status] tests: 29/71, fuzzing: 26838/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:32:37.81] [status] tests: 29/71, fuzzing: 26838/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:32:40.81] [status] tests: 29/71, fuzzing: 26838/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:32:43.82] [status] tests: 29/71, fuzzing: 26838/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:32:46.82] [status] tests: 29/71, fuzzing: 26838/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:32:49.82] [status] tests: 29/71, fuzzing: 26838/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:32:52.82] [status] tests: 29/71, fuzzing: 26838/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:32:55.83] [status] tests: 29/71, fuzzing: 26838/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:32:58.83] [status] tests: 29/71, fuzzing: 26838/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:33:01.83] [status] tests: 29/71, fuzzing: 26838/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:33:04.83] [status] tests: 29/71, fuzzing: 26838/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:33:07.84] [status] tests: 29/71, fuzzing: 26838/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:33:10.84] [status] tests: 29/71, fuzzing: 26838/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:33:13.84] [status] tests: 29/71, fuzzing: 26838/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:33:16.84] [status] tests: 29/71, fuzzing: 26838/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:33:19.85] [status] tests: 29/71, fuzzing: 26838/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:33:22.85] [status] tests: 29/71, fuzzing: 26838/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:33:25.86] [status] tests: 29/71, fuzzing: 26838/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:33:28.86] [status] tests: 29/71, fuzzing: 26838/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:33:31.86] [status] tests: 29/71, fuzzing: 26838/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:33:34.86] [status] tests: 29/71, fuzzing: 26838/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:33:37.87] [status] tests: 29/71, fuzzing: 26838/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:33:40.87] [status] tests: 29/71, fuzzing: 26838/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:33:43.88] [status] tests: 29/71, fuzzing: 26838/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:33:46.88] [status] tests: 29/71, fuzzing: 26838/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:33:49.88] [status] tests: 29/71, fuzzing: 26838/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:33:52.88] [status] tests: 29/71, fuzzing: 26838/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:33:55.88] [status] tests: 29/71, fuzzing: 26838/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:33:58.89] [status] tests: 29/71, fuzzing: 26838/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:34:01.89] [status] tests: 29/71, fuzzing: 26838/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:34:04.89] [status] tests: 29/71, fuzzing: 26838/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:34:07.90] [status] tests: 29/71, fuzzing: 26838/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:34:10.90] [status] tests: 29/71, fuzzing: 26838/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:34:13.90] [status] tests: 29/71, fuzzing: 26838/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:34:16.90] [status] tests: 29/71, fuzzing: 26838/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:34:19.90] [status] tests: 29/71, fuzzing: 26838/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:34:22.90] [status] tests: 29/71, fuzzing: 26838/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:34:25.91] [status] tests: 29/71, fuzzing: 27685/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:34:28.91] [status] tests: 29/71, fuzzing: 29301/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:34:31.91] [status] tests: 29/71, fuzzing: 30881/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:34:32.43] [Worker 5] Test limit reached. Stopping.
[2024-09-19 17:34:34.91] [status] tests: 29/71, fuzzing: 31184/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:34:37.92] [status] tests: 29/71, fuzzing: 31184/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:34:40.92] [status] tests: 29/71, fuzzing: 31992/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:34:43.92] [status] tests: 29/71, fuzzing: 33666/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:34:46.92] [status] tests: 29/71, fuzzing: 35483/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:34:48.19] [Worker 2] Test limit reached. Stopping.
[2024-09-19 17:34:49.93] [status] tests: 29/71, fuzzing: 36180/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:34:52.93] [status] tests: 29/71, fuzzing: 36584/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:34:55.93] [status] tests: 29/71, fuzzing: 38381/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:34:58.93] [status] tests: 29/71, fuzzing: 40265/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:35:00.38] [Worker 3] Test limit reached. Stopping.
[2024-09-19 17:35:01.94] [status] tests: 29/71, fuzzing: 41162/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:35:04.94] [status] tests: 29/71, fuzzing: 41162/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:35:07.94] [status] tests: 29/71, fuzzing: 41162/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:35:10.94] [status] tests: 29/71, fuzzing: 41162/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:35:13.95] [status] tests: 29/71, fuzzing: 41162/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:35:16.95] [status] tests: 29/71, fuzzing: 41162/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:35:19.95] [status] tests: 29/71, fuzzing: 41162/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:35:22.96] [status] tests: 29/71, fuzzing: 41162/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:35:25.96] [status] tests: 29/71, fuzzing: 41162/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:35:28.96] [status] tests: 29/71, fuzzing: 41162/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:35:31.96] [status] tests: 29/71, fuzzing: 41162/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:35:34.97] [status] tests: 29/71, fuzzing: 41162/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:35:37.97] [status] tests: 29/71, fuzzing: 41162/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:35:40.97] [status] tests: 29/71, fuzzing: 41162/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:35:43.98] [status] tests: 29/71, fuzzing: 41162/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:35:46.98] [status] tests: 29/71, fuzzing: 41162/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:35:49.98] [status] tests: 29/71, fuzzing: 41162/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:35:52.99] [status] tests: 29/71, fuzzing: 41162/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:35:55.99] [status] tests: 29/71, fuzzing: 41162/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:35:58.99] [status] tests: 29/71, fuzzing: 41162/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:36:02.00] [status] tests: 29/71, fuzzing: 41162/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:36:05.00] [status] tests: 29/71, fuzzing: 41162/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:36:08.00] [status] tests: 29/71, fuzzing: 41162/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:36:11.00] [status] tests: 29/71, fuzzing: 41162/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:36:14.01] [status] tests: 29/71, fuzzing: 41162/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:36:17.01] [status] tests: 29/71, fuzzing: 41162/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:36:20.01] [status] tests: 29/71, fuzzing: 41162/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:36:23.01] [status] tests: 29/71, fuzzing: 41162/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:36:26.02] [status] tests: 29/71, fuzzing: 41162/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:36:29.02] [status] tests: 29/71, fuzzing: 41162/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:36:32.02] [status] tests: 29/71, fuzzing: 41162/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:36:35.02] [status] tests: 29/71, fuzzing: 41162/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:36:38.03] [status] tests: 29/71, fuzzing: 41162/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:36:41.03] [status] tests: 29/71, fuzzing: 41162/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:36:44.03] [status] tests: 29/71, fuzzing: 41162/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:36:47.04] [status] tests: 29/71, fuzzing: 41162/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:36:50.04] [status] tests: 29/71, fuzzing: 41162/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:36:53.04] [status] tests: 29/71, fuzzing: 41162/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:36:56.05] [status] tests: 29/71, fuzzing: 41162/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:36:59.05] [status] tests: 29/71, fuzzing: 41566/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:37:02.05] [status] tests: 29/71, fuzzing: 43373/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:37:05.06] [status] tests: 29/71, fuzzing: 45090/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:37:05.50] [Worker 4] Test limit reached. Stopping.
[2024-09-19 17:37:08.06] [status] tests: 29/71, fuzzing: 45449/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:37:11.06] [status] tests: 29/71, fuzzing: 45449/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:37:14.07] [status] tests: 29/71, fuzzing: 45449/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:37:17.07] [status] tests: 29/71, fuzzing: 45449/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:37:20.07] [status] tests: 29/71, fuzzing: 45449/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:37:23.08] [status] tests: 29/71, fuzzing: 45449/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:37:26.08] [status] tests: 29/71, fuzzing: 45449/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:37:29.08] [status] tests: 29/71, fuzzing: 45449/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:37:32.09] [status] tests: 29/71, fuzzing: 45449/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:37:35.09] [status] tests: 29/71, fuzzing: 45449/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:37:38.09] [status] tests: 29/71, fuzzing: 45449/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:37:41.09] [status] tests: 29/71, fuzzing: 45449/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:37:44.10] [status] tests: 29/71, fuzzing: 45449/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:37:47.10] [status] tests: 29/71, fuzzing: 45449/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:37:50.10] [status] tests: 29/71, fuzzing: 45449/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:37:53.11] [status] tests: 29/71, fuzzing: 45449/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:37:56.11] [status] tests: 29/71, fuzzing: 45449/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:37:59.11] [status] tests: 29/71, fuzzing: 45449/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:38:02.12] [status] tests: 29/71, fuzzing: 45449/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:38:05.12] [status] tests: 29/71, fuzzing: 45449/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:38:08.12] [status] tests: 29/71, fuzzing: 45449/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:38:11.12] [status] tests: 29/71, fuzzing: 45449/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:38:14.13] [status] tests: 29/71, fuzzing: 45449/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:38:17.13] [status] tests: 29/71, fuzzing: 47284/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:38:20.13] [status] tests: 29/71, fuzzing: 49162/50000, values: [], cov: 31653, corpus: 34
[2024-09-19 17:38:22.09] [Worker 0] Test limit reached. Stopping.
[2024-09-19 17:38:22.09] [status] tests: 29/71, fuzzing: 50485/50000, values: [], cov: 31653, corpus: 34
borrower_setInterestBatchManager(uint256,uint256,uint256,uint256): failed!
  Call sequence:
    CryticTester.target_switchBranch()
    CryticTester.target_switchBranch()
    CryticTester.target_changePrice(2041684395015745962565)
    CryticTester.target_clamped_openTrove(1522014490312189279477950124351427740349044088,16344021393122608895281033697716146711754,11850003058476939112992142160038037656403723375310688368100168868656517,17629206493597926589122501956147303205695111,361768746156222550367003777515619018993958)
    CryticTester.borrower_setInterestBatchManager(0,0,0,0) Time delay: 1 seconds Block delay: 1

Traces:
call ******************************************::[1mgetTroveIdsCountXD() [1m(/recon/contracts/src/test/recon/Setup.sol:94)
  (1)
call ******************************************::[1mgetTroveFromTroveIdsArrayXD(0) [1m(/recon/contracts/src/test/recon/Setup.sol:97)
  (26802468881051830246333047020546859964896986467110827088175505586445302238804)
call ******************************************::[1mgetCollateralXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:40)
  (0)
call ******************************************::[1mbalanceOfXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:41)
  (18928802283853288246)
call ******************************************::[1mbalanceOfXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:42)
  (100000000000000000000)
call ******************************************::[1mgetTroveIdsCountXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:48)
  (1)
call ******************************************::[1mgetTroveFromTroveIdsArrayXD(0) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:50)
  (26802468881051830246333047020546859964896986467110827088175505586445302238804)
call ******************************************::[1mgetBatchXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:51)
  (0, 0, 0, 0, 0, 0, 0, 0)
call ******************************************::[1mgetLatestTroveDataXD(26802468881051830246333047020546859964896986467110827088175505586445302238804) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:63)
  ((2030581878876385075819, 81033697716146711754, 0, 0, 61565681397772, 2030581817310703678047, 956147303205695111, 1941535328560148787103126271784985928217, 0, 1524958792))
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mgetEntireSystemDebtXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:65)
 call ******************************************::[1mgetBoldDebtXD() [1m<no source map>
    (2030581878876385075820)
 call 0x01f816b03111C36098D6394f7Bb6e2538564E351::[1mgetBoldDebtXD() [1m<no source map>
    (0)
  (2030581878876385075820)
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mapplyPendingDebtXD(26802468881051830246333047020546859964896986467110827088175505586445302238804, 0, 1) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:69)
 call ******************************************::[1mgetTroveStatusXD(26802468881051830246333047020546859964896986467110827088175505586445302238804) [1m<no source map>
    (1)
 call ******************************************::[1mgetLatestTroveDataXD(26802468881051830246333047020546859964896986467110827088175505586445302238804) [1m<no source map>
    ((2030581878876385075819, 81033697716146711754, 0, 0, 61565681397772, 2030581817310703678047, 956147303205695111, 1941535328560148787103126271784985928217, 0, 1524958792))
 call ******************************************::0xb5a672c23b41a8e845a57fed0c03d9eb78ca4b981c727966e694f6c89fce57aefada0e54000000000000000000000000000000000000000000000004649186bce74eacca00000000000000000000000000000000000000000000006e13fc045aac34626b00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000005b4a61d33e56a0d2f734ab12bfcb6606d00000000000000000000000000000005b4a61a4ce78fb890ec156e0d4c59a2190000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000 [1m<no source map>
   emit TroveUpdated(_troveId=26802468881051830246333047020546859964896986467110827088175505586445302238804, _debt=2030581878876385075819, _coll=81033697716146711754, _stake=81033697716146711754, _annualInterestRate=956147303205695111, _snapshotOfTotalCollRedist=0, _snapshotOfTotalDebtRedist=0) [1m<no source map>
   emit TroveOperation(_troveId=26802468881051830246333047020546859964896986467110827088175505586445302238804, _operation=4, _annualInterestRate=956147303205695111, _debtIncreaseFromRedist=0, _debtIncreaseFromUpfrontFee=0, _debtChangeFromOperation=0, _collIncreaseFromRedist=0, _collChangeFromOperation=0) [1m<no source map>
    0x
 call ******************************************::0x71d4eb2100000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000005b4a61d33e56a0d2f734ab12bfcb6606d00000000000000000000000000000005b4a61a4ce78fb890ec156e0d4c59a21900000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000 [1m<no source map>
   call 0xbC9E133D7fcbB155D53ce908245bCB5bb4dF19e4::[1mmintXD([1mLogging, 17238390791377) [1m<no source map>
     emit Transfer(from=0x0000000000000000000000000000000000000000, to=[1mLogging, value=17238390791377) [1m<no source map>
      0x
   call 0xbC9E133D7fcbB155D53ce908245bCB5bb4dF19e4::[1mmintXD(0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a, 44327290606396) [1m<no source map>
     emit Transfer(from=0x0000000000000000000000000000000000000000, to=0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a, value=44327290606396) [1m<no source map>
      0x
   call 0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a::[1mtriggerBoldRewardsXD(44327290606396) [1m<no source map>
      0x
    0x
 call ******************************************::[1mgetTroveStatusXD(26802468881051830246333047020546859964896986467110827088175505586445302238804) [1m<no source map>
    (1)
  0x
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mMCRXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:138)
  (1100000000000000000)
call [1mPriceFeedTestnet::getPrice()() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:139)
  (2041684395015745962565)
emit Log(BA-01: For all operations, weightedRecordedDebt is equal to the sum of trove debt * rate) [1m(/recon/contracts/lib/chimera/src/CryticAsserts.sol:39)

check_removeFromBatch(): passing
borrower_adjustTroveInterestRate(uint256,uint256,uint256,uint256,uint256): failed!
  Call sequence:
    CryticTester.target_switchBranch()
    CryticTester.target_switchBranch()
    CryticTester.target_changePrice(1973221613012002699721)
    CryticTester.target_clamped_openTrove(1113698475233351089056978755765484761123,4356142153949614276299,5641328349084110751617504254411614013214179330489730055196652934710518428,813951125314596765397502116630452995631842002188182952380,2180026779081580366227395345684043)
    *wait* Time delay: 1 seconds Block delay: 1
    CryticTester.borrower_adjustTroveInterestRate(0,0,0,0,0)

Traces:
call ******************************************::[1mgetTroveIdsCountXD() [1m(/recon/contracts/src/test/recon/Setup.sol:94)
  (1)
call ******************************************::[1mgetTroveFromTroveIdsArrayXD(0) [1m(/recon/contracts/src/test/recon/Setup.sol:97)
  (106324404231948620474488265026585870180244924628155821024776299987688533875586)
call ******************************************::[1mgetCollateralXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:40)
  (0)
call ******************************************::[1mbalanceOfXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:41)
  (43820346050385723701)
call ******************************************::[1mbalanceOfXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:42)
  (100000000000000000000)
call ******************************************::[1mgetTroveIdsCountXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:48)
  (1)
call ******************************************::[1mgetTroveFromTroveIdsArrayXD(0) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:50)
  (106324404231948620474488265026585870180244924628155821024776299987688533875586)
call ******************************************::[1mgetBatchXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:51)
  (0, 0, 0, 0, 0, 0, 0, 0)
call ******************************************::[1mgetLatestTroveDataXD(106324404231948620474488265026585870180244924628155821024776299987688533875586) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:63)
  ((2003840615792109081198, 56142153949614276299, 0, 0, 53501970389968, 2003840562290138691230, 842002188182952380, 1687238138218054468850635535330613627400, 0, 1524958792))
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mgetEntireSystemDebtXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:65)
 call ******************************************::[1mgetBoldDebtXD() [1m<no source map>
    (2003840615792109081199)
 call 0x01f816b03111C36098D6394f7Bb6e2538564E351::[1mgetBoldDebtXD() [1m<no source map>
    (0)
  (2003840615792109081199)
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mapplyPendingDebtXD(106324404231948620474488265026585870180244924628155821024776299987688533875586, 0, 1) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:69)
 call ******************************************::[1mgetTroveStatusXD(106324404231948620474488265026585870180244924628155821024776299987688533875586) [1m<no source map>
    (1)
 call ******************************************::[1mgetLatestTroveDataXD(106324404231948620474488265026585870180244924628155821024776299987688533875586) [1m<no source map>
    ((2003840615792109081198, 56142153949614276299, 0, 0, 53501970389968, 2003840562290138691230, 842002188182952380, 1687238138218054468850635535330613627400, 0, 1524958792))
 call ******************************************::0xb5a672c2eb117aed74049990fa4a471e4e6f680363ff7e867a4e558e2a1fdb49af2d5b820000000000000000000000000000000000000000000000030b20ff90340d7ecb00000000000000000000000000000000000000000000006ca0dff742a4aad26e00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000004f5564109348a831c449348e5e01146c800000000000000000000000000000004f5563ed09c1844ffaceeb240e3f78a080000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000 [1m<no source map>
   emit TroveUpdated(_troveId=106324404231948620474488265026585870180244924628155821024776299987688533875586, _debt=2003840615792109081198, _coll=56142153949614276299, _stake=56142153949614276299, _annualInterestRate=842002188182952380, _snapshotOfTotalCollRedist=0, _snapshotOfTotalDebtRedist=0) [1m<no source map>
   emit TroveOperation(_troveId=106324404231948620474488265026585870180244924628155821024776299987688533875586, _operation=4, _annualInterestRate=842002188182952380, _debtIncreaseFromRedist=0, _debtIncreaseFromUpfrontFee=0, _debtChangeFromOperation=0, _collIncreaseFromRedist=0, _collChangeFromOperation=0) [1m<no source map>
    0x
 call ******************************************::0x71d4eb2100000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000004f5564109348a831c449348e5e01146c800000000000000000000000000000004f5563ed09c1844ffaceeb240e3f78a0800000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000 [1m<no source map>
   call 0xbC9E133D7fcbB155D53ce908245bCB5bb4dF19e4::[1mmintXD([1mLogging, 14980551709192) [1m<no source map>
     emit Transfer(from=0x0000000000000000000000000000000000000000, to=[1mLogging, value=14980551709192) [1m<no source map>
      0x
   call 0xbC9E133D7fcbB155D53ce908245bCB5bb4dF19e4::[1mmintXD(0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a, 38521418680777) [1m<no source map>
     emit Transfer(from=0x0000000000000000000000000000000000000000, to=0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a, value=38521418680777) [1m<no source map>
      0x
   call 0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a::[1mtriggerBoldRewardsXD(38521418680777) [1m<no source map>
      0x
    0x
 call ******************************************::[1mgetTroveStatusXD(106324404231948620474488265026585870180244924628155821024776299987688533875586) [1m<no source map>
    (1)
  0x
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mMCRXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:138)
  (1100000000000000000)
call [1mPriceFeedTestnet::getPrice()() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:139)
  (1973221613012002699721)
emit Log(BA-01: For all operations, weightedRecordedDebt is equal to the sum of trove debt * rate) [1m(/recon/contracts/lib/chimera/src/CryticAsserts.sol:39)

borrower_applyPendingDebt(uint256,uint256,uint256): failed!
  Call sequence:
    CryticTester.target_switchBranch()
    CryticTester.target_switchBranch()
    CryticTester.target_changePrice(2014381690261125649259)
    CryticTester.target_clamped_openTrove(1651034305931391248623990687528,5618759580421918448831554674625,264191505354654582241682895323961981641452782571409693543773134813190425114,38302098780302916,1348231484793424897538064)
    *wait* Time delay: 1 seconds Block delay: 1
    CryticTester.borrower_applyPendingDebt(0,0,0)

Traces:
call ******************************************::[1mgetTroveIdsCountXD() [1m(/recon/contracts/src/test/recon/Setup.sol:94)
  (1)
call ******************************************::[1mgetTroveFromTroveIdsArrayXD(0) [1m(/recon/contracts/src/test/recon/Setup.sol:97)
  (23257859129465791338464903579234880249150851184398093853667353556078438308715)
call ******************************************::[1mgetCollateralXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:40)
  (0)
call ******************************************::[1mbalanceOfXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:41)
  (78044051168445325375)
call ******************************************::[1mbalanceOfXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:42)
  (100000000000000000000)
call ******************************************::[1mgetTroveIdsCountXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:48)
  (1)
call ******************************************::[1mgetTroveFromTroveIdsArrayXD(0) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:50)
  (23257859129465791338464903579234880249150851184398093853667353556078438308715)
call ******************************************::[1mgetBatchXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:51)
  (0, 0, 0, 0, 0, 0, 0, 0)
call ******************************************::[1mgetLatestTroveDataXD(23257859129465791338464903579234880249150851184398093853667353556078438308715) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:63)
  ((2000443318548529402731, 21918448831554674625, 0, 0, 2429641600024, 2000443316118887802707, 38302098780302916, 76621177498382373130566134415104793612, 0, 1524958792))
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mgetEntireSystemDebtXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:65)
 call ******************************************::[1mgetBoldDebtXD() [1m<no source map>
    (2000443318548529402732)
 call 0x01f816b03111C36098D6394f7Bb6e2538564E351::[1mgetBoldDebtXD() [1m<no source map>
    (0)
  (2000443318548529402732)
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mapplyPendingDebtXD(23257859129465791338464903579234880249150851184398093853667353556078438308715, 0, 1) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:69)
 call ******************************************::[1mgetTroveStatusXD(23257859129465791338464903579234880249150851184398093853667353556078438308715) [1m<no source map>
    (1)
 call ******************************************::[1mgetLatestTroveDataXD(23257859129465791338464903579234880249150851184398093853667353556078438308715) [1m<no source map>
    ((2000443318548529402731, 21918448831554674625, 0, 0, 2429641600024, 2000443316118887802707, 38302098780302916, 76621177498382373130566134415104793612, 0, 1524958792))
 call ******************************************::0xb5a672c2336b7b55592e31494dbf257ba8cfd91d7d7fff92181bbfb85559e945692adb6b000000000000000000000000000000000000000000000001302df91558645fc100000000000000000000000000000000000000000000006c71ba576abcafd36b0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000039a4b3b838dfed93ce85581753f7e26c0000000000000000000000000000000039a4b3b70c2e312a735718d27159dc0c0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000 [1m<no source map>
   emit TroveUpdated(_troveId=23257859129465791338464903579234880249150851184398093853667353556078438308715, _debt=2000443318548529402731, _coll=21918448831554674625, _stake=21918448831554674625, _annualInterestRate=38302098780302916, _snapshotOfTotalCollRedist=0, _snapshotOfTotalDebtRedist=0) [1m<no source map>
   emit TroveOperation(_troveId=23257859129465791338464903579234880249150851184398093853667353556078438308715, _operation=4, _annualInterestRate=38302098780302916, _debtIncreaseFromRedist=0, _debtIncreaseFromUpfrontFee=0, _debtChangeFromOperation=0, _collIncreaseFromRedist=0, _collChangeFromOperation=0) [1m<no source map>
    0x
 call ******************************************::0x71d4eb210000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000039a4b3b838dfed93ce85581753f7e26c0000000000000000000000000000000039a4b3b70c2e312a735718d27159dc0c00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000 [1m<no source map>
   call 0xbC9E133D7fcbB155D53ce908245bCB5bb4dF19e4::[1mmintXD([1mLogging, 680299648007) [1m<no source map>
     emit Transfer(from=0x0000000000000000000000000000000000000000, to=[1mLogging, value=680299648007) [1m<no source map>
      0x
   call 0xbC9E133D7fcbB155D53ce908245bCB5bb4dF19e4::[1mmintXD(0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a, 1749341952018) [1m<no source map>
     emit Transfer(from=0x0000000000000000000000000000000000000000, to=0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a, value=1749341952018) [1m<no source map>
      0x
   call 0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a::[1mtriggerBoldRewardsXD(1749341952018) [1m<no source map>
      0x
    0x
 call ******************************************::[1mgetTroveStatusXD(23257859129465791338464903579234880249150851184398093853667353556078438308715) [1m<no source map>
    (1)
  0x
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mMCRXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:138)
  (1100000000000000000)
call [1mPriceFeedTestnet::getPrice()() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:139)
  (2014381690261125649259)
emit Log(BA-01: For all operations, weightedRecordedDebt is equal to the sum of trove debt * rate) [1m(/recon/contracts/lib/chimera/src/CryticAsserts.sol:39)

check_adjustTrove(): passing
property_GL01(uint256,uint256): passing
borrower_repayBold(uint256,uint256): failed!
  Call sequence:
    CryticTester.target_changePrice(2239623662133908660723)
    CryticTester.target_clamped_openTrove(0,1345286845958212144,5696355998354657911993218062825971512046377035898503791,5990816173025748237422497259,1032787723990916540)
    CryticTester.borrower_removeFromBatch(0,0,0,0,0)
    CryticTester.target_switchBranch()
    CryticTester.borrower_repayBold(0,0)

Traces:
call ******************************************::[1mgetTroveIdsCountXD() [1m(/recon/contracts/src/test/recon/Setup.sol:94)
  (0)
call ******************************************::[1mgetCollateralXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:40)
  (0)
call ******************************************::[1mbalanceOfXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:41)
  (98617213154041787856)
call ******************************************::[1mbalanceOfXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:42)
  (100000000000000000000)
call ******************************************::[1mgetTroveIdsCountXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:48)
  (0)
emit Log(CP-01: Total debt == SUM(userDebt)) [1m(/recon/contracts/lib/chimera/src/CryticAsserts.sol:39)

borrower_setBatchManagerAnnualInterestRate(uint128,uint256,uint256,uint256): failed!
  Call sequence:
    CryticTester.target_changePrice(2125519770245858739782)
    CryticTester.target_clamped_openTrove(0,1455952784919110401,897839369503823739856761467924715775714824884062161138,60017553209949022020,906915856208848402)
    CryticTester.borrower_setBatchManagerAnnualInterestRate(0,0,0,0) Time delay: 1 seconds Block delay: 1

Traces:
call ******************************************::[1mgetCollateralXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:40)
  (0)
call ******************************************::[1mbalanceOfXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:41)
  (98506547215080889599)
call ******************************************::[1mbalanceOfXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:42)
  (100000000000000000000)
call ******************************************::[1mgetTroveIdsCountXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:48)
  (1)
call ******************************************::[1mgetTroveFromTroveIdsArrayXD(0) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:50)
  (8648653994422793423523514607345318943026410206026333401532383970483429052211)
call ******************************************::[1mgetBatchXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:51)
  (0, 0, 0, 0, 0, 0, 0, 0)
call ******************************************::[1mgetLatestTroveDataXD(8648653994422793423523514607345318943026410206026333401532383970483429052211) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:63)
  ((2062716600738064140574, 1455952784919110401, 0, 0, 1148125873222, 2062716599589938267352, 17553209949022020, 36207297537934974734301918725695091040, 0, 1524958792))
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mgetEntireSystemDebtXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:65)
 call ******************************************::[1mgetBoldDebtXD() [1m<no source map>
    (2062716600738064140575)
 call 0x01f816b03111C36098D6394f7Bb6e2538564E351::[1mgetBoldDebtXD() [1m<no source map>
    (0)
  (2062716600738064140575)
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mapplyPendingDebtXD(8648653994422793423523514607345318943026410206026333401532383970483429052211, 0, 1) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:69)
 call ******************************************::[1mgetTroveStatusXD(8648653994422793423523514607345318943026410206026333401532383970483429052211) [1m<no source map>
    (1)
 call ******************************************::[1mgetLatestTroveDataXD(8648653994422793423523514607345318943026410206026333401532383970483429052211) [1m<no source map>
    ((2062716600738064140574, 1455952784919110401, 0, 0, 1148125873222, 2062716599589938267352, 17553209949022020, 36207297537934974734301918725695091040, 0, 1524958792))
 call ******************************************::0xb5a672c2131ef6f5e9ad7f4b2efd52cffeb247a384f286ced0b2014c98237c55de760f33000000000000000000000000000000000000000000000000143495580ad8230100000000000000000000000000000000000000000000006fd1f17ba15d90891e000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001b3d45986fbd329b009d4184f9647df8000000000000000000000000000000001b3d45982e9ec87efd4f4a407f9031600000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000 [1m<no source map>
   emit TroveUpdated(_troveId=8648653994422793423523514607345318943026410206026333401532383970483429052211, _debt=2062716600738064140574, _coll=1455952784919110401, _stake=1455952784919110401, _annualInterestRate=17553209949022020, _snapshotOfTotalCollRedist=0, _snapshotOfTotalDebtRedist=0) [1m<no source map>
   emit TroveOperation(_troveId=8648653994422793423523514607345318943026410206026333401532383970483429052211, _operation=4, _annualInterestRate=17553209949022020, _debtIncreaseFromRedist=0, _debtIncreaseFromUpfrontFee=0, _debtChangeFromOperation=0, _collIncreaseFromRedist=0, _collChangeFromOperation=0) [1m<no source map>
    0x
 call ******************************************::0x71d4eb21000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001b3d45986fbd329b009d4184f9647df8000000000000000000000000000000001b3d45982e9ec87efd4f4a407f90316000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000 [1m<no source map>
   call 0xbC9E133D7fcbB155D53ce908245bCB5bb4dF19e4::[1mmintXD([1mLogging, 321475244503) [1m<no source map>
     emit Transfer(from=0x0000000000000000000000000000000000000000, to=[1mLogging, value=321475244503) [1m<no source map>
      0x
   call 0xbC9E133D7fcbB155D53ce908245bCB5bb4dF19e4::[1mmintXD(0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a, 826650628720) [1m<no source map>
     emit Transfer(from=0x0000000000000000000000000000000000000000, to=0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a, value=826650628720) [1m<no source map>
      0x
   call 0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a::[1mtriggerBoldRewardsXD(826650628720) [1m<no source map>
      0x
    0x
 call ******************************************::[1mgetTroveStatusXD(8648653994422793423523514607345318943026410206026333401532383970483429052211) [1m<no source map>
    (1)
  0x
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mMCRXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:138)
  (1100000000000000000)
call [1mPriceFeedTestnet::getPrice()() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:139)
  (2125519770245858739782)
emit Log(BA-01: For all operations, weightedRecordedDebt is equal to the sum of trove debt * rate) [1m(/recon/contracts/lib/chimera/src/CryticAsserts.sol:39)

check_withdrawBold(): passing
property_BT01(): passing
check_addColl(): passing
check_lowerBatchManagementFee(): passing
check_registerBatchManager(): passing
borrower_claimCollateral(): failed!
  Call sequence:
    CryticTester.target_switchBranch()
    CryticTester.target_switchBranch()
    CryticTester.target_changePrice(2079377620832671811807)
    CryticTester.target_clamped_openTrove(6858124276357,35755714733632980106190,324631700766831631697788062382165135626861828406677334396820253392006,5005457013547521005027239326321465,245335990916441022)
    CryticTester.borrower_claimCollateral() Time delay: 1 seconds Block delay: 1

Traces:
call ******************************************::[1mgetCollateralXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:40)
  (0)
call ******************************************::[1mbalanceOfXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:41)
  (44247766367019893810)
call ******************************************::[1mbalanceOfXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:42)
  (100000000000000000000)
call ******************************************::[1mgetTroveIdsCountXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:48)
  (1)
call ******************************************::[1mgetTroveFromTroveIdsArrayXD(0) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:50)
  (48334186358033063382202880034696335217732608035113036169645192754539428746865)
call ******************************************::[1mgetBatchXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:51)
  (0, 0, 0, 0, 0, 0, 0, 0)
call ******************************************::[1mgetLatestTroveDataXD(48334186358033063382202880034696335217732608035113036169645192754539428746865) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:63)
  ((2029360747745189742057, 55714733632980106190, 0, 0, 323505902991, 2029360747421683839066, 5027239326321465, 10202082156731410553561161904841351690, 0, 1524958792))
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mgetEntireSystemDebtXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:65)
 call ******************************************::[1mgetBoldDebtXD() [1m<no source map>
    (2029360747745189742058)
 call 0x01f816b03111C36098D6394f7Bb6e2538564E351::[1mgetBoldDebtXD() [1m<no source map>
    (0)
  (2029360747745189742058)
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mapplyPendingDebtXD(48334186358033063382202880034696335217732608035113036169645192754539428746865, 0, 1) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:69)
 call ******************************************::[1mgetTroveStatusXD(48334186358033063382202880034696335217732608035113036169645192754539428746865) [1m<no source map>
    (1)
 call ******************************************::[1mgetLatestTroveDataXD(48334186358033063382202880034696335217732608035113036169645192754539428746865) [1m<no source map>
    ((2029360747745189742057, 55714733632980106190, 0, 0, 323505902991, 2029360747421683839066, 5027239326321465, 10202082156731410553561161904841351690, 0, 1524958792))
 call ******************************************::0xb5a672c26adc2e1546e1cce29ea7852daa56730694ee686a7f7181f70d5682d3fe12967100000000000000000000000000000000000000000000000305327f0cc549bbce00000000000000000000000000000000000000000000006e0309b00f6922c5e90000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000007acd97c43eaae1bfa3a7184299613e10000000000000000000000000000000007acd97c3ea966e14cc02ff73dd0e20a0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000 [1m<no source map>
   emit TroveUpdated(_troveId=48334186358033063382202880034696335217732608035113036169645192754539428746865, _debt=2029360747745189742057, _coll=55714733632980106190, _stake=55714733632980106190, _annualInterestRate=5027239326321465, _snapshotOfTotalCollRedist=0, _snapshotOfTotalDebtRedist=0) [1m<no source map>
   emit TroveOperation(_troveId=48334186358033063382202880034696335217732608035113036169645192754539428746865, _operation=4, _annualInterestRate=5027239326321465, _debtIncreaseFromRedist=0, _debtIncreaseFromUpfrontFee=0, _debtChangeFromOperation=0, _collIncreaseFromRedist=0, _collChangeFromOperation=0) [1m<no source map>
    0x
 call ******************************************::0x71d4eb210000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000007acd97c43eaae1bfa3a7184299613e10000000000000000000000000000000007acd97c3ea966e14cc02ff73dd0e20a00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000 [1m<no source map>
   call 0xbC9E133D7fcbB155D53ce908245bCB5bb4dF19e4::[1mmintXD([1mLogging, 90581652838) [1m<no source map>
     emit Transfer(from=0x0000000000000000000000000000000000000000, to=[1mLogging, value=90581652838) [1m<no source map>
      0x
   call 0xbC9E133D7fcbB155D53ce908245bCB5bb4dF19e4::[1mmintXD(0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a, 232924250154) [1m<no source map>
     emit Transfer(from=0x0000000000000000000000000000000000000000, to=0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a, value=232924250154) [1m<no source map>
      0x
   call 0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a::[1mtriggerBoldRewardsXD(232924250154) [1m<no source map>
      0x
    0x
 call ******************************************::[1mgetTroveStatusXD(48334186358033063382202880034696335217732608035113036169645192754539428746865) [1m<no source map>
    (1)
  0x
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mMCRXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:138)
  (1100000000000000000)
call [1mPriceFeedTestnet::getPrice()() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:139)
  (2079377620832671811807)
emit Log(BA-01: For all operations, weightedRecordedDebt is equal to the sum of trove debt * rate) [1m(/recon/contracts/lib/chimera/src/CryticAsserts.sol:39)

borrower_openTroveAndJoinInterestBatchManager(uint256,uint256,uint256,uint256,uint256,address,uint256,address,address,address): failed!
  Call sequence:
    CryticTester.target_changePrice(2061528955591962753246)
    CryticTester.target_clamped_openTrove(35027320721212832153665,143413082684542067290024,22228202720911931966966824654361385761017252907816002779821072,2570281327816329934312639,13516083894331701812)
    *wait* Time delay: 1 seconds Block delay: 1
    CryticTester.borrower_openTroveAndJoinInterestBatchManager(0,0,0,0,0,0x0,0,0x0,0x0,0x0)

Traces:
call ******************************************::[1mgetCollateralXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:40)
  (0)
call ******************************************::[1mbalanceOfXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:41)
  (86879815457932709976)
call ******************************************::[1mbalanceOfXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:42)
  (100000000000000000000)
call ******************************************::[1mgetTroveIdsCountXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:48)
  (1)
call ******************************************::[1mgetTroveFromTroveIdsArrayXD(0) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:50)
  (96309894041646719234585528859257823022079595181424258137984156807477317216538)
call ******************************************::[1mgetBatchXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:51)
  (0, 0, 0, 0, 0, 0, 0, 0)
call ******************************************::[1mgetLatestTroveDataXD(96309894041646719234585528859257823022079595181424258137984156807477317216538) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:63)
  ((2013464196614587417913, 13082684542067290024, 0, 0, 20929935201901, 2013464175684652216012, 327816329934312639, 660046436527158778610655903687369775668, 0, 1524958792))
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mgetEntireSystemDebtXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:65)
 call ******************************************::[1mgetBoldDebtXD() [1m<no source map>
    (2013464196614587417914)
 call 0x01f816b03111C36098D6394f7Bb6e2538564E351::[1mgetBoldDebtXD() [1m<no source map>
    (0)
  (2013464196614587417914)
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mapplyPendingDebtXD(96309894041646719234585528859257823022079595181424258137984156807477317216538, 0, 1) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:69)
 call ******************************************::[1mgetTroveStatusXD(96309894041646719234585528859257823022079595181424258137984156807477317216538) [1m<no source map>
    (1)
 call ******************************************::[1mgetLatestTroveDataXD(96309894041646719234585528859257823022079595181424258137984156807477317216538) [1m<no source map>
    ((2013464196614587417913, 13082684542067290024, 0, 0, 20929935201901, 2013464175684652216012, 327816329934312639, 660046436527158778610655903687369775668, 0, 1524958792))
 call ******************************************::0xb5a672c2d4ed77e183678775da6f6b74a6d59317e89eb8265453a7fd00d0352b2d9e291a000000000000000000000000000000000000000000000000b58f0846a66a2fa800000000000000000000000000000000000000000000006d266dcb07cbc0053900000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001f090522d1b90dc6e8d513bdf2fa6d98700000000000000000000000000000001f09051d681ea4c992a2bcc6519135a340000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000 [1m<no source map>
   emit TroveUpdated(_troveId=96309894041646719234585528859257823022079595181424258137984156807477317216538, _debt=2013464196614587417913, _coll=13082684542067290024, _stake=13082684542067290024, _annualInterestRate=327816329934312639, _snapshotOfTotalCollRedist=0, _snapshotOfTotalDebtRedist=0) [1m<no source map>
   emit TroveOperation(_troveId=96309894041646719234585528859257823022079595181424258137984156807477317216538, _operation=4, _annualInterestRate=327816329934312639, _debtIncreaseFromRedist=0, _debtIncreaseFromUpfrontFee=0, _debtChangeFromOperation=0, _collIncreaseFromRedist=0, _collChangeFromOperation=0) [1m<no source map>
    0x
 call ******************************************::0x71d4eb2100000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001f090522d1b90dc6e8d513bdf2fa6d98700000000000000000000000000000001f09051d681ea4c992a2bcc6519135a3400000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000 [1m<no source map>
   call 0xbC9E133D7fcbB155D53ce908245bCB5bb4dF19e4::[1mmintXD([1mLogging, 5860381856533) [1m<no source map>
     emit Transfer(from=0x0000000000000000000000000000000000000000, to=[1mLogging, value=5860381856533) [1m<no source map>
      0x
   call 0xbC9E133D7fcbB155D53ce908245bCB5bb4dF19e4::[1mmintXD(0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a, 15069553345369) [1m<no source map>
     emit Transfer(from=0x0000000000000000000000000000000000000000, to=0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a, value=15069553345369) [1m<no source map>
      0x
   call 0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a::[1mtriggerBoldRewardsXD(15069553345369) [1m<no source map>
      0x
    0x
 call ******************************************::[1mgetTroveStatusXD(96309894041646719234585528859257823022079595181424258137984156807477317216538) [1m<no source map>
    (1)
  0x
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mMCRXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:138)
  (1100000000000000000)
call [1mPriceFeedTestnet::getPrice()() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:139)
  (2061528955591962753246)
emit Log(BA-01: For all operations, weightedRecordedDebt is equal to the sum of trove debt * rate) [1m(/recon/contracts/lib/chimera/src/CryticAsserts.sol:39)

check_batchLiquidateTroves(): passing
check_setInterestIndividualDelegate(): passing
borrower_adjustUnredeemableTrove(uint256,uint256,bool,uint256,bool,uint256,uint256,uint256): failed!
  Call sequence:
    CryticTester.target_switchBranch()
    CryticTester.target_switchBranch()
    CryticTester.target_changePrice(2033523209270282109368)
    CryticTester.target_clamped_openTrove(84218272695074366428434,1566650117344363592,44396129165499340957847851227767117128111704012520897243,5003606344240068,259638664310044632)
    *wait* Time delay: 1 seconds Block delay: 1
    CryticTester.borrower_adjustUnredeemableTrove(0,0,false,0,false,0,0,0)

Traces:
call ******************************************::[1mgetTroveIdsCountXD() [1m(/recon/contracts/src/test/recon/Setup.sol:94)
  (1)
call ******************************************::[1mgetTroveFromTroveIdsArrayXD(0) [1m(/recon/contracts/src/test/recon/Setup.sol:97)
  (26359449758992656897041352863529237173815097058349709754605798346198439329556)
call ******************************************::[1mgetCollateralXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:40)
  (0)
call ******************************************::[1mbalanceOfXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:41)
  (98395849882655636408)
call ******************************************::[1mbalanceOfXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:42)
  (100000000000000000000)
call ******************************************::[1mgetTroveIdsCountXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:48)
  (1)
call ******************************************::[1mgetTroveFromTroveIdsArrayXD(0) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:50)
  (26359449758992656897041352863529237173815097058349709754605798346198439329556)
call ******************************************::[1mgetBatchXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:51)
  (0, 0, 0, 0, 0, 0, 0, 0)
call ******************************************::[1mgetLatestTroveDataXD(26359449758992656897041352863529237173815097058349709754605798346198439329556) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:63)
  ((2020045394484812058361, 1566650117344363592, 0, 0, 320507101404, 2020045394164304956957, 5003606344240068, 10107511949893445118666928497614753076, 0, 1524958792))
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mgetEntireSystemDebtXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:65)
 call ******************************************::[1mgetBoldDebtXD() [1m<no source map>
    (2020045394484812058362)
 call 0x01f816b03111C36098D6394f7Bb6e2538564E351::[1mgetBoldDebtXD() [1m<no source map>
    (0)
  (2020045394484812058362)
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mapplyPendingDebtXD(26359449758992656897041352863529237173815097058349709754605798346198439329556, 0, 1) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:69)
 call ******************************************::[1mgetTroveStatusXD(26359449758992656897041352863529237173815097058349709754605798346198439329556) [1m<no source map>
    (1)
 call ******************************************::[1mgetLatestTroveDataXD(26359449758992656897041352863529237173815097058349709754605798346198439329556) [1m<no source map>
    ((2020045394484812058361, 1566650117344363592, 0, 0, 320507101404, 2020045394164304956957, 5003606344240068, 10107511949893445118666928497614753076, 0, 1524958792))
 call ******************************************::0xb5a672c23a46eb7bbf77715b49c6bf99f2f1aff4edef0689f423c60167c2367335567f1400000000000000000000000000000000000000000000000015bddbfb33a2844800000000000000000000000000000000000000000000006d81c2e79fef0c0ef900000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000079aa2d071027312e1e20e29802aa9a400000000000000000000000000000000079aa2d06bd3e837bbe114c8e8218d340000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000 [1m<no source map>
   emit TroveUpdated(_troveId=26359449758992656897041352863529237173815097058349709754605798346198439329556, _debt=2020045394484812058361, _coll=1566650117344363592, _stake=1566650117344363592, _annualInterestRate=5003606344240068, _snapshotOfTotalCollRedist=0, _snapshotOfTotalDebtRedist=0) [1m<no source map>
   emit TroveOperation(_troveId=26359449758992656897041352863529237173815097058349709754605798346198439329556, _operation=4, _annualInterestRate=5003606344240068, _debtIncreaseFromRedist=0, _debtIncreaseFromUpfrontFee=0, _debtChangeFromOperation=0, _collIncreaseFromRedist=0, _collChangeFromOperation=0) [1m<no source map>
    0x
 call ******************************************::0x71d4eb2100000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000079aa2d071027312e1e20e29802aa9a400000000000000000000000000000000079aa2d06bd3e837bbe114c8e8218d3400000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000 [1m<no source map>
   call 0xbC9E133D7fcbB155D53ce908245bCB5bb4dF19e4::[1mmintXD([1mLogging, 89741988394) [1m<no source map>
     emit Transfer(from=0x0000000000000000000000000000000000000000, to=[1mLogging, value=89741988394) [1m<no source map>
      0x
   call 0xbC9E133D7fcbB155D53ce908245bCB5bb4dF19e4::[1mmintXD(0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a, 230765113011) [1m<no source map>
     emit Transfer(from=0x0000000000000000000000000000000000000000, to=0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a, value=230765113011) [1m<no source map>
      0x
   call 0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a::[1mtriggerBoldRewardsXD(230765113011) [1m<no source map>
      0x
    0x
 call ******************************************::[1mgetTroveStatusXD(26359449758992656897041352863529237173815097058349709754605798346198439329556) [1m<no source map>
    (1)
  0x
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mMCRXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:138)
  (1100000000000000000)
call [1mPriceFeedTestnet::getPrice()() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:139)
  (2033523209270282109368)
emit Log(BA-01: For all operations, weightedRecordedDebt is equal to the sum of trove debt * rate) [1m(/recon/contracts/lib/chimera/src/CryticAsserts.sol:39)

borrower_shutdown(): failed!
  Call sequence:
    CryticTester.target_switchBranch()
    CryticTester.target_switchBranch()
    CryticTester.target_changePrice(2130446723812172582627)
    CryticTester.target_clamped_openTrove(265530324267076692647708,1937080244906992466,11095444883174819207028507327826206153624267272160203344083561,4676537331124756137847,1025302710233410345431836)
    *wait* Time delay: 1 seconds Block delay: 1
    CryticTester.borrower_shutdown()

Traces:
call ******************************************::[1mgetCollateralXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:40)
  (0)
call ******************************************::[1mbalanceOfXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:41)
  (98025419755093007534)
call ******************************************::[1mbalanceOfXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:42)
  (100000000000000000000)
call ******************************************::[1mgetTroveIdsCountXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:48)
  (1)
call ******************************************::[1mgetTroveFromTroveIdsArrayXD(0) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:50)
  (39917040934854729753363084315727828274977297176369540582885669715662369638055)
call ******************************************::[1mgetBatchXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:51)
  (0, 0, 0, 0, 0, 0, 0, 0)
call ******************************************::[1mgetLatestTroveDataXD(39917040934854729753363084315727828274977297176369540582885669715662369638055) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:63)
  ((2049845820031831742083, 1937080244906992466, 0, 0, 34926621679579, 2049845785105210062504, 537331124756137847, 1101445941287210959772862779640509988888, 0, 1524958792))
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mgetEntireSystemDebtXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:65)
 call ******************************************::[1mgetBoldDebtXD() [1m<no source map>
    (2049845820031831742084)
 call 0x01f816b03111C36098D6394f7Bb6e2538564E351::[1mgetBoldDebtXD() [1m<no source map>
    (0)
  (2049845820031831742084)
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mapplyPendingDebtXD(39917040934854729753363084315727828274977297176369540582885669715662369638055, 0, 1) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:69)
 call ******************************************::[1mgetTroveStatusXD(39917040934854729753363084315727828274977297176369540582885669715662369638055) [1m<no source map>
    (1)
 call ******************************************::[1mgetLatestTroveDataXD(39917040934854729753363084315727828274977297176369540582885669715662369638055) [1m<no source map>
    ((2049845820031831742083, 1937080244906992466, 0, 0, 34926621679579, 2049845785105210062504, 537331124756137847, 1101445941287210959772862779640509988888, 0, 1524958792))
 call ******************************************::0xb5a672c258403e99872e1d616cceb736b6a0021d1b57c7bb2c7887a2ea18b7828c82c6a70000000000000000000000000000000000000000000000001ae1e4383b1ba35200000000000000000000000000000000000000000000006f1f5348c534a19683000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000033ca2c6919bb8f9c3c82281230b27cbe5000000000000000000000000000000033ca2c5a4bbc151401eaae0fa34f980180000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000 [1m<no source map>
   emit TroveUpdated(_troveId=39917040934854729753363084315727828274977297176369540582885669715662369638055, _debt=2049845820031831742083, _coll=1937080244906992466, _stake=1937080244906992466, _annualInterestRate=537331124756137847, _snapshotOfTotalCollRedist=0, _snapshotOfTotalDebtRedist=0) [1m<no source map>
   emit TroveOperation(_troveId=39917040934854729753363084315727828274977297176369540582885669715662369638055, _operation=4, _annualInterestRate=537331124756137847, _debtIncreaseFromRedist=0, _debtIncreaseFromUpfrontFee=0, _debtChangeFromOperation=0, _collIncreaseFromRedist=0, _collChangeFromOperation=0) [1m<no source map>
    0x
 call ******************************************::0x71d4eb21000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000033ca2c6919bb8f9c3c82281230b27cbe5000000000000000000000000000000033ca2c5a4bbc151401eaae0fa34f9801800000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000 [1m<no source map>
   call 0xbC9E133D7fcbB155D53ce908245bCB5bb4dF19e4::[1mmintXD([1mLogging, 9779454070283) [1m<no source map>
     emit Transfer(from=0x0000000000000000000000000000000000000000, to=[1mLogging, value=9779454070283) [1m<no source map>
      0x
   call 0xbC9E133D7fcbB155D53ce908245bCB5bb4dF19e4::[1mmintXD(0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a, 25147167609297) [1m<no source map>
     emit Transfer(from=0x0000000000000000000000000000000000000000, to=0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a, value=25147167609297) [1m<no source map>
      0x
   call 0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a::[1mtriggerBoldRewardsXD(25147167609297) [1m<no source map>
      0x
    0x
 call ******************************************::[1mgetTroveStatusXD(39917040934854729753363084315727828274977297176369540582885669715662369638055) [1m<no source map>
    (1)
  0x
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mMCRXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:138)
  (1100000000000000000)
call [1mPriceFeedTestnet::getPrice()() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:139)
  (2130446723812172582627)
emit Log(BA-01: For all operations, weightedRecordedDebt is equal to the sum of trove debt * rate) [1m(/recon/contracts/lib/chimera/src/CryticAsserts.sol:39)

check_applyPendingDebt(): passing
check_closeTrove(): passing
check_urgentRedemption(): passing
borrower_removeFromBatch(uint256,uint256,uint256,uint256,uint256): failed!
  Call sequence:
    CryticTester.target_changePrice(2065338015567088385863)
    CryticTester.target_clamped_openTrove(1600088450706323510013,410253885495525717475,7086457362980447677199771284152610621870910970768488415,6199590066992774083620642,3959368524523904995)
    *wait* Time delay: 1 seconds Block delay: 1
    CryticTester.borrower_removeFromBatch(0,0,0,0,0)

Traces:
call ******************************************::[1mgetTroveIdsCountXD() [1m(/recon/contracts/src/test/recon/Setup.sol:94)
  (1)
call ******************************************::[1mgetTroveFromTroveIdsArrayXD(0) [1m(/recon/contracts/src/test/recon/Setup.sol:97)
  (39362929220450822050716333097395288665023974156017514272538874433377666588395)
call ******************************************::[1mgetCollateralXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:40)
  (0)
call ******************************************::[1mbalanceOfXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:41)
  (89708614504474282525)
call ******************************************::[1mbalanceOfXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:42)
  (100000000000000000000)
call ******************************************::[1mgetTroveIdsCountXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:48)
  (1)
call ******************************************::[1mgetTroveFromTroveIdsArrayXD(0) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:50)
  (39362929220450822050716333097395288665023974156017514272538874433377666588395)
call ******************************************::[1mgetBatchXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:51)
  (0, 0, 0, 0, 0, 0, 0, 0)
call ******************************************::[1mgetLatestTroveDataXD(39362929220450822050716333097395288665023974156017514272538874433377666588395) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:63)
  ((2034386999387478712096, 10253885495525717475, 0, 0, 4321703080894, 2034386995065775631202, 66992774083620642, 136289228359097368599673924857066471684, 0, 1524958792))
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mgetEntireSystemDebtXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:65)
 call ******************************************::[1mgetBoldDebtXD() [1m<no source map>
    (2034386999387478712097)
 call 0x01f816b03111C36098D6394f7Bb6e2538564E351::[1mgetBoldDebtXD() [1m<no source map>
    (0)
  (2034386999387478712097)
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mapplyPendingDebtXD(39362929220450822050716333097395288665023974156017514272538874433377666588395, 0, 1) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:69)
 call ******************************************::[1mgetTroveStatusXD(39362929220450822050716333097395288665023974156017514272538874433377666588395) [1m<no source map>
    (1)
 call ******************************************::[1mgetLatestTroveDataXD(39362929220450822050716333097395288665023974156017514272538874433377666588395) [1m<no source map>
    ((2034386999387478712096, 10253885495525717475, 0, 0, 4321703080894, 2034386995065775631202, 66992774083620642, 136289228359097368599673924857066471684, 0, 1524958792))
 call ******************************************::0xb5a672c25706a0dfbc56b6a6cea490486bde787756581cda385660fab0a3e53423b7deeb0000000000000000000000000000000000000000000000008e4d1e87e05735e300000000000000000000000000000000000000000000006e48ca854c737e5f2000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000668859c489b9580989b390db8d6f824000000000000000000000000000000000668859c0e239a1d78d6734c148bcc1040000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000 [1m<no source map>
   emit TroveUpdated(_troveId=39362929220450822050716333097395288665023974156017514272538874433377666588395, _debt=2034386999387478712096, _coll=10253885495525717475, _stake=10253885495525717475, _annualInterestRate=66992774083620642, _snapshotOfTotalCollRedist=0, _snapshotOfTotalDebtRedist=0) [1m<no source map>
   emit TroveOperation(_troveId=39362929220450822050716333097395288665023974156017514272538874433377666588395, _operation=4, _annualInterestRate=66992774083620642, _debtIncreaseFromRedist=0, _debtIncreaseFromUpfrontFee=0, _debtChangeFromOperation=0, _collIncreaseFromRedist=0, _collChangeFromOperation=0) [1m<no source map>
    0x
 call ******************************************::0x71d4eb2100000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000668859c489b9580989b390db8d6f824000000000000000000000000000000000668859c0e239a1d78d6734c148bcc10400000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000 [1m<no source map>
   call 0xbC9E133D7fcbB155D53ce908245bCB5bb4dF19e4::[1mmintXD([1mLogging, 1210076862651) [1m<no source map>
     emit Transfer(from=0x0000000000000000000000000000000000000000, to=[1mLogging, value=1210076862651) [1m<no source map>
      0x
   call 0xbC9E133D7fcbB155D53ce908245bCB5bb4dF19e4::[1mmintXD(0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a, 3111626218244) [1m<no source map>
     emit Transfer(from=0x0000000000000000000000000000000000000000, to=0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a, value=3111626218244) [1m<no source map>
      0x
   call 0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a::[1mtriggerBoldRewardsXD(3111626218244) [1m<no source map>
      0x
    0x
 call ******************************************::[1mgetTroveStatusXD(39362929220450822050716333097395288665023974156017514272538874433377666588395) [1m<no source map>
    (1)
  0x
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mMCRXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:138)
  (1100000000000000000)
call [1mPriceFeedTestnet::getPrice()() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:139)
  (2065338015567088385863)
emit Log(BA-01: For all operations, weightedRecordedDebt is equal to the sum of trove debt * rate) [1m(/recon/contracts/lib/chimera/src/CryticAsserts.sol:39)

borrower_removeInterestIndividualDelegate(uint256): failed!
  Call sequence:
    CryticTester.target_switchBranch()
    CryticTester.target_switchBranch()
    CryticTester.target_changePrice(2087236154983614577954)
    CryticTester.target_clamped_openTrove(1730900158957103908,1540726526653194069,1016562737049771750524451776882017937325043143945127056708514,116136740133730876516,10555997744402729349)
    *wait* Time delay: 1 seconds Block delay: 1
    CryticTester.borrower_removeInterestIndividualDelegate(0)

Traces:
call ******************************************::[1mgetTroveIdsCountXD() [1m(/recon/contracts/src/test/recon/Setup.sol:94)
  (1)
call ******************************************::[1mgetTroveFromTroveIdsArrayXD(0) [1m(/recon/contracts/src/test/recon/Setup.sol:97)
  (29867788007852221082631030288032237261540328831518434101829349170690178956210)
call ******************************************::[1mgetCollateralXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:40)
  (0)
call ******************************************::[1mbalanceOfXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:41)
  (98421773473346805931)
call ******************************************::[1mbalanceOfXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:42)
  (100000000000000000000)
call ******************************************::[1mgetTroveIdsCountXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:48)
  (1)
call ******************************************::[1mgetTroveFromTroveIdsArrayXD(0) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:50)
  (29867788007852221082631030288032237261540328831518434101829349170690178956210)
call ******************************************::[1mgetBatchXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:51)
  (0, 0, 0, 0, 0, 0, 0, 0)
call ******************************************::[1mgetLatestTroveDataXD(29867788007852221082631030288032237261540328831518434101829349170690178956210) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:63)
  ((2031612304712935456422, 1540726526653194069, 0, 0, 8809073345737, 2031612295903862110685, 136740133730876516, 277802937031187176921703431506859173460, 0, 1524958792))
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mgetEntireSystemDebtXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:65)
 call ******************************************::[1mgetBoldDebtXD() [1m<no source map>
    (2031612304712935456423)
 call 0x01f816b03111C36098D6394f7Bb6e2538564E351::[1mgetBoldDebtXD() [1m<no source map>
    (0)
  (2031612304712935456423)
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mapplyPendingDebtXD(29867788007852221082631030288032237261540328831518434101829349170690178956210, 0, 1) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:69)
 call ******************************************::[1mgetTroveStatusXD(29867788007852221082631030288032237261540328831518434101829349170690178956210) [1m<no source map>
    (1)
 call ******************************************::[1mgetLatestTroveDataXD(29867788007852221082631030288032237261540328831518434101829349170690178956210) [1m<no source map>
    ((2031612304712935456422, 1540726526653194069, 0, 0, 8809073345737, 2031612295903862110685, 136740133730876516, 277802937031187176921703431506859173460, 0, 1524958792))
 call ******************************************::0xb5a672c2420891a5dc8d8892286c99b552cc128735585aaf17c9a4ae64d25448900c3bb20000000000000000000000000000000000000000000000001561c29ce52f5f5500000000000000000000000000000000000000000000006e2248d3316f2cbea600000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000d0fee655467730b5a7dc6c76cbd8e0d800000000000000000000000000000000d0fee6461257959d4cb83495b9eca6540000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000 [1m<no source map>
   emit TroveUpdated(_troveId=29867788007852221082631030288032237261540328831518434101829349170690178956210, _debt=2031612304712935456422, _coll=1540726526653194069, _stake=1540726526653194069, _annualInterestRate=136740133730876516, _snapshotOfTotalCollRedist=0, _snapshotOfTotalDebtRedist=0) [1m<no source map>
   emit TroveOperation(_troveId=29867788007852221082631030288032237261540328831518434101829349170690178956210, _operation=4, _annualInterestRate=136740133730876516, _debtIncreaseFromRedist=0, _debtIncreaseFromUpfrontFee=0, _debtChangeFromOperation=0, _collIncreaseFromRedist=0, _collChangeFromOperation=0) [1m<no source map>
    0x
 call ******************************************::0x71d4eb2100000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000d0fee655467730b5a7dc6c76cbd8e0d800000000000000000000000000000000d0fee6461257959d4cb83495b9eca65400000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000 [1m<no source map>
   call 0xbC9E133D7fcbB155D53ce908245bCB5bb4dF19e4::[1mmintXD([1mLogging, 2466540536807) [1m<no source map>
     emit Transfer(from=0x0000000000000000000000000000000000000000, to=[1mLogging, value=2466540536807) [1m<no source map>
      0x
   call 0xbC9E133D7fcbB155D53ce908245bCB5bb4dF19e4::[1mmintXD(0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a, 6342532808931) [1m<no source map>
     emit Transfer(from=0x0000000000000000000000000000000000000000, to=0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a, value=6342532808931) [1m<no source map>
      0x
   call 0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a::[1mtriggerBoldRewardsXD(6342532808931) [1m<no source map>
      0x
    0x
 call ******************************************::[1mgetTroveStatusXD(29867788007852221082631030288032237261540328831518434101829349170690178956210) [1m<no source map>
    (1)
  0x
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mMCRXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:138)
  (1100000000000000000)
call [1mPriceFeedTestnet::getPrice()() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:139)
  (2087236154983614577954)
emit Log(BA-01: For all operations, weightedRecordedDebt is equal to the sum of trove debt * rate) [1m(/recon/contracts/lib/chimera/src/CryticAsserts.sol:39)

target_changePrice(uint256): passing
target_new_clamped_openTrove(uint256,uint256,uint256,uint256): failed!
  Call sequence:
    CryticTester.target_changePrice(2139003812478695301022)
    CryticTester.target_clamped_openTrove(0,1457297373974417299,1532126570804236713719964901430824714697103713859809,7984426118823952820027274084974403144,1120978271908879645)
    *wait* Time delay: 1 seconds Block delay: 1
    CryticTester.target_new_clamped_openTrove(0,0,0,1)

Traces:
call ******************************************::[1msymbolXD() [1m(/recon/contracts/src/test/recon/TargetFunctions.sol:41)
  (WETH)
call ******************************************::[1msymbolXD() [1m(/recon/contracts/src/test/recon/TargetFunctions.sol:42)
  (WETH)
call [1mPriceFeedTestnet::getPrice()() [1m(/recon/contracts/src/test/recon/TargetFunctions.sol:45)
  (2139003812478695301022)
call ******************************************::[1mbalanceOfXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/TargetFunctions.sol:52)
  (98505202626025582701)
call ******************************************::[1mgetCollateralXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:40)
  (0)
call ******************************************::[1mbalanceOfXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:41)
  (98505202626025582701)
call ******************************************::[1mbalanceOfXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:42)
  (100000000000000000000)
call ******************************************::[1mgetTroveIdsCountXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:48)
  (1)
call ******************************************::[1mgetTroveFromTroveIdsArrayXD(0) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:50)
  (8648653994422793423523514607345318943026410206026333401532383970483429052211)
call ******************************************::[1mgetBatchXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:51)
  (0, 0, 0, 0, 0, 0, 0, 0)
call ******************************************::[1mgetLatestTroveDataXD(8648653994422793423523514607345318943026410206026333401532383970483429052211) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:63)
  ((2067032454122493139734, 1457297373974417299, 0, 0, 1787684511346, 2067032452334808628388, 27274084974403144, 56376418749828486959646764731594851872, 0, 1524958792))
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mgetEntireSystemDebtXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:65)
 call ******************************************::[1mgetBoldDebtXD() [1m<no source map>
    (2067032454122493139735)
 call 0x01f816b03111C36098D6394f7Bb6e2538564E351::[1mgetBoldDebtXD() [1m<no source map>
    (0)
  (2067032454122493139735)
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mapplyPendingDebtXD(8648653994422793423523514607345318943026410206026333401532383970483429052211, 0, 1) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:69)
 call ******************************************::[1mgetTroveStatusXD(8648653994422793423523514607345318943026410206026333401532383970483429052211) [1m<no source map>
    (1)
 call ******************************************::[1mgetLatestTroveDataXD(8648653994422793423523514607345318943026410206026333401532383970483429052211) [1m<no source map>
    ((2067032454122493139734, 1457297373974417299, 0, 0, 1787684511346, 2067032452334808628388, 27274084974403144, 56376418749828486959646764731594851872, 0, 1524958792))
 call ******************************************::0xb5a672c2131ef6f5e9ad7f4b2efd52cffeb247a384f286ced0b2014c98237c55de760f3300000000000000000000000000000000000000000000000014395c3d93dacf930000000000000000000000000000000000000000000000700dd6796ac71f5b16000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002a69b40c43e058f88b7cdc2266e43230000000000000000000000000000000002a69b40ba6551f5d48011331775246200000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000 [1m<no source map>
   emit TroveUpdated(_troveId=8648653994422793423523514607345318943026410206026333401532383970483429052211, _debt=2067032454122493139734, _coll=1457297373974417299, _stake=1457297373974417299, _annualInterestRate=27274084974403144, _snapshotOfTotalCollRedist=0, _snapshotOfTotalDebtRedist=0) [1m<no source map>
   emit TroveOperation(_troveId=8648653994422793423523514607345318943026410206026333401532383970483429052211, _operation=4, _annualInterestRate=27274084974403144, _debtIncreaseFromRedist=0, _debtIncreaseFromUpfrontFee=0, _debtChangeFromOperation=0, _collIncreaseFromRedist=0, _collChangeFromOperation=0) [1m<no source map>
    0x
 call ******************************************::0x71d4eb21000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002a69b40c43e058f88b7cdc2266e43230000000000000000000000000000000002a69b40ba6551f5d480113317752462000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000 [1m<no source map>
   call 0xbC9E133D7fcbB155D53ce908245bCB5bb4dF19e4::[1mmintXD([1mLogging, 500551663178) [1m<no source map>
     emit Transfer(from=0x0000000000000000000000000000000000000000, to=[1mLogging, value=500551663178) [1m<no source map>
      0x
   call 0xbC9E133D7fcbB155D53ce908245bCB5bb4dF19e4::[1mmintXD(0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a, 1287132848169) [1m<no source map>
     emit Transfer(from=0x0000000000000000000000000000000000000000, to=0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a, value=1287132848169) [1m<no source map>
      0x
   call 0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a::[1mtriggerBoldRewardsXD(1287132848169) [1m<no source map>
      0x
    0x
 call ******************************************::[1mgetTroveStatusXD(8648653994422793423523514607345318943026410206026333401532383970483429052211) [1m<no source map>
    (1)
  0x
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mMCRXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:138)
  (1100000000000000000)
call [1mPriceFeedTestnet::getPrice()() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:139)
  (2139003812478695301022)
emit Log(BA-01: For all operations, weightedRecordedDebt is equal to the sum of trove debt * rate) [1m(/recon/contracts/lib/chimera/src/CryticAsserts.sol:39)

check_openTroveAndJoinInterestBatchManager(): passing
pool_provideToSP(uint256,bool): failed!
  Call sequence:
    CryticTester.target_changePrice(2002987678650964851508)
    CryticTester.target_clamped_openTrove(1099936651572900667,1509454822481413290,1257644720395936632135335602836518101736347740060792405,6040228846929377,295369840280265020)
    CryticTester.pool_provideToSP(0,false) Time delay: 1 seconds Block delay: 1

Traces:
call ******************************************::[1mgetCollateralXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:40)
  (0)
call ******************************************::[1mbalanceOfXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:41)
  (98453045177518586710)
call ******************************************::[1mbalanceOfXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:42)
  (100000000000000000000)
call ******************************************::[1mgetTroveIdsCountXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:48)
  (1)
call ******************************************::[1mgetTroveFromTroveIdsArrayXD(0) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:50)
  (91777311139984357900206180099863324950530989499537232115993033222638145581096)
call ******************************************::[1mgetBatchXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:51)
  (0, 0, 0, 0, 0, 0, 0, 0)
call ******************************************::[1mgetLatestTroveDataXD(91777311139984357900206180099863324950530989499537232115993033222638145581096) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:63)
  ((2001313698450116572717, 1509454822481413290, 0, 0, 383320418912, 2001313698066796153805, 6040228846929377, 12088392730817971483783038468064829485, 0, 1524958792))
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mgetEntireSystemDebtXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:65)
 call ******************************************::[1mgetBoldDebtXD() [1m<no source map>
    (2001313698450116572718)
 call 0x01f816b03111C36098D6394f7Bb6e2538564E351::[1mgetBoldDebtXD() [1m<no source map>
    (0)
  (2001313698450116572718)
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mapplyPendingDebtXD(91777311139984357900206180099863324950530989499537232115993033222638145581096, 0, 1) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:69)
 call ******************************************::[1mgetTroveStatusXD(91777311139984357900206180099863324950530989499537232115993033222638145581096) [1m<no source map>
    (1)
 call ******************************************::[1mgetLatestTroveDataXD(91777311139984357900206180099863324950530989499537232115993033222638145581096) [1m<no source map>
    ((2001313698450116572717, 1509454822481413290, 0, 0, 383320418912, 2001313698066796153805, 6040228846929377, 12088392730817971483783038468064829485, 0, 1524958792))
 call ******************************************::0xb5a672c2cae81e05bd659d83be1d75460a9cadc86b3afee717a12d58ea423108d5ef302800000000000000000000000000000000000000000000000014f2a929e28144aa00000000000000000000000000000000000000000000006c7dce8d53726df62d00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000091823c6f22ec894db5e750d8cb2be8d00000000000000000000000000000000091823c6eab3939e82657abd2204602d0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000 [1m<no source map>
   emit TroveUpdated(_troveId=91777311139984357900206180099863324950530989499537232115993033222638145581096, _debt=2001313698450116572717, _coll=1509454822481413290, _stake=1509454822481413290, _annualInterestRate=6040228846929377, _snapshotOfTotalCollRedist=0, _snapshotOfTotalDebtRedist=0) [1m<no source map>
   emit TroveOperation(_troveId=91777311139984357900206180099863324950530989499537232115993033222638145581096, _operation=4, _annualInterestRate=6040228846929377, _debtIncreaseFromRedist=0, _debtIncreaseFromUpfrontFee=0, _debtChangeFromOperation=0, _collIncreaseFromRedist=0, _collChangeFromOperation=0) [1m<no source map>
    0x
 call ******************************************::0x71d4eb2100000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000091823c6f22ec894db5e750d8cb2be8d00000000000000000000000000000000091823c6eab3939e82657abd2204602d00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000 [1m<no source map>
   call 0xbC9E133D7fcbB155D53ce908245bCB5bb4dF19e4::[1mmintXD([1mLogging, 107329717296) [1m<no source map>
     emit Transfer(from=0x0000000000000000000000000000000000000000, to=[1mLogging, value=107329717296) [1m<no source map>
      0x
   call 0xbC9E133D7fcbB155D53ce908245bCB5bb4dF19e4::[1mmintXD(0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a, 275990701617) [1m<no source map>
     emit Transfer(from=0x0000000000000000000000000000000000000000, to=0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a, value=275990701617) [1m<no source map>
      0x
   call 0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a::[1mtriggerBoldRewardsXD(275990701617) [1m<no source map>
      0x
    0x
 call ******************************************::[1mgetTroveStatusXD(91777311139984357900206180099863324950530989499537232115993033222638145581096) [1m<no source map>
    (1)
  0x
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mMCRXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:138)
  (1100000000000000000)
call [1mPriceFeedTestnet::getPrice()() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:139)
  (2002987678650964851508)
emit Log(BA-01: For all operations, weightedRecordedDebt is equal to the sum of trove debt * rate) [1m(/recon/contracts/lib/chimera/src/CryticAsserts.sol:39)

check_redeemCollateral(): passing
check_provideToSP(): passing
borrower_addCollateral(uint256,uint256): failed!
  Call sequence:
    CryticTester.target_changePrice(2231919969651240436405)
    CryticTester.target_clamped_openTrove(0,1500258867394940450,1240438131885250851972262277134984259366629767376774052,84730792087224054903368684156120,2167141382107328291)
    *wait* Time delay: 1 seconds Block delay: 1
    CryticTester.borrower_addCollateral(0,0)

Traces:
call ******************************************::[1mgetTroveIdsCountXD() [1m(/recon/contracts/src/test/recon/Setup.sol:94)
  (1)
call ******************************************::[1mgetTroveFromTroveIdsArrayXD(0) [1m(/recon/contracts/src/test/recon/Setup.sol:97)
  (8648653994422793423523514607345318943026410206026333401532383970483429052211)
call ******************************************::[1mgetCollateralXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:40)
  (0)
call ******************************************::[1mbalanceOfXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:41)
  (98462241132605059550)
call ******************************************::[1mbalanceOfXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:42)
  (100000000000000000000)
call ******************************************::[1mgetTroveIdsCountXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:48)
  (1)
call ******************************************::[1mgetTroveFromTroveIdsArrayXD(0) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:50)
  (8648653994422793423523514607345318943026410206026333401532383970483429052211)
call ******************************************::[1mgetBatchXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:51)
  (0, 0, 0, 0, 0, 0, 0, 0)
call ******************************************::[1mgetLatestTroveDataXD(8648653994422793423523514607345318943026410206026333401532383970483429052211) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:63)
  ((2037533812554663194162, 1500258867394940450, 0, 0, 3547294200988, 2037533809007368993174, 54903368684156120, 111867469922364519683324231810230324880, 0, 1524958792))
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mgetEntireSystemDebtXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:65)
 call ******************************************::[1mgetBoldDebtXD() [1m<no source map>
    (2037533812554663194163)
 call 0x01f816b03111C36098D6394f7Bb6e2538564E351::[1mgetBoldDebtXD() [1m<no source map>
    (0)
  (2037533812554663194163)
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mapplyPendingDebtXD(8648653994422793423523514607345318943026410206026333401532383970483429052211, 0, 1) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:69)
 call ******************************************::[1mgetTroveStatusXD(8648653994422793423523514607345318943026410206026333401532383970483429052211) [1m<no source map>
    (1)
 call ******************************************::[1mgetLatestTroveDataXD(8648653994422793423523514607345318943026410206026333401532383970483429052211) [1m<no source map>
    ((2037533812554663194162, 1500258867394940450, 0, 0, 3547294200988, 2037533809007368993174, 54903368684156120, 111867469922364519683324231810230324880, 0, 1524958792))
 call ******************************************::0xb5a672c2131ef6f5e9ad7f4b2efd52cffeb247a384f286ced0b2014c98237c55de760f3300000000000000000000000000000000000000000000000014d1fd7dbe348e2200000000000000000000000000000000000000000000006e74763f33a00bee32000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000005428e41bdcaf41a2cf9d54d9e7fb2230000000000000000000000000000000005428e4196762e1f63eb90ac4bbd50e900000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000 [1m<no source map>
   emit TroveUpdated(_troveId=8648653994422793423523514607345318943026410206026333401532383970483429052211, _debt=2037533812554663194162, _coll=1500258867394940450, _stake=1500258867394940450, _annualInterestRate=54903368684156120, _snapshotOfTotalCollRedist=0, _snapshotOfTotalDebtRedist=0) [1m<no source map>
   emit TroveOperation(_troveId=8648653994422793423523514607345318943026410206026333401532383970483429052211, _operation=4, _annualInterestRate=54903368684156120, _debtIncreaseFromRedist=0, _debtIncreaseFromUpfrontFee=0, _debtChangeFromOperation=0, _collIncreaseFromRedist=0, _collChangeFromOperation=0) [1m<no source map>
    0x
 call ******************************************::0x71d4eb21000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000005428e41bdcaf41a2cf9d54d9e7fb2230000000000000000000000000000000005428e4196762e1f63eb90ac4bbd50e9000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000 [1m<no source map>
   call 0xbC9E133D7fcbB155D53ce908245bCB5bb4dF19e4::[1mmintXD([1mLogging, 993242376277) [1m<no source map>
     emit Transfer(from=0x0000000000000000000000000000000000000000, to=[1mLogging, value=993242376277) [1m<no source map>
      0x
   call 0xbC9E133D7fcbB155D53ce908245bCB5bb4dF19e4::[1mmintXD(0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a, 2554051824712) [1m<no source map>
     emit Transfer(from=0x0000000000000000000000000000000000000000, to=0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a, value=2554051824712) [1m<no source map>
      0x
   call 0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a::[1mtriggerBoldRewardsXD(2554051824712) [1m<no source map>
      0x
    0x
 call ******************************************::[1mgetTroveStatusXD(8648653994422793423523514607345318943026410206026333401532383970483429052211) [1m<no source map>
    (1)
  0x
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mMCRXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:138)
  (1100000000000000000)
call [1mPriceFeedTestnet::getPrice()() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:139)
  (2231919969651240436405)
emit Log(BA-01: For all operations, weightedRecordedDebt is equal to the sum of trove debt * rate) [1m(/recon/contracts/lib/chimera/src/CryticAsserts.sol:39)

check_withdrawColl(): passing
check_repayBold(): passing
check_hasRedistributedDebt(): passing
borrower_withdrawBold(uint256,uint256,uint256): failed!
  Call sequence:
    CryticTester.target_changePrice(2140597112755816296121)
    CryticTester.target_clamped_openTrove(28,1970160309562358770,14839697449087372881886115679897958027690315783567944892960636505,5213740800501376,227597946948941539)
    *wait* Time delay: 1 seconds Block delay: 1
    CryticTester.borrower_withdrawBold(0,0,0)

Traces:
call ******************************************::[1mgetTroveIdsCountXD() [1m(/recon/contracts/src/test/recon/Setup.sol:94)
  (1)
call ******************************************::[1mgetTroveFromTroveIdsArrayXD(0) [1m(/recon/contracts/src/test/recon/Setup.sol:97)
  (99273443723535702592969713506806924404683161760727287879752630711951583501289)
call ******************************************::[1mgetCollateralXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:40)
  (0)
call ******************************************::[1mbalanceOfXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:41)
  (97992339690437641230)
call ******************************************::[1mbalanceOfXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:42)
  (100000000000000000000)
call ******************************************::[1mgetTroveIdsCountXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:48)
  (1)
call ******************************************::[1mgetTroveFromTroveIdsArrayXD(0) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:50)
  (99273443723535702592969713506806924404683161760727287879752630711951583501289)
call ******************************************::[1mgetBatchXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:51)
  (0, 0, 0, 0, 0, 0, 0, 0)
call ******************************************::[1mgetLatestTroveDataXD(99273443723535702592969713506806924404683161760727287879752630711951583501289) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:63)
  ((2046825695172883083098, 1970160309562358770, 0, 0, 338394807098, 2046825694834488276000, 5213740800501376, 10671618636673150051276197193867776000, 0, 1524958792))
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mgetEntireSystemDebtXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:65)
 call ******************************************::[1mgetBoldDebtXD() [1m<no source map>
    (2046825695172883083099)
 call 0x01f816b03111C36098D6394f7Bb6e2538564E351::[1mgetBoldDebtXD() [1m<no source map>
    (0)
  (2046825695172883083099)
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mapplyPendingDebtXD(99273443723535702592969713506806924404683161760727287879752630711951583501289, 0, 1) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:69)
 call ******************************************::[1mgetTroveStatusXD(99273443723535702592969713506806924404683161760727287879752630711951583501289) [1m<no source map>
    (1)
 call ******************************************::[1mgetLatestTroveDataXD(99273443723535702592969713506806924404683161760727287879752630711951583501289) [1m<no source map>
    ((2046825695172883083098, 1970160309562358770, 0, 0, 338394807098, 2046825694834488276000, 5213740800501376, 10671618636673150051276197193867776000, 0, 1524958792))
 call ******************************************::0xb5a672c2db7ac72897b4c673063536ed631fe12e42d1f75dfc7d2575590c251719da5be90000000000000000000000000000000000000000000000001b576a5cfa09bbf200000000000000000000000000000000000000000000006ef569a53661ab6f5a000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000807476bd422ffda664b3906d7c78900000000000000000000000000000000000807476bce6f9a334a74f8f6581ed0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000 [1m<no source map>
   emit TroveUpdated(_troveId=99273443723535702592969713506806924404683161760727287879752630711951583501289, _debt=2046825695172883083098, _coll=1970160309562358770, _stake=1970160309562358770, _annualInterestRate=5213740800501376, _snapshotOfTotalCollRedist=0, _snapshotOfTotalDebtRedist=0) [1m<no source map>
   emit TroveOperation(_troveId=99273443723535702592969713506806924404683161760727287879752630711951583501289, _operation=4, _annualInterestRate=5213740800501376, _debtIncreaseFromRedist=0, _debtIncreaseFromUpfrontFee=0, _debtChangeFromOperation=0, _collIncreaseFromRedist=0, _collChangeFromOperation=0) [1m<no source map>
    0x
 call ******************************************::0x71d4eb21000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000807476bd422ffda664b3906d7c78900000000000000000000000000000000000807476bce6f9a334a74f8f6581ed00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000 [1m<no source map>
   call 0xbC9E133D7fcbB155D53ce908245bCB5bb4dF19e4::[1mmintXD([1mLogging, 94750545988) [1m<no source map>
     emit Transfer(from=0x0000000000000000000000000000000000000000, to=[1mLogging, value=94750545988) [1m<no source map>
      0x
   call 0xbC9E133D7fcbB155D53ce908245bCB5bb4dF19e4::[1mmintXD(0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a, 243644261111) [1m<no source map>
     emit Transfer(from=0x0000000000000000000000000000000000000000, to=0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a, value=243644261111) [1m<no source map>
      0x
   call 0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a::[1mtriggerBoldRewardsXD(243644261111) [1m<no source map>
      0x
    0x
 call ******************************************::[1mgetTroveStatusXD(99273443723535702592969713506806924404683161760727287879752630711951583501289) [1m<no source map>
    (1)
  0x
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mMCRXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:138)
  (1100000000000000000)
call [1mPriceFeedTestnet::getPrice()() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:139)
  (2140597112755816296121)
emit Log(BA-01: For all operations, weightedRecordedDebt is equal to the sum of trove debt * rate) [1m(/recon/contracts/lib/chimera/src/CryticAsserts.sol:39)

property_TR03(): passing
borrower_withdrawColl(uint256,uint256): failed!
  Call sequence:
    CryticTester.target_switchBranch()
    CryticTester.target_switchBranch()
    CryticTester.target_changePrice(2079652246808121795799)
    CryticTester.target_clamped_openTrove(635270446620657426879028,1575917294020645209,377273270102079458819958019822982327713308290671484911,5013278774497393,902013694757334069678431043)
    *wait* Time delay: 1 seconds Block delay: 1
    CryticTester.borrower_withdrawColl(0,0)

Traces:
call ******************************************::[1mgetTroveIdsCountXD() [1m(/recon/contracts/src/test/recon/Setup.sol:94)
  (1)
call ******************************************::[1mgetTroveFromTroveIdsArrayXD(0) [1m(/recon/contracts/src/test/recon/Setup.sol:97)
  (91803402519874532285639611741860248401749947851993609258015835999523573285555)
call ******************************************::[1mgetCollateralXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:40)
  (0)
call ******************************************::[1mbalanceOfXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:41)
  (98386582705979354791)
call ******************************************::[1mbalanceOfXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:42)
  (100000000000000000000)
call ******************************************::[1mgetTroveIdsCountXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:48)
  (1)
call ******************************************::[1mgetTroveFromTroveIdsArrayXD(0) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:50)
  (91803402519874532285639611741860248401749947851993609258015835999523573285555)
call ******************************************::[1mgetBatchXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:51)
  (0, 0, 0, 0, 0, 0, 0, 0)
call ******************************************::[1mgetLatestTroveDataXD(91803402519874532285639611741860248401749947851993609258015835999523573285555) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:63)
  ((2004810621626326267407, 1575917294020645209, 0, 0, 318704798791, 2004810621307621468616, 5013278774497393, 10050674534688429602403361721923318088, 0, 1524958792))
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mgetEntireSystemDebtXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:65)
 call ******************************************::[1mgetBoldDebtXD() [1m<no source map>
    (2004810621626326267408)
 call 0x01f816b03111C36098D6394f7Bb6e2538564E351::[1mgetBoldDebtXD() [1m<no source map>
    (0)
  (2004810621626326267408)
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mapplyPendingDebtXD(91803402519874532285639611741860248401749947851993609258015835999523573285555, 0, 1) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:69)
 call ******************************************::[1mgetTroveStatusXD(91803402519874532285639611741860248401749947851993609258015835999523573285555) [1m<no source map>
    (1)
 call ******************************************::[1mgetLatestTroveDataXD(91803402519874532285639611741860248401749947851993609258015835999523573285555) [1m<no source map>
    ((2004810621626326267407, 1575917294020645209, 0, 0, 318704798791, 2004810621307621468616, 5013278774497393, 10050674534688429602403361721923318088, 0, 1524958792))
 call ******************************************::0xb5a672c2caf6e26cceb974ee31138c3f55642d9bb9cb22c5b6a01711beb3bca1001742b300000000000000000000000000000000000000000000000015dec86de97ed15900000000000000000000000000000000000000000000006cae561e6d5f3d360f00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000078fb0836788530af613cf35f61a4c9f00000000000000000000000000000000078fb083625eb10c7a437d642a82b1480000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000 [1m<no source map>
   emit TroveUpdated(_troveId=91803402519874532285639611741860248401749947851993609258015835999523573285555, _debt=2004810621626326267407, _coll=1575917294020645209, _stake=1575917294020645209, _annualInterestRate=5013278774497393, _snapshotOfTotalCollRedist=0, _snapshotOfTotalDebtRedist=0) [1m<no source map>
   emit TroveOperation(_troveId=91803402519874532285639611741860248401749947851993609258015835999523573285555, _operation=4, _annualInterestRate=5013278774497393, _debtIncreaseFromRedist=0, _debtIncreaseFromUpfrontFee=0, _debtChangeFromOperation=0, _collIncreaseFromRedist=0, _collChangeFromOperation=0) [1m<no source map>
    0x
 call ******************************************::0x71d4eb2100000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000078fb0836788530af613cf35f61a4c9f00000000000000000000000000000000078fb083625eb10c7a437d642a82b14800000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000 [1m<no source map>
   call 0xbC9E133D7fcbB155D53ce908245bCB5bb4dF19e4::[1mmintXD([1mLogging, 89237343662) [1m<no source map>
     emit Transfer(from=0x0000000000000000000000000000000000000000, to=[1mLogging, value=89237343662) [1m<no source map>
      0x
   call 0xbC9E133D7fcbB155D53ce908245bCB5bb4dF19e4::[1mmintXD(0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a, 229467455130) [1m<no source map>
     emit Transfer(from=0x0000000000000000000000000000000000000000, to=0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a, value=229467455130) [1m<no source map>
      0x
   call 0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a::[1mtriggerBoldRewardsXD(229467455130) [1m<no source map>
      0x
    0x
 call ******************************************::[1mgetTroveStatusXD(91803402519874532285639611741860248401749947851993609258015835999523573285555) [1m<no source map>
    (1)
  0x
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mMCRXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:138)
  (1100000000000000000)
call [1mPriceFeedTestnet::getPrice()() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:139)
  (2079652246808121795799)
emit Log(BA-01: For all operations, weightedRecordedDebt is equal to the sum of trove debt * rate) [1m(/recon/contracts/lib/chimera/src/CryticAsserts.sol:39)

pool_claimAllCollGains(): failed!
  Call sequence:
    CryticTester.target_switchBranch()
    CryticTester.target_switchBranch()
    CryticTester.target_changePrice(1978208411201327543955)
    CryticTester.target_clamped_openTrove(50937711764629452619894,1675856255056641967,438147478369590626888864192760540869366414049252777265546856123887474,4563402416817002910232289435986477672110950870,40919863275537068858803281973976)
    *wait* Time delay: 1 seconds Block delay: 1
    CryticTester.pool_claimAllCollGains()

Traces:
call ******************************************::[1mgetCollateralXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:40)
  (0)
call ******************************************::[1mbalanceOfXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:41)
  (98286643744943358033)
call ******************************************::[1mbalanceOfXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:42)
  (100000000000000000000)
call ******************************************::[1mgetTroveIdsCountXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:48)
  (1)
call ******************************************::[1mgetTroveFromTroveIdsArrayXD(0) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:50)
  (7599446695712713487007073444271937529967445864008562083160834908063856696258)
call ******************************************::[1mgetBatchXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:51)
  (0, 0, 0, 0, 0, 0, 0, 0)
call ******************************************::[1mgetLatestTroveDataXD(7599446695712713487007073444271937529967445864008562083160834908063856696258) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:63)
  ((2008397668052119728260, 1675856255056641967, 0, 0, 62824689062593, 2008397605227430665667, 986477672110950870, 1981239394277964295214492467444432780290, 0, 1524958792))
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mgetEntireSystemDebtXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:65)
 call ******************************************::[1mgetBoldDebtXD() [1m<no source map>
    (2008397668052119728261)
 call 0x01f816b03111C36098D6394f7Bb6e2538564E351::[1mgetBoldDebtXD() [1m<no source map>
    (0)
  (2008397668052119728261)
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mapplyPendingDebtXD(7599446695712713487007073444271937529967445864008562083160834908063856696258, 0, 1) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:69)
 call ******************************************::[1mgetTroveStatusXD(7599446695712713487007073444271937529967445864008562083160834908063856696258) [1m<no source map>
    (1)
 call ******************************************::[1mgetLatestTroveDataXD(7599446695712713487007073444271937529967445864008562083160834908063856696258) [1m<no source map>
    ((2008397668052119728260, 1675856255056641967, 0, 0, 62824689062593, 2008397605227430665667, 986477672110950870, 1981239394277964295214492467444432780290, 0, 1524958792))
 call ******************************************::0xb5a672c210cd2268416e36b1807813dd0e26c2603ea4b6865331e728d4884a6264a99bc20000000000000000000000000000000000000000000000001741d66289447faf00000000000000000000000000000000000000000000006ce01dde25817e308400000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000005d284d6eeb5f10e382c478a94e6cd325800000000000000000000000000000005d284d3e0796c04f81c140a1f54d43c020000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000 [1m<no source map>
   emit TroveUpdated(_troveId=7599446695712713487007073444271937529967445864008562083160834908063856696258, _debt=2008397668052119728260, _coll=1675856255056641967, _stake=1675856255056641967, _annualInterestRate=986477672110950870, _snapshotOfTotalCollRedist=0, _snapshotOfTotalDebtRedist=0) [1m<no source map>
   emit TroveOperation(_troveId=7599446695712713487007073444271937529967445864008562083160834908063856696258, _operation=4, _annualInterestRate=986477672110950870, _debtIncreaseFromRedist=0, _debtIncreaseFromUpfrontFee=0, _debtChangeFromOperation=0, _collIncreaseFromRedist=0, _collChangeFromOperation=0) [1m<no source map>
    0x
 call ******************************************::0x71d4eb2100000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000005d284d6eeb5f10e382c478a94e6cd325800000000000000000000000000000005d284d3e0796c04f81c140a1f54d43c0200000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000 [1m<no source map>
   call 0xbC9E133D7fcbB155D53ce908245bCB5bb4dF19e4::[1mmintXD([1mLogging, 17590912937527) [1m<no source map>
     emit Transfer(from=0x0000000000000000000000000000000000000000, to=[1mLogging, value=17590912937527) [1m<no source map>
      0x
   call 0xbC9E133D7fcbB155D53ce908245bCB5bb4dF19e4::[1mmintXD(0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a, 45233776125067) [1m<no source map>
     emit Transfer(from=0x0000000000000000000000000000000000000000, to=0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a, value=45233776125067) [1m<no source map>
      0x
   call 0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a::[1mtriggerBoldRewardsXD(45233776125067) [1m<no source map>
      0x
    0x
 call ******************************************::[1mgetTroveStatusXD(7599446695712713487007073444271937529967445864008562083160834908063856696258) [1m<no source map>
    (1)
  0x
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mMCRXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:138)
  (1100000000000000000)
call [1mPriceFeedTestnet::getPrice()() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:139)
  (1978208411201327543955)
emit Log(BA-01: For all operations, weightedRecordedDebt is equal to the sum of trove debt * rate) [1m(/recon/contracts/lib/chimera/src/CryticAsserts.sol:39)

check_withdrawFromSP(): passing
check_batch_zero_debt(): passing
property_AP01(): failed!
  Call sequence:
    CryticTester.target_switchBranch()
    CryticTester.target_switchBranch()
    CryticTester.target_changePrice(2095675818376396703782)
    CryticTester.target_clamped_openTrove(364769084860671056694676296312335971239,197037944697004003271980403250,1147286989469565194124521241616565331934572698568456639409184555762,1436782475058708379189519329728683,32654446981221402392896590863923822930178582122)
    CryticTester.property_AP01()

Traces:
call ******************************************::[1maggWeightedDebtSumXD() [1m(/recon/contracts/src/test/recon/Properties.sol:324)
  (776075045625205781498923804239474351405)
call ******************************************::[1maggRecordedDebtXD() [1m(/recon/contracts/src/test/recon/Properties.sol:324)
  (2046667974887672582535)
emit Log(AP-01: newAggWeightedDebtSum == aggRecordedDebt if all troves have been synced in this block) [1m(/recon/contracts/lib/chimera/src/CryticAsserts.sol:39)

borrower_switchBatchManager(uint256,uint256,uint256,address,uint256,uint256,uint256): failed!
  Call sequence:
    CryticTester.target_switchBranch()
    CryticTester.target_switchBranch()
    CryticTester.target_changePrice(2154185046683540651680)
    CryticTester.target_clamped_openTrove(22492002987316121494162139530040764195,518376055995955238368803227250840276830407441018,94064021245920691600809130976557187708274477108980631285640141708427,319102732566268647768357312247170246224536294,20843352304808684363731541258403588016513066547)
    CryticTester.borrower_switchBatchManager(0,0,0,0x0,0,0,0) Time delay: 1 seconds Block delay: 1

Traces:
call ******************************************::[1mgetTroveIdsCountXD() [1m(/recon/contracts/src/test/recon/Setup.sol:94)
  (1)
call ******************************************::[1mgetTroveFromTroveIdsArrayXD(0) [1m(/recon/contracts/src/test/recon/Setup.sol:97)
  (115290311915943685526855176057731450328982498818200842303377455921312521114869)
call ******************************************::[1mgetCollateralXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:40)
  (0)
call ******************************************::[1mbalanceOfXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:41)
  (49122223169592558982)
call ******************************************::[1mbalanceOfXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:42)
  (100000000000000000000)
call ******************************************::[1mgetTroveIdsCountXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:48)
  (1)
call ******************************************::[1mgetTroveFromTroveIdsArrayXD(0) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:50)
  (115290311915943685526855176057731450328982498818200842303377455921312521114869)
call ******************************************::[1mgetBatchXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:51)
  (0, 0, 0, 0, 0, 0, 0, 0)
call ******************************************::[1mgetLatestTroveDataXD(115290311915943685526855176057731450328982498818200842303377455921312521114869) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:63)
  ((2109497861498746565071, 50840276830407441018, 0, 0, 16533647315770, 2109497844965099249301, 247170246224536294, 521405101750152271173593442826228630494, 0, 1524958792))
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mgetEntireSystemDebtXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:65)
 call ******************************************::[1mgetBoldDebtXD() [1m<no source map>
    (2109497861498746565072)
 call 0x01f816b03111C36098D6394f7Bb6e2538564E351::[1mgetBoldDebtXD() [1m<no source map>
    (0)
  (2109497861498746565072)
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mapplyPendingDebtXD(115290311915943685526855176057731450328982498818200842303377455921312521114869, 0, 1) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:69)
 call ******************************************::[1mgetTroveStatusXD(115290311915943685526855176057731450328982498818200842303377455921312521114869) [1m<no source map>
    (1)
 call ******************************************::[1mgetLatestTroveDataXD(115290311915943685526855176057731450328982498818200842303377455921312521114869) [1m<no source map>
    ((2109497861498746565071, 50840276830407441018, 0, 0, 16533647315770, 2109497844965099249301, 247170246224536294, 521405101750152271173593442826228630494, 0, 1524958792))
 call ******************************************::0xb5a672c2fee4010cbb9f1717b321e2c4a6c8c5eaec90c547a0cce2d0fe50a645f1fdd4f5000000000000000000000000000000000000000000000002c18cf269f6f53a7a0000000000000000000000000000000000000000000000725b29e86f36f5f1cf000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000018842f87c6fa635a039c0d5a769e67dfa000000000000000000000000000000018842f848db0cad878c25bc4e85d083de0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000 [1m<no source map>
   emit TroveUpdated(_troveId=115290311915943685526855176057731450328982498818200842303377455921312521114869, _debt=2109497861498746565071, _coll=50840276830407441018, _stake=50840276830407441018, _annualInterestRate=247170246224536294, _snapshotOfTotalCollRedist=0, _snapshotOfTotalDebtRedist=0) [1m<no source map>
   emit TroveOperation(_troveId=115290311915943685526855176057731450328982498818200842303377455921312521114869, _operation=4, _annualInterestRate=247170246224536294, _debtIncreaseFromRedist=0, _debtIncreaseFromUpfrontFee=0, _debtChangeFromOperation=0, _collIncreaseFromRedist=0, _collChangeFromOperation=0) [1m<no source map>
    0x
 call ******************************************::0x71d4eb21000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000018842f87c6fa635a039c0d5a769e67dfa000000000000000000000000000000018842f848db0cad878c25bc4e85d083de00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000 [1m<no source map>
   call 0xbC9E133D7fcbB155D53ce908245bCB5bb4dF19e4::[1mmintXD([1mLogging, 4629421248416) [1m<no source map>
     emit Transfer(from=0x0000000000000000000000000000000000000000, to=[1mLogging, value=4629421248416) [1m<no source map>
      0x
   call 0xbC9E133D7fcbB155D53ce908245bCB5bb4dF19e4::[1mmintXD(0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a, 11904226067355) [1m<no source map>
     emit Transfer(from=0x0000000000000000000000000000000000000000, to=0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a, value=11904226067355) [1m<no source map>
      0x
   call 0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a::[1mtriggerBoldRewardsXD(11904226067355) [1m<no source map>
      0x
    0x
 call ******************************************::[1mgetTroveStatusXD(115290311915943685526855176057731450328982498818200842303377455921312521114869) [1m<no source map>
    (1)
  0x
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mMCRXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:138)
  (1100000000000000000)
call [1mPriceFeedTestnet::getPrice()() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:139)
  (2154185046683540651680)
emit Log(BA-01: For all operations, weightedRecordedDebt is equal to the sum of trove debt * rate) [1m(/recon/contracts/lib/chimera/src/CryticAsserts.sol:39)

property_SR02(): passing
check_singleDebt(): passing
check_setBatchManagerAnnualInterestRate(): passing
borrower_setInterestIndividualDelegate(uint256,address,uint128,uint128,uint256,uint256,uint256,uint256): failed!
  Call sequence:
    CryticTester.target_changePrice(2045939408491371539624)
    CryticTester.target_clamped_openTrove(1566914627392,37309419603327543986873194093,10585619149125192810676114143304117893191610543470072119698766855826,202127730620617356,1906438865621077231042306)
    *wait* Time delay: 1 seconds Block delay: 1
    CryticTester.borrower_setInterestIndividualDelegate(0,0x0,0,0,0,0,0,0)

Traces:
call ******************************************::[1mgetTroveIdsCountXD() [1m(/recon/contracts/src/test/recon/Setup.sol:94)
  (1)
call ******************************************::[1mgetTroveFromTroveIdsArrayXD(0) [1m(/recon/contracts/src/test/recon/Setup.sol:97)
  (1000234705977523930009083818796983201399638916104674744667566304507422681393)
call ******************************************::[1mgetCollateralXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:40)
  (0)
call ******************************************::[1mbalanceOfXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:41)
  (96634956013126805907)
call ******************************************::[1mbalanceOfXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:42)
  (100000000000000000000)
call ******************************************::[1mgetTroveIdsCountXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:48)
  (1)
call ******************************************::[1mgetTroveFromTroveIdsArrayXD(0) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:50)
  (1000234705977523930009083818796983201399638916104674744667566304507422681393)
call ******************************************::[1mgetBatchXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:51)
  (0, 0, 0, 0, 0, 0, 0, 0)
call ******************************************::[1mgetLatestTroveDataXD(1000234705977523930009083818796983201399638916104674744667566304507422681393) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:63)
  ((2000565134424590402783, 3327543986873194093, 0, 0, 12822478690645, 2000565121602111712138, 202127730620617356, 404369687988194239855388441543718667128, 0, 1524958792))
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mgetEntireSystemDebtXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:65)
 call ******************************************::[1mgetBoldDebtXD() [1m<no source map>
    (2000565134424590402784)
 call 0x01f816b03111C36098D6394f7Bb6e2538564E351::[1mgetBoldDebtXD() [1m<no source map>
    (0)
  (2000565134424590402784)
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mapplyPendingDebtXD(1000234705977523930009083818796983201399638916104674744667566304507422681393, 0, 1) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:69)
 call ******************************************::[1mgetTroveStatusXD(1000234705977523930009083818796983201399638916104674744667566304507422681393) [1m<no source map>
    (1)
 call ******************************************::[1mgetLatestTroveDataXD(1000234705977523930009083818796983201399638916104674744667566304507422681393) [1m<no source map>
    ((2000565134424590402783, 3327543986873194093, 0, 0, 12822478690645, 2000565121602111712138, 202127730620617356, 404369687988194239855388441543718667128, 0, 1524958792))
 call ******************************************::0xb5a672c202361cdf3c450bd881f4f08adc143db1816177536ab01b1934d498003c2da9310000000000000000000000000000000000000000000000002e2dcfa04010566d00000000000000000000000000000000000000000000006c736b1e4f914e44df000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000013036c5027a4c219083628201e8c797f4000000000000000000000000000000013036c4e1c3cf303d476b60b4adb703780000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000 [1m<no source map>
   emit TroveUpdated(_troveId=1000234705977523930009083818796983201399638916104674744667566304507422681393, _debt=2000565134424590402783, _coll=3327543986873194093, _stake=3327543986873194093, _annualInterestRate=202127730620617356, _snapshotOfTotalCollRedist=0, _snapshotOfTotalDebtRedist=0) [1m<no source map>
   emit TroveOperation(_troveId=1000234705977523930009083818796983201399638916104674744667566304507422681393, _operation=4, _annualInterestRate=202127730620617356, _debtIncreaseFromRedist=0, _debtIncreaseFromUpfrontFee=0, _debtChangeFromOperation=0, _collIncreaseFromRedist=0, _collChangeFromOperation=0) [1m<no source map>
    0x
 call ******************************************::0x71d4eb21000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000013036c5027a4c219083628201e8c797f4000000000000000000000000000000013036c4e1c3cf303d476b60b4adb7037800000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000 [1m<no source map>
   call 0xbC9E133D7fcbB155D53ce908245bCB5bb4dF19e4::[1mmintXD([1mLogging, 3590294033381) [1m<no source map>
     emit Transfer(from=0x0000000000000000000000000000000000000000, to=[1mLogging, value=3590294033381) [1m<no source map>
      0x
   call 0xbC9E133D7fcbB155D53ce908245bCB5bb4dF19e4::[1mmintXD(0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a, 9232184657265) [1m<no source map>
     emit Transfer(from=0x0000000000000000000000000000000000000000, to=0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a, value=9232184657265) [1m<no source map>
      0x
   call 0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a::[1mtriggerBoldRewardsXD(9232184657265) [1m<no source map>
      0x
    0x
 call ******************************************::[1mgetTroveStatusXD(1000234705977523930009083818796983201399638916104674744667566304507422681393) [1m<no source map>
    (1)
  0x
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mMCRXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:138)
  (1100000000000000000)
call [1mPriceFeedTestnet::getPrice()() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:139)
  (2045939408491371539624)
emit Log(BA-01: For all operations, weightedRecordedDebt is equal to the sum of trove debt * rate) [1m(/recon/contracts/lib/chimera/src/CryticAsserts.sol:39)

borrower_closeTrove(uint256): failed!
  Call sequence:
    CryticTester.target_changePrice(2107917889660948713688)
    CryticTester.target_clamped_openTrove(340492551508907078359652,8993033651909627206,272099743193173219506098994419256912589204748873411292107287691,155261049023785278755179,2675343487324525039)
    *wait* Time delay: 1 seconds Block delay: 1
    CryticTester.borrower_closeTrove(0)

Traces:
call ******************************************::[1mgetTroveIdsCountXD() [1m(/recon/contracts/src/test/recon/Setup.sol:94)
  (1)
call ******************************************::[1mgetTroveFromTroveIdsArrayXD(0) [1m(/recon/contracts/src/test/recon/Setup.sol:97)
  (57065846034348849420313509665458790240885005559739737413404388851225966599810)
call ******************************************::[1mgetCollateralXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:40)
  (0)
call ******************************************::[1mbalanceOfXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:41)
  (90969466348090372794)
call ******************************************::[1mbalanceOfXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:42)
  (100000000000000000000)
call ******************************************::[1mgetTroveIdsCountXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:48)
  (1)
call ******************************************::[1mgetTroveFromTroveIdsArrayXD(0) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:50)
  (57065846034348849420313509665458790240885005559739737413404388851225966599810)
call ******************************************::[1mgetBatchXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:51)
  (0, 0, 0, 0, 0, 0, 0, 0)
call ******************************************::[1mgetLatestTroveDataXD(57065846034348849420313509665458790240885005559739737413404388851225966599810) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:63)
  ((2089683994943922071656, 8993033651909627206, 0, 0, 3248484884237, 2089683991695437187419, 49023785278755179, 102444219309329133315529867577239893001, 0, 1524958792))
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mgetEntireSystemDebtXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:65)
 call ******************************************::[1mgetBoldDebtXD() [1m<no source map>
    (2089683994943922071657)
 call 0x01f816b03111C36098D6394f7Bb6e2538564E351::[1mgetBoldDebtXD() [1m<no source map>
    (0)
  (2089683994943922071657)
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mapplyPendingDebtXD(57065846034348849420313509665458790240885005559739737413404388851225966599810, 0, 1) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:69)
 call ******************************************::[1mgetTroveStatusXD(57065846034348849420313509665458790240885005559739737413404388851225966599810) [1m<no source map>
    (1)
 call ******************************************::[1mgetLatestTroveDataXD(57065846034348849420313509665458790240885005559739737413404388851225966599810) [1m<no source map>
    ((2089683994943922071656, 8993033651909627206, 0, 0, 3248484884237, 2089683991695437187419, 49023785278755179, 102444219309329133315529867577239893001, 0, 1524958792))
 call ******************************************::0xb5a672c27e2a1fcf0c41f5ad26147c8746b21441fc6c32d098f014c344e269cf8526de820000000000000000000000000000000000000000000000007ccdac7593350d460000000000000000000000000000000000000000000000714830e9c7fba76868000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000004d1209fb463a97e60344118763bd8b78000000000000000000000000000000004d1209f943a79405d59582a1618d74090000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000 [1m<no source map>
   emit TroveUpdated(_troveId=57065846034348849420313509665458790240885005559739737413404388851225966599810, _debt=2089683994943922071656, _coll=8993033651909627206, _stake=8993033651909627206, _annualInterestRate=49023785278755179, _snapshotOfTotalCollRedist=0, _snapshotOfTotalDebtRedist=0) [1m<no source map>
   emit TroveOperation(_troveId=57065846034348849420313509665458790240885005559739737413404388851225966599810, _operation=4, _annualInterestRate=49023785278755179, _debtIncreaseFromRedist=0, _debtIncreaseFromUpfrontFee=0, _debtChangeFromOperation=0, _collIncreaseFromRedist=0, _collChangeFromOperation=0) [1m<no source map>
    0x
 call ******************************************::0x71d4eb21000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000004d1209fb463a97e60344118763bd8b78000000000000000000000000000000004d1209f943a79405d59582a1618d740900000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000 [1m<no source map>
   call 0xbC9E133D7fcbB155D53ce908245bCB5bb4dF19e4::[1mmintXD([1mLogging, 909575767587) [1m<no source map>
     emit Transfer(from=0x0000000000000000000000000000000000000000, to=[1mLogging, value=909575767587) [1m<no source map>
      0x
   call 0xbC9E133D7fcbB155D53ce908245bCB5bb4dF19e4::[1mmintXD(0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a, 2338909116651) [1m<no source map>
     emit Transfer(from=0x0000000000000000000000000000000000000000, to=0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a, value=2338909116651) [1m<no source map>
      0x
   call 0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a::[1mtriggerBoldRewardsXD(2338909116651) [1m<no source map>
      0x
    0x
 call ******************************************::[1mgetTroveStatusXD(57065846034348849420313509665458790240885005559739737413404388851225966599810) [1m<no source map>
    (1)
  0x
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mMCRXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:138)
  (1100000000000000000)
call [1mPriceFeedTestnet::getPrice()() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:139)
  (2107917889660948713688)
emit Log(BA-01: For all operations, weightedRecordedDebt is equal to the sum of trove debt * rate) [1m(/recon/contracts/lib/chimera/src/CryticAsserts.sol:39)

trove_urgentRedemption(uint256,uint256,uint256): failed!
  Call sequence:
    CryticTester.target_switchBranch()
    CryticTester.target_switchBranch()
    CryticTester.target_changePrice(2017654682915320398437)
    CryticTester.target_clamped_openTrove(7146465841591531776948713134303,3822139676933924376,117783034629240084440662999645746813005101380672812776548238292928,8407199640548043535,125529462140280381309792)
    *wait* Time delay: 1 seconds Block delay: 1
    CryticTester.trove_urgentRedemption(0,0,0)

Traces:
call ******************************************::[1mgetTroveIdsCountXD() [1m(/recon/contracts/src/test/recon/Setup.sol:94)
  (1)
call ******************************************::[1mgetTroveFromTroveIdsArrayXD(0) [1m(/recon/contracts/src/test/recon/Setup.sol:97)
  (42819979945104069314328947013144015940341303396344567035325315821728588844559)
call ******************************************::[1mgetCollateralXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:40)
  (0)
call ******************************************::[1mbalanceOfXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:41)
  (96140360323066075624)
call ******************************************::[1mbalanceOfXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:42)
  (100000000000000000000)
call ******************************************::[1mgetTroveIdsCountXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:48)
  (1)
call ******************************************::[1mgetTroveFromTroveIdsArrayXD(0) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:50)
  (42819979945104069314328947013144015940341303396344567035325315821728588844559)
call ******************************************::[1mgetBatchXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:51)
  (0, 0, 0, 0, 0, 0, 0, 0)
call ******************************************::[1mgetLatestTroveDataXD(42819979945104069314328947013144015940341303396344567035325315821728588844559) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:63)
  ((2026445635310657701652, 3822139676933924376, 0, 0, 26165903210106, 2026445609144754491546, 407199640548043535, 825167923633905151966833821693997455110, 0, 1524958792))
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mgetEntireSystemDebtXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:65)
 call ******************************************::[1mgetBoldDebtXD() [1m<no source map>
    (2026445635310657701653)
 call 0x01f816b03111C36098D6394f7Bb6e2538564E351::[1mgetBoldDebtXD() [1m<no source map>
    (0)
  (2026445635310657701653)
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mapplyPendingDebtXD(42819979945104069314328947013144015940341303396344567035325315821728588844559, 0, 1) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:69)
 call ******************************************::[1mgetTroveStatusXD(42819979945104069314328947013144015940341303396344567035325315821728588844559) [1m<no source map>
    (1)
 call ******************************************::[1mgetLatestTroveDataXD(42819979945104069314328947013144015940341303396344567035325315821728588844559) [1m<no source map>
    ((2026445635310657701652, 3822139676933924376, 0, 0, 26165903210106, 2026445609144754491546, 407199640548043535, 825167923633905151966833821693997455110, 0, 1524958792))
 call ******************************************::0xb5a672c25eab3ff1d8cfc78863dc35d2225c3f13044faf9f6a4249036cc38ff9a1b74e0f000000000000000000000000000000000000000000000000350af7c91d92a61800000000000000000000000000000000000000000000006dda9520bef4878714000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000026cc99039a30343b05446d705c806462c000000000000000000000000000000026cc98fb327aba0307884925c397397060000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000 [1m<no source map>
   emit TroveUpdated(_troveId=42819979945104069314328947013144015940341303396344567035325315821728588844559, _debt=2026445635310657701652, _coll=3822139676933924376, _stake=3822139676933924376, _annualInterestRate=407199640548043535, _snapshotOfTotalCollRedist=0, _snapshotOfTotalDebtRedist=0) [1m<no source map>
   emit TroveOperation(_troveId=42819979945104069314328947013144015940341303396344567035325315821728588844559, _operation=4, _annualInterestRate=407199640548043535, _debtIncreaseFromRedist=0, _debtIncreaseFromUpfrontFee=0, _debtChangeFromOperation=0, _collIncreaseFromRedist=0, _collChangeFromOperation=0) [1m<no source map>
    0x
 call ******************************************::0x71d4eb21000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000026cc99039a30343b05446d705c806462c000000000000000000000000000000026cc98fb327aba0307884925c3973970600000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000 [1m<no source map>
   call 0xbC9E133D7fcbB155D53ce908245bCB5bb4dF19e4::[1mmintXD([1mLogging, 7326452898830) [1m<no source map>
     emit Transfer(from=0x0000000000000000000000000000000000000000, to=[1mLogging, value=7326452898830) [1m<no source map>
      0x
   call 0xbC9E133D7fcbB155D53ce908245bCB5bb4dF19e4::[1mmintXD(0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a, 18839450311277) [1m<no source map>
     emit Transfer(from=0x0000000000000000000000000000000000000000, to=0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a, value=18839450311277) [1m<no source map>
      0x
   call 0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a::[1mtriggerBoldRewardsXD(18839450311277) [1m<no source map>
      0x
    0x
 call ******************************************::[1mgetTroveStatusXD(42819979945104069314328947013144015940341303396344567035325315821728588844559) [1m<no source map>
    (1)
  0x
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mMCRXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:138)
  (1100000000000000000)
call [1mPriceFeedTestnet::getPrice()() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:139)
  (2017654682915320398437)
emit Log(BA-01: For all operations, weightedRecordedDebt is equal to the sum of trove debt * rate) [1m(/recon/contracts/lib/chimera/src/CryticAsserts.sol:39)

pool_withdrawFromSP(uint256,bool): failed!
  Call sequence:
    CryticTester.target_switchBranch()
    CryticTester.target_switchBranch()
    CryticTester.target_changePrice(2034590756990073974841)
    CryticTester.target_clamped_openTrove(76705127052395741469971,634775156401564173631940,9670869682307855783835698463157494629016920459623521292839478593499909,1081444760462592126510997,6701220862197131401018)
    *wait* Time delay: 1 seconds Block delay: 1
    CryticTester.pool_withdrawFromSP(0,false)

Traces:
call ******************************************::[1mgetCollateralXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:40)
  (0)
call ******************************************::[1mbalanceOfXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:41)
  (24806098435826368060)
call ******************************************::[1mbalanceOfXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:42)
  (100000000000000000000)
call ******************************************::[1mgetTroveIdsCountXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:48)
  (1)
call ******************************************::[1mgetTroveFromTroveIdsArrayXD(0) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:50)
  (75618617559272532610931009790405678057444019412878568060038852775714901861758)
call ******************************************::[1mgetBatchXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:51)
  (0, 0, 0, 0, 0, 0, 0, 0)
call ******************************************::[1mgetLatestTroveDataXD(75618617559272532610931009790405678057444019412878568060038852775714901861758) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:63)
  ((2025502241305136397008, 75156401564173631940, 0, 0, 48843183905300, 2025502192461952491708, 760462592126510997, 1540318647637547555019171681428613312876, 0, 1524958792))
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mgetEntireSystemDebtXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:65)
 call ******************************************::[1mgetBoldDebtXD() [1m<no source map>
    (2025502241305136397009)
 call 0x01f816b03111C36098D6394f7Bb6e2538564E351::[1mgetBoldDebtXD() [1m<no source map>
    (0)
  (2025502241305136397009)
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mapplyPendingDebtXD(75618617559272532610931009790405678057444019412878568060038852775714901861758, 0, 1) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:69)
 call ******************************************::[1mgetTroveStatusXD(75618617559272532610931009790405678057444019412878568060038852775714901861758) [1m<no source map>
    (1)
 call ******************************************::[1mgetLatestTroveDataXD(75618617559272532610931009790405678057444019412878568060038852775714901861758) [1m<no source map>
    ((2025502241305136397008, 75156401564173631940, 0, 0, 48843183905300, 2025502192461952491708, 760462592126510997, 1540318647637547555019171681428613312876, 0, 1524958792))
 call ******************************************::0xb5a672c2a72e9eed92c618e2d89103025d9b37c6bbdde54e2a78c48dd0fbc5278626257e00000000000000000000000000000000000000000000000413012d06e52e81c400000000000000000000000000000000000000000000006dcd7d84e56e32fad00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000486ce96801515ecb4f4d9f79dea55ab100000000000000000000000000000000486ce94ab443dc7efdf976fa8a353316c0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000 [1m<no source map>
   emit TroveUpdated(_troveId=75618617559272532610931009790405678057444019412878568060038852775714901861758, _debt=2025502241305136397008, _coll=75156401564173631940, _stake=75156401564173631940, _annualInterestRate=760462592126510997, _snapshotOfTotalCollRedist=0, _snapshotOfTotalDebtRedist=0) [1m<no source map>
   emit TroveOperation(_troveId=75618617559272532610931009790405678057444019412878568060038852775714901861758, _operation=4, _annualInterestRate=760462592126510997, _debtIncreaseFromRedist=0, _debtIncreaseFromUpfrontFee=0, _debtChangeFromOperation=0, _collIncreaseFromRedist=0, _collChangeFromOperation=0) [1m<no source map>
    0x
 call ******************************************::0x71d4eb210000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000486ce96801515ecb4f4d9f79dea55ab100000000000000000000000000000000486ce94ab443dc7efdf976fa8a353316c00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000 [1m<no source map>
   call 0xbC9E133D7fcbB155D53ce908245bCB5bb4dF19e4::[1mmintXD([1mLogging, 13676091493485) [1m<no source map>
     emit Transfer(from=0x0000000000000000000000000000000000000000, to=[1mLogging, value=13676091493485) [1m<no source map>
      0x
   call 0xbC9E133D7fcbB155D53ce908245bCB5bb4dF19e4::[1mmintXD(0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a, 35167092411816) [1m<no source map>
     emit Transfer(from=0x0000000000000000000000000000000000000000, to=0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a, value=35167092411816) [1m<no source map>
      0x
   call 0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a::[1mtriggerBoldRewardsXD(35167092411816) [1m<no source map>
      0x
    0x
 call ******************************************::[1mgetTroveStatusXD(75618617559272532610931009790405678057444019412878568060038852775714901861758) [1m<no source map>
    (1)
  0x
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mMCRXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:138)
  (1100000000000000000)
call [1mPriceFeedTestnet::getPrice()() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:139)
  (2034590756990073974841)
emit Log(BA-01: For all operations, weightedRecordedDebt is equal to the sum of trove debt * rate) [1m(/recon/contracts/lib/chimera/src/CryticAsserts.sol:39)

check_removeInterestIndividualDelegate(): passing
property_CS05(): passing
check_shutdown(): passing
borrower_registerBatchManager(uint128,uint128,uint128,uint128,uint128): failed!
  Call sequence:
    CryticTester.target_switchBranch()
    CryticTester.target_switchBranch()
    CryticTester.target_changePrice(2123131161372207499305)
    CryticTester.target_clamped_openTrove(250929061415543261,1459379983883332283,5314437430429298416805246772181220908979681160293258937298236,12125202718860015969,7149485738979440637)
    *wait* Time delay: 1 seconds Block delay: 1
    CryticTester.borrower_registerBatchManager(0,0,0,0,0)

Traces:
call ******************************************::[1mgetCollateralXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:40)
  (0)
call ******************************************::[1mbalanceOfXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:41)
  (98503120016116667717)
call ******************************************::[1mbalanceOfXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:42)
  (100000000000000000000)
call ******************************************::[1mgetTroveIdsCountXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:48)
  (1)
call ******************************************::[1mgetTroveFromTroveIdsArrayXD(0) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:50)
  (111631805554598874815588952061587902363819350558761314572199018540763539819130)
call ******************************************::[1mgetBatchXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:51)
  (0, 0, 0, 0, 0, 0, 0, 0)
call ******************************************::[1mgetLatestTroveDataXD(111631805554598874815588952061587902363819350558761314572199018540763539819130) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:63)
  ((2033577441633729801420, 1459379983883332283, 0, 0, 8073611862446, 2033577433560117938974, 125202718860015969, 254609423694100249419840880373007475806, 0, 1524958792))
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mgetEntireSystemDebtXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:65)
 call ******************************************::[1mgetBoldDebtXD() [1m<no source map>
    (2033577441633729801421)
 call 0x01f816b03111C36098D6394f7Bb6e2538564E351::[1mgetBoldDebtXD() [1m<no source map>
    (0)
  (2033577441633729801421)
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mapplyPendingDebtXD(111631805554598874815588952061587902363819350558761314572199018540763539819130, 0, 1) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:69)
 call ******************************************::[1mgetTroveStatusXD(111631805554598874815588952061587902363819350558761314572199018540763539819130) [1m<no source map>
    (1)
 call ******************************************::[1mgetLatestTroveDataXD(111631805554598874815588952061587902363819350558761314572199018540763539819130) [1m<no source map>
    ((2033577441633729801420, 1459379983883332283, 0, 0, 8073611862446, 2033577433560117938974, 125202718860015969, 254609423694100249419840880373007475806, 0, 1524958792))
 call ******************************************::0xb5a672c2f6cd5ce5c12db9eb4bfc800b8376fc1e99d6f02ff88933def63886f716e3ca7a0000000000000000000000000000000000000000000000001440c25cfbae6abb00000000000000000000000000000000000000000000006e3d8e64cf92206ccc00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000bf8bfdd40234ed8c9c0bfe56cc75a54c00000000000000000000000000000000bf8bfdc7400333e383daaa689201645e0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000 [1m<no source map>
   emit TroveUpdated(_troveId=111631805554598874815588952061587902363819350558761314572199018540763539819130, _debt=2033577441633729801420, _coll=1459379983883332283, _stake=1459379983883332283, _annualInterestRate=125202718860015969, _snapshotOfTotalCollRedist=0, _snapshotOfTotalDebtRedist=0) [1m<no source map>
   emit TroveOperation(_troveId=111631805554598874815588952061587902363819350558761314572199018540763539819130, _operation=4, _annualInterestRate=125202718860015969, _debtIncreaseFromRedist=0, _debtIncreaseFromUpfrontFee=0, _debtChangeFromOperation=0, _collIncreaseFromRedist=0, _collChangeFromOperation=0) [1m<no source map>
    0x
 call ******************************************::0x71d4eb2100000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000bf8bfdd40234ed8c9c0bfe56cc75a54c00000000000000000000000000000000bf8bfdc7400333e383daaa689201645e00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000 [1m<no source map>
   call 0xbC9E133D7fcbB155D53ce908245bCB5bb4dF19e4::[1mmintXD([1mLogging, 2260611321486) [1m<no source map>
     emit Transfer(from=0x0000000000000000000000000000000000000000, to=[1mLogging, value=2260611321486) [1m<no source map>
      0x
   call 0xbC9E133D7fcbB155D53ce908245bCB5bb4dF19e4::[1mmintXD(0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a, 5813000540961) [1m<no source map>
     emit Transfer(from=0x0000000000000000000000000000000000000000, to=0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a, value=5813000540961) [1m<no source map>
      0x
   call 0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a::[1mtriggerBoldRewardsXD(5813000540961) [1m<no source map>
      0x
    0x
 call ******************************************::[1mgetTroveStatusXD(111631805554598874815588952061587902363819350558761314572199018540763539819130) [1m<no source map>
    (1)
  0x
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mMCRXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:138)
  (1100000000000000000)
call [1mPriceFeedTestnet::getPrice()() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:139)
  (2123131161372207499305)
emit Log(BA-01: For all operations, weightedRecordedDebt is equal to the sum of trove debt * rate) [1m(/recon/contracts/lib/chimera/src/CryticAsserts.sol:39)

check_unredeemableTrove(): passing
check_adjustUnredeemableTrove(): passing
borrower_adjustTrove(uint256,uint256,bool,uint256,bool,uint256): failed!
  Call sequence:
    CryticTester.target_switchBranch()
    CryticTester.target_switchBranch()
    CryticTester.target_changePrice(2058853159032137639207)
    CryticTester.target_clamped_openTrove(1922150722630532491566213748391947444582,272669040231278698107483384447583026375,151693581511053155366017878170180473785440765033968751758770797,453688467678620352843834824600684751550643180,921340573920424158087591328481501176235561025)
    *wait* Time delay: 1 seconds Block delay: 1
    CryticTester.borrower_adjustTrove(0,0,false,0,false,0)

Traces:
call ******************************************::[1mgetTroveIdsCountXD() [1m(/recon/contracts/src/test/recon/Setup.sol:94)
  (1)
call ******************************************::[1mgetTroveFromTroveIdsArrayXD(0) [1m(/recon/contracts/src/test/recon/Setup.sol:97)
  (107035423567180712599367756847045798109990345422870680498307224143949928455911)
call ******************************************::[1mgetCollateralXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:40)
  (0)
call ******************************************::[1mbalanceOfXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:41)
  (92479115552416973625)
call ******************************************::[1mbalanceOfXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:42)
  (100000000000000000000)
call ******************************************::[1mgetTroveIdsCountXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:48)
  (1)
call ******************************************::[1mgetTroveFromTroveIdsArrayXD(0) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:50)
  (107035423567180712599367756847045798109990345422870680498307224143949928455911)
call ******************************************::[1mgetBatchXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:51)
  (0, 0, 0, 0, 0, 0, 0, 0)
call ******************************************::[1mgetLatestTroveDataXD(107035423567180712599367756847045798109990345422870680498307224143949928455911) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:63)
  ((2020620447824322416922, 7483384447583026375, 0, 0, 38487946111123, 2020620409336376305799, 600684751550643180, 1213755868560380124261056109750813800820, 0, 1524958792))
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mgetEntireSystemDebtXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:65)
 call ******************************************::[1mgetBoldDebtXD() [1m<no source map>
    (2020620447824322416923)
 call 0x01f816b03111C36098D6394f7Bb6e2538564E351::[1mgetBoldDebtXD() [1m<no source map>
    (0)
  (2020620447824322416923)
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mapplyPendingDebtXD(107035423567180712599367756847045798109990345422870680498307224143949928455911, 0, 1) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:69)
 call ******************************************::[1mgetTroveStatusXD(107035423567180712599367756847045798109990345422870680498307224143949928455911) [1m<no source map>
    (1)
 call ******************************************::[1mgetLatestTroveDataXD(107035423567180712599367756847045798109990345422870680498307224143949928455911) [1m<no source map>
    ((2020620447824322416922, 7483384447583026375, 0, 0, 38487946111123, 2020620409336376305799, 600684751550643180, 1213755868560380124261056109750813800820, 0, 1524958792))
 call ******************************************::0xb5a672c2eca3e7225e4267c52634234c349f9175af206fef5c5b80faa2f449488a35dee700000000000000000000000000000000000000000000000067da5281a3de7cc700000000000000000000000000000000000000000000006d89bde7971b76d11a000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000039120e1b6d791293a51f21f3d435e41f8000000000000000000000000000000039120e09309a76040e7a95a0c602539740000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000 [1m<no source map>
   emit TroveUpdated(_troveId=107035423567180712599367756847045798109990345422870680498307224143949928455911, _debt=2020620447824322416922, _coll=7483384447583026375, _stake=7483384447583026375, _annualInterestRate=600684751550643180, _snapshotOfTotalCollRedist=0, _snapshotOfTotalDebtRedist=0) [1m<no source map>
   emit TroveOperation(_troveId=107035423567180712599367756847045798109990345422870680498307224143949928455911, _operation=4, _annualInterestRate=600684751550643180, _debtIncreaseFromRedist=0, _debtIncreaseFromUpfrontFee=0, _debtChangeFromOperation=0, _collIncreaseFromRedist=0, _collChangeFromOperation=0) [1m<no source map>
    0x
 call ******************************************::0x71d4eb21000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000039120e1b6d791293a51f21f3d435e41f8000000000000000000000000000000039120e09309a76040e7a95a0c6025397400000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000 [1m<no source map>
   call 0xbC9E133D7fcbB155D53ce908245bCB5bb4dF19e4::[1mmintXD([1mLogging, 10776624911115) [1m<no source map>
     emit Transfer(from=0x0000000000000000000000000000000000000000, to=[1mLogging, value=10776624911115) [1m<no source map>
      0x
   call 0xbC9E133D7fcbB155D53ce908245bCB5bb4dF19e4::[1mmintXD(0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a, 27711321200009) [1m<no source map>
     emit Transfer(from=0x0000000000000000000000000000000000000000, to=0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a, value=27711321200009) [1m<no source map>
      0x
   call 0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a::[1mtriggerBoldRewardsXD(27711321200009) [1m<no source map>
      0x
    0x
 call ******************************************::[1mgetTroveStatusXD(107035423567180712599367756847045798109990345422870680498307224143949928455911) [1m<no source map>
    (1)
  0x
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mMCRXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:138)
  (1100000000000000000)
call [1mPriceFeedTestnet::getPrice()() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:139)
  (2058853159032137639207)
emit Log(BA-01: For all operations, weightedRecordedDebt is equal to the sum of trove debt * rate) [1m(/recon/contracts/lib/chimera/src/CryticAsserts.sol:39)

target_switchBranch(): passing
check_aggBatchManagementFees_zero(): passing
check_claimAllCollGains(): passing
check_batch_zero_shares(): passing
check_openedTrove(): passing
check_setInterestBatchManager(): passing
check_switchBatchManager(): passing
property_CS04(): passing
check_claimCollateral(): passing
target_clamped_openTrove(uint256,uint256,uint256,uint256,uint256): failed!
  Call sequence:
    CryticTester.target_changePrice(2066007546983489073971)
    CryticTester.target_clamped_openTrove(0,1799318319520317785,580217775542684553876391644297444746062961589317741420,4868671082016997826717806312,763260085157297376)
    CryticTester.borrower_removeFromBatch(0,0,0,0,0)
    CryticTester.target_switchBranch()
    CryticTester.target_clamped_openTrove(0,0,0,0,0)

Traces:
call ******************************************::[1msymbolXD() [1m(/recon/contracts/src/test/recon/TargetFunctions.sol:97)
  (0x73744554480000000000000000000000000000000000000000000000000000000000000001)
call ******************************************::[1msymbolXD() [1m(/recon/contracts/src/test/recon/TargetFunctions.sol:98)
  (WETH)
call ******************************************::[1mbalanceOfXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/TargetFunctions.sol:102)
  (100000000000000000000)
call [1mPriceFeedTestnet::getPrice()() [1m(/recon/contracts/src/test/recon/TargetFunctions.sol:106)
  (200000000000000000000)
call ******************************************::[1mgetCollateralXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:40)
  (0)
call ******************************************::[1mbalanceOfXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:41)
  (98163181680479682215)
call ******************************************::[1mbalanceOfXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:42)
  (100000000000000000000)
call ******************************************::[1mgetTroveIdsCountXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:48)
  (0)
emit Log(CP-01: Total debt == SUM(userDebt)) [1m(/recon/contracts/lib/chimera/src/CryticAsserts.sol:39)

check_adjustTroveInterestRate(): passing
trove_batchLiquidateTroves(uint256): failed!
  Call sequence:
    CryticTester.target_switchBranch()
    CryticTester.target_switchBranch()
    CryticTester.target_changePrice(2072958901696706022666)
    CryticTester.target_clamped_openTrove(6777363433783432930970215667101509,228814005681780716771721929086298718683,20330865849112233367559587857085975613061301129274526189670550113299332,29378532058356164161982428841,11237438060652691198311)
    *wait* Time delay: 1 seconds Block delay: 1
    CryticTester.trove_batchLiquidateTroves(0)

Traces:
call ******************************************::[1mgetTroveIdsCountXD() [1m(/recon/contracts/src/test/recon/Setup.sol:94)
  (1)
call ******************************************::[1mgetTroveFromTroveIdsArrayXD(0) [1m(/recon/contracts/src/test/recon/Setup.sol:97)
  (59530874200037103495826621531175857844376222695369361670579434184289162424503)
call ******************************************::[1mgetCollateralXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:40)
  (0)
call ******************************************::[1mbalanceOfXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:41)
  (28240570913701281317)
call ******************************************::[1mbalanceOfXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:42)
  (100000000000000000000)
call ******************************************::[1mgetTroveIdsCountXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:48)
  (1)
call ******************************************::[1mgetTroveFromTroveIdsArrayXD(0) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:50)
  (59530874200037103495826621531175857844376222695369361670579434184289162424503)
call ******************************************::[1mgetBatchXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:51)
  (0, 0, 0, 0, 0, 0, 0, 0)
call ******************************************::[1mgetLatestTroveDataXD(59530874200037103495826621531175857844376222695369361670579434184289162424503) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:63)
  ((2006265358449119576510, 71721929086298718683, 0, 0, 22658542999627, 2006265335790576576883, 356164161982428841, 714559812036246906790997655311913082603, 0, 1524958792))
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mgetEntireSystemDebtXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:65)
 call ******************************************::[1mgetBoldDebtXD() [1m<no source map>
    (2006265358449119576511)
 call 0x01f816b03111C36098D6394f7Bb6e2538564E351::[1mgetBoldDebtXD() [1m<no source map>
    (0)
  (2006265358449119576511)
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mapplyPendingDebtXD(59530874200037103495826621531175857844376222695369361670579434184289162424503, 0, 1) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:69)
 call ******************************************::[1mgetTroveStatusXD(59530874200037103495826621531175857844376222695369361670579434184289162424503) [1m<no source map>
    (1)
 call ******************************************::[1mgetLatestTroveDataXD(59530874200037103495826621531175857844376222695369361670579434184289162424503) [1m<no source map>
    ((2006265358449119576510, 71721929086298718683, 0, 0, 22658542999627, 2006265335790576576883, 356164161982428841, 714559812036246906790997655311913082603, 0, 1524958792))
 call ******************************************::0xb5a672c2839d47df62c1a8c409e16708c9fd27946208da72fdd28eaf3abcaa81f180f0b7000000000000000000000000000000000000000000000003e3577a81429781db00000000000000000000000000000000000000000000006cc28661dc2b83c1be00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002199337286117bc72fff33ec79f097a6e00000000000000000000000000000002199336c284ff08b0e91a344731b45eeb0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000 [1m<no source map>
   emit TroveUpdated(_troveId=59530874200037103495826621531175857844376222695369361670579434184289162424503, _debt=2006265358449119576510, _coll=71721929086298718683, _stake=71721929086298718683, _annualInterestRate=356164161982428841, _snapshotOfTotalCollRedist=0, _snapshotOfTotalDebtRedist=0) [1m<no source map>
   emit TroveOperation(_troveId=59530874200037103495826621531175857844376222695369361670579434184289162424503, _operation=4, _annualInterestRate=356164161982428841, _debtIncreaseFromRedist=0, _debtIncreaseFromUpfrontFee=0, _debtChangeFromOperation=0, _collIncreaseFromRedist=0, _collChangeFromOperation=0) [1m<no source map>
    0x
 call ******************************************::0x71d4eb2100000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002199337286117bc72fff33ec79f097a6e00000000000000000000000000000002199336c284ff08b0e91a344731b45eeb00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000 [1m<no source map>
   call 0xbC9E133D7fcbB155D53ce908245bCB5bb4dF19e4::[1mmintXD([1mLogging, 6344392039896) [1m<no source map>
     emit Transfer(from=0x0000000000000000000000000000000000000000, to=[1mLogging, value=6344392039896) [1m<no source map>
      0x
   call 0xbC9E133D7fcbB155D53ce908245bCB5bb4dF19e4::[1mmintXD(0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a, 16314150959732) [1m<no source map>
     emit Transfer(from=0x0000000000000000000000000000000000000000, to=0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a, value=16314150959732) [1m<no source map>
      0x
   call 0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a::[1mtriggerBoldRewardsXD(16314150959732) [1m<no source map>
      0x
    0x
 call ******************************************::[1mgetTroveStatusXD(59530874200037103495826621531175857844376222695369361670579434184289162424503) [1m<no source map>
    (1)
  0x
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mMCRXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:138)
  (1100000000000000000)
call [1mPriceFeedTestnet::getPrice()() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:139)
  (2072958901696706022666)
emit Log(BA-01: For all operations, weightedRecordedDebt is equal to the sum of trove debt * rate) [1m(/recon/contracts/lib/chimera/src/CryticAsserts.sol:39)

borrower_openTrove(uint256,uint256,uint256,uint256,uint256,uint256,uint256,address,address,address): failed!
  Call sequence:
    CryticTester.target_switchBranch()
    CryticTester.target_switchBranch()
    CryticTester.target_changePrice(2172265993492405814107)
    CryticTester.target_clamped_openTrove(133112715396482474625097017441438562825,5415601960000050815416266247867426592476461690122,23004059225043194236872988016758735645935997554840650773956521,58818849919468489172355323560360436700226,433246023090841195361569089694574900657851127)
    CryticTester.borrower_openTrove(0,0,0,0,0,0,0,0x0,0x0,0x0) Time delay: 1 seconds Block delay: 1

Traces:
call ******************************************::[1mgetCollateralXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:40)
  (0)
call ******************************************::[1mbalanceOfXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:41)
  (32535907523538309878)
call ******************************************::[1mbalanceOfXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:42)
  (100000000000000000000)
call ******************************************::[1mgetTroveIdsCountXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:48)
  (1)
call ******************************************::[1mgetTroveFromTroveIdsArrayXD(0) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:50)
  (50782028518904066572293589213291037743437722472971635478645368960692300394317)
call ******************************************::[1mgetBatchXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:51)
  (0, 0, 0, 0, 0, 0, 0, 0)
call ******************************************::[1mgetLatestTroveDataXD(50782028518904066572293589213291037743437722472971635478645368960692300394317) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:63)
  ((2116503200506682639602, 67426592476461690122, 0, 0, 21715389757582, 2116503178791292882020, 323560360436700226, 684816531395132506279704735610325336520, 0, 1524958792))
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mgetEntireSystemDebtXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:65)
 call ******************************************::[1mgetBoldDebtXD() [1m<no source map>
    (2116503200506682639603)
 call 0x01f816b03111C36098D6394f7Bb6e2538564E351::[1mgetBoldDebtXD() [1m<no source map>
    (0)
  (2116503200506682639603)
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mapplyPendingDebtXD(50782028518904066572293589213291037743437722472971635478645368960692300394317, 0, 1) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:69)
 call ******************************************::[1mgetTroveStatusXD(50782028518904066572293589213291037743437722472971635478645368960692300394317) [1m<no source map>
    (1)
 call ******************************************::[1mgetLatestTroveDataXD(50782028518904066572293589213291037743437722472971635478645368960692300394317) [1m<no source map>
    ((2116503200506682639602, 67426592476461690122, 0, 0, 21715389757582, 2116503178791292882020, 323560360436700226, 684816531395132506279704735610325336520, 0, 1524958792))
 call ******************************************::0xb5a672c270459c0d445c153b14801f85611df94b72685ea3720ce430f2bf24423959af4d000000000000000000000000000000000000000000000003a7bb609dae866d0a000000000000000000000000000000000000000000000072bc61df25722d84f2000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000020332de268cea5f86b65edc3862f32e64000000000000000000000000000000020332ddcddde96a1882908436f43e81c80000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000 [1m<no source map>
   emit TroveUpdated(_troveId=50782028518904066572293589213291037743437722472971635478645368960692300394317, _debt=2116503200506682639602, _coll=67426592476461690122, _stake=67426592476461690122, _annualInterestRate=323560360436700226, _snapshotOfTotalCollRedist=0, _snapshotOfTotalDebtRedist=0) [1m<no source map>
   emit TroveOperation(_troveId=50782028518904066572293589213291037743437722472971635478645368960692300394317, _operation=4, _annualInterestRate=323560360436700226, _debtIncreaseFromRedist=0, _debtIncreaseFromUpfrontFee=0, _debtChangeFromOperation=0, _collIncreaseFromRedist=0, _collChangeFromOperation=0) [1m<no source map>
    0x
 call ******************************************::0x71d4eb21000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000020332de268cea5f86b65edc3862f32e64000000000000000000000000000000020332ddcddde96a1882908436f43e81c800000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000 [1m<no source map>
   call 0xbC9E133D7fcbB155D53ce908245bCB5bb4dF19e4::[1mmintXD([1mLogging, 6080309132124) [1m<no source map>
     emit Transfer(from=0x0000000000000000000000000000000000000000, to=[1mLogging, value=6080309132124) [1m<no source map>
      0x
   call 0xbC9E133D7fcbB155D53ce908245bCB5bb4dF19e4::[1mmintXD(0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a, 15635080625459) [1m<no source map>
     emit Transfer(from=0x0000000000000000000000000000000000000000, to=0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a, value=15635080625459) [1m<no source map>
      0x
   call 0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a::[1mtriggerBoldRewardsXD(15635080625459) [1m<no source map>
      0x
    0x
 call ******************************************::[1mgetTroveStatusXD(50782028518904066572293589213291037743437722472971635478645368960692300394317) [1m<no source map>
    (1)
  0x
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mMCRXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:138)
  (1100000000000000000)
call [1mPriceFeedTestnet::getPrice()() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:139)
  (2172265993492405814107)
emit Log(BA-01: For all operations, weightedRecordedDebt is equal to the sum of trove debt * rate) [1m(/recon/contracts/lib/chimera/src/CryticAsserts.sol:39)

borrower_lowerBatchManagementFee(uint256): failed!
  Call sequence:
    CryticTester.target_changePrice(2118379089348581815918)
    CryticTester.target_clamped_openTrove(0,1535628794990988009,3078072699694679940631140634991346918156222751590566857,173234553181707105757715810389156,6381138804150924203)
    *wait* Time delay: 1 seconds Block delay: 1
    CryticTester.borrower_lowerBatchManagementFee(0)

Traces:
call ******************************************::[1mgetCollateralXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:40)
  (0)
call ******************************************::[1mbalanceOfXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:41)
  (98426871205009011991)
call ******************************************::[1mbalanceOfXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:42)
  (100000000000000000000)
call ******************************************::[1mgetTroveIdsCountXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:48)
  (1)
call ******************************************::[1mgetTroveFromTroveIdsArrayXD(0) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:50)
  (8648653994422793423523514607345318943026410206026333401532383970483429052211)
call ******************************************::[1mgetBatchXD([1mCryticTester) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:51)
  (0, 0, 0, 0, 0, 0, 0, 0)
call ******************************************::[1mgetLatestTroveDataXD(8648653994422793423523514607345318943026410206026333401532383970483429052211) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:63)
  ((2075743838901194061772, 1535628794990988009, 0, 0, 6961121457175, 2075743831940072604597, 105757715810389156, 219525926273486387634398496473084550132, 0, 1524958792))
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mgetEntireSystemDebtXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:65)
 call ******************************************::[1mgetBoldDebtXD() [1m<no source map>
    (2075743838901194061773)
 call 0x01f816b03111C36098D6394f7Bb6e2538564E351::[1mgetBoldDebtXD() [1m<no source map>
    (0)
  (2075743838901194061773)
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mapplyPendingDebtXD(8648653994422793423523514607345318943026410206026333401532383970483429052211, 0, 1) [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:69)
 call ******************************************::[1mgetTroveStatusXD(8648653994422793423523514607345318943026410206026333401532383970483429052211) [1m<no source map>
    (1)
 call ******************************************::[1mgetLatestTroveDataXD(8648653994422793423523514607345318943026410206026333401532383970483429052211) [1m<no source map>
    ((2075743838901194061772, 1535628794990988009, 0, 0, 6961121457175, 2075743831940072604597, 105757715810389156, 219525926273486387634398496473084550132, 0, 1524958792))
 call ******************************************::0xb5a672c2131ef6f5e9ad7f4b2efd52cffeb247a384f286ced0b2014c98237c55de760f33000000000000000000000000000000000000000000000000154fa640c04c8ae900000000000000000000000000000000000000000000007086bb87b7eb55f7cc00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000a52727d8016143828bd1055273ae4eb000000000000000000000000000000000a52727ceb69d43178d0d1112f9fb5bf40000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000 [1m<no source map>
   emit TroveUpdated(_troveId=8648653994422793423523514607345318943026410206026333401532383970483429052211, _debt=2075743838901194061772, _coll=1535628794990988009, _stake=1535628794990988009, _annualInterestRate=105757715810389156, _snapshotOfTotalCollRedist=0, _snapshotOfTotalDebtRedist=0) [1m<no source map>
   emit TroveOperation(_troveId=8648653994422793423523514607345318943026410206026333401532383970483429052211, _operation=4, _annualInterestRate=105757715810389156, _debtIncreaseFromRedist=0, _debtIncreaseFromUpfrontFee=0, _debtChangeFromOperation=0, _collIncreaseFromRedist=0, _collChangeFromOperation=0) [1m<no source map>
    0x
 call ******************************************::0x71d4eb2100000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000a52727d8016143828bd1055273ae4eb000000000000000000000000000000000a52727ceb69d43178d0d1112f9fb5bf400000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000 [1m<no source map>
   call 0xbC9E133D7fcbB155D53ce908245bCB5bb4dF19e4::[1mmintXD([1mLogging, 1949114008010) [1m<no source map>
     emit Transfer(from=0x0000000000000000000000000000000000000000, to=[1mLogging, value=1949114008010) [1m<no source map>
      0x
   call 0xbC9E133D7fcbB155D53ce908245bCB5bb4dF19e4::[1mmintXD(0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a, 5012007449166) [1m<no source map>
     emit Transfer(from=0x0000000000000000000000000000000000000000, to=0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a, value=5012007449166) [1m<no source map>
      0x
   call 0x2bFAf6D06e3843c2b531fFed796a27ce3E73126a::[1mtriggerBoldRewardsXD(5012007449166) [1m<no source map>
      0x
    0x
 call ******************************************::[1mgetTroveStatusXD(8648653994422793423523514607345318943026410206026333401532383970483429052211) [1m<no source map>
    (1)
  0x
call 0x2074237E4D46843D787B4c8A8eA642F5c3c855fb::[1mMCRXD() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:138)
  (1100000000000000000)
call [1mPriceFeedTestnet::getPrice()() [1m(/recon/contracts/src/test/recon/BeforeAfter.sol:139)
  (2118379089348581815918)
emit Log(BA-01: For all operations, weightedRecordedDebt is equal to the sum of trove debt * rate) [1m(/recon/contracts/lib/chimera/src/CryticAsserts.sol:39)

AssertionFailed(..): passing


Unique instructions: 31653
Unique codehashes: 13
Corpus size: 34
Seed: 8466549472236835941

[2024-09-19 17:38:25.52] Saving test reproducers... Done! (0.018568449s)
[2024-09-19 17:38:25.54] Saving corpus... Done! (2.609434944s)
[2024-09-19 17:38:28.15] Saving coverage... Done! (7.256655711s)
