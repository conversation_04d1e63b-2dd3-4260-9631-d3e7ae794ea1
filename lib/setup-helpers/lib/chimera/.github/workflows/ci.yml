name: CI

on:
  pull_request:
    types: [opened, synchronize]
  push:
    branches:
      - main

jobs:
  test:
    strategy:
      fail-fast: true

    name: Foundry
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
        with:
          submodules: recursive

      - name: Setup Foundry
        uses: foundry-rs/foundry-toolchain@v1
        with:
          version: nightly

      - name: Format
        run: |
          forge fmt --check
        id: fmt

      - name: Build
        run: |
          forge build
        id: build

  slither:
    strategy:
      fail-fast: true

    name: Slither
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
        with:
          submodules: recursive

      - name: Setup Foundry
        uses: foundry-rs/foundry-toolchain@v1
        with:
          version: nightly

      - name: Run Slither
        uses: crytic/slither-action@v0.3.0
        id: slither
        with:
          fail-on: low

  solhint:
    strategy:
      fail-fast: true

    name: Solhint
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
        with:
          submodules: recursive

      - name: Install dependencies
        run: npm install --global solhint

      - name: Run solhint
        run: solhint