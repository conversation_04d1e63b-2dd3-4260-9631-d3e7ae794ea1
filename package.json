{"name": "@recon-fuzz/log-parser", "description": "Fuzzer log parser for Recon Fuzz", "version": "0.0.31", "license": "GPL-2.0-only", "main": "./lib/cjs/index.js", "module": "./lib/esm/index.js", "types": "./lib/esm/index.d.ts", "files": ["lib/cjs", "lib/esm"], "exports": {"require": "./lib/cjs/index.js", "import": "./lib/esm/index.js"}, "author": "0xsi <<EMAIL>>", "scripts": {"test": "jest", "build": "tsc && tsc -p tsconfig.cjs.json"}, "dependencies": {"@babel/parser": "^7.25.6", "ethereumjs-util": "^7.1.5", "ts-node": "^10.9.2"}, "devDependencies": {"@types/jest": "^29.5.12", "jest": "^29.7.0", "ts-jest": "^29.1.5", "ts-loader": "^9.5.1", "typescript": "^5.5.2"}}