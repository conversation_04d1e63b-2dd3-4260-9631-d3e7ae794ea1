import type { Config } from "tailwindcss";

const config: Config = {
  darkMode: "class",
  content: ["./src/app/**/*.{js,ts,jsx,tsx,mdx}"],
  theme: {
    extend: {
      colors: {
        primary: "var(--primary)",
        primaryBg: "var(--primary-bg)",
        textSecondary: "var(--text-secondary)",
        greySecondary: "var(--grey-secondary)",
        blockBg: "var(--block-bg)",
        aside: "var(--aside)",
        inputBg: "var(--input-bg)",
        divider: "var(--divider)",
        success: "var(--success)",
        error: "var(--error)",
        primaryGradient: "var(--primary-gradient)",
        textPrimary: "var(--text-primary)",
        dashboardBG: "var(--dashboard-bg)",
      },
    },
  },
};
export default config;
