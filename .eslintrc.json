{"$schema": "https://json.schemastore.org/eslintrc", "root": true, "env": {"browser": true, "es2021": true}, "extends": ["eslint:recommended", "plugin:react/recommended", "plugin:@typescript-eslint/recommended", "plugin:react-hooks/recommended", "next/core-web-vitals", "plugin:tailwindcss/recommended", "prettier"], "overrides": [], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": "latest", "sourceType": "module"}, "plugins": ["react", "@typescript-eslint", "prettier", "simple-import-sort", "react-hooks", "tailwindcss"], "rules": {"@typescript-eslint/consistent-type-imports": "error", "@typescript-eslint/no-explicit-any": "off", "tailwindcss/no-contradicting-classname": "off", "react-hooks/exhaustive-deps": "error", "@typescript-eslint/no-unused-vars": "off", "simple-import-sort/imports": "off", "simple-import-sort/exports": "warn", "react/no-unescaped-entities": ["error", {"forbid": [">", "\"", "}"]}]}}